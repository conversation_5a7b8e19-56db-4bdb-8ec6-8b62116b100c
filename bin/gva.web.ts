#!/usr/bin/env node
import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import * as ecs from "aws-cdk-lib/aws-ecs";
import { config } from '../lib/config';
//import { prodConfig } from '../lib/prod.config';
import * as sm from "aws-cdk-lib/aws-secretsmanager";
import { PortMapping, Secret } from "aws-cdk-lib/aws-ecs/lib/container-definition";

// Our stack imports
import { EcsStack } from '../lib/ecs-stack';
import { PipelineStack } from '../lib/pipeline-stack';
import { InfrastructureStack } from '../lib/infrastructure-stack';

const app = new cdk.App();
let iStack: InfrastructureStack;

iStack = new InfrastructureStack(app, 'InfrastructureStack', {
  env: { account: config.awsAccount, region: config.awsRegion },
  config: config,
});

const CODE_REPOSITORY_OWNER = "Streamline-TS";
const CODE_REPOSITORY_NAME = "gva.web";

const CONTAINER_REPOSITORIES = [
  "gva-remix"
];
const CONTAINERS: { name: string, repo_name: string, image_registry?: string, logs: boolean, ports?: PortMapping[], env?: { [key: string]: string; }, secrets?: { [key: string]: Secret; } }[] = [{
  "name": "remix",
  "repo_name": "gva-remix",
  "logs": true,
  "ports": [ { "containerPort": 3000 } ],
  "secrets": {
    DATABASE_URL: ecs.Secret.fromSecretsManager(iStack.databaseUrlSecret, 'databaseUrl'),
    SESSION_SECRET: ecs.Secret.fromSecretsManager(iStack.encryptionSecret, 'sessionSecret'),
    SALT_TOKEN: ecs.Secret.fromSecretsManager(iStack.encryptionSecret, 'saltToken'),
    SUPERSET_USERNAME:ecs.Secret.fromSecretsManager(iStack.supersetSecret, 'supersetUsername'),
    SUPERSET_PASSWORD:ecs.Secret.fromSecretsManager(iStack.supersetSecret, 'supersetPassword'),
    SUPERSET_GUEST_USERNAME:ecs.Secret.fromSecretsManager(iStack.supersetSecret, 'supersetGuestUsername'),
    SUPERSET_GUEST_FIRST_NAME:ecs.Secret.fromSecretsManager(iStack.supersetSecret, 'supersetGuestFirstName'),
    SUPERSET_GUEST_LAST_NAME:ecs.Secret.fromSecretsManager(iStack.supersetSecret, 'supersetGuestLastName'),
    S3_ACCESS_KEY: ecs.Secret.fromSecretsManager(iStack.s3CredentialsSecret, 'access_key'),
    S3_SECRET_KEY: ecs.Secret.fromSecretsManager(iStack.s3CredentialsSecret, 'secret_key'),
    S3_REGION: ecs.Secret.fromSecretsManager(iStack.s3CredentialsSecret, 'region'),
    S3_BUCKET: ecs.Secret.fromSecretsManager(iStack.s3CredentialsSecret, 'bucket'),
    SPARKPOST_API_KEY: ecs.Secret.fromSecretsManager(iStack.emailSecret, 'sparkpost_api_key'),
    MAIL_FROM_ADDRESS: ecs.Secret.fromSecretsManager(iStack.emailSecret, 'mail_from_address'),
    CONTACT_FORM_EMAILS: ecs.Secret.fromSecretsManager(iStack.emailSecret, 'contact_form_emails'),
    CF_TURNSTILE_SITE_KEY: ecs.Secret.fromSecretsManager(iStack.cloudflareSecret, 'turnstile_site_key'),
    CF_TURNSTILE_SECRET: ecs.Secret.fromSecretsManager(iStack.cloudflareSecret, 'turnstile_secret'),
    SMARTY_STREETS_AUTH_ID: ecs.Secret.fromSecretsManager(iStack.geocodeSecret, 'smarty_streets_auth_id'),
    SMARTY_STREETS_AUTH_TOKEN: ecs.Secret.fromSecretsManager(iStack.geocodeSecret, 'smarty_streets_auth_token'),
    GOOGLE_GEOCODE_API_KEY: ecs.Secret.fromSecretsManager(iStack.geocodeSecret, 'google_geocode_api_key'),
    GEOCODIO_API_KEY: ecs.Secret.fromSecretsManager(iStack.geocodeSecret, 'geocodio_api_key'),
    TINY_API_KEY: ecs.Secret.fromSecretsManager(iStack.tinyMceSecret, 'tiny_api_key'),
    FLYDER_API_KEY: ecs.Secret.fromSecretsManager(iStack.flyderSecret, 'flyder_api_key'),
    REDIS_HOST: ecs.Secret.fromSecretsManager(iStack.redisSecret, 'redis_host'),
    REDIS_PORT: ecs.Secret.fromSecretsManager(iStack.redisSecret, 'redis_port'),
    REDIS_USER: ecs.Secret.fromSecretsManager(iStack.redisSecret, 'redis_user'),
    REDIS_PASS: ecs.Secret.fromSecretsManager(iStack.redisSecret, 'redis_pass'),
  },
  "env": {
    FILE_PUBLIC_URL: config.filePublicUrl,
  }
}];

// IP address that are allowed to access the load balancer (CloudFront addresses)
const LB_INGRESS = [
  '************/20',
  '************/22',
  '************/22',
  '**********/22',
  '************/18',
  '*************/18',
  '************/20',
  '************/20',
  '*************/22',
  '************/17',
  '***********/15',
  '**********/13',
  '**********/14',
  '**********/13',
  '**********/22',
];

// Path to the load balancer health check
const LB_HEALTH_CHECK = '/';

new EcsStack(app, 'EcsStack', {
  env: { account: config.awsAccount, region: config.awsRegion },
  vpc: iStack.vpc,
  auroraServerlessCluster: iStack.auroraServerlessCluster,
  containers: CONTAINERS,
  lbIngress: LB_INGRESS,
  lbHealthCheck: LB_HEALTH_CHECK,
  config: config
});

new PipelineStack(app, 'PipelineStack', {
  env: { account: config.awsAccount, region: config.awsRegion },
  containerRepositories: CONTAINER_REPOSITORIES,
  containers: CONTAINERS,
  codeRepositoryOwner: CODE_REPOSITORY_OWNER,
  codeRepositoryName: CODE_REPOSITORY_NAME,
  config: config,
});