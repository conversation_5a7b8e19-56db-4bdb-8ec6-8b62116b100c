import type { Config } from 'tailwindcss';

export default {
	content: [
		'./app/!(node_modules)/**/*.{js,jsx,ts,tsx}',
	],
	theme: {
		extend: {
			colors: {
				orange: {
					500: '#f65d0c', // orange-medium
					600: '#e54d00', // slightly darker for hover
					800: '#d64c02' // orange-dark
				},
				gray: {
					50: '#f9fafb',
					200: '#E5E5E5', // gray-light
					300: '#d1d5db',
					500: '#DDDDDD', // gray-medium
					600: '#666666',
					800: '#333333' // gray-dark
				},
				blue: {
					200: '#2764B0', // blue-light
					500: '#1E559A' // blue-medium
				}
			},
			keyframes: {
				fade: {
					'0%': { opacity: '0' },
					'50%': { opacity: '1' },
					'100%': { opacity: '0' }
				}
			},
			animation: {
				fade: 'fade 5s ease-in-out'
			},
			textDecorationThickness: {
				3: '3px'
			},
			textUnderlineOffset: {
				10: '10px'
			}
		},
		fontFamily: {
			onest: ['Onest', 'sans-serif']
		}
	},
	plugins: []
} satisfies Config;
