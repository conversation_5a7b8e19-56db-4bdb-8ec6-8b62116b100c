/** @type {import('eslint').Linter.Config} */
module.exports = {
	'root': true,
	'globals': {
		'Atomics': 'readonly',
		'SharedArrayBuffer': 'readonly',
		'fetch': false
	},
	'extends': [
		'plugin:prettier/recommended',
		'plugin:react/recommended',
		'eslint:recommended',
		'prettier',
		'plugin:tailwindcss/recommended',
		'@remix-run/eslint-config',
		'@remix-run/eslint-config/node'
	],
	'plugins': [
		'prettier',
		'eslint-plugin-prettier',
		'jsx-a11y',
		'react'
	],
	ignorePatterns: ['dist', '.eslintrc.cjs', 'src/tinymce'],
	'parser': '@typescript-eslint/parser',
	'parserOptions': {
		'sourceType': 'module',
		'requireConfigFile': false,
		'ecmaFeatures': {
			'jsx': true
		}
	},
	'rules': {
		'global-require': 0,
		'prettier/prettier': 2,
		'react/prop-types': 0,
		'react/display-name': 0,
		'react/no-array-index-key': 0,
		'react/destructuring-assignment': 0,
		'react/jsx-filename-extension': 0,
		'react/no-access-state-in-setstate': 1,
		'react/react-in-jsx-scope': 0,
		'react-hooks/rules-of-hooks': 2,
		'react-hooks/exhaustive-deps': 0,
		'jsx-a11y/no-static-element-interactions': 0,
		'jsx-a11y/click-events-have-key-events': 0,
		'jsx-a11y/no-noninteractive-element-interactions': 0
	},
	'settings': {
		'react': {
			'version': 'detect'
		}
	}
};
