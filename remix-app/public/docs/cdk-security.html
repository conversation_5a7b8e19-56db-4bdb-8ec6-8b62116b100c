<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en-US" data-preset="contrast" data-primary-color="#307FFF"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><meta charset="UTF-8"><meta name="robots" content="noindex"><meta name="built-on" content="2025-06-27T10:23:38.367971"><title>CDK Security | Docs</title><script type="application/json" id="virtual-toc-data">[{"id":"security-best-practices","level":0,"title":"Security Best Practices","anchor":"#security-best-practices"},{"id":"security-implementations","level":0,"title":"Security Implementations","anchor":"#security-implementations"},{"id":"compliance","level":0,"title":"Compliance","anchor":"#compliance"}]</script><script type="application/json" id="topic-shortcuts"></script><link href="https://resources.jetbrains.com/writerside/apidoc/6.22.0-b776/app.css" rel="stylesheet"><link rel="icon" type="image/svg" sizes="16x16" href="images/Gun-Violence-Archive-Logo-Icon.svg"><meta name="image" content=""><!-- Open Graph --><meta property="og:title" content="CDK Security | Docs"><meta property="og:description" content=""><meta property="og:image" content=""><meta property="og:site_name" content="Docs Help"><meta property="og:type" content="website"><meta property="og:locale" content="en_US"><meta property="og:url" content="writerside-documentation/docs/1.0/cdk-security.html"><!-- End Open Graph --><!-- Twitter Card --><meta name="twitter:card" content="summary_large_image"><meta name="twitter:site" content=""><meta name="twitter:title" content="CDK Security | Docs"><meta name="twitter:description" content=""><meta name="twitter:creator" content=""><meta name="twitter:image:src" content=""><!-- End Twitter Card --><!-- Schema.org WebPage --><script type="application/ld+json">{
    "@context": "http://schema.org",
    "@type": "WebPage",
    "@id": "writerside-documentation/docs/1.0/cdk-security.html#webpage",
    "url": "writerside-documentation/docs/1.0/cdk-security.html",
    "name": "CDK Security | Docs",
    "description": "",
    "image": "",
    "inLanguage":"en-US"
}</script><!-- End Schema.org --><!-- Schema.org WebSite --><script type="application/ld+json">{
    "@type": "WebSite",
    "@id": "writerside-documentationdocs/#website",
    "url": "writerside-documentationdocs/",
    "name": "Docs Help"
}</script><!-- End Schema.org --></head><body data-id="CDK-Security" data-main-title="CDK Security" data-article-props="{&quot;seeAlsoStyle&quot;:&quot;links&quot;}" data-template="article" data-breadcrumbs="CDK-Infrastructure.md|CDK Infrastructure Overview"><div class="wrapper"><main class="panel _main"><header class="panel__header"><div class="container"><h3>Docs 1.0 Help</h3><div class="panel-trigger"></div></div></header><section class="panel__content"><div class="container"><article class="article" data-shortcut-switcher="inactive"><h1 data-toc="CDK-Security" id="CDK-Security.md">CDK Security</h1><section class="chapter"><h2 id="security-best-practices" data-toc="security-best-practices">Security Best Practices</h2><section class="chapter"><h3 id="iam-configuration" data-toc="iam-configuration">IAM Configuration</h3><ul class="list _bullet" id="mzwjk1_9"><li class="list__item" id="mzwjk1_10"><p id="mzwjk1_14">Use least privilege principle</p></li><li class="list__item" id="mzwjk1_11"><p id="mzwjk1_15">Implement role-based access</p></li><li class="list__item" id="mzwjk1_12"><p id="mzwjk1_16">Regular permission audits</p></li><li class="list__item" id="mzwjk1_13"><p id="mzwjk1_17">Temporary credentials</p></li></ul></section><section class="chapter"><h3 id="network-security" data-toc="network-security">Network Security</h3><ul class="list _bullet" id="mzwjk1_18"><li class="list__item" id="mzwjk1_19"><p id="mzwjk1_23">VPC security groups</p></li><li class="list__item" id="mzwjk1_20"><p id="mzwjk1_24">Network ACLs</p></li><li class="list__item" id="mzwjk1_21"><p id="mzwjk1_25">Private subnets</p></li><li class="list__item" id="mzwjk1_22"><p id="mzwjk1_26">VPC endpoints</p></li></ul></section><section class="chapter"><h3 id="data-protection" data-toc="data-protection">Data Protection</h3><ul class="list _bullet" id="mzwjk1_27"><li class="list__item" id="mzwjk1_28"><p id="mzwjk1_32">Encryption at rest</p></li><li class="list__item" id="mzwjk1_29"><p id="mzwjk1_33">Encryption in transit</p></li><li class="list__item" id="mzwjk1_30"><p id="mzwjk1_34">Key rotation</p></li><li class="list__item" id="mzwjk1_31"><p id="mzwjk1_35">Backup encryption</p></li></ul></section></section><section class="chapter"><h2 id="security-implementations" data-toc="security-implementations">Security Implementations</h2><section class="chapter"><h3 id="iam-roles" data-toc="iam-roles">IAM Roles</h3><div class="code-block" data-lang="ts">
const taskRole = new iam.Role(this, 'TaskRole', {
  assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
  description: 'ECS task role'
});

taskRole.addManagedPolicy(
  iam.ManagedPolicy.fromAwsManagedPolicyName(
    'service-role/AmazonECSTaskExecutionRolePolicy'
  )
);
</div></section><section class="chapter"><h3 id="security-groups" data-toc="security-groups">Security Groups</h3><div class="code-block" data-lang="ts">
const dbSecurityGroup = new ec2.SecurityGroup(this, 'DBSecurityGroup', {
  vpc,
  description: 'Database security group',
  allowAllOutbound: false
});

dbSecurityGroup.addIngressRule(
  ec2.Peer.ipv4(vpc.vpcCidrBlock),
  ec2.Port.tcp(5432),
  'Allow database access from VPC'
);
</div></section></section><section class="chapter"><h2 id="compliance" data-toc="compliance">Compliance</h2><section class="chapter"><h3 id="audit-logging" data-toc="audit-logging">Audit Logging</h3><ul class="list _bullet" id="mzwjk1_42"><li class="list__item" id="mzwjk1_43"><p id="mzwjk1_47">CloudTrail configuration</p></li><li class="list__item" id="mzwjk1_44"><p id="mzwjk1_48">VPC Flow Logs</p></li><li class="list__item" id="mzwjk1_45"><p id="mzwjk1_49">Application logs</p></li><li class="list__item" id="mzwjk1_46"><p id="mzwjk1_50">Database audit logs</p></li></ul></section><section class="chapter"><h3 id="monitoring" data-toc="monitoring">Monitoring</h3><ul class="list _bullet" id="mzwjk1_51"><li class="list__item" id="mzwjk1_52"><p id="mzwjk1_56">Security alerts</p></li><li class="list__item" id="mzwjk1_53"><p id="mzwjk1_57">Access logging</p></li><li class="list__item" id="mzwjk1_54"><p id="mzwjk1_58">Resource monitoring</p></li><li class="list__item" id="mzwjk1_55"><p id="mzwjk1_59">Compliance checking</p></li></ul></section></section><div class="last-modified">21 March 2025</div><div data-feedback-placeholder="true"></div><div class="navigation-links _bottom"><a href="cdk-deployment.html" class="navigation-links__prev">CDK Deployment</a><a href="cdk-monitoring.html" class="navigation-links__next">CDK Monitoring</a></div></article><div id="disqus_thread"></div></div></section></main></div><script src="https://resources.jetbrains.com/writerside/apidoc/6.22.0-b776/app.js"></script></body></html>