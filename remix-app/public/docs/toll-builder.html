<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en-US" data-preset="contrast" data-primary-color="#307FFF"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><meta charset="UTF-8"><meta name="robots" content="noindex"><meta name="built-on" content="2025-06-27T10:23:38.364407"><title>Toll System | Docs</title><script type="application/json" id="virtual-toc-data">[{"id":"overview","level":0,"title":"Overview","anchor":"#overview"},{"id":"available-tolls","level":0,"title":"Available Tolls","anchor":"#available-tolls"},{"id":"base-toll-class","level":0,"title":"Base Toll Class","anchor":"#base-toll-class"},{"id":"example-implementations","level":0,"title":"Example Implementations","anchor":"#example-implementations"},{"id":"usage-examples","level":0,"title":"Usage Examples","anchor":"#usage-examples"},{"id":"toll-data-structure","level":0,"title":"Toll Data Structure","anchor":"#toll-data-structure"},{"id":"factory-pattern","level":0,"title":"Factory Pattern","anchor":"#factory-pattern"},{"id":"additional-functions","level":0,"title":"Additional Functions","anchor":"#additional-functions"},{"id":"best-practices","level":0,"title":"Best Practices","anchor":"#best-practices"}]</script><script type="application/json" id="topic-shortcuts"></script><link href="https://resources.jetbrains.com/writerside/apidoc/6.22.0-b776/app.css" rel="stylesheet"><link rel="icon" type="image/svg" sizes="16x16" href="images/Gun-Violence-Archive-Logo-Icon.svg"><meta name="image" content=""><!-- Open Graph --><meta property="og:title" content="Toll System | Docs"><meta property="og:description" content=""><meta property="og:image" content=""><meta property="og:site_name" content="Docs Help"><meta property="og:type" content="website"><meta property="og:locale" content="en_US"><meta property="og:url" content="writerside-documentation/docs/1.0/toll-builder.html"><!-- End Open Graph --><!-- Twitter Card --><meta name="twitter:card" content="summary_large_image"><meta name="twitter:site" content=""><meta name="twitter:title" content="Toll System | Docs"><meta name="twitter:description" content=""><meta name="twitter:creator" content=""><meta name="twitter:image:src" content=""><!-- End Twitter Card --><!-- Schema.org WebPage --><script type="application/ld+json">{
    "@context": "http://schema.org",
    "@type": "WebPage",
    "@id": "writerside-documentation/docs/1.0/toll-builder.html#webpage",
    "url": "writerside-documentation/docs/1.0/toll-builder.html",
    "name": "Toll System | Docs",
    "description": "",
    "image": "",
    "inLanguage":"en-US"
}</script><!-- End Schema.org --><!-- Schema.org WebSite --><script type="application/ld+json">{
    "@type": "WebSite",
    "@id": "writerside-documentationdocs/#website",
    "url": "writerside-documentationdocs/",
    "name": "Docs Help"
}</script><!-- End Schema.org --></head><body data-id="Toll-Builder" data-main-title="Toll System" data-article-props="{&quot;seeAlsoStyle&quot;:&quot;links&quot;}" data-template="article" data-breadcrumbs=""><div class="wrapper"><main class="panel _main"><header class="panel__header"><div class="container"><h3>Docs 1.0 Help</h3><div class="panel-trigger"></div></div></header><section class="panel__content"><div class="container"><article class="article" data-shortcut-switcher="inactive"><h1 data-toc="Toll-Builder" id="Toll-Builder.md">Toll System</h1><section class="chapter"><h2 id="overview" data-toc="overview">Overview</h2><p id="-p0t4at_12">The toll system tracks and reports various statistics related to gun violence incidents. It provides a flexible framework for querying and displaying different types of incident data.</p></section><section class="chapter"><h2 id="available-tolls" data-toc="available-tolls">Available Tolls</h2><section class="chapter"><h3 id="standard-tolls" data-toc="standard-tolls">Standard Tolls</h3><div class="code-block" data-lang="ts">
const standardTolls = {
    'TotalDeaths': 'Total Deaths',
    'Homicides': 'Homicides',
    'Suicides': 'Suicides',
    'NumberOfInjuries': 'Number of Injuries',
    'MassShooting': 'Mass Shootings',
    'MassMurder': 'Mass Murders',
    'NumberOfChildrenKilled': 'Children (0-11) Killed',
    'NumberOfChildrenInjured': 'Children (0-11) Injured',
    'NumberOfTeensKilled': 'Teens (12-17) Killed',
    'NumberOfTeensInjured': 'Teens (12-17) Injured',
    'OfficerInvolvedIncident': 'Officer Involved Shootings',
    'OfficerShotKilled': 'Officers Shot/Killed',
    'OfficerInvolvedPerpetrator': 'Officer Involved Incidents',
    'PerpetratorShotKilled': 'Subjects-Suspects Shot/Killed',
    'DefensiveUse': 'Defensive Use',
    'AccidentalShooting': 'Unintentional Shootings',
    'MurderSuicide': 'Murder/Suicides'
}
</div></section><section class="chapter"><h3 id="review-tolls" data-toc="review-tolls">Review Tolls</h3><div class="code-block" data-lang="ts">
const reviewTolls = {
    'AccidentalShooting': 'Unintentional Shootings',
    'DefensiveGunUse': 'Defensive Gun Use',
    'MassMurder': 'Mass Murders',
    'MassShooting': 'Mass Shootings',
    'MurderSuicides': 'Murder/Suicides',
    'ChildrenInjured': 'Children (0-11) Injured',
    'ChildrenKilled': 'Children (0-11) Killed',
    'Deaths': 'Deaths',
    'Injuries': 'Injuries',
    'TeensInjured': 'Teens (12-17) Injured',
    'TeensKilled': 'Teens (12-17) Killed',
    'OisOfficerInjured': 'Officers Injured',
    'OisOfficerKilled': 'Officers Killed',
    'OisSuspectKilled': 'Subjects-Suspects Killed',
    'OisSuspectInjured': 'Subjects-Suspects Injured'
}
</div></section><section class="chapter"><h3 id="legacy-tolls" data-toc="legacy-tolls">Legacy Tolls</h3><div class="code-block" data-lang="ts">
const legacyTolls = {
    'TotalOld': 'Total Incidents',
    'NumberOfDeathsOld': 'Number of Deaths',
    'NumberOfInjuriesOld': 'Number of Injuries',
    'NumberOfChildrenKilledInjuredOld': 'Children (0-11) Killed or Injured',
    'NumberOfTeensKilledInjuredOld': 'Teens (12-17) Killed or Injured',
    'OfficerShotKilledOld': 'Officers Shot/Killed',
    'PerpetratorShotKilledOld': 'Subjects-Suspects Shot/Killed',
    'HomeInvasionOld': 'Home Invasions',
    'DefensiveUse': 'Defensive Use',
    'AccidentalShooting': 'Unintentional Shootings'
}
</div></section></section><section class="chapter"><h2 id="base-toll-class" data-toc="base-toll-class">Base Toll Class</h2><div class="code-block" data-lang="ts">
abstract class TollItem {
    protected yearRange: TollYearRange;
    protected stateAbbrev?: string;
    protected stateTid?: number;
    protected district?: string;

    constructor(
        yearRange: TollYearRange,
        stateAbbrev?: string,
        stateTid?: number,
        district?: string
    ) {
        this.yearRange = yearRange;
        this.stateAbbrev = stateAbbrev;
        this.stateTid = stateTid;
        this.district = district;
    }

    abstract getLabel(): string;
    abstract getValue(results: any): Promise&lt;number&gt;;
    abstract getValueLabel(): string;
    abstract getHideLabel(): boolean;
    abstract getParentItem(): string | false;
    abstract getReportLink(isPastToll: boolean): string;
}
</div></section><section class="chapter"><h2 id="example-implementations" data-toc="example-implementations">Example Implementations</h2><section class="chapter"><h3 id="total-deaths-toll" data-toc="total-deaths-toll">Total Deaths Toll</h3><div class="code-block" data-lang="ts">
class TotalDeathsToll extends TollItem {
    getLabel(): string {
        return 'Total Deaths';
    }

    async getValue(results: any): Promise&lt;number&gt; {
        return results.TotalDeathsToll || 0;
    }

    getValueLabel(): string {
        return 'Deaths';
    }

    getHideLabel(): boolean {
        return false;
    }

    getParentItem(): string | false {
        return false;
    }

    getReportLink(isPastToll: boolean): string {
        return `/reports/total-deaths`;
    }
}
</div></section><section class="chapter"><h3 id="mass-shooting-toll" data-toc="mass-shooting-toll">Mass Shooting Toll</h3><div class="code-block" data-lang="ts">
class MassShootingToll extends TollItem {
    getLabel(): string {
        return 'Mass Shootings';
    }

    async getValue(results: any): Promise&lt;number&gt; {
        return results.MassShootingToll || 0;
    }

    getValueLabel(): string {
        return 'Incidents';
    }

    getHideLabel(): boolean {
        return false;
    }

    getParentItem(): string | false {
        return false;
    }

    getReportLink(isPastToll: boolean): string {
        return `/reports/mass-shootings`;
    }
}
</div></section></section><section class="chapter"><h2 id="usage-examples" data-toc="usage-examples">Usage Examples</h2><section class="chapter"><h3 id="creating-a-toll-instance" data-toc="creating-a-toll-instance">Creating a Toll Instance</h3><div class="code-block" data-lang="ts">
const factory = new TollItemFactory();
const toll = factory.factory(
    'TotalDeaths',
    yearRange,
    stateAbbrev,
    stateTid,
    district
);
</div></section><section class="chapter"><h3 id="adding-toll-to-querybuilder" data-toc="adding-toll-to-querybuilder">Adding Toll to QueryBuilder</h3><div class="code-block" data-lang="ts">
const qb = new QueryBuilder('gva_data.incidents', 'inc');
await qb.addTollItem(toll);
const results = await qb.execute(true);
const value = await toll.getValue(results);
</div></section><section class="chapter"><h3 id="generating-toll-report" data-toc="generating-toll-report">Generating Toll Report</h3><div class="code-block" data-lang="ts">
const data: TollData = {
    year_range: validateYearRange('2023'),
    state_abbrev: 'CA'
};

await generateTollImage(data, ['state-toll', 'CA']);
</div></section></section><section class="chapter"><h2 id="toll-data-structure" data-toc="toll-data-structure">Toll Data Structure</h2><div class="code-block" data-lang="ts">
interface TollData {
    year_range?: TollYearRange;
    state?: string;
    state_abbrev?: string;
    district?: string;
    past_toll?: boolean;
    values?: { [key: string]: TollValue };
    totals?: { [key: string]: number };
    footer?: string;
    date_validated?: string;
}

interface TollValue {
    label: string;
    value: string;
    valueLabel: string;
    hideLabel: boolean;
    report: string;
    parent: string | false;
}

interface TollYearRange {
    start: number;
    end: number;
    str: string;
}
</div></section><section class="chapter"><h2 id="factory-pattern" data-toc="factory-pattern">Factory Pattern</h2><div class="code-block" data-lang="ts">
class TollItemFactory {
    private type: string;

    constructor(type: string = 'standard') {
        this.type = type;
    }

    factory(
        name: string,
        yearRange: TollYearRange,
        stateAbbrev?: string,
        stateTid?: number,
        district?: string
    ): TollItem {
        const classes = this.type === 'review' ? reviewClasses : tollClasses;
        if (classes[name]) {
            return new classes[name](yearRange, stateAbbrev, stateTid, district);
        }
        throw new Error(`Invalid toll type: ${name}`);
    }

    all(): string[] {
        return Object.keys(this.type === 'review' ? reviewClasses : tollClasses);
    }

    review(): string[] {
        return [
            'AccidentalShooting',
            'DefensiveGunUse',
            'MassMurder',
            'MassShooting',
            'MurderSuicides',
            'ChildrenInjured',
            'ChildrenKilled',
            'Deaths',
            'Injuries',
            'TeensInjured',
            'TeensKilled',
            'OisOfficerInjured',
            'OisOfficerKilled',
            'OisSuspectKilled',
            'OisSuspectInjured'
        ];
    }

    past(): string[] {
        return [
            'TotalOld',
            'NumberOfDeathsOld',
            'NumberOfInjuriesOld',
            'NumberOfChildrenKilledInjuredOld',
            'NumberOfTeensKilledInjuredOld',
            'MassShooting',
            'OfficerShotKilledOld',
            'PerpetratorShotKilledOld',
            'HomeInvasionOld',
            'DefensiveUse',
            'AccidentalShooting'
        ];
    }
}
</div></section><section class="chapter"><h2 id="additional-functions" data-toc="additional-functions">Additional Functions</h2><section class="chapter"><h3 id="state-and-geography-handling" data-toc="state-and-geography-handling">State and Geography Handling</h3><div class="code-block" data-lang="ts">
async function getTaxonomyStateByAbbr(
    abbr: string
): Promise&lt;{tid: number, value: string} | undefined&gt;
</div><p id="-p0t4at_38">Retrieves state taxonomy information.</p><ul class="list _bullet" id="-p0t4at_39"><li class="list__item" id="-p0t4at_40"><p id="-p0t4at_43">Maps state abbreviations to full names</p></li><li class="list__item" id="-p0t4at_41"><p id="-p0t4at_44">Returns taxonomy ID and state name</p></li><li class="list__item" id="-p0t4at_42"><p id="-p0t4at_45">Used for geographic filtering</p></li></ul></section><section class="chapter"><h3 id="year-range-processing" data-toc="year-range-processing">Year Range Processing</h3><div class="code-block" data-lang="ts">
function validateYearRange(
    year: string,
    convertToTimestamp: boolean = true,
    returnArray: boolean = false
): TollYearRange | TollYearRange[]
</div><p id="-p0t4at_47">Enhanced year range validation with features:</p><ul class="list _bullet" id="-p0t4at_48"><li class="list__item" id="-p0t4at_49"><p id="-p0t4at_53">Supports single years and ranges (e.g., &quot;2014-2023&quot;)</p></li><li class="list__item" id="-p0t4at_50"><p id="-p0t4at_54">Validates against allowed range (2014 to current year)</p></li><li class="list__item" id="-p0t4at_51"><p id="-p0t4at_55">Optional array return for multi-year processing</p></li><li class="list__item" id="-p0t4at_52"><p id="-p0t4at_56">Timestamp conversion for database queries</p></li></ul></section><section class="chapter"><h3 id="image-generation" data-toc="image-generation">Image Generation</h3><div class="code-block" data-lang="ts">
async function generateTollImage(
    data: TollData,
    segments?: Array&lt;string&gt;,
    file_path?: string
): Promise&lt;void&gt;
</div><p id="-p0t4at_58">Enhanced image generation with:</p><ul class="list _bullet" id="-p0t4at_59"><li class="list__item" id="-p0t4at_60"><p id="-p0t4at_64">Logo embedding support</p></li><li class="list__item" id="-p0t4at_61"><p id="-p0t4at_65">Handlebars template processing</p></li><li class="list__item" id="-p0t4at_62"><p id="-p0t4at_66">Hierarchical toll organization</p></li><li class="list__item" id="-p0t4at_63"><p id="-p0t4at_67">Date formatting and validation</p></li></ul></section><section class="chapter"><h3 id="data-processing" data-toc="data-processing">Data Processing</h3><div class="code-block" data-lang="ts">
interface ProcessedToll {
    name: string;
    info: {
        label: string;
        valueLabel: string;
        hideLabel: boolean;
        report: string;
        parent: string | false;
    };
    years: Array&lt;{
        year: string;
        value: string;
    }&gt;;
}
</div><div class="code-block" data-lang="ts">
interface NumericToll {
    name: string;
    years: Array&lt;{
        year: string;
        value: number;
    }&gt;;
}
</div></section><section class="chapter"><h3 id="utility-functions" data-toc="utility-functions">Utility Functions</h3><div class="code-block" data-lang="ts">
function number_format(value: number): string
</div><p id="-p0t4at_71">Formats numbers for display:</p><ul class="list _bullet" id="-p0t4at_72"><li class="list__item" id="-p0t4at_73"><p id="-p0t4at_76">Adds thousands separators</p></li><li class="list__item" id="-p0t4at_74"><p id="-p0t4at_77">Handles decimal places</p></li><li class="list__item" id="-p0t4at_75"><p id="-p0t4at_78">Localizes number format</p></li></ul></section></section><section class="chapter"><h2 id="best-practices" data-toc="best-practices">Best Practices</h2><ol class="list _decimal" id="-p0t4at_79" type="1"><li class="list__item" id="-p0t4at_80"><p id="-p0t4at_85"><span class="control" id="-p0t4at_87">Extending Base Toll</span></p><ul class="list _bullet" id="-p0t4at_86"><li class="list__item" id="-p0t4at_88"><p id="-p0t4at_91">Always extend the <code class="code" id="-p0t4at_92">TollItem</code> base class</p></li><li class="list__item" id="-p0t4at_89"><p id="-p0t4at_93">Implement all abstract methods</p></li><li class="list__item" id="-p0t4at_90"><p id="-p0t4at_94">Use consistent naming conventions</p></li></ul></li><li class="list__item" id="-p0t4at_81"><p id="-p0t4at_95"><span class="control" id="-p0t4at_97">Query Building</span></p><ul class="list _bullet" id="-p0t4at_96"><li class="list__item" id="-p0t4at_98"><p id="-p0t4at_101">Use the <code class="code" id="-p0t4at_102">QueryBuilder</code> class for all database queries</p></li><li class="list__item" id="-p0t4at_99"><p id="-p0t4at_103">Implement proper filtering and joins</p></li><li class="list__item" id="-p0t4at_100"><p id="-p0t4at_104">Handle null/undefined values</p></li></ul></li><li class="list__item" id="-p0t4at_82"><p id="-p0t4at_105"><span class="control" id="-p0t4at_107">Data Validation</span></p><ul class="list _bullet" id="-p0t4at_106"><li class="list__item" id="-p0t4at_108"><p id="-p0t4at_111">Validate year ranges</p></li><li class="list__item" id="-p0t4at_109"><p id="-p0t4at_112">Check state and district values</p></li><li class="list__item" id="-p0t4at_110"><p id="-p0t4at_113">Handle missing data gracefully</p></li></ul></li><li class="list__item" id="-p0t4at_83"><p id="-p0t4at_114"><span class="control" id="-p0t4at_116">Performance</span></p><ul class="list _bullet" id="-p0t4at_115"><li class="list__item" id="-p0t4at_117"><p id="-p0t4at_120">Use caching where appropriate</p></li><li class="list__item" id="-p0t4at_118"><p id="-p0t4at_121">Optimize database queries</p></li><li class="list__item" id="-p0t4at_119"><p id="-p0t4at_122">Implement proper indexing</p></li></ul></li><li class="list__item" id="-p0t4at_84"><p id="-p0t4at_123"><span class="control" id="-p0t4at_125">Error Handling</span></p><ul class="list _bullet" id="-p0t4at_124"><li class="list__item" id="-p0t4at_126"><p id="-p0t4at_129">Handle database errors</p></li><li class="list__item" id="-p0t4at_127"><p id="-p0t4at_130">Validate input parameters</p></li><li class="list__item" id="-p0t4at_128"><p id="-p0t4at_131">Provide meaningful error messages</p></li></ul></li></ol></section><div class="last-modified">21 March 2025</div><div data-feedback-placeholder="true"></div><div class="navigation-links _bottom"><a href="table-users.html" class="navigation-links__prev">Users Table</a><a href="toll-items-implementation.html" class="navigation-links__next">Toll Items Implementation Guide</a></div></article><div id="disqus_thread"></div></div></section></main></div><script src="https://resources.jetbrains.com/writerside/apidoc/6.22.0-b776/app.js"></script></body></html>