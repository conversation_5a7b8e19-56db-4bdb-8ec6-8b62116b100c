<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en-US" data-preset="contrast" data-primary-color="#307FFF"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><meta charset="UTF-8"><meta name="robots" content="noindex"><meta name="built-on" content="2025-06-27T10:23:38.365281"><title>Incident Types Table | Docs</title><script type="application/json" id="virtual-toc-data">[{"id":"overview","level":0,"title":"Overview","anchor":"#overview"},{"id":"table-structure","level":0,"title":"Table Structure","anchor":"#table-structure"},{"id":"common-queries","level":0,"title":"Common Queries","anchor":"#common-queries"},{"id":"data-processing","level":0,"title":"Data Processing","anchor":"#data-processing"},{"id":"best-practices","level":0,"title":"Best Practices","anchor":"#best-practices"}]</script><script type="application/json" id="topic-shortcuts"></script><link href="https://resources.jetbrains.com/writerside/apidoc/6.22.0-b776/app.css" rel="stylesheet"><link rel="icon" type="image/svg" sizes="16x16" href="images/Gun-Violence-Archive-Logo-Icon.svg"><meta name="image" content=""><!-- Open Graph --><meta property="og:title" content="Incident Types Table | Docs"><meta property="og:description" content=""><meta property="og:image" content=""><meta property="og:site_name" content="Docs Help"><meta property="og:type" content="website"><meta property="og:locale" content="en_US"><meta property="og:url" content="writerside-documentation/docs/1.0/table-incident-types.html"><!-- End Open Graph --><!-- Twitter Card --><meta name="twitter:card" content="summary_large_image"><meta name="twitter:site" content=""><meta name="twitter:title" content="Incident Types Table | Docs"><meta name="twitter:description" content=""><meta name="twitter:creator" content=""><meta name="twitter:image:src" content=""><!-- End Twitter Card --><!-- Schema.org WebPage --><script type="application/ld+json">{
    "@context": "http://schema.org",
    "@type": "WebPage",
    "@id": "writerside-documentation/docs/1.0/table-incident-types.html#webpage",
    "url": "writerside-documentation/docs/1.0/table-incident-types.html",
    "name": "Incident Types Table | Docs",
    "description": "",
    "image": "",
    "inLanguage":"en-US"
}</script><!-- End Schema.org --><!-- Schema.org WebSite --><script type="application/ld+json">{
    "@type": "WebSite",
    "@id": "writerside-documentationdocs/#website",
    "url": "writerside-documentationdocs/",
    "name": "Docs Help"
}</script><!-- End Schema.org --></head><body data-id="Table-Incident-Types" data-main-title="Incident Types Table" data-article-props="{&quot;seeAlsoStyle&quot;:&quot;links&quot;}" data-template="article" data-breadcrumbs="Database-Structure.md|Database Structure///Schema-GVA-Data.md|GVA Data Schema"><div class="wrapper"><main class="panel _main"><header class="panel__header"><div class="container"><h3>Docs 1.0 Help</h3><div class="panel-trigger"></div></div></header><section class="panel__content"><div class="container"><article class="article" data-shortcut-switcher="inactive"><h1 data-toc="Table-Incident-Types" id="Table-Incident-Types.md">Incident Types Table</h1><section class="chapter"><h2 id="overview" data-toc="overview">Overview</h2><p id="a4i2tj_8">The <code class="code" id="a4i2tj_9">incident_types</code> table manages the classification and categorization of gun violence incidents, allowing multiple type assignments per incident with weighted relationships.</p></section><section class="chapter"><h2 id="table-structure" data-toc="table-structure">Table Structure</h2><section class="chapter"><h3 id="primary-fields" data-toc="primary-fields">Primary Fields</h3><ul class="list _bullet" id="a4i2tj_12"><li class="list__item" id="a4i2tj_13"><p id="a4i2tj_17"><code class="code" id="a4i2tj_18">incident_id</code>: Part of composite primary key, references incidents table</p></li><li class="list__item" id="a4i2tj_14"><p id="a4i2tj_19"><code class="code" id="a4i2tj_20">type_tid</code>: Part of composite primary key, references taxonomy table</p></li><li class="list__item" id="a4i2tj_15"><p id="a4i2tj_21"><code class="code" id="a4i2tj_22">weight</code>: Integer indicating the significance of this type classification</p></li><li class="list__item" id="a4i2tj_16"><p id="a4i2tj_23"><code class="code" id="a4i2tj_24">changed_date</code>: Unix timestamp of last modification</p></li></ul></section><section class="chapter"><h3 id="related-tables" data-toc="related-tables">Related Tables</h3><ul class="list _bullet" id="a4i2tj_25"><li class="list__item" id="a4i2tj_26"><p id="a4i2tj_30">Links to <code class="code" id="a4i2tj_31">incidents</code> table via <code class="code" id="a4i2tj_32">incident_id</code></p></li><li class="list__item" id="a4i2tj_27"><p id="a4i2tj_33">Links to <code class="code" id="a4i2tj_34">taxonomy</code> table via <code class="code" id="a4i2tj_35">type_tid</code> for type definitions</p></li><li class="list__item" id="a4i2tj_28"><p id="a4i2tj_36">Has corresponding <code class="code" id="a4i2tj_37">incident_types_pending</code> table for draft changes</p></li><li class="list__item" id="a4i2tj_29"><p id="a4i2tj_38">Has corresponding <code class="code" id="a4i2tj_39">incident_types_temp</code> table for bulk operations</p></li></ul></section></section><section class="chapter"><h2 id="common-queries" data-toc="common-queries">Common Queries</h2><section class="chapter"><h3 id="type-assignment" data-toc="type-assignment">Type Assignment</h3><div class="code-block" data-lang="ts">
const qb = new QueryBuilder('gva_data.incident_types', 'it');
qb.join('gva_data.taxonomy', 't.tid = it.type_tid', 'inner', 't');
qb.where('t.vocabulary', '=', 'incident_types');
</div></section><section class="chapter"><h3 id="mass-shooting-analysis" data-toc="mass-shooting-analysis">Mass Shooting Analysis</h3><div class="code-block" data-lang="ts">
const massTid = await qb.getTaxonomyItem('incident_types', 'mass_shooting');
qb.where('it.type_tid', '=', massTid);
qb.where('it.weight', '&gt;=', 1);
</div></section></section><section class="chapter"><h2 id="data-processing" data-toc="data-processing">Data Processing</h2><section class="chapter"><h3 id="pending-types" data-toc="pending-types">Pending Types</h3><p id="a4i2tj_46">The system maintains three related tables for type management:</p><ul class="list _bullet" id="a4i2tj_47"><li class="list__item" id="a4i2tj_48"><p id="a4i2tj_51"><code class="code" id="a4i2tj_52">incident_types</code>: Primary table for active classifications</p></li><li class="list__item" id="a4i2tj_49"><p id="a4i2tj_53"><code class="code" id="a4i2tj_54">incident_types_pending</code>: Holds proposed changes awaiting validation</p></li><li class="list__item" id="a4i2tj_50"><p id="a4i2tj_55"><code class="code" id="a4i2tj_56">incident_types_temp</code>: Temporary storage for bulk operations</p></li></ul></section><section class="chapter"><h3 id="type-migration" data-toc="type-migration">Type Migration</h3><div class="code-block" data-lang="ts">
// Example of promoting pending types to active
const promotePendingTypes = async (incidentId: number) =&gt; {
    await prisma.$transaction([
        prisma.incident_types.deleteMany({
            where: { incident_id: incidentId }
        }),
        prisma.$executeRaw`
            INSERT INTO gva_data.incident_types 
            SELECT * FROM gva_data.incident_types_pending 
            WHERE incident_id = ${incidentId}
        `
    ]);
};
</div></section></section><section class="chapter"><h2 id="best-practices" data-toc="best-practices">Best Practices</h2><ol class="list _decimal" id="a4i2tj_58" type="1"><li class="list__item" id="a4i2tj_59"><p id="a4i2tj_64">Always validate type taxonomy IDs before assignment</p></li><li class="list__item" id="a4i2tj_60"><p id="a4i2tj_65">Maintain consistent weighting criteria</p></li><li class="list__item" id="a4i2tj_61"><p id="a4i2tj_66">Use transaction blocks for type updates</p></li><li class="list__item" id="a4i2tj_62"><p id="a4i2tj_67">Regular validation of pending types</p></li><li class="list__item" id="a4i2tj_63"><p id="a4i2tj_68">Implement proper change tracking</p></li></ol></section><div class="last-modified">21 March 2025</div><div data-feedback-placeholder="true"></div><div class="navigation-links _bottom"><a href="table-incidents.html" class="navigation-links__prev">Incidents Table</a><a href="table-incident-participants.html" class="navigation-links__next">Incident Participants Table</a></div></article><div id="disqus_thread"></div></div></section></main></div><script src="https://resources.jetbrains.com/writerside/apidoc/6.22.0-b776/app.js"></script></body></html>