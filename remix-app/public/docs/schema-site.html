<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en-US" data-preset="contrast" data-primary-color="#307FFF"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><meta charset="UTF-8"><meta name="robots" content="noindex"><meta name="built-on" content="2025-06-27T10:23:38.294191"><title>Site Schema | Docs</title><script type="application/json" id="virtual-toc-data">[{"id":"overview","level":0,"title":"Overview","anchor":"#overview"},{"id":"core-components","level":0,"title":"Core Components","anchor":"#core-components"},{"id":"schema-structure","level":0,"title":"Schema Structure","anchor":"#schema-structure"},{"id":"common-access-patterns","level":0,"title":"Common Access Patterns","anchor":"#common-access-patterns"},{"id":"security-considerations","level":0,"title":"Security Considerations","anchor":"#security-considerations"},{"id":"best-practices","level":0,"title":"Best Practices","anchor":"#best-practices"}]</script><script type="application/json" id="topic-shortcuts"></script><link href="https://resources.jetbrains.com/writerside/apidoc/6.22.0-b776/app.css" rel="stylesheet"><link rel="icon" type="image/svg" sizes="16x16" href="images/Gun-Violence-Archive-Logo-Icon.svg"><meta name="image" content=""><!-- Open Graph --><meta property="og:title" content="Site Schema | Docs"><meta property="og:description" content=""><meta property="og:image" content=""><meta property="og:site_name" content="Docs Help"><meta property="og:type" content="website"><meta property="og:locale" content="en_US"><meta property="og:url" content="writerside-documentation/docs/1.0/schema-site.html"><!-- End Open Graph --><!-- Twitter Card --><meta name="twitter:card" content="summary_large_image"><meta name="twitter:site" content=""><meta name="twitter:title" content="Site Schema | Docs"><meta name="twitter:description" content=""><meta name="twitter:creator" content=""><meta name="twitter:image:src" content=""><!-- End Twitter Card --><!-- Schema.org WebPage --><script type="application/ld+json">{
    "@context": "http://schema.org",
    "@type": "WebPage",
    "@id": "writerside-documentation/docs/1.0/schema-site.html#webpage",
    "url": "writerside-documentation/docs/1.0/schema-site.html",
    "name": "Site Schema | Docs",
    "description": "",
    "image": "",
    "inLanguage":"en-US"
}</script><!-- End Schema.org --><!-- Schema.org WebSite --><script type="application/ld+json">{
    "@type": "WebSite",
    "@id": "writerside-documentationdocs/#website",
    "url": "writerside-documentationdocs/",
    "name": "Docs Help"
}</script><!-- End Schema.org --></head><body data-id="Schema-Site" data-main-title="Site Schema" data-article-props="{&quot;seeAlsoStyle&quot;:&quot;links&quot;}" data-template="article" data-breadcrumbs="Database-Structure.md|Database Structure"><div class="wrapper"><main class="panel _main"><header class="panel__header"><div class="container"><h3>Docs 1.0 Help</h3><div class="panel-trigger"></div></div></header><section class="panel__content"><div class="container"><article class="article" data-shortcut-switcher="inactive"><h1 data-toc="Schema-Site" id="Schema-Site.md">Site Schema</h1><section class="chapter"><h2 id="overview" data-toc="overview">Overview</h2><p id="-hbxo0d_9">The <code class="code" id="-hbxo0d_10">site</code> schema manages all website-related content, configurations, and user management aspects of the Gun Violence Archive platform. This schema is separate from the core incident data to maintain clear boundaries between content management and data collection.</p></section><section class="chapter"><h2 id="core-components" data-toc="core-components">Core Components</h2><section class="chapter"><h3 id="content-management" data-toc="content-management">Content Management</h3><ul class="list _bullet" id="-hbxo0d_12"><li class="list__item" id="-hbxo0d_13"><p id="-hbxo0d_17"><code class="code" id="-hbxo0d_18">content</code>: Manages editable website content</p></li><li class="list__item" id="-hbxo0d_14"><p id="-hbxo0d_19"><code class="code" id="-hbxo0d_20">files</code>: Handles file uploads and media assets</p></li><li class="list__item" id="-hbxo0d_15"><p id="-hbxo0d_21"><code class="code" id="-hbxo0d_22">configurations</code>: Stores system settings and parameters</p></li><li class="list__item" id="-hbxo0d_16"><p id="-hbxo0d_23"><code class="code" id="-hbxo0d_24">users</code>: Manages user accounts and authentication</p></li></ul></section></section><section class="chapter"><h2 id="schema-structure" data-toc="schema-structure">Schema Structure</h2><section class="chapter"><h3 id="content-management-tables" data-toc="content-management-tables">Content Management Tables</h3><ul class="list _bullet" id="-hbxo0d_27"><li class="list__item" id="-hbxo0d_28"><p id="-hbxo0d_30"><code class="code" id="-hbxo0d_32">content</code>: Dynamic website content</p><ul class="list _bullet" id="-hbxo0d_31"><li class="list__item" id="-hbxo0d_33"><p id="-hbxo0d_36">Versioned content storage</p></li><li class="list__item" id="-hbxo0d_34"><p id="-hbxo0d_37">Section-based organization</p></li><li class="list__item" id="-hbxo0d_35"><p id="-hbxo0d_38">Multi-language support</p></li></ul></li><li class="list__item" id="-hbxo0d_29"><p id="-hbxo0d_39"><code class="code" id="-hbxo0d_41">files</code>: Media and document storage</p><ul class="list _bullet" id="-hbxo0d_40"><li class="list__item" id="-hbxo0d_42"><p id="-hbxo0d_45">Image management</p></li><li class="list__item" id="-hbxo0d_43"><p id="-hbxo0d_46">Document archives</p></li><li class="list__item" id="-hbxo0d_44"><p id="-hbxo0d_47">Upload tracking</p></li></ul></li></ul></section><section class="chapter"><h3 id="system-management-tables" data-toc="system-management-tables">System Management Tables</h3><ul class="list _bullet" id="-hbxo0d_48"><li class="list__item" id="-hbxo0d_49"><p id="-hbxo0d_51"><code class="code" id="-hbxo0d_53">configurations</code>: System settings</p><ul class="list _bullet" id="-hbxo0d_52"><li class="list__item" id="-hbxo0d_54"><p id="-hbxo0d_57">Feature flags</p></li><li class="list__item" id="-hbxo0d_55"><p id="-hbxo0d_58">System parameters</p></li><li class="list__item" id="-hbxo0d_56"><p id="-hbxo0d_59">Site customizations</p></li></ul></li><li class="list__item" id="-hbxo0d_50"><p id="-hbxo0d_60"><code class="code" id="-hbxo0d_62">users</code>: User management</p><ul class="list _bullet" id="-hbxo0d_61"><li class="list__item" id="-hbxo0d_63"><p id="-hbxo0d_66">Authentication</p></li><li class="list__item" id="-hbxo0d_64"><p id="-hbxo0d_67">Role-based access</p></li><li class="list__item" id="-hbxo0d_65"><p id="-hbxo0d_68">User profiles</p></li></ul></li></ul></section></section><section class="chapter"><h2 id="common-access-patterns" data-toc="common-access-patterns">Common Access Patterns</h2><section class="chapter"><h3 id="content-retrieval" data-toc="content-retrieval">Content Retrieval</h3><div class="code-block" data-lang="ts">
const getPageContent = async (section: string) =&gt; {
    return await prisma.site_content.findMany({
        where: {
            section: section,
            status: 'published'
        },
        orderBy: { weight: 'asc' }
    });
};
</div></section><section class="chapter"><h3 id="configuration-management" data-toc="configuration-management">Configuration Management</h3><div class="code-block" data-lang="ts">
const updateSiteSettings = async (settings: Record&lt;string, any&gt;) =&gt; {
    const updates = Object.entries(settings).map(([key, value]) =&gt; 
        prisma.site_configurations.upsert({
            where: { key },
            update: { value },
            create: { key, value }
        })
    );
    
    await prisma.$transaction(updates);
};
</div></section></section><section class="chapter"><h2 id="security-considerations" data-toc="security-considerations">Security Considerations</h2><section class="chapter"><h3 id="access-control" data-toc="access-control">Access Control</h3><ul class="list _bullet" id="-hbxo0d_75"><li class="list__item" id="-hbxo0d_76"><p id="-hbxo0d_79">Role-based access control (RBAC)</p></li><li class="list__item" id="-hbxo0d_77"><p id="-hbxo0d_80">Content-level permissions</p></li><li class="list__item" id="-hbxo0d_78"><p id="-hbxo0d_81">File access restrictions</p></li></ul></section><section class="chapter"><h3 id="data-protection" data-toc="data-protection">Data Protection</h3><ul class="list _bullet" id="-hbxo0d_82"><li class="list__item" id="-hbxo0d_83"><p id="-hbxo0d_86">Configuration encryption</p></li><li class="list__item" id="-hbxo0d_84"><p id="-hbxo0d_87">Secure file storage</p></li><li class="list__item" id="-hbxo0d_85"><p id="-hbxo0d_88">User data privacy</p></li></ul></section></section><section class="chapter"><h2 id="best-practices" data-toc="best-practices">Best Practices</h2><ol class="list _decimal" id="-hbxo0d_89" type="1"><li class="list__item" id="-hbxo0d_90"><p id="-hbxo0d_95">Maintain clear content organization</p></li><li class="list__item" id="-hbxo0d_91"><p id="-hbxo0d_96">Regular configuration audits</p></li><li class="list__item" id="-hbxo0d_92"><p id="-hbxo0d_97">Implement proper backup procedures</p></li><li class="list__item" id="-hbxo0d_93"><p id="-hbxo0d_98">Monitor file storage usage</p></li><li class="list__item" id="-hbxo0d_94"><p id="-hbxo0d_99">Regular security reviews</p></li></ol></section><div class="last-modified">20 May 2025</div><div data-feedback-placeholder="true"></div><div class="navigation-links _bottom"><a href="table-taxonomy.html" class="navigation-links__prev">Taxonomy Table</a><a href="table-content.html" class="navigation-links__next">Content Table</a></div></article><div id="disqus_thread"></div></div></section></main></div><script src="https://resources.jetbrains.com/writerside/apidoc/6.22.0-b776/app.js"></script></body></html>