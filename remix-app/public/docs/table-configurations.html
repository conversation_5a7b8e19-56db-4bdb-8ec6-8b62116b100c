<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en-US" data-preset="contrast" data-primary-color="#307FFF"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><meta charset="UTF-8"><meta name="robots" content="noindex"><meta name="built-on" content="2025-06-27T10:23:38.357055"><title>Configurations Table | Docs</title><script type="application/json" id="virtual-toc-data">[{"id":"overview","level":0,"title":"Overview","anchor":"#overview"},{"id":"table-structure","level":0,"title":"Table Structure","anchor":"#table-structure"},{"id":"common-usage","level":0,"title":"Common Usage","anchor":"#common-usage"},{"id":"best-practices","level":0,"title":"Best Practices","anchor":"#best-practices"}]</script><script type="application/json" id="topic-shortcuts"></script><link href="https://resources.jetbrains.com/writerside/apidoc/6.22.0-b776/app.css" rel="stylesheet"><link rel="icon" type="image/svg" sizes="16x16" href="images/Gun-Violence-Archive-Logo-Icon.svg"><meta name="image" content=""><!-- Open Graph --><meta property="og:title" content="Configurations Table | Docs"><meta property="og:description" content=""><meta property="og:image" content=""><meta property="og:site_name" content="Docs Help"><meta property="og:type" content="website"><meta property="og:locale" content="en_US"><meta property="og:url" content="writerside-documentation/docs/1.0/table-configurations.html"><!-- End Open Graph --><!-- Twitter Card --><meta name="twitter:card" content="summary_large_image"><meta name="twitter:site" content=""><meta name="twitter:title" content="Configurations Table | Docs"><meta name="twitter:description" content=""><meta name="twitter:creator" content=""><meta name="twitter:image:src" content=""><!-- End Twitter Card --><!-- Schema.org WebPage --><script type="application/ld+json">{
    "@context": "http://schema.org",
    "@type": "WebPage",
    "@id": "writerside-documentation/docs/1.0/table-configurations.html#webpage",
    "url": "writerside-documentation/docs/1.0/table-configurations.html",
    "name": "Configurations Table | Docs",
    "description": "",
    "image": "",
    "inLanguage":"en-US"
}</script><!-- End Schema.org --><!-- Schema.org WebSite --><script type="application/ld+json">{
    "@type": "WebSite",
    "@id": "writerside-documentationdocs/#website",
    "url": "writerside-documentationdocs/",
    "name": "Docs Help"
}</script><!-- End Schema.org --></head><body data-id="Table-Configurations" data-main-title="Configurations Table" data-article-props="{&quot;seeAlsoStyle&quot;:&quot;links&quot;}" data-template="article" data-breadcrumbs="Database-Structure.md|Database Structure///Schema-Site.md|Site Schema"><div class="wrapper"><main class="panel _main"><header class="panel__header"><div class="container"><h3>Docs 1.0 Help</h3><div class="panel-trigger"></div></div></header><section class="panel__content"><div class="container"><article class="article" data-shortcut-switcher="inactive"><h1 data-toc="Table-Configurations" id="Table-Configurations.md">Configurations Table</h1><section class="chapter"><h2 id="overview" data-toc="overview">Overview</h2><p id="z1dro3s_7">The <code class="code" id="z1dro3s_8">configurations</code> table stores system-wide settings, feature flags, and customizable parameters used across the application.</p></section><section class="chapter"><h2 id="table-structure" data-toc="table-structure">Table Structure</h2><section class="chapter"><h3 id="primary-fields" data-toc="primary-fields">Primary Fields</h3><ul class="list _bullet" id="z1dro3s_12"><li class="list__item" id="z1dro3s_13"><p id="z1dro3s_17"><code class="code" id="z1dro3s_18">key</code>: Primary key, unique setting identifier</p></li><li class="list__item" id="z1dro3s_14"><p id="z1dro3s_19"><code class="code" id="z1dro3s_20">value</code>: Configuration value (JSON)</p></li><li class="list__item" id="z1dro3s_15"><p id="z1dro3s_21"><code class="code" id="z1dro3s_22">type</code>: Value type (string, number, boolean, json)</p></li><li class="list__item" id="z1dro3s_16"><p id="z1dro3s_23"><code class="code" id="z1dro3s_24">scope</code>: Configuration scope (system, user, feature)</p></li></ul></section><section class="chapter"><h3 id="metadata-fields" data-toc="metadata-fields">Metadata Fields</h3><ul class="list _bullet" id="z1dro3s_25"><li class="list__item" id="z1dro3s_26"><p id="z1dro3s_30"><code class="code" id="z1dro3s_31">description</code>: Setting description</p></li><li class="list__item" id="z1dro3s_27"><p id="z1dro3s_32"><code class="code" id="z1dro3s_33">created_at</code>: Creation timestamp</p></li><li class="list__item" id="z1dro3s_28"><p id="z1dro3s_34"><code class="code" id="z1dro3s_35">updated_at</code>: Last update timestamp</p></li><li class="list__item" id="z1dro3s_29"><p id="z1dro3s_36"><code class="code" id="z1dro3s_37">updated_by</code>: User ID who last updated the setting</p></li></ul></section><section class="chapter"><h3 id="control-fields" data-toc="control-fields">Control Fields</h3><ul class="list _bullet" id="z1dro3s_38"><li class="list__item" id="z1dro3s_39"><p id="z1dro3s_42"><code class="code" id="z1dro3s_43">is_encrypted</code>: Boolean indicating if value is encrypted</p></li><li class="list__item" id="z1dro3s_40"><p id="z1dro3s_44"><code class="code" id="z1dro3s_45">is_editable</code>: Boolean indicating if setting can be modified</p></li><li class="list__item" id="z1dro3s_41"><p id="z1dro3s_46"><code class="code" id="z1dro3s_47">requires_restart</code>: Boolean indicating if change requires system restart</p></li></ul></section></section><section class="chapter"><h2 id="common-usage" data-toc="common-usage">Common Usage</h2><section class="chapter"><h3 id="toll-system-configuration" data-toc="toll-system-configuration">Toll System Configuration</h3><div class="code-block" data-lang="ts">
// From admin.toll._index.tsx
await upsertConfigByKey(B42019_FOOTER_KEY, { 
    content: String(formData.get('b42019Footer')) 
});
await upsertConfigByKey(PASTTOLLS_STARTING_YEAR_KEY, {
    content: String(formData.get('pastTollsStartingYear'))
});
</div></section><section class="chapter"><h3 id="configuration-retrieval" data-toc="configuration-retrieval">Configuration Retrieval</h3><div class="code-block" data-lang="ts">
const getConfig = async (key: string) =&gt; {
    const config = await prisma.site_configurations.findUnique({
        where: { key: key }
    });
    return config?.is_encrypted ? decrypt(config.value) : config?.value;
};
</div></section></section><section class="chapter"><h2 id="best-practices" data-toc="best-practices">Best Practices</h2><ol class="list _decimal" id="z1dro3s_52" type="1"><li class="list__item" id="z1dro3s_53"><p id="z1dro3s_58">Use consistent key naming conventions</p></li><li class="list__item" id="z1dro3s_54"><p id="z1dro3s_59">Encrypt sensitive configuration values</p></li><li class="list__item" id="z1dro3s_55"><p id="z1dro3s_60">Document configuration purposes</p></li><li class="list__item" id="z1dro3s_56"><p id="z1dro3s_61">Implement change validation</p></li><li class="list__item" id="z1dro3s_57"><p id="z1dro3s_62">Maintain configuration history</p></li></ol></section><div class="last-modified">21 March 2025</div><div data-feedback-placeholder="true"></div><div class="navigation-links _bottom"><a href="table-files.html" class="navigation-links__prev">Files Table</a><a href="table-users.html" class="navigation-links__next">Users Table</a></div></article><div id="disqus_thread"></div></div></section></main></div><script src="https://resources.jetbrains.com/writerside/apidoc/6.22.0-b776/app.js"></script></body></html>