{"entities": {"pages": {"Overview": {"id": "Overview", "title": "Overview", "url": "overview.html", "level": 0, "tabIndex": 0}, "Libraries-and-Dependencies": {"id": "Libraries-and-Dependencies", "title": "Libraries and Dependencies", "url": "libraries-and-dependencies.html", "level": 0, "tabIndex": 1}, "CDK-Infrastructure": {"id": "CDK-Infrastructure", "title": "CDK Infrastructure Overview", "url": "cdk-infrastructure.html", "level": 0, "pages": ["CDK-Stacks", "CDK-Deployment", "CDK-Security", "CDK-Monitoring", "CDK-Troubleshooting"], "tabIndex": 2}, "CDK-Stacks": {"id": "CDK-Stacks", "title": "CDK Stacks", "url": "cdk-stacks.html", "level": 1, "parentId": "CDK-Infrastructure", "tabIndex": 0}, "CDK-Deployment": {"id": "CDK-Deployment", "title": "CDK Deployment", "url": "cdk-deployment.html", "level": 1, "parentId": "CDK-Infrastructure", "tabIndex": 1}, "CDK-Security": {"id": "CDK-Security", "title": "CDK Security", "url": "cdk-security.html", "level": 1, "parentId": "CDK-Infrastructure", "tabIndex": 2}, "CDK-Monitoring": {"id": "CDK-Monitoring", "title": "CDK Monitoring", "url": "cdk-monitoring.html", "level": 1, "parentId": "CDK-Infrastructure", "tabIndex": 3}, "CDK-Troubleshooting": {"id": "CDK-Troubleshooting", "title": "CDK Troubleshooting", "url": "cdk-troubleshooting.html", "level": 1, "parentId": "CDK-Infrastructure", "tabIndex": 4}, "Database-Structure": {"id": "Database-Structure", "title": "Database Structure", "url": "database-structure.html", "level": 0, "pages": ["Schema-GVA-Data", "Schema-Site"], "tabIndex": 3}, "Schema-GVA-Data": {"id": "Schema-GVA-Data", "title": "GVA Data Schema", "url": "schema-gva-data.html", "level": 1, "parentId": "Database-Structure", "pages": ["Table-Incidents", "Table-Incident-Types", "Table-Incident-Participants", "Table-Incident-Guns", "Table-Incident-Sources", "Table-Taxonomy"], "tabIndex": 0}, "Table-Incidents": {"id": "Table-Incidents", "title": "Incidents Table", "url": "table-incidents.html", "level": 2, "parentId": "Schema-GVA-Data", "tabIndex": 0}, "Table-Incident-Types": {"id": "Table-Incident-Types", "title": "Incident Types Table", "url": "table-incident-types.html", "level": 2, "parentId": "Schema-GVA-Data", "tabIndex": 1}, "Table-Incident-Participants": {"id": "Table-Incident-Participants", "title": "Incident Participants Table", "url": "table-incident-participants.html", "level": 2, "parentId": "Schema-GVA-Data", "tabIndex": 2}, "Table-Incident-Guns": {"id": "Table-Incident-Guns", "title": "Incident Guns Table", "url": "table-incident-guns.html", "level": 2, "parentId": "Schema-GVA-Data", "tabIndex": 3}, "Table-Incident-Sources": {"id": "Table-Incident-Sources", "title": "Incident Sources Table", "url": "table-incident-sources.html", "level": 2, "parentId": "Schema-GVA-Data", "tabIndex": 4}, "Table-Taxonomy": {"id": "Table-Taxonomy", "title": "Taxonomy Table", "url": "table-taxonomy.html", "level": 2, "parentId": "Schema-GVA-Data", "tabIndex": 5}, "Schema-Site": {"id": "Schema-Site", "title": "Site Schema", "url": "schema-site.html", "level": 1, "parentId": "Database-Structure", "pages": ["Table-Content", "Table-Files", "Table-Configurations", "Table-Users"], "tabIndex": 1}, "Table-Content": {"id": "Table-Content", "title": "Content Table", "url": "table-content.html", "level": 2, "parentId": "Schema-Site", "tabIndex": 0}, "Table-Files": {"id": "Table-Files", "title": "Files Table", "url": "table-files.html", "level": 2, "parentId": "Schema-Site", "tabIndex": 1}, "Table-Configurations": {"id": "Table-Configurations", "title": "Configurations Table", "url": "table-configurations.html", "level": 2, "parentId": "Schema-Site", "tabIndex": 2}, "Table-Users": {"id": "Table-Users", "title": "Users Table", "url": "table-users.html", "level": 2, "parentId": "Schema-Site", "tabIndex": 3}, "Toll-Builder": {"id": "Toll-Builder", "title": "Toll System", "url": "toll-builder.html", "level": 0, "pages": ["Toll-Items-Implementation"], "tabIndex": 4}, "Toll-Items-Implementation": {"id": "Toll-Items-Implementation", "title": "Toll Items Implementation Guide", "url": "toll-items-implementation.html", "level": 1, "parentId": "Toll-Builder", "tabIndex": 0}, "QueryBuilder": {"id": "QueryBuilder", "title": "Query Builder", "url": "querybuilder.html", "level": 0, "tabIndex": 5}, "SearchBuilder": {"id": "SearchBuilder", "title": "Search Builder", "url": "searchbuilder.html", "level": 0, "tabIndex": 6}}}, "topLevelIds": ["Overview", "Libraries-and-Dependencies", "CDK-Infrastructure", "Database-Structure", "Toll-Builder", "QueryBuilder", "SearchBuilder"]}