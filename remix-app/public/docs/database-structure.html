<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en-US" data-preset="contrast" data-primary-color="#307FFF"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><meta charset="UTF-8"><meta name="robots" content="noindex"><meta name="built-on" content="2025-06-27T10:23:38.294168"><title>Database Structure | Docs</title><script type="application/json" id="virtual-toc-data">[{"id":"overview","level":0,"title":"Overview","anchor":"#overview"},{"id":"schema-organization","level":0,"title":"Schema Organization","anchor":"#schema-organization"},{"id":"database-relationships","level":0,"title":"Database Relationships","anchor":"#database-relationships"},{"id":"schema-conventions","level":0,"title":"Schema Conventions","anchor":"#schema-conventions"},{"id":"access-patterns","level":0,"title":"Access Patterns","anchor":"#access-patterns"}]</script><script type="application/json" id="topic-shortcuts"></script><link href="https://resources.jetbrains.com/writerside/apidoc/6.22.0-b776/app.css" rel="stylesheet"><link rel="icon" type="image/svg" sizes="16x16" href="images/Gun-Violence-Archive-Logo-Icon.svg"><meta name="image" content=""><!-- Open Graph --><meta property="og:title" content="Database Structure | Docs"><meta property="og:description" content=""><meta property="og:image" content=""><meta property="og:site_name" content="Docs Help"><meta property="og:type" content="website"><meta property="og:locale" content="en_US"><meta property="og:url" content="writerside-documentation/docs/1.0/database-structure.html"><!-- End Open Graph --><!-- Twitter Card --><meta name="twitter:card" content="summary_large_image"><meta name="twitter:site" content=""><meta name="twitter:title" content="Database Structure | Docs"><meta name="twitter:description" content=""><meta name="twitter:creator" content=""><meta name="twitter:image:src" content=""><!-- End Twitter Card --><!-- Schema.org WebPage --><script type="application/ld+json">{
    "@context": "http://schema.org",
    "@type": "WebPage",
    "@id": "writerside-documentation/docs/1.0/database-structure.html#webpage",
    "url": "writerside-documentation/docs/1.0/database-structure.html",
    "name": "Database Structure | Docs",
    "description": "",
    "image": "",
    "inLanguage":"en-US"
}</script><!-- End Schema.org --><!-- Schema.org WebSite --><script type="application/ld+json">{
    "@type": "WebSite",
    "@id": "writerside-documentationdocs/#website",
    "url": "writerside-documentationdocs/",
    "name": "Docs Help"
}</script><!-- End Schema.org --></head><body data-id="Database-Structure" data-main-title="Database Structure" data-article-props="{&quot;seeAlsoStyle&quot;:&quot;links&quot;}" data-template="article" data-breadcrumbs=""><div class="wrapper"><main class="panel _main"><header class="panel__header"><div class="container"><h3>Docs 1.0 Help</h3><div class="panel-trigger"></div></div></header><section class="panel__content"><div class="container"><article class="article" data-shortcut-switcher="inactive"><h1 data-toc="Database-Structure" id="Database-Structure.md">Database Structure</h1><section class="chapter"><h2 id="overview" data-toc="overview">Overview</h2><p id="-lnkwd9_8">The Gun Violence Archive application uses a PostgreSQL database with multiple schemas to organize different aspects of the data. This separation helps maintain clear boundaries between different types of data and their relationships.</p></section><section class="chapter"><h2 id="schema-organization" data-toc="schema-organization">Schema Organization</h2><section class="chapter"><h3 id="gva-data-schema-gva-data" data-toc="gva-data-schema-gva-data">GVA Data Schema (<code class="code" id="-lnkwd9_14">gva_data</code>)</h3><p id="-lnkwd9_12">The <code class="code" id="-lnkwd9_15">gva_data</code> schema contains all core incident-related data, including:</p><ul class="list _bullet" id="-lnkwd9_13"><li class="list__item" id="-lnkwd9_16"><p id="-lnkwd9_21">Incident records</p></li><li class="list__item" id="-lnkwd9_17"><p id="-lnkwd9_22">Participant information</p></li><li class="list__item" id="-lnkwd9_18"><p id="-lnkwd9_23">Weapon details</p></li><li class="list__item" id="-lnkwd9_19"><p id="-lnkwd9_24">Source documentation</p></li><li class="list__item" id="-lnkwd9_20"><p id="-lnkwd9_25">Taxonomy and classification data</p></li></ul></section><section class="chapter"><h3 id="site-schema-site" data-toc="site-schema-site">Site Schema (<code class="code" id="-lnkwd9_29">site</code>)</h3><p id="-lnkwd9_27">The <code class="code" id="-lnkwd9_30">site</code> schema manages website-specific content and files:</p><ul class="list _bullet" id="-lnkwd9_28"><li class="list__item" id="-lnkwd9_31"><p id="-lnkwd9_34">Content management</p></li><li class="list__item" id="-lnkwd9_32"><p id="-lnkwd9_35">File storage</p></li><li class="list__item" id="-lnkwd9_33"><p id="-lnkwd9_36">Site configurations</p></li></ul></section></section><section class="chapter"><h2 id="database-relationships" data-toc="database-relationships">Database Relationships</h2><section class="chapter"><h3 id="core-incident-relationships" data-toc="core-incident-relationships">Core Incident Relationships</h3><ul class="list _bullet" id="-lnkwd9_40"><li class="list__item" id="-lnkwd9_41"><p id="-lnkwd9_45"><code class="code" id="-lnkwd9_47">incidents</code> &rarr; <code class="code" id="-lnkwd9_48">incident_participants</code> (1:n)</p><ul class="list _bullet" id="-lnkwd9_46"><li class="list__item" id="-lnkwd9_49"><p id="-lnkwd9_52">Each incident can have multiple participants</p></li><li class="list__item" id="-lnkwd9_50"><p id="-lnkwd9_53">Connected via <code class="code" id="-lnkwd9_54">incident_id</code></p></li><li class="list__item" id="-lnkwd9_51"><p id="-lnkwd9_55">Participants include victims, suspects, and officers</p></li></ul></li><li class="list__item" id="-lnkwd9_42"><p id="-lnkwd9_56"><code class="code" id="-lnkwd9_58">incidents</code> &rarr; <code class="code" id="-lnkwd9_59">incident_guns</code> (1:n)</p><ul class="list _bullet" id="-lnkwd9_57"><li class="list__item" id="-lnkwd9_60"><p id="-lnkwd9_63">Each incident can involve multiple weapons</p></li><li class="list__item" id="-lnkwd9_61"><p id="-lnkwd9_64">Connected via <code class="code" id="-lnkwd9_65">incident_id</code></p></li><li class="list__item" id="-lnkwd9_62"><p id="-lnkwd9_66">Includes weapon details and classifications</p></li></ul></li><li class="list__item" id="-lnkwd9_43"><p id="-lnkwd9_67"><code class="code" id="-lnkwd9_69">incidents</code> &rarr; <code class="code" id="-lnkwd9_70">incident_sources</code> (1:n)</p><ul class="list _bullet" id="-lnkwd9_68"><li class="list__item" id="-lnkwd9_71"><p id="-lnkwd9_74">Each incident can have multiple source documents</p></li><li class="list__item" id="-lnkwd9_72"><p id="-lnkwd9_75">Connected via <code class="code" id="-lnkwd9_76">incident_id</code></p></li><li class="list__item" id="-lnkwd9_73"><p id="-lnkwd9_77">Links to news articles, police reports, and other documentation</p></li></ul></li><li class="list__item" id="-lnkwd9_44"><p id="-lnkwd9_78"><code class="code" id="-lnkwd9_80">incidents</code> &rarr; <code class="code" id="-lnkwd9_81">incident_types</code> (1:n)</p><ul class="list _bullet" id="-lnkwd9_79"><li class="list__item" id="-lnkwd9_82"><p id="-lnkwd9_85">Each incident can have multiple type classifications</p></li><li class="list__item" id="-lnkwd9_83"><p id="-lnkwd9_86">Connected via <code class="code" id="-lnkwd9_87">incident_id</code></p></li><li class="list__item" id="-lnkwd9_84"><p id="-lnkwd9_88">Weighted relationships for primary/secondary classifications</p></li></ul></li></ul></section><section class="chapter"><h3 id="taxonomy-relationships" data-toc="taxonomy-relationships">Taxonomy Relationships</h3><ul class="list _bullet" id="-lnkwd9_89"><li class="list__item" id="-lnkwd9_90"><p id="-lnkwd9_93"><code class="code" id="-lnkwd9_95">taxonomy</code> &rarr; <code class="code" id="-lnkwd9_96">incidents</code> (1:n)</p><ul class="list _bullet" id="-lnkwd9_94"><li class="list__item" id="-lnkwd9_97"><p id="-lnkwd9_100">State classifications via <code class="code" id="-lnkwd9_101">state_taxonomy_id</code></p></li><li class="list__item" id="-lnkwd9_98"><p id="-lnkwd9_102">Location type classifications</p></li><li class="list__item" id="-lnkwd9_99"><p id="-lnkwd9_103">Incident type definitions</p></li></ul></li><li class="list__item" id="-lnkwd9_91"><p id="-lnkwd9_104"><code class="code" id="-lnkwd9_106">taxonomy</code> &rarr; <code class="code" id="-lnkwd9_107">incident_participants</code> (1:n)</p><ul class="list _bullet" id="-lnkwd9_105"><li class="list__item" id="-lnkwd9_108"><p id="-lnkwd9_111">Age group classifications via <code class="code" id="-lnkwd9_112">age_group_tid</code></p></li><li class="list__item" id="-lnkwd9_109"><p id="-lnkwd9_113">Status classifications (killed, injured, etc.)</p></li><li class="list__item" id="-lnkwd9_110"><p id="-lnkwd9_114">Type classifications (victim, suspect, officer)</p></li></ul></li><li class="list__item" id="-lnkwd9_92"><p id="-lnkwd9_115"><code class="code" id="-lnkwd9_117">taxonomy</code> &rarr; <code class="code" id="-lnkwd9_118">incident_guns</code> (1:n)</p><ul class="list _bullet" id="-lnkwd9_116"><li class="list__item" id="-lnkwd9_119"><p id="-lnkwd9_122">Weapon type classifications via <code class="code" id="-lnkwd9_123">gun_type_tid</code></p></li><li class="list__item" id="-lnkwd9_120"><p id="-lnkwd9_124">Caliber classifications</p></li><li class="list__item" id="-lnkwd9_121"><p id="-lnkwd9_125">Status classifications</p></li></ul></li></ul></section><section class="chapter"><h3 id="site-management-relationships" data-toc="site-management-relationships">Site Management Relationships</h3><ul class="list _bullet" id="-lnkwd9_126"><li class="list__item" id="-lnkwd9_127"><p id="-lnkwd9_130"><code class="code" id="-lnkwd9_132">users</code> &rarr; <code class="code" id="-lnkwd9_133">users_roles</code> (1:n)</p><ul class="list _bullet" id="-lnkwd9_131"><li class="list__item" id="-lnkwd9_134"><p id="-lnkwd9_136">User role assignments</p></li><li class="list__item" id="-lnkwd9_135"><p id="-lnkwd9_137">Connected via <code class="code" id="-lnkwd9_138">uid</code></p></li></ul></li><li class="list__item" id="-lnkwd9_128"><p id="-lnkwd9_139"><code class="code" id="-lnkwd9_141">users</code> &rarr; <code class="code" id="-lnkwd9_142">users_sessions</code> (1:n)</p><ul class="list _bullet" id="-lnkwd9_140"><li class="list__item" id="-lnkwd9_143"><p id="-lnkwd9_145">User session tracking</p></li><li class="list__item" id="-lnkwd9_144"><p id="-lnkwd9_146">Connected via <code class="code" id="-lnkwd9_147">uid</code></p></li></ul></li><li class="list__item" id="-lnkwd9_129"><p id="-lnkwd9_148"><code class="code" id="-lnkwd9_150">files</code> &rarr; <code class="code" id="-lnkwd9_151">incident_sources</code> (1:1)</p><ul class="list _bullet" id="-lnkwd9_149"><li class="list__item" id="-lnkwd9_152"><p id="-lnkwd9_155">Source documentation attachments</p></li><li class="list__item" id="-lnkwd9_153"><p id="-lnkwd9_156">Connected via <code class="code" id="-lnkwd9_157">image_fid</code></p></li><li class="list__item" id="-lnkwd9_154"><p id="-lnkwd9_158">Includes screenshots and archived content</p></li></ul></li></ul></section></section><section class="chapter"><h2 id="schema-conventions" data-toc="schema-conventions">Schema Conventions</h2><section class="chapter"><h3 id="naming-conventions" data-toc="naming-conventions">Naming Conventions</h3><ul class="list _bullet" id="-lnkwd9_161"><li class="list__item" id="-lnkwd9_162"><p id="-lnkwd9_166">Table names: lowercase with underscores (snake_case)</p></li><li class="list__item" id="-lnkwd9_163"><p id="-lnkwd9_167">Column names: lowercase with underscores</p></li><li class="list__item" id="-lnkwd9_164"><p id="-lnkwd9_168">Primary keys: typically <code class="code" id="-lnkwd9_169">id</code> or <code class="code" id="-lnkwd9_170">[table_name]_id</code></p></li><li class="list__item" id="-lnkwd9_165"><p id="-lnkwd9_171">Foreign keys: referenced table name + <code class="code" id="-lnkwd9_172">_id</code></p></li></ul></section><section class="chapter"><h3 id="common-fields" data-toc="common-fields">Common Fields</h3><p id="-lnkwd9_173">Most tables include:</p><ul class="list _bullet" id="-lnkwd9_174"><li class="list__item" id="-lnkwd9_175"><p id="-lnkwd9_179">Primary key fields</p></li><li class="list__item" id="-lnkwd9_176"><p id="-lnkwd9_180">Timestamp fields (<code class="code" id="-lnkwd9_181">created_at</code>, <code class="code" id="-lnkwd9_182">updated_at</code>)</p></li><li class="list__item" id="-lnkwd9_177"><p id="-lnkwd9_183">Soft delete flags where applicable</p></li><li class="list__item" id="-lnkwd9_178"><p id="-lnkwd9_184">User reference fields for tracking changes</p></li></ul></section></section><section class="chapter"><h2 id="access-patterns" data-toc="access-patterns">Access Patterns</h2><ul class="list _bullet" id="-lnkwd9_185"><li class="list__item" id="-lnkwd9_186"><p id="-lnkwd9_189">Read operations primarily occur through the Query Builder system</p></li><li class="list__item" id="-lnkwd9_187"><p id="-lnkwd9_190">Write operations are managed through Prisma Client</p></li><li class="list__item" id="-lnkwd9_188"><p id="-lnkwd9_191">Batch operations use dedicated background job processors</p></li></ul></section><div class="last-modified">21 March 2025</div><div data-feedback-placeholder="true"></div><div class="navigation-links _bottom"><a href="cdk-troubleshooting.html" class="navigation-links__prev">CDK Troubleshooting</a><a href="schema-gva-data.html" class="navigation-links__next">GVA Data Schema</a></div></article><div id="disqus_thread"></div></div></section></main></div><script src="https://resources.jetbrains.com/writerside/apidoc/6.22.0-b776/app.js"></script></body></html>