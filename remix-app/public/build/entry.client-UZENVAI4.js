import {
  require_client
} from "/build/_shared/chunk-YR2B2LOX.js";
import {
  browserTracingIntegration,
  feedbackSyncIntegration,
  init,
  replayIntegration
} from "/build/_shared/chunk-OFHA6TWD.js";
import {
  RemixBrowser,
  useLocation,
  useMatches
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/entry.client.tsx
var import_react2 = __toESM(require_react(), 1);
var import_client = __toESM(require_client(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/entry.client.tsx"
  );
  import.meta.hot.lastModified = "1748982261097.1738";
}
init({
  dsn: "https://<EMAIL>/27",
  tracesSampleRate: 1,
  integrations: [
    browserTracingIntegration({
      useEffect: import_react2.useEffect,
      useLocation,
      useMatches
    }),
    replayIntegration({
      maskAllText: false,
      blockAllMedia: false
    }),
    feedbackSyncIntegration({
      colorScheme: "system",
      showBranding: false
    })
  ],
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1
});
(0, import_react2.startTransition)(() => {
  (0, import_client.hydrateRoot)(
    document,
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(import_react2.StrictMode, { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(RemixBrowser, {}, void 0, false, {
      fileName: "app/entry.client.tsx",
      lineNumber: 49,
      columnNumber: 4
    }, this) }, void 0, false, {
      fileName: "app/entry.client.tsx",
      lineNumber: 48,
      columnNumber: 3
    }, this)
  );
});
//# sourceMappingURL=/build/entry.client-UZENVAI4.js.map
