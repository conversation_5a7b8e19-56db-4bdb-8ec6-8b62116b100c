import {
  require_toll
} from "/build/_shared/chunk-OQS22UZD.js";
import {
  esm_default
} from "/build/_shared/chunk-WBY37MGY.js";
import {
  dashboard_default
} from "/build/_shared/chunk-VVRI3FBH.js";
import {
  Gun_Violence_Archive_Logo_Reverse_default
} from "/build/_shared/chunk-MPHBVVM4.js";
import {
  moment_default,
  require_moment_timezone
} from "/build/_shared/chunk-XAWK6254.js";
import "/build/_shared/chunk-NPKMIMFJ.js";
import {
  ClientOnly
} from "/build/_shared/chunk-WRTK6IZB.js";
import {
  require_config
} from "/build/_shared/chunk-3PRZVPKO.js";
import {
  Footer,
  Header
} from "/build/_shared/chunk-4556QKWU.js";
import "/build/_shared/chunk-W7JLG7II.js";
import "/build/_shared/chunk-OHIZ2QAR.js";
import "/build/_shared/chunk-DFTVPORL.js";
import {
  Link,
  isRouteErrorResponse,
  useLoaderData,
  useRouteError
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __commonJS,
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// empty-module:~/services/toll/TollItemFactory.server
var require_TollItemFactory = __commonJS({
  "empty-module:~/services/toll/TollItemFactory.server"(exports, module) {
    module.exports = {};
  }
});

// empty-module:~/services/query/QueryBuilder.server
var require_QueryBuilder = __commonJS({
  "empty-module:~/services/query/QueryBuilder.server"(exports, module) {
    module.exports = {};
  }
});

// app/routes/_index/last-year-review.tsx
var import_toll = __toESM(require_toll(), 1);
var import_TollItemFactory = __toESM(require_TollItemFactory(), 1);
var import_QueryBuilder = __toESM(require_QueryBuilder(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/_index/last-year-review.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/_index/last-year-review.tsx"
  );
  import.meta.hot.lastModified = "1748452852611.8994";
}
var LastYearReview = ({
  data
}) => {
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] bg-orange-500 p-8 md:p-9 text-white", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[20px] md:text-[25px] font-bold md:text-center", children: [
      data.year,
      " Year in Review"
    ] }, void 0, true, {
      fileName: "app/routes/_index/last-year-review.tsx",
      lineNumber: 84,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "block md:hidden mt-4", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid grid-cols-2 border-b border-orange-800 text-center", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "border-r border-orange-800 pb-[15px]", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[30px] leading-8 font-bold", children: data.totalGunDeaths.toLocaleString("en-US") }, void 0, false, {
            fileName: "app/routes/_index/last-year-review.tsx",
            lineNumber: 88,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[15px]", children: "Total Gun Deaths" }, void 0, false, {
            fileName: "app/routes/_index/last-year-review.tsx",
            lineNumber: 89,
            columnNumber: 7
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/_index/last-year-review.tsx",
          lineNumber: 87,
          columnNumber: 6
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "pb-[15px]", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[30px] leading-8 font-bold", children: data.childrenKilled.toLocaleString("en-US") }, void 0, false, {
            fileName: "app/routes/_index/last-year-review.tsx",
            lineNumber: 92,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[15px]", children: "Children Killed" }, void 0, false, {
            fileName: "app/routes/_index/last-year-review.tsx",
            lineNumber: 93,
            columnNumber: 7
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/_index/last-year-review.tsx",
          lineNumber: 91,
          columnNumber: 6
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/_index/last-year-review.tsx",
        lineNumber: 86,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid grid-cols-2 text-center", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "border-r border-orange-800 pt-[15px]", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[30px] leading-8 font-bold", children: data.massShootings.toLocaleString("en-US") }, void 0, false, {
            fileName: "app/routes/_index/last-year-review.tsx",
            lineNumber: 98,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[15px]", children: "Mass Shootings" }, void 0, false, {
            fileName: "app/routes/_index/last-year-review.tsx",
            lineNumber: 99,
            columnNumber: 7
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/_index/last-year-review.tsx",
          lineNumber: 97,
          columnNumber: 6
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "pt-[15px]", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[30px] leading-8 font-bold", children: data.massMurders.toLocaleString("en-US") }, void 0, false, {
            fileName: "app/routes/_index/last-year-review.tsx",
            lineNumber: 102,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[15px]", children: "Mass Murders" }, void 0, false, {
            fileName: "app/routes/_index/last-year-review.tsx",
            lineNumber: 103,
            columnNumber: 7
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/_index/last-year-review.tsx",
          lineNumber: 101,
          columnNumber: 6
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/_index/last-year-review.tsx",
        lineNumber: 96,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/_index/last-year-review.tsx",
      lineNumber: 85,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "hidden md:block mt-5", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid grid-cols-4 text-center", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "border-r border-orange-800", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[40px] leading-10 font-bold", children: data.totalGunDeaths.toLocaleString("en-US") }, void 0, false, {
          fileName: "app/routes/_index/last-year-review.tsx",
          lineNumber: 110,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[15px] mt-1", children: "Total Gun Deaths" }, void 0, false, {
          fileName: "app/routes/_index/last-year-review.tsx",
          lineNumber: 111,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/_index/last-year-review.tsx",
        lineNumber: 109,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "border-r border-orange-800", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[40px] leading-10 font-bold", children: data.childrenKilled.toLocaleString("en-US") }, void 0, false, {
          fileName: "app/routes/_index/last-year-review.tsx",
          lineNumber: 114,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[15px] mt-1", children: "Children Killed" }, void 0, false, {
          fileName: "app/routes/_index/last-year-review.tsx",
          lineNumber: 115,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/_index/last-year-review.tsx",
        lineNumber: 113,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "border-r border-orange-800", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[40px] leading-10 font-bold", children: data.massShootings.toLocaleString("en-US") }, void 0, false, {
          fileName: "app/routes/_index/last-year-review.tsx",
          lineNumber: 118,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[15px] mt-1", children: "Mass Shootings" }, void 0, false, {
          fileName: "app/routes/_index/last-year-review.tsx",
          lineNumber: 119,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/_index/last-year-review.tsx",
        lineNumber: 117,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[40px] leading-10 font-bold", children: data.massMurders.toLocaleString("en-US") }, void 0, false, {
          fileName: "app/routes/_index/last-year-review.tsx",
          lineNumber: 122,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[15px] mt-1", children: "Mass Murders" }, void 0, false, {
          fileName: "app/routes/_index/last-year-review.tsx",
          lineNumber: 123,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/_index/last-year-review.tsx",
        lineNumber: 121,
        columnNumber: 6
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/_index/last-year-review.tsx",
      lineNumber: 108,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/_index/last-year-review.tsx",
      lineNumber: 107,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/_index/last-year-review.tsx",
    lineNumber: 83,
    columnNumber: 10
  }, this);
};
_c = LastYearReview;
var _c;
$RefreshReg$(_c, "LastYearReview");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;

// app/routes/_index/year-review.tsx
var import_react = __toESM(require_react(), 1);
var import_toll2 = __toESM(require_toll(), 1);
var import_jsx_dev_runtime2 = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/_index/year-review.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/_index/year-review.tsx"
  );
  import.meta.hot.lastModified = "1748452862963.665";
}
var YearReview = ({
  data,
  shareUrl
}) => {
  const tolls = data.values ? Object.entries(data.values) : [];
  return /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "rounded-[10px] bg-gray-800 py-9 text-white", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "px-9 pb-4", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "text-[25px] font-bold", children: [
        "Gun Violence Archive",
        " ",
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("span", { className: data.year_range?.str.includes("-") ? "block" : "inline", children: data.year_range?.str }, void 0, false, {
          fileName: "app/routes/_index/year-review.tsx",
          lineNumber: 48,
          columnNumber: 6
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/_index/year-review.tsx",
        lineNumber: 46,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "text-[15px]", children: [
        "Publish Date: ",
        moment_default().format("MMMM DD, YYYY")
      ] }, void 0, true, {
        fileName: "app/routes/_index/year-review.tsx",
        lineNumber: 52,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/_index/year-review.tsx",
      lineNumber: 45,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "text-sm", children: tolls.filter(([k, v]) => !v.parent).map(([key, item], index) => /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "px-9 py-3.5 space-y-1.5" + (index % 2 == 0 ? " bg-black" : ""), children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "flex space-x-3 justify-between", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "flex-1", children: !item.hideLabel && /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("a", { href: item.report || "#", children: /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { dangerouslySetInnerHTML: {
          __html: item.label
        } }, void 0, false, {
          fileName: "app/routes/_index/year-review.tsx",
          lineNumber: 60,
          columnNumber: 12
        }, this) }, void 0, false, {
          fileName: "app/routes/_index/year-review.tsx",
          lineNumber: 59,
          columnNumber: 30
        }, this) }, void 0, false, {
          fileName: "app/routes/_index/year-review.tsx",
          lineNumber: 58,
          columnNumber: 9
        }, this),
        item.valueLabel && /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "flex justify-between w-[100px] lg:w-28", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { dangerouslySetInnerHTML: {
            __html: item.valueLabel
          } }, void 0, false, {
            fileName: "app/routes/_index/year-review.tsx",
            lineNumber: 66,
            columnNumber: 11
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("a", { href: item.report || "#", children: item.value }, void 0, false, {
            fileName: "app/routes/_index/year-review.tsx",
            lineNumber: 69,
            columnNumber: 11
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/_index/year-review.tsx",
          lineNumber: 65,
          columnNumber: 29
        }, this),
        !item.valueLabel && /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("a", { href: item.report || "#", children: item.value }, void 0, false, {
          fileName: "app/routes/_index/year-review.tsx",
          lineNumber: 72,
          columnNumber: 11
        }, this) }, void 0, false, {
          fileName: "app/routes/_index/year-review.tsx",
          lineNumber: 71,
          columnNumber: 30
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/_index/year-review.tsx",
        lineNumber: 57,
        columnNumber: 8
      }, this),
      tolls.filter(([k, v]) => v.parent === key).map(([subkey, subitem]) => /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "flex space-x-3 justify-between", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "flex-1", children: !subitem.hideLabel && /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("a", { href: subitem.report || "#", children: /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { dangerouslySetInnerHTML: {
          __html: subitem.label
        } }, void 0, false, {
          fileName: "app/routes/_index/year-review.tsx",
          lineNumber: 78,
          columnNumber: 14
        }, this) }, void 0, false, {
          fileName: "app/routes/_index/year-review.tsx",
          lineNumber: 77,
          columnNumber: 35
        }, this) }, void 0, false, {
          fileName: "app/routes/_index/year-review.tsx",
          lineNumber: 76,
          columnNumber: 11
        }, this),
        subitem.valueLabel && /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "flex justify-between w-[100px] lg:w-28", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { dangerouslySetInnerHTML: {
            __html: subitem.valueLabel
          } }, void 0, false, {
            fileName: "app/routes/_index/year-review.tsx",
            lineNumber: 84,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("a", { href: subitem.report || "#", children: subitem.value }, void 0, false, {
            fileName: "app/routes/_index/year-review.tsx",
            lineNumber: 87,
            columnNumber: 13
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/_index/year-review.tsx",
          lineNumber: 83,
          columnNumber: 34
        }, this),
        !subitem.valueLabel && /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("a", { href: subitem.report || "#", children: subitem.value }, void 0, false, {
          fileName: "app/routes/_index/year-review.tsx",
          lineNumber: 90,
          columnNumber: 13
        }, this) }, void 0, false, {
          fileName: "app/routes/_index/year-review.tsx",
          lineNumber: 89,
          columnNumber: 35
        }, this)
      ] }, `toll-${key}-sub-${subkey}`, true, {
        fileName: "app/routes/_index/year-review.tsx",
        lineNumber: 75,
        columnNumber: 79
      }, this))
    ] }, `toll-${key}`, true, {
      fileName: "app/routes/_index/year-review.tsx",
      lineNumber: 56,
      columnNumber: 70
    }, this)) }, void 0, false, {
      fileName: "app/routes/_index/year-review.tsx",
      lineNumber: 55,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "px-9 pt-4 text-xs", children: [
      esm_default(data.footer),
      /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "mt-4 font-bold text-sm lg:text-xl", children: [
        "Data Sources Verified: ",
        data.date_validated
      ] }, void 0, true, {
        fileName: "app/routes/_index/year-review.tsx",
        lineNumber: 98,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "mt-4 flex justify-between items-start", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "sm:flex items-end", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("img", { src: Gun_Violence_Archive_Logo_Reverse_default, alt: "Gun Violence Archive", className: "w-[230px] xl:w-[175px] 2xl:w-[230px]" }, void 0, false, {
            fileName: "app/routes/_index/year-review.tsx",
            lineNumber: 101,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "sm:pl-1 pb-0.5 text-[10px]", children: [
            "\xA9 2013-",
            (/* @__PURE__ */ new Date()).getFullYear()
          ] }, void 0, true, {
            fileName: "app/routes/_index/year-review.tsx",
            lineNumber: 102,
            columnNumber: 7
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/_index/year-review.tsx",
          lineNumber: 100,
          columnNumber: 6
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)(ClientOnly, { children: () => /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)(ShareButton, { shareUrl }, void 0, false, {
          fileName: "app/routes/_index/year-review.tsx",
          lineNumber: 106,
          columnNumber: 15
        }, this) }, void 0, false, {
          fileName: "app/routes/_index/year-review.tsx",
          lineNumber: 105,
          columnNumber: 7
        }, this) }, void 0, false, {
          fileName: "app/routes/_index/year-review.tsx",
          lineNumber: 104,
          columnNumber: 6
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/_index/year-review.tsx",
        lineNumber: 99,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/_index/year-review.tsx",
      lineNumber: 96,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/_index/year-review.tsx",
    lineNumber: 44,
    columnNumber: 10
  }, this);
};
_c2 = YearReview;
var ShareButton = ({
  shareUrl
}) => {
  _s();
  (0, import_react.useEffect)(() => {
    if (window.a2a) {
      window.a2a.init_all();
    }
  }, []);
  return /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "a2a_kit a2a_default_style", "data-a2a-url": shareUrl, children: /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("a", { className: "a2a_dd", href: `https://www.addtoany.com/share#url=${encodeURIComponent(shareUrl)}&title=Gun%20Violence%20Archive`, children: /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "rounded-full bg-orange-500 px-2.5 py-1", children: "Share" }, void 0, false, {
    fileName: "app/routes/_index/year-review.tsx",
    lineNumber: 125,
    columnNumber: 5
  }, this) }, void 0, false, {
    fileName: "app/routes/_index/year-review.tsx",
    lineNumber: 124,
    columnNumber: 4
  }, this) }, void 0, false, {
    fileName: "app/routes/_index/year-review.tsx",
    lineNumber: 123,
    columnNumber: 10
  }, this);
};
_s(ShareButton, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c22 = ShareButton;
var _c2;
var _c22;
$RefreshReg$(_c2, "YearReview");
$RefreshReg$(_c22, "ShareButton");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;

// app/routes/_index/past-years-review.tsx
var import_config = __toESM(require_config(), 1);
var import_toll3 = __toESM(require_toll(), 1);
var import_moment_timezone = __toESM(require_moment_timezone(), 1);
var import_jsx_dev_runtime3 = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/_index/past-years-review.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/_index/past-years-review.tsx"
  );
  import.meta.hot.lastModified = "1748972656807.813";
}
var PastYearsReview = ({
  data
}) => {
  return /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: "rounded-[10px] bg-blue-500 py-9 text-white", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("table", { className: "table-blue w-full text-sm", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("thead", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("tr", { className: "text-[25px] font-bold", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("th", { align: "left", className: "pb-4 pl-9", children: "GVA 10-Year Review" }, void 0, false, {
          fileName: "app/routes/_index/past-years-review.tsx",
          lineNumber: 48,
          columnNumber: 13
        }, this),
        data.values.year_range.map((year, index, row) => /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("th", { align: "right", className: "pb-4 " + (index + 1 === row.length ? "pr-9" : ""), children: year.str }, `past-review-col-${index}`, false, {
          fileName: "app/routes/_index/past-years-review.tsx",
          lineNumber: 51,
          columnNumber: 63
        }, this))
      ] }, void 0, true, {
        fileName: "app/routes/_index/past-years-review.tsx",
        lineNumber: 47,
        columnNumber: 11
      }, this) }, void 0, false, {
        fileName: "app/routes/_index/past-years-review.tsx",
        lineNumber: 46,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("tbody", { children: Object.values(data.values.values).map((item, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("tr", { children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("td", { align: "left", className: "py-2.5 pl-9", children: /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { dangerouslySetInnerHTML: {
          __html: item.info.label
        } }, void 0, false, {
          fileName: "app/routes/_index/past-years-review.tsx",
          lineNumber: 59,
          columnNumber: 19
        }, this) }, void 0, false, {
          fileName: "app/routes/_index/past-years-review.tsx",
          lineNumber: 58,
          columnNumber: 17
        }, this),
        item.years.map((year, index2, row) => /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("td", { align: "right", className: index2 + 1 === row.length ? "pr-9" : "", children: year.value }, `past-review-val-${index2}`, false, {
          fileName: "app/routes/_index/past-years-review.tsx",
          lineNumber: 63,
          columnNumber: 55
        }, this))
      ] }, `past-review-row-${index}`, true, {
        fileName: "app/routes/_index/past-years-review.tsx",
        lineNumber: 57,
        columnNumber: 67
      }, this)) }, void 0, false, {
        fileName: "app/routes/_index/past-years-review.tsx",
        lineNumber: 56,
        columnNumber: 9
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/_index/past-years-review.tsx",
      lineNumber: 45,
      columnNumber: 7
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: "mt-9 px-9 text-xs", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { dangerouslySetInnerHTML: {
        __html: data.footer
      } }, void 0, false, {
        fileName: "app/routes/_index/past-years-review.tsx",
        lineNumber: 71,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: "mt-4", children: [
        "\xA9 2013-",
        (/* @__PURE__ */ new Date()).getFullYear()
      ] }, void 0, true, {
        fileName: "app/routes/_index/past-years-review.tsx",
        lineNumber: 74,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("img", { className: "mt-4", src: Gun_Violence_Archive_Logo_Reverse_default, alt: "Gun Violence Archive", width: 230 }, void 0, false, {
        fileName: "app/routes/_index/past-years-review.tsx",
        lineNumber: 75,
        columnNumber: 9
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/_index/past-years-review.tsx",
      lineNumber: 70,
      columnNumber: 7
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/_index/past-years-review.tsx",
    lineNumber: 44,
    columnNumber: 10
  }, this);
};
_c3 = PastYearsReview;
var _c3;
$RefreshReg$(_c3, "PastYearsReview");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;

// app/images/bg-home.png
var bg_home_default = "/build/_assets/bg-home-E4ZRSAJG.png";

// app/routes/_index/route.tsx
var import_jsx_dev_runtime4 = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/_index/route.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s2 = $RefreshSig$();
var _s22 = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/_index/route.tsx"
  );
}
var links = () => [{
  rel: "stylesheet",
  href: dashboard_default
}];
var meta = () => {
  return [{
    title: "Gun Violence Archive"
  }, {
    name: "description",
    content: "Welcome to Gun Violence Archive"
  }];
};
function Index() {
  _s2();
  const data = useLoaderData();
  return /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { className: "min-h-screen", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)(Header, {}, void 0, false, {
      fileName: "app/routes/_index/route.tsx",
      lineNumber: 68,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("main", { className: "bg-gray-500 py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { className: "container mx-auto", children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { className: "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-10", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { className: "md:col-span-2 xl:col-span-3 overflow-x-auto", children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)(PastYearsReview, { data: data.pastYearsData }, void 0, false, {
        fileName: "app/routes/_index/route.tsx",
        lineNumber: 73,
        columnNumber: 8
      }, this) }, void 0, false, {
        fileName: "app/routes/_index/route.tsx",
        lineNumber: 72,
        columnNumber: 7
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { className: "md:col-span-2", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)(
          "div",
          {
            className: "rounded-[10px]",
            children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)(Link, { to: `https://www.thetrace.org/2023/02/gun-violence-map-america-shootings/`, target: "_blank", children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("img", { src: bg_home_default, alt: "" }, void 0, false, {
              fileName: "app/routes/_index/route.tsx",
              lineNumber: 81,
              columnNumber: 10
            }, this) }, void 0, false, {
              fileName: "app/routes/_index/route.tsx",
              lineNumber: 79,
              columnNumber: 9
            }, this)
          },
          void 0,
          false,
          {
            fileName: "app/routes/_index/route.tsx",
            lineNumber: 76,
            columnNumber: 8
          },
          this
        ),
        /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { className: "mt-10", children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)(LastYearReview, { data: data.lastYearData }, void 0, false, {
          fileName: "app/routes/_index/route.tsx",
          lineNumber: 85,
          columnNumber: 9
        }, this) }, void 0, false, {
          fileName: "app/routes/_index/route.tsx",
          lineNumber: 84,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/_index/route.tsx",
        lineNumber: 75,
        columnNumber: 7
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { className: "md:col-span-2 xl:col-span-1", children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)(YearReview, { data: data.yearData, shareUrl: data.shareUrl }, void 0, false, {
        fileName: "app/routes/_index/route.tsx",
        lineNumber: 92,
        columnNumber: 8
      }, this) }, void 0, false, {
        fileName: "app/routes/_index/route.tsx",
        lineNumber: 91,
        columnNumber: 7
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/_index/route.tsx",
      lineNumber: 71,
      columnNumber: 6
    }, this) }, void 0, false, {
      fileName: "app/routes/_index/route.tsx",
      lineNumber: 70,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/_index/route.tsx",
      lineNumber: 69,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)(Footer, {}, void 0, false, {
      fileName: "app/routes/_index/route.tsx",
      lineNumber: 97,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/_index/route.tsx",
    lineNumber: 67,
    columnNumber: 10
  }, this);
}
_s2(Index, "5thj+e1edPyRpKif1JmVRC6KArE=", false, function() {
  return [useLoaderData];
});
_c4 = Index;
function ErrorBoundary() {
  _s22();
  const error = useRouteError();
  if (error instanceof Error) {
    return /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { children: [
      "An unexpected error occurred: ",
      error.message
    ] }, void 0, true, {
      fileName: "app/routes/_index/route.tsx",
      lineNumber: 108,
      columnNumber: 12
    }, this);
  }
  if (!isRouteErrorResponse(error)) {
    return /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("h1", { children: "Unknown Error" }, void 0, false, {
      fileName: "app/routes/_index/route.tsx",
      lineNumber: 111,
      columnNumber: 12
    }, this);
  }
  if (error.status === 404) {
    return /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { children: "Dashboard not found" }, void 0, false, {
      fileName: "app/routes/_index/route.tsx",
      lineNumber: 114,
      columnNumber: 12
    }, this);
  }
  return /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { children: [
    "An unexpected error occurred: ",
    error.statusText
  ] }, void 0, true, {
    fileName: "app/routes/_index/route.tsx",
    lineNumber: 116,
    columnNumber: 10
  }, this);
}
_s22(ErrorBoundary, "oAgjgbJzsRXlB89+MoVumxMQqKM=", false, function() {
  return [useRouteError];
});
_c23 = ErrorBoundary;
var _c4;
var _c23;
$RefreshReg$(_c4, "Index");
$RefreshReg$(_c23, "ErrorBoundary");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  ErrorBoundary,
  Index as default,
  links,
  meta
};
//# sourceMappingURL=/build/routes/_index-VYSSPVCM.js.map
