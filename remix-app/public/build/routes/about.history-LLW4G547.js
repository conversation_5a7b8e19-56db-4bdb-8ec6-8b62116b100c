import {
  require_range
} from "/build/_shared/chunk-XEU2OR47.js";
import {
  require_about,
  require_js,
  require_reduce
} from "/build/_shared/chunk-O7YZF2UJ.js";
import {
  Editor,
  htmlFrom,
  require_browser,
  tinyConfig
} from "/build/_shared/chunk-SIGMETZO.js";
import "/build/_shared/chunk-WBY37MGY.js";
import "/build/_shared/chunk-IVTPFYOU.js";
import {
  Title
} from "/build/_shared/chunk-YPQMQ54K.js";
import {
  Foot<PERSON>,
  Header
} from "/build/_shared/chunk-4556QKWU.js";
import "/build/_shared/chunk-W7JLG7II.js";
import "/build/_shared/chunk-OHIZ2QAR.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import {
  useOptionalUser
} from "/build/_shared/chunk-DFTVPORL.js";
import {
  Link,
  useLoaderData,
  useNavigate
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/about.history.tsx
var import_react = __toESM(require_react(), 1);
var import_about = __toESM(require_about(), 1);
var import_auth = __toESM(require_auth(), 1);
var import_react_multi_ref = __toESM(require_js(), 1);
var import_isomorphic_dompurify = __toESM(require_browser(), 1);
var import_reduce = __toESM(require_reduce(), 1);
var import_range = __toESM(require_range(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/about.history.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/about.history.tsx"
  );
  import.meta.hot.lastModified = "1748455205758.4536";
}
function AboutHistory() {
  _s();
  const data = useLoaderData();
  const user = useOptionalUser();
  const navigate = useNavigate();
  const [editorRefs] = (0, import_react.useState)(() => new import_react_multi_ref.default());
  const [editing, setEditing] = (0, import_react.useState)(false);
  const editingRef = (0, import_react.useRef)(editing);
  const alertUser = (e) => {
    if (editingRef.current) {
      e.preventDefault();
    }
  };
  (0, import_react.useEffect)(() => {
    editingRef.current = editing;
  }, [editing]);
  (0, import_react.useEffect)(() => {
    window.addEventListener("beforeunload", alertUser);
    return () => {
      window.removeEventListener("beforeunload", alertUser);
    };
  }, []);
  const save = async (body) => {
    await fetch("/about/history", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(body)
    });
    navigate(".", {
      replace: true
    });
  };
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "min-h-screen", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Header, {}, void 0, false, {
      fileName: "app/routes/about.history.tsx",
      lineNumber: 92,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Title, { title: "Gun Violence Archive History" }, void 0, false, {
      fileName: "app/routes/about.history.tsx",
      lineNumber: 93,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { children: [
      user && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "fixed bottom-0 left-0 border-2 bg-white px-5 py-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { className: "text-xl", onClick: () => {
        if (editing) {
          save([...(0, import_range.default)(1, 24).map((value) => ({
            contentid: "about-history-container-" + value,
            content: editorRefs.map.get("about-history-container-" + value)?.getContent() || ""
          }))]).then(() => setEditing(false));
        } else {
          setEditing(true);
        }
      }, children: editing ? "Save" : "Edit" }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 96,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 95,
        columnNumber: 14
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex flex-col items-center", children: [
        editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-1",
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-1", instance), initialValue: data.content["about-history-container-1"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 111,
          columnNumber: 18
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "max-w-[1000px] text-center font-bold text-4xl md:text-[50px] md:leading-[60px]", id: "about-history-container-1", children: htmlFrom(data.content["about-history-container-1"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 114,
          columnNumber: 163
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-7", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "inline-block rounded-full bg-orange-500 px-5 py-2 text-lg text-white", to: "/contact", children: "Contact GVA" }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 118,
          columnNumber: 8
        }, this) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 117,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 110,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 109,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-gray-200", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg:flex", children: [
        editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-2",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-2", instance), initialValue: data.content["about-history-container-2"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 127,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-none lg:w-[90px]", id: "about-history-container-2", children: htmlFrom(data.content["about-history-container-2"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 131,
          columnNumber: 165
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 lg:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-3",
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-3", instance), initialValue: data.content["about-history-container-3"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 135,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "paragraph", id: "about-history-container-3", children: htmlFrom(data.content["about-history-container-3"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 138,
          columnNumber: 167
        }, this) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 134,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 126,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 125,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 124,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-white", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg:flex", children: [
        editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-4",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-4", instance), initialValue: data.content["about-history-container-4"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 148,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-none lg:w-[90px]", id: "about-history-container-4", children: htmlFrom(data.content["about-history-container-4"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 152,
          columnNumber: 165
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 lg:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-5",
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-5", instance), initialValue: data.content["about-history-container-5"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 156,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "paragraph", id: "about-history-container-5", children: htmlFrom(data.content["about-history-container-5"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 159,
          columnNumber: 167
        }, this) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 155,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 147,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 146,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 145,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-gray-200", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg:flex", children: [
        editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-6",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-6", instance), initialValue: data.content["about-history-container-6"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 169,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-none lg:w-[90px]", id: "about-history-container-6", children: htmlFrom(data.content["about-history-container-6"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 173,
          columnNumber: 165
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 lg:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-7",
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-7", instance), initialValue: data.content["about-history-container-7"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 177,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "paragraph", id: "about-history-container-7", children: htmlFrom(data.content["about-history-container-7"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 180,
          columnNumber: 167
        }, this) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 176,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 168,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 167,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 166,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-white", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg:flex", children: [
        editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-8",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-8", instance), initialValue: data.content["about-history-container-8"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 190,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-none lg:w-[90px]", id: "about-history-container-8", children: htmlFrom(data.content["about-history-container-8"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 194,
          columnNumber: 165
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 lg:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-9",
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-9", instance), initialValue: data.content["about-history-container-9"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 198,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "paragraph", id: "about-history-container-9", children: htmlFrom(data.content["about-history-container-9"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 201,
          columnNumber: 167
        }, this) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 197,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 189,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 188,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 187,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-gray-200", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg:flex", children: [
        editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-10",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-10", instance), initialValue: data.content["about-history-container-10"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 211,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-none lg:w-[90px]", id: "about-history-container-10", children: htmlFrom(data.content["about-history-container-10"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 215,
          columnNumber: 167
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 lg:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-11",
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-11", instance), initialValue: data.content["about-history-container-11"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 219,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "paragraph", id: "about-history-container-11", children: htmlFrom(data.content["about-history-container-11"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 222,
          columnNumber: 169
        }, this) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 218,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 210,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 209,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 208,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-white", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg:flex", children: [
        editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-12",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-12", instance), initialValue: data.content["about-history-container-12"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 232,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-none lg:w-[90px]", id: "about-history-container-12", children: htmlFrom(data.content["about-history-container-12"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 236,
          columnNumber: 167
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 lg:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-13",
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-13", instance), initialValue: data.content["about-history-container-13"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 240,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "paragraph", id: "about-history-container-13", children: htmlFrom(data.content["about-history-container-13"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 243,
          columnNumber: 169
        }, this) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 239,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 231,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 230,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 229,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-gray-200", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg:flex", children: [
        editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-14",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-14", instance), initialValue: data.content["about-history-container-14"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 253,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-none lg:w-[90px]", id: "about-history-container-14", children: htmlFrom(data.content["about-history-container-14"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 257,
          columnNumber: 167
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 lg:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-15",
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-15", instance), initialValue: data.content["about-history-container-15"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 261,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "paragraph", id: "about-history-container-15", children: htmlFrom(data.content["about-history-container-15"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 264,
          columnNumber: 169
        }, this) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 260,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 252,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 251,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 250,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-white", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg:flex", children: [
        editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-16",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-16", instance), initialValue: data.content["about-history-container-16"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 274,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-none lg:w-[90px]", id: "about-history-container-16", children: htmlFrom(data.content["about-history-container-16"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 278,
          columnNumber: 167
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 lg:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-17",
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-17", instance), initialValue: data.content["about-history-container-17"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 282,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "paragraph", id: "about-history-container-17", children: htmlFrom(data.content["about-history-container-17"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 285,
          columnNumber: 169
        }, this) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 281,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 273,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 272,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 271,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-gray-200", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg:flex", children: [
        editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-18",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-18", instance), initialValue: data.content["about-history-container-18"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 295,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-none lg:w-[90px]", id: "about-history-container-18", children: htmlFrom(data.content["about-history-container-18"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 299,
          columnNumber: 167
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 lg:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-19",
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-19", instance), initialValue: data.content["about-history-container-19"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 303,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "paragraph", id: "about-history-container-19", children: htmlFrom(data.content["about-history-container-19"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 306,
          columnNumber: 169
        }, this) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 302,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 294,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 293,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 292,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-white", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg:flex", children: [
        editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-20",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-20", instance), initialValue: data.content["about-history-container-20"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 316,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-none lg:w-[90px]", id: "about-history-container-20", children: htmlFrom(data.content["about-history-container-20"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 320,
          columnNumber: 167
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 lg:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-21",
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-21", instance), initialValue: data.content["about-history-container-21"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 324,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "paragraph", id: "about-history-21", children: htmlFrom(data.content["about-history-container-21"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 327,
          columnNumber: 169
        }, this) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 323,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 315,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 314,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 313,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-gray-200", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg:flex", children: [
        editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-22",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-22", instance), initialValue: data.content["about-history-container-22"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 337,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-none lg:w-[90px]", id: "about-history-container-22", children: htmlFrom(data.content["about-history-container-22"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 341,
          columnNumber: 167
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 lg:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-history-container-23",
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-history-container-23", instance), initialValue: data.content["about-history-container-23"] }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 345,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "paragraph", id: "about-history-container-23", children: htmlFrom(data.content["about-history-container-23"]) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 348,
          columnNumber: 169
        }, this) }, void 0, false, {
          fileName: "app/routes/about.history.tsx",
          lineNumber: 344,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 336,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 335,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.history.tsx",
        lineNumber: 334,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/about.history.tsx",
      lineNumber: 94,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Footer, {}, void 0, false, {
      fileName: "app/routes/about.history.tsx",
      lineNumber: 356,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/about.history.tsx",
    lineNumber: 91,
    columnNumber: 10
  }, this);
}
_s(AboutHistory, "zVyzhMJ5DkvqVWYPz1OnDlPPbmY=", false, function() {
  return [useLoaderData, useOptionalUser, useNavigate];
});
_c = AboutHistory;
var _c;
$RefreshReg$(_c, "AboutHistory");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  AboutHistory as default
};
//# sourceMappingURL=/build/routes/about.history-LLW4G547.js.map
