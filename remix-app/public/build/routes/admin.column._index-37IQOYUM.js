import {
  require_column_templates
} from "/build/_shared/chunk-PUZHN2JW.js";
import {
  moment_default
} from "/build/_shared/chunk-XAWK6254.js";
import "/build/_shared/chunk-NPKMIMFJ.js";
import {
  Icon_Admin_Edit_default
} from "/build/_shared/chunk-O4ZMMQVI.js";
import {
  Icon_Admin_Delete_default
} from "/build/_shared/chunk-RUOV274T.js";
import {
  require_cookies
} from "/build/_shared/chunk-MUAEGCBW.js";
import {
  ucwords
} from "/build/_shared/chunk-DFTVPORL.js";
import {
  Form,
  Link,
  useLoaderData
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/admin.column._index.tsx
var import_react = __toESM(require_react(), 1);
var import_cookies = __toESM(require_cookies(), 1);
var import_column_templates = __toESM(require_column_templates(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.column._index.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.column._index.tsx"
  );
  import.meta.hot.lastModified = "1748456569470.563";
}
function ColumnTemplateIndexPage() {
  _s();
  const {
    templateList,
    formSubmitMessage: formSubmitMessage2
  } = useLoaderData();
  const [showMessage, setShowMessage] = (0, import_react.useState)(!!formSubmitMessage2);
  (0, import_react.useEffect)(() => {
    if (showMessage) {
      const timeoutId = setTimeout(() => setShowMessage(false), 5e3);
      return () => clearTimeout(timeoutId);
    }
  }, [showMessage]);
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { className: "container mx-auto pb-10 pt-8", children: [
    showMessage ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("p", { className: "animate-fade bg-black text-white", children: formSubmitMessage2 }, void 0, false, {
      fileName: "app/routes/admin.column._index.tsx",
      lineNumber: 55,
      columnNumber: 19
    }, this) : null,
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[25px] font-bold", children: "Manage Column Templates" }, void 0, false, {
      fileName: "app/routes/admin.column._index.tsx",
      lineNumber: 57,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-8", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "new", className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white hover:bg-orange-800 focus:bg-orange-800", children: "Add a Column Template" }, void 0, false, {
      fileName: "app/routes/admin.column._index.tsx",
      lineNumber: 59,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/admin.column._index.tsx",
      lineNumber: 58,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("table", { className: "table-gray mt-5 w-full table-auto text-sm", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("thead", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Column Template" }, void 0, false, {
          fileName: "app/routes/admin.column._index.tsx",
          lineNumber: 66,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Type" }, void 0, false, {
          fileName: "app/routes/admin.column._index.tsx",
          lineNumber: 67,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Overridden" }, void 0, false, {
          fileName: "app/routes/admin.column._index.tsx",
          lineNumber: 68,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Source" }, void 0, false, {
          fileName: "app/routes/admin.column._index.tsx",
          lineNumber: 69,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Supported Export Types" }, void 0, false, {
          fileName: "app/routes/admin.column._index.tsx",
          lineNumber: 70,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Author" }, void 0, false, {
          fileName: "app/routes/admin.column._index.tsx",
          lineNumber: 71,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Created" }, void 0, false, {
          fileName: "app/routes/admin.column._index.tsx",
          lineNumber: 72,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Updated" }, void 0, false, {
          fileName: "app/routes/admin.column._index.tsx",
          lineNumber: 73,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "\xA0" }, void 0, false, {
          fileName: "app/routes/admin.column._index.tsx",
          lineNumber: 74,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.column._index.tsx",
        lineNumber: 65,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.column._index.tsx",
        lineNumber: 64,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tbody", { children: [
        templateList.map((template) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: template.name }, void 0, false, {
            fileName: "app/routes/admin.column._index.tsx",
            lineNumber: 79,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: ["incidents", "participants"].includes(template.type) ? ucwords(template.type) : "Unrecognized" }, void 0, false, {
            fileName: "app/routes/admin.column._index.tsx",
            lineNumber: 80,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: template.source == "code" ? template.overridden ? "Yes" : "No" : "N/A" }, void 0, false, {
            fileName: "app/routes/admin.column._index.tsx",
            lineNumber: 83,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: template.source && ["code", "database", "hidden"].includes(template.source) ? ucwords(template.source) : "Unrecognized" }, void 0, false, {
            fileName: "app/routes/admin.column._index.tsx",
            lineNumber: 84,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: !template.supported_export_types || template.supported_export_types.split(",").length == 2 ? "All" : template.supported_export_types }, void 0, false, {
            fileName: "app/routes/admin.column._index.tsx",
            lineNumber: 87,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: template.author?.name }, void 0, false, {
            fileName: "app/routes/admin.column._index.tsx",
            lineNumber: 90,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: template.created ? moment_default.unix(template.created).format("MM/DD/YYYY HH:mm") : "" }, void 0, false, {
            fileName: "app/routes/admin.column._index.tsx",
            lineNumber: 91,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: template.changed ? moment_default.unix(template.changed).format("MM/DD/YYYY HH:mm") : "" }, void 0, false, {
            fileName: "app/routes/admin.column._index.tsx",
            lineNumber: 92,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex space-x-10", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: `/admin/column/${template.ctuuid}`, className: "flex items-center font-bold text-blue-500", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Edit_default, alt: "", width: 16 }, void 0, false, {
                fileName: "app/routes/admin.column._index.tsx",
                lineNumber: 96,
                columnNumber: 11
              }, this),
              " Edit"
            ] }, void 0, true, {
              fileName: "app/routes/admin.column._index.tsx",
              lineNumber: 95,
              columnNumber: 10
            }, this),
            template.source && template.source != "code" && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { action: `/admin/column/${template.ctuuid}/destroy`, method: "post", onSubmit: (e) => {
              if (!confirm("Please confirm you want to delete this record.")) {
                e.preventDefault();
              }
            }, children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", className: "flex items-center font-bold text-blue-500", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Delete_default, alt: "", width: 16 }, void 0, false, {
                fileName: "app/routes/admin.column._index.tsx",
                lineNumber: 104,
                columnNumber: 13
              }, this),
              " Delete"
            ] }, void 0, true, {
              fileName: "app/routes/admin.column._index.tsx",
              lineNumber: 103,
              columnNumber: 12
            }, this) }, void 0, false, {
              fileName: "app/routes/admin.column._index.tsx",
              lineNumber: 98,
              columnNumber: 59
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.column._index.tsx",
            lineNumber: 94,
            columnNumber: 9
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.column._index.tsx",
            lineNumber: 93,
            columnNumber: 8
          }, this)
        ] }, `template-${template.ctuuid}`, true, {
          fileName: "app/routes/admin.column._index.tsx",
          lineNumber: 78,
          columnNumber: 36
        }, this)),
        templateList.length === 0 && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { colSpan: 9, children: "No column templates yet." }, void 0, false, {
          fileName: "app/routes/admin.column._index.tsx",
          lineNumber: 111,
          columnNumber: 8
        }, this) }, void 0, false, {
          fileName: "app/routes/admin.column._index.tsx",
          lineNumber: 110,
          columnNumber: 36
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.column._index.tsx",
        lineNumber: 77,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.column._index.tsx",
      lineNumber: 63,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.column._index.tsx",
    lineNumber: 54,
    columnNumber: 10
  }, this);
}
_s(ColumnTemplateIndexPage, "AczMGfTQzEnG9HWYJ2EhtD3XyuA=", false, function() {
  return [useLoaderData];
});
_c = ColumnTemplateIndexPage;
var _c;
$RefreshReg$(_c, "ColumnTemplateIndexPage");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  ColumnTemplateIndexPage as default
};
//# sourceMappingURL=/build/routes/admin.column._index-37IQOYUM.js.map
