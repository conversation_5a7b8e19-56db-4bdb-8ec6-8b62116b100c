import {
  require_build
} from "/build/_shared/chunk-E55QCST2.js";
import {
  Icon_Success_default
} from "/build/_shared/chunk-GBO3FEGG.js";
import {
  require_users
} from "/build/_shared/chunk-N5WPI4L5.js";
import {
  Title
} from "/build/_shared/chunk-YPQMQ54K.js";
import {
  Icon_Admin_Delete_default
} from "/build/_shared/chunk-RUOV274T.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import {
  Form,
  useActionData,
  useLoaderData,
  useNavigation,
  useSearchParams
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/user.profile.tsx
var import_remix_auth = __toESM(require_build(), 1);
var import_auth = __toESM(require_auth(), 1);
var import_users = __toESM(require_users(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/user.profile.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/user.profile.tsx"
  );
  import.meta.hot.lastModified = "1748457333684.3083";
}
var meta = () => {
  return [{
    title: "Gun Violence Archive | Edit Profile"
  }];
};
function UserProfile() {
  _s();
  const data = useLoaderData();
  const actionData = useActionData();
  const [searchParams] = useSearchParams();
  const redirectTo = searchParams.get("redirectTo") || "/";
  const transition = useNavigation();
  const isSubmitting = Boolean(transition.state == "submitting" && transition.formAction == "/user/profile");
  const userData = data?.userData;
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(import_jsx_dev_runtime.Fragment, { children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Title, { title: "Edit Profile" }, void 0, false, {
      fileName: "app/routes/user.profile.tsx",
      lineNumber: 135,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-center", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] bg-gray-200 p-4 sm:w-[480px] xl:p-10", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { method: "post", id: "profile", children: [
      actionData?.errors?.message && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-5 flex bg-white px-5 py-3.5 text-sm font-light", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Icon_Admin_Delete_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/routes/user.profile.tsx",
          lineNumber: 142,
          columnNumber: 12
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "pl-2 text-red-500", children: actionData.errors.message }, void 0, false, {
          fileName: "app/routes/user.profile.tsx",
          lineNumber: 143,
          columnNumber: 12
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/user.profile.tsx",
        lineNumber: 141,
        columnNumber: 41
      }, this),
      actionData?.messages?.message && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-5 flex bg-white px-5 py-3.5 text-sm font-light", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Icon_Success_default, alt: "", width: 30 }, void 0, false, {
          fileName: "app/routes/user.profile.tsx",
          lineNumber: 146,
          columnNumber: 12
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "pl-2", children: actionData.messages.message }, void 0, false, {
          fileName: "app/routes/user.profile.tsx",
          lineNumber: 147,
          columnNumber: 12
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/user.profile.tsx",
        lineNumber: 145,
        columnNumber: 43
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-8", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "name", className: "block text-lg font-bold", children: "Name" }, void 0, false, {
          fileName: "app/routes/user.profile.tsx",
          lineNumber: 150,
          columnNumber: 10
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "text", name: "name", id: "name", required: true, defaultValue: userData?.name, className: "block w-full rounded-md px-3 py-2 leading-loose" }, void 0, false, {
          fileName: "app/routes/user.profile.tsx",
          lineNumber: 153,
          columnNumber: 10
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/user.profile.tsx",
        lineNumber: 149,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-8", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "email", className: "block text-lg font-bold", children: "Email" }, void 0, false, {
          fileName: "app/routes/user.profile.tsx",
          lineNumber: 156,
          columnNumber: 10
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "email", name: "email", id: "email", required: true, disabled: true, defaultValue: userData?.email, className: "block w-full rounded-md px-3 py-2 leading-loose" }, void 0, false, {
          fileName: "app/routes/user.profile.tsx",
          lineNumber: 159,
          columnNumber: 10
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/user.profile.tsx",
        lineNumber: 155,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-8", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "org", className: "block", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "text-lg font-bold", children: "Organization" }, void 0, false, {
            fileName: "app/routes/user.profile.tsx",
            lineNumber: 163,
            columnNumber: 11
          }, this),
          " (optional)"
        ] }, void 0, true, {
          fileName: "app/routes/user.profile.tsx",
          lineNumber: 162,
          columnNumber: 10
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "text", name: "org", id: "org", defaultValue: userData?.org || "", className: "block w-full rounded-md px-3 py-2 leading-loose" }, void 0, false, {
          fileName: "app/routes/user.profile.tsx",
          lineNumber: 165,
          columnNumber: 10
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/user.profile.tsx",
        lineNumber: 161,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-8", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "currentpassword", className: "block", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "text-lg font-bold", children: "Current Password" }, void 0, false, {
            fileName: "app/routes/user.profile.tsx",
            lineNumber: 169,
            columnNumber: 11
          }, this),
          " (only required if changing password)"
        ] }, void 0, true, {
          fileName: "app/routes/user.profile.tsx",
          lineNumber: 168,
          columnNumber: 10
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "password", name: "currentpassword", id: "currentpassword", className: "block w-full rounded-md px-3 py-2 leading-loose" }, void 0, false, {
          fileName: "app/routes/user.profile.tsx",
          lineNumber: 171,
          columnNumber: 10
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/user.profile.tsx",
        lineNumber: 167,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-8", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "password", className: "block", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "text-lg font-bold", children: "New Password" }, void 0, false, {
            fileName: "app/routes/user.profile.tsx",
            lineNumber: 175,
            columnNumber: 11
          }, this),
          " (min 8 characters)"
        ] }, void 0, true, {
          fileName: "app/routes/user.profile.tsx",
          lineNumber: 174,
          columnNumber: 10
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "password", name: "password", id: "password", minLength: 8, className: "block w-full rounded-md px-3 py-2 leading-loose" }, void 0, false, {
          fileName: "app/routes/user.profile.tsx",
          lineNumber: 177,
          columnNumber: 10
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "pass-meter-con mt-3.5", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: "Password Strength" }, void 0, false, {
            fileName: "app/routes/user.profile.tsx",
            lineNumber: 179,
            columnNumber: 11
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("progress", { id: "strength", value: "0", max: "5" }, void 0, false, {
            fileName: "app/routes/user.profile.tsx",
            lineNumber: 180,
            columnNumber: 11
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/user.profile.tsx",
          lineNumber: 178,
          columnNumber: 10
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/user.profile.tsx",
        lineNumber: 173,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-8", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "confirmpassword", className: "block text-lg font-bold", children: "Confirm New Password" }, void 0, false, {
          fileName: "app/routes/user.profile.tsx",
          lineNumber: 184,
          columnNumber: 10
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "password", name: "confirmpassword", id: "confirmpassword", className: "block w-full rounded-md px-3 py-2 leading-loose" }, void 0, false, {
          fileName: "app/routes/user.profile.tsx",
          lineNumber: 187,
          columnNumber: 10
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/user.profile.tsx",
        lineNumber: 183,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "redirectTo", value: redirectTo }, void 0, false, {
        fileName: "app/routes/user.profile.tsx",
        lineNumber: 189,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", disabled: isSubmitting, className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white", children: isSubmitting ? "Saving" : "Save" }, void 0, false, {
          fileName: "app/routes/user.profile.tsx",
          lineNumber: 191,
          columnNumber: 10
        }, this),
        isSubmitting && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lds-ring", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
            fileName: "app/routes/user.profile.tsx",
            lineNumber: 195,
            columnNumber: 12
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
            fileName: "app/routes/user.profile.tsx",
            lineNumber: 196,
            columnNumber: 12
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
            fileName: "app/routes/user.profile.tsx",
            lineNumber: 197,
            columnNumber: 12
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
            fileName: "app/routes/user.profile.tsx",
            lineNumber: 198,
            columnNumber: 12
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/user.profile.tsx",
          lineNumber: 194,
          columnNumber: 27
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/user.profile.tsx",
        lineNumber: 190,
        columnNumber: 9
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/user.profile.tsx",
      lineNumber: 140,
      columnNumber: 8
    }, this) }, void 0, false, {
      fileName: "app/routes/user.profile.tsx",
      lineNumber: 139,
      columnNumber: 7
    }, this) }, void 0, false, {
      fileName: "app/routes/user.profile.tsx",
      lineNumber: 138,
      columnNumber: 6
    }, this) }, void 0, false, {
      fileName: "app/routes/user.profile.tsx",
      lineNumber: 137,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/user.profile.tsx",
      lineNumber: 136,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/user.profile.tsx",
    lineNumber: 134,
    columnNumber: 10
  }, this);
}
_s(UserProfile, "622mGUwI5+3A5W9xGXhexewuZMw=", false, function() {
  return [useLoaderData, useActionData, useSearchParams, useNavigation];
});
_c = UserProfile;
var _c;
$RefreshReg$(_c, "UserProfile");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  UserProfile as default,
  meta
};
//# sourceMappingURL=/build/routes/user.profile-SKTODZSD.js.map
