import {
  require_users
} from "/build/_shared/chunk-N5WPI4L5.js";
import {
  Title
} from "/build/_shared/chunk-YPQMQ54K.js";
import {
  Icon_Admin_Delete_default
} from "/build/_shared/chunk-RUOV274T.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import "/build/_shared/chunk-DFTVPORL.js";
import {
  Form,
  useActionData,
  useNavigation,
  useSearchParams
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import "/build/_shared/chunk-QT64XSGC.js";
import {
  require_node
} from "/build/_shared/chunk-ZP6BZTHN.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/user.register.tsx
var import_node = __toESM(require_node(), 1);
var import_auth = __toESM(require_auth(), 1);
var import_users = __toESM(require_users(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/user.register.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/user.register.tsx"
  );
  import.meta.hot.lastModified = "1748458618759.3025";
}
var meta = () => {
  return [{
    title: "Gun Violence Archive | Sign Up"
  }];
};
function UserRegister() {
  _s();
  const errors = useActionData();
  const [searchParams] = useSearchParams();
  const redirectTo = searchParams.get("redirectTo") || "/";
  const transition = useNavigation();
  const isSubmitting = Boolean(transition.state == "submitting");
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(import_jsx_dev_runtime.Fragment, { children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Title, { title: "Sign Up" }, void 0, false, {
      fileName: "app/routes/user.register.tsx",
      lineNumber: 141,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-center", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] bg-gray-200 p-4 sm:w-[480px] xl:p-10", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, {
        method: "post",
        id: "register",
        // @ts-ignore
        children: [
          errors?.message && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-5 flex bg-white px-5 py-3.5 text-sm font-light", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Icon_Admin_Delete_default, alt: "", width: 16 }, void 0, false, {
              fileName: "app/routes/user.register.tsx",
              lineNumber: 150,
              columnNumber: 12
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "pl-2 text-red-500", children: errors.message }, void 0, false, {
              fileName: "app/routes/user.register.tsx",
              lineNumber: 151,
              columnNumber: 12
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/user.register.tsx",
            lineNumber: 149,
            columnNumber: 34
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-8", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "name", className: "block text-lg font-bold", children: "Name" }, void 0, false, {
              fileName: "app/routes/user.register.tsx",
              lineNumber: 154,
              columnNumber: 10
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "name", name: "name", type: "text", required: true, className: "block w-full rounded-md px-3 py-2 leading-loose" }, void 0, false, {
              fileName: "app/routes/user.register.tsx",
              lineNumber: 157,
              columnNumber: 10
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/user.register.tsx",
            lineNumber: 153,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-8", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "email", className: "block text-lg font-bold", children: "Email" }, void 0, false, {
              fileName: "app/routes/user.register.tsx",
              lineNumber: 160,
              columnNumber: 10
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "email", name: "email", type: "email", required: true, className: "block w-full rounded-md px-3 py-2 leading-loose" }, void 0, false, {
              fileName: "app/routes/user.register.tsx",
              lineNumber: 163,
              columnNumber: 10
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/user.register.tsx",
            lineNumber: 159,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-8", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "org", className: "block", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "text-lg font-bold", children: "Organization" }, void 0, false, {
                fileName: "app/routes/user.register.tsx",
                lineNumber: 167,
                columnNumber: 11
              }, this),
              " (optional)"
            ] }, void 0, true, {
              fileName: "app/routes/user.register.tsx",
              lineNumber: 166,
              columnNumber: 10
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "org", name: "org", type: "text", className: "block w-full rounded-md px-3 py-2 leading-loose" }, void 0, false, {
              fileName: "app/routes/user.register.tsx",
              lineNumber: 169,
              columnNumber: 10
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/user.register.tsx",
            lineNumber: 165,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-8", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "password", className: "block", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "text-lg font-bold", children: "Password" }, void 0, false, {
                fileName: "app/routes/user.register.tsx",
                lineNumber: 173,
                columnNumber: 11
              }, this),
              " (min 8 characters)"
            ] }, void 0, true, {
              fileName: "app/routes/user.register.tsx",
              lineNumber: 172,
              columnNumber: 10
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "password", name: "password", id: "password", minLength: 8, required: true, className: "block w-full rounded-md px-3 py-2 leading-loose" }, void 0, false, {
              fileName: "app/routes/user.register.tsx",
              lineNumber: 175,
              columnNumber: 10
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "pass-meter-con mt-3.5", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: "Password Strength" }, void 0, false, {
                fileName: "app/routes/user.register.tsx",
                lineNumber: 177,
                columnNumber: 11
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("progress", { id: "strength", value: "0", max: "5" }, void 0, false, {
                fileName: "app/routes/user.register.tsx",
                lineNumber: 178,
                columnNumber: 11
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/user.register.tsx",
              lineNumber: 176,
              columnNumber: 10
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/user.register.tsx",
            lineNumber: 171,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-8", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "confirmpassword", className: "block text-lg font-bold", children: "Confirm Password" }, void 0, false, {
              fileName: "app/routes/user.register.tsx",
              lineNumber: 182,
              columnNumber: 10
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "password", name: "confirmpassword", id: "confirmpassword", required: true, className: "block w-full rounded-md px-3 py-2 leading-loose" }, void 0, false, {
              fileName: "app/routes/user.register.tsx",
              lineNumber: 185,
              columnNumber: 10
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/user.register.tsx",
            lineNumber: 181,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "redirectTo", value: redirectTo }, void 0, false, {
            fileName: "app/routes/user.register.tsx",
            lineNumber: 187,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-8", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", disabled: isSubmitting, className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white", children: isSubmitting ? "Submitting" : "Submit" }, void 0, false, {
              fileName: "app/routes/user.register.tsx",
              lineNumber: 189,
              columnNumber: 10
            }, this),
            isSubmitting && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lds-ring", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
                fileName: "app/routes/user.register.tsx",
                lineNumber: 193,
                columnNumber: 12
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
                fileName: "app/routes/user.register.tsx",
                lineNumber: 194,
                columnNumber: 12
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
                fileName: "app/routes/user.register.tsx",
                lineNumber: 195,
                columnNumber: 12
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
                fileName: "app/routes/user.register.tsx",
                lineNumber: 196,
                columnNumber: 12
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/user.register.tsx",
              lineNumber: 192,
              columnNumber: 27
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/user.register.tsx",
            lineNumber: 188,
            columnNumber: 9
          }, this)
        ]
      }, void 0, true, {
        fileName: "app/routes/user.register.tsx",
        lineNumber: 146,
        columnNumber: 8
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-4 text-lg text-blue-500", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { href: "/user/login", children: "Back to Sign In" }, void 0, false, {
        fileName: "app/routes/user.register.tsx",
        lineNumber: 201,
        columnNumber: 9
      }, this) }, void 0, false, {
        fileName: "app/routes/user.register.tsx",
        lineNumber: 200,
        columnNumber: 8
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/user.register.tsx",
      lineNumber: 145,
      columnNumber: 7
    }, this) }, void 0, false, {
      fileName: "app/routes/user.register.tsx",
      lineNumber: 144,
      columnNumber: 6
    }, this) }, void 0, false, {
      fileName: "app/routes/user.register.tsx",
      lineNumber: 143,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/user.register.tsx",
      lineNumber: 142,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/user.register.tsx",
    lineNumber: 140,
    columnNumber: 10
  }, this);
}
_s(UserRegister, "76bsy40UGcgzV0h1QyoO066Wez4=", false, function() {
  return [useActionData, useSearchParams, useNavigation];
});
_c = UserRegister;
var _c;
$RefreshReg$(_c, "UserRegister");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  UserRegister as default,
  meta
};
//# sourceMappingURL=/build/routes/user.register-ZPRLAQGC.js.map
