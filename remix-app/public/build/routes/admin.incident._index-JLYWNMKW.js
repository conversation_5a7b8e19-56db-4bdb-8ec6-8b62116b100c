import {
  Icon_Admin_Columns_default,
  Icon_Admin_Export_default
} from "/build/_shared/chunk-35JPSIWF.js";
import {
  Icon_Admin_Incidents_default
} from "/build/_shared/chunk-AN3YALPB.js";
import {
  AdminPagination
} from "/build/_shared/chunk-5JSGFT47.js";
import "/build/_shared/chunk-QGERY6II.js";
import "/build/_shared/chunk-ULKIBZZM.js";
import "/build/_shared/chunk-OHSQI4V6.js";
import {
  require_incidents
} from "/build/_shared/chunk-J24HANUS.js";
import {
  require_taxonomy
} from "/build/_shared/chunk-AO7MZAIH.js";
import {
  moment_default
} from "/build/_shared/chunk-XAWK6254.js";
import "/build/_shared/chunk-NPKMIMFJ.js";
import {
  Icon_Admin_Edit_default
} from "/build/_shared/chunk-O4ZMMQVI.js";
import {
  Icon_Admin_Delete_default
} from "/build/_shared/chunk-RUOV274T.js";
import {
  require_cookies
} from "/build/_shared/chunk-MUAEGCBW.js";
import {
  Form,
  Link,
  useLoaderData
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/admin.incident._index.tsx
var import_react = __toESM(require_react(), 1);
var import_cookies = __toESM(require_cookies(), 1);
var import_incidents = __toESM(require_incidents(), 1);
var import_taxonomy = __toESM(require_taxonomy(), 1);

// app/images/Icon-Admin-Download.svg
var Icon_Admin_Download_default = "/build/_assets/Icon-Admin-Download-WBZOZPUP.svg";

// app/images/Icon-Admin-Map.svg
var Icon_Admin_Map_default = "/build/_assets/Icon-Admin-Map-ADLKF3IO.svg";

// app/routes/admin.incident._index.tsx
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.incident._index.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.incident._index.tsx"
  );
  import.meta.hot.lastModified = "1748459219265.026";
}
var PAGE_SIZE = 25;
function IncidentIndexPage() {
  _s();
  const {
    incidents,
    incidentsCount,
    page,
    formSubmitMessage: formSubmitMessage2
  } = useLoaderData();
  const [showMessage, setShowMessage] = (0, import_react.useState)(!!formSubmitMessage2);
  (0, import_react.useEffect)(() => {
    if (showMessage) {
      const timeoutId = setTimeout(() => setShowMessage(false), 5e3);
      return () => clearTimeout(timeoutId);
    }
  }, [showMessage]);
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { className: "container mx-auto py-10", children: [
    showMessage ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("p", { className: "animate-fade bg-black text-white", children: formSubmitMessage2 }, void 0, false, {
      fileName: "app/routes/admin.incident._index.tsx",
      lineNumber: 87,
      columnNumber: 19
    }, this) : null,
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-gray-500 px-10 py-4", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("ul", { className: "flex space-x-10 text-sm", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "flex items-center", to: `/admin/incident`, children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Incidents_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 121,
          columnNumber: 8
        }, this),
        " Incidents"
      ] }, void 0, true, {
        fileName: "app/routes/admin.incident._index.tsx",
        lineNumber: 120,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.incident._index.tsx",
        lineNumber: 119,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "flex items-center", to: `/admin/incident`, children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Columns_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 126,
          columnNumber: 8
        }, this),
        " Choose Columns"
      ] }, void 0, true, {
        fileName: "app/routes/admin.incident._index.tsx",
        lineNumber: 125,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.incident._index.tsx",
        lineNumber: 124,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "flex items-center", to: `/admin/incident`, children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Delete_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 131,
          columnNumber: 8
        }, this),
        " Delete"
      ] }, void 0, true, {
        fileName: "app/routes/admin.incident._index.tsx",
        lineNumber: 130,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.incident._index.tsx",
        lineNumber: 129,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "flex items-center", to: `/admin/incident`, children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Download_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 136,
          columnNumber: 8
        }, this),
        " Download Query"
      ] }, void 0, true, {
        fileName: "app/routes/admin.incident._index.tsx",
        lineNumber: 135,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.incident._index.tsx",
        lineNumber: 134,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "flex items-center", to: `/admin/incident`, children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Edit_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 141,
          columnNumber: 8
        }, this),
        " Edit Label"
      ] }, void 0, true, {
        fileName: "app/routes/admin.incident._index.tsx",
        lineNumber: 140,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.incident._index.tsx",
        lineNumber: 139,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "flex items-center", to: `/admin/incident`, children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Edit_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 146,
          columnNumber: 8
        }, this),
        " Edit Search"
      ] }, void 0, true, {
        fileName: "app/routes/admin.incident._index.tsx",
        lineNumber: 145,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.incident._index.tsx",
        lineNumber: 144,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "flex items-center", to: `/admin/incident`, children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Export_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 151,
          columnNumber: 8
        }, this),
        " Export as CSV"
      ] }, void 0, true, {
        fileName: "app/routes/admin.incident._index.tsx",
        lineNumber: 150,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.incident._index.tsx",
        lineNumber: 149,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "flex items-center", to: `/admin/incident`, children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Map_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 156,
          columnNumber: 8
        }, this),
        " Map"
      ] }, void 0, true, {
        fileName: "app/routes/admin.incident._index.tsx",
        lineNumber: 155,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.incident._index.tsx",
        lineNumber: 154,
        columnNumber: 6
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.incident._index.tsx",
      lineNumber: 118,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/admin.incident._index.tsx",
      lineNumber: 117,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("table", { className: "table-gray mt-5 w-full table-auto text-sm", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("thead", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Incident ID" }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 165,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Incident Date" }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 166,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Incident Time" }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 167,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "State" }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 168,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "City/County" }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 169,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Victims Killed" }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 170,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Victims Injured" }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 171,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Suspects Killed" }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 172,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Suspects Injured" }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 173,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Suspects Arrested" }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 174,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "\xA0" }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 175,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.incident._index.tsx",
        lineNumber: 164,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.incident._index.tsx",
        lineNumber: 163,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tbody", { children: [
        incidents.map((incident) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: incident.incident_id }, void 0, false, {
            fileName: "app/routes/admin.incident._index.tsx",
            lineNumber: 180,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: incident.incident_date ? moment_default.unix(incident.incident_date).format("MMMM D, YYYY") : "" }, void 0, false, {
            fileName: "app/routes/admin.incident._index.tsx",
            lineNumber: 181,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: incident.incident_time ? moment_default.unix(incident.incident_time).format("h:mm A") : "" }, void 0, false, {
            fileName: "app/routes/admin.incident._index.tsx",
            lineNumber: 184,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: incident.state?.value }, void 0, false, {
            fileName: "app/routes/admin.incident._index.tsx",
            lineNumber: 185,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: [
            incident.city_county,
            "/",
            incident.county
          ] }, void 0, true, {
            fileName: "app/routes/admin.incident._index.tsx",
            lineNumber: 186,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: incident.incident_participants.filter((s) => s.participant_type == "victim" && s.participant_status?.includes("Killed")).length }, void 0, false, {
            fileName: "app/routes/admin.incident._index.tsx",
            lineNumber: 189,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: incident.incident_participants.filter((s) => s.participant_type == "victim" && s.participant_status?.includes("Injured")).length }, void 0, false, {
            fileName: "app/routes/admin.incident._index.tsx",
            lineNumber: 192,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: incident.incident_participants.filter((s) => s.participant_type == "perpetrator" && s.participant_status?.includes("Killed")).length }, void 0, false, {
            fileName: "app/routes/admin.incident._index.tsx",
            lineNumber: 195,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: incident.incident_participants.filter((s) => s.participant_type == "perpetrator" && s.participant_status?.includes("Injured")).length }, void 0, false, {
            fileName: "app/routes/admin.incident._index.tsx",
            lineNumber: 198,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: incident.incident_participants.filter((s) => s.participant_type == "perpetrator" && s.participant_status?.includes("Arrested")).length }, void 0, false, {
            fileName: "app/routes/admin.incident._index.tsx",
            lineNumber: 201,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex space-x-10", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { href: `/admin/incident/${incident.incident_id}`, className: "flex items-center font-bold text-blue-500", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Edit_default, alt: "", width: 16 }, void 0, false, {
                fileName: "app/routes/admin.incident._index.tsx",
                lineNumber: 207,
                columnNumber: 11
              }, this),
              " Edit"
            ] }, void 0, true, {
              fileName: "app/routes/admin.incident._index.tsx",
              lineNumber: 206,
              columnNumber: 10
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { action: `/admin/incident/${incident.incident_id}/destroy`, method: "post", onSubmit: (e) => {
              if (!confirm("Please confirm you want to delete this record.")) {
                e.preventDefault();
              }
            }, children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", className: "flex items-center font-bold text-blue-500", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Delete_default, alt: "", width: 16 }, void 0, false, {
                fileName: "app/routes/admin.incident._index.tsx",
                lineNumber: 215,
                columnNumber: 12
              }, this),
              " Delete"
            ] }, void 0, true, {
              fileName: "app/routes/admin.incident._index.tsx",
              lineNumber: 214,
              columnNumber: 11
            }, this) }, void 0, false, {
              fileName: "app/routes/admin.incident._index.tsx",
              lineNumber: 209,
              columnNumber: 10
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.incident._index.tsx",
            lineNumber: 205,
            columnNumber: 9
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.incident._index.tsx",
            lineNumber: 204,
            columnNumber: 8
          }, this)
        ] }, `incident-${incident.incident_id}`, true, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 179,
          columnNumber: 33
        }, this)),
        incidents.length === 0 && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { colSpan: 11, children: "No Incidents yet." }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 222,
          columnNumber: 8
        }, this) }, void 0, false, {
          fileName: "app/routes/admin.incident._index.tsx",
          lineNumber: 221,
          columnNumber: 33
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.incident._index.tsx",
        lineNumber: 178,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.incident._index.tsx",
      lineNumber: 162,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "my-10 flex justify-center text-sm", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(AdminPagination, { total: incidentsCount, defaultPageSize: PAGE_SIZE }, void 0, false, {
      fileName: "app/routes/admin.incident._index.tsx",
      lineNumber: 228,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/admin.incident._index.tsx",
      lineNumber: 227,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.incident._index.tsx",
    lineNumber: 86,
    columnNumber: 10
  }, this);
}
_s(IncidentIndexPage, "3sP40FbaTY5Igo/cSRtNOx6PCqg=", false, function() {
  return [useLoaderData];
});
_c = IncidentIndexPage;
var _c;
$RefreshReg$(_c, "IncidentIndexPage");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  IncidentIndexPage as default
};
//# sourceMappingURL=/build/routes/admin.incident._index-JLYWNMKW.js.map
