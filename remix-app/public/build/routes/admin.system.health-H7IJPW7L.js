import {
  require_cloudflare
} from "/build/_shared/chunk-WMMS5YEM.js";
import {
  require_db
} from "/build/_shared/chunk-PC2ACFOD.js";
import {
  useUser
} from "/build/_shared/chunk-DFTVPORL.js";
import {
  useLoaderData
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import "/build/_shared/chunk-QT64XSGC.js";
import {
  require_node
} from "/build/_shared/chunk-ZP6BZTHN.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/admin.system.health.tsx
var import_node = __toESM(require_node(), 1);
var import_db = __toESM(require_db(), 1);
var import_cloudflare = __toESM(require_cloudflare(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.system.health.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.system.health.tsx"
  );
  import.meta.hot.lastModified = "1748978030911.131";
}
function SiteHealthPage() {
  _s();
  const user = useUser();
  if (user.role !== "Admin") {
    throw new Error("No permission.");
  }
  const data = useLoaderData();
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "p-8", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h1", { className: "mb-8 text-3xl font-bold", children: "Site Health" }, void 0, false, {
      fileName: "app/routes/admin.system.health.tsx",
      lineNumber: 133,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid grid-cols-1 gap-8 md:grid-cols-2", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-lg bg-gray-100 p-6 shadow", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h2", { className: "mb-4 text-xl font-semibold", children: "Database Information" }, void 0, false, {
          fileName: "app/routes/admin.system.health.tsx",
          lineNumber: 138,
          columnNumber: 6
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-2", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Version:" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 141,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.database.version }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 142,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 140,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Provider:" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 145,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.database.provider }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 146,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 144,
            columnNumber: 7
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.system.health.tsx",
          lineNumber: 139,
          columnNumber: 6
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.system.health.tsx",
        lineNumber: 137,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-lg bg-gray-100 p-6 shadow", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h2", { className: "mb-4 text-xl font-semibold", children: "Node.js Information" }, void 0, false, {
          fileName: "app/routes/admin.system.health.tsx",
          lineNumber: 153,
          columnNumber: 6
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-2", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Version:" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 156,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.node.version }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 157,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 155,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Environment:" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 160,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.node.environment }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 161,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 159,
            columnNumber: 7
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.system.health.tsx",
          lineNumber: 154,
          columnNumber: 6
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.system.health.tsx",
        lineNumber: 152,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-lg bg-gray-100 p-6 shadow", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h2", { className: "mb-4 text-xl font-semibold", children: "System Information" }, void 0, false, {
          fileName: "app/routes/admin.system.health.tsx",
          lineNumber: 168,
          columnNumber: 6
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-2", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Platform:" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 171,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.system.platform }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 172,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 170,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "OS Type:" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 175,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.system.type }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 176,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 174,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "OS Release:" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 179,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.system.release }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 180,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 178,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Architecture:" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 183,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.system.arch }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 184,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 182,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "CPU Cores:" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 187,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.system.cpus }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 188,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 186,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Total Memory:" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 191,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.system.totalMemory }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 192,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 190,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Free Memory:" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 195,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.system.freeMemory }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 196,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 194,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Uptime:" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 199,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.system.uptime }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 200,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 198,
            columnNumber: 7
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.system.health.tsx",
          lineNumber: 169,
          columnNumber: 6
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.system.health.tsx",
        lineNumber: 167,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-lg bg-gray-100 p-6 shadow", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h2", { className: "mb-4 text-xl font-semibold", children: "Application Statistics" }, void 0, false, {
          fileName: "app/routes/admin.system.health.tsx",
          lineNumber: 207,
          columnNumber: 6
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-2", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Total Users:" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 210,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.stats.users }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 211,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 209,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Total Incidents:" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 214,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.stats.incidents }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 215,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 213,
            columnNumber: 7
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.system.health.tsx",
          lineNumber: 208,
          columnNumber: 6
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.system.health.tsx",
        lineNumber: 206,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-lg bg-gray-100 p-6 shadow md:col-span-2", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h2", { className: "mb-4 text-xl font-semibold", children: "Key Dependencies" }, void 0, false, {
          fileName: "app/routes/admin.system.health.tsx",
          lineNumber: 222,
          columnNumber: 6
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid grid-cols-2 gap-4 md:grid-cols-3", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Remix:" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 225,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.dependencies.remix }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 226,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 224,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "React:" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 229,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.dependencies.react }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 230,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 228,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Prisma:" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 233,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.dependencies.prisma }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 234,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 232,
            columnNumber: 7
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.system.health.tsx",
          lineNumber: 223,
          columnNumber: 6
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.system.health.tsx",
        lineNumber: 221,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-lg bg-gray-100 p-6 shadow md:col-span-2", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h2", { className: "mb-4 text-xl font-semibold", children: "Traffic Metrics (Last 30 Days)" }, void 0, false, {
          fileName: "app/routes/admin.system.health.tsx",
          lineNumber: 241,
          columnNumber: 6
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid grid-cols-1 gap-6 md:grid-cols-2", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h3", { className: "mb-3 text-lg font-medium", children: "Overview" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 244,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-2", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Total Visitors:" }, void 0, false, {
                  fileName: "app/routes/admin.system.health.tsx",
                  lineNumber: 247,
                  columnNumber: 10
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.traffic.totalVisitors.toLocaleString() }, void 0, false, {
                  fileName: "app/routes/admin.system.health.tsx",
                  lineNumber: 248,
                  columnNumber: 10
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.health.tsx",
                lineNumber: 246,
                columnNumber: 9
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Unique Visitors:" }, void 0, false, {
                  fileName: "app/routes/admin.system.health.tsx",
                  lineNumber: 251,
                  columnNumber: 10
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.traffic.uniqueVisitors.toLocaleString() }, void 0, false, {
                  fileName: "app/routes/admin.system.health.tsx",
                  lineNumber: 252,
                  columnNumber: 10
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.health.tsx",
                lineNumber: 250,
                columnNumber: 9
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Page Views:" }, void 0, false, {
                  fileName: "app/routes/admin.system.health.tsx",
                  lineNumber: 255,
                  columnNumber: 10
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.traffic.pageViews.toLocaleString() }, void 0, false, {
                  fileName: "app/routes/admin.system.health.tsx",
                  lineNumber: 256,
                  columnNumber: 10
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.health.tsx",
                lineNumber: 254,
                columnNumber: 9
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 245,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 243,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h3", { className: "mb-3 text-lg font-medium", children: "Top Countries" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 262,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-2", children: data.traffic.topCountries.length > 0 ? data.traffic.topCountries.map((country, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: [
                country.country,
                ":"
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.health.tsx",
                lineNumber: 265,
                columnNumber: 12
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: country.visits.toLocaleString() }, void 0, false, {
                fileName: "app/routes/admin.system.health.tsx",
                lineNumber: 266,
                columnNumber: 12
              }, this)
            ] }, index, true, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 264,
              columnNumber: 99
            }, this)) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-gray-500", children: "No country data available" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 267,
              columnNumber: 21
            }, this) }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 263,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 261,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "md:col-span-2", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h3", { className: "mb-3 text-lg font-medium", children: "Recent Traffic Trend" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 272,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-2", children: data.traffic.trafficByDay.length > 0 ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid grid-cols-1 gap-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4", children: data.traffic.trafficByDay.slice(-8).map((day, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: [
                day.date,
                ":"
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.health.tsx",
                lineNumber: 276,
                columnNumber: 13
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: day.visits.toLocaleString() }, void 0, false, {
                fileName: "app/routes/admin.system.health.tsx",
                lineNumber: 277,
                columnNumber: 13
              }, this)
            ] }, index, true, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 275,
              columnNumber: 68
            }, this)) }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 274,
              columnNumber: 49
            }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-gray-500", children: "No traffic trend data available" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 279,
              columnNumber: 19
            }, this) }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 273,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 271,
            columnNumber: 7
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.system.health.tsx",
          lineNumber: 242,
          columnNumber: 6
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.system.health.tsx",
        lineNumber: 240,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-lg bg-gray-100 p-6 shadow md:col-span-2", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h2", { className: "mb-4 text-xl font-semibold", children: "Firewall Statistics (Last 30 Days)" }, void 0, false, {
          fileName: "app/routes/admin.system.health.tsx",
          lineNumber: 287,
          columnNumber: 6
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid grid-cols-1 gap-6 md:grid-cols-2", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h3", { className: "mb-3 text-lg font-medium", children: "Overview" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 290,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-2", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Total Threats:" }, void 0, false, {
                  fileName: "app/routes/admin.system.health.tsx",
                  lineNumber: 293,
                  columnNumber: 10
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.traffic.firewall.totalThreats.toLocaleString() }, void 0, false, {
                  fileName: "app/routes/admin.system.health.tsx",
                  lineNumber: 294,
                  columnNumber: 10
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.health.tsx",
                lineNumber: 292,
                columnNumber: 9
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Blocked Requests:" }, void 0, false, {
                  fileName: "app/routes/admin.system.health.tsx",
                  lineNumber: 297,
                  columnNumber: 10
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: data.traffic.firewall.blockedRequests.toLocaleString() }, void 0, false, {
                  fileName: "app/routes/admin.system.health.tsx",
                  lineNumber: 298,
                  columnNumber: 10
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.health.tsx",
                lineNumber: 296,
                columnNumber: 9
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 291,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 289,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h3", { className: "mb-3 text-lg font-medium", children: "Top Threat Countries" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 304,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-2", children: data.traffic.firewall.topCountries.length > 0 ? data.traffic.firewall.topCountries.map((country, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: [
                country.country,
                ":"
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.health.tsx",
                lineNumber: 307,
                columnNumber: 12
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: country.count.toLocaleString() }, void 0, false, {
                fileName: "app/routes/admin.system.health.tsx",
                lineNumber: 308,
                columnNumber: 12
              }, this)
            ] }, index, true, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 306,
              columnNumber: 117
            }, this)) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-gray-500", children: "No threat country data available" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 309,
              columnNumber: 21
            }, this) }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 305,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 303,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "md:col-span-2", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h3", { className: "mb-3 text-lg font-medium", children: "Top Firewall Rules" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 314,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-2", children: data.traffic.firewall.topRules.length > 0 ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid grid-cols-1 gap-2", children: data.traffic.firewall.topRules.map((rule, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: [
                rule.name,
                " (",
                rule.ruleId,
                "):"
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.health.tsx",
                lineNumber: 318,
                columnNumber: 13
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: rule.count.toLocaleString() }, void 0, false, {
                fileName: "app/routes/admin.system.health.tsx",
                lineNumber: 319,
                columnNumber: 13
              }, this)
            ] }, index, true, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 317,
              columnNumber: 64
            }, this)) }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 316,
              columnNumber: 54
            }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-gray-500", children: "No firewall rule data available" }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 321,
              columnNumber: 19
            }, this) }, void 0, false, {
              fileName: "app/routes/admin.system.health.tsx",
              lineNumber: 315,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.health.tsx",
            lineNumber: 313,
            columnNumber: 7
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.system.health.tsx",
          lineNumber: 288,
          columnNumber: 6
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.system.health.tsx",
        lineNumber: 286,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.system.health.tsx",
      lineNumber: 135,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.system.health.tsx",
    lineNumber: 132,
    columnNumber: 10
  }, this);
}
_s(SiteHealthPage, "mNG6CBjAPO0fhbVziK8fnisI310=", false, function() {
  return [useUser, useLoaderData];
});
_c = SiteHealthPage;
var _c;
$RefreshReg$(_c, "SiteHealthPage");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  SiteHealthPage as default
};
//# sourceMappingURL=/build/routes/admin.system.health-H7IJPW7L.js.map
