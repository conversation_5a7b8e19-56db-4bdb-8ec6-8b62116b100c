import {
  dashboard_default
} from "/build/_shared/chunk-VVRI3FBH.js";
import {
  Title
} from "/build/_shared/chunk-YPQMQ54K.js";
import {
  require_dashboard
} from "/build/_shared/chunk-LR32EY3Q.js";
import {
  NavLink,
  isRouteErrorResponse,
  useLoaderData,
  useRouteError
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __commonJS,
  __esm,
  __export,
  __toCommonJS,
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// node_modules/@superset-ui/embedded-sdk/lib/const.js
var require_const = __commonJS({
  "node_modules/@superset-ui/embedded-sdk/lib/const.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.IFRAME_COMMS_MESSAGE_TYPE = exports.DASHBOARD_UI_FILTER_CONFIG_URL_PARAM_KEY = void 0;
    var IFRAME_COMMS_MESSAGE_TYPE = exports.IFRAME_COMMS_MESSAGE_TYPE = "__embedded_comms__";
    var DASHBOARD_UI_FILTER_CONFIG_URL_PARAM_KEY = exports.DASHBOARD_UI_FILTER_CONFIG_URL_PARAM_KEY = {
      visible: "show_filters",
      expanded: "expand_filters"
    };
  }
});

// node_modules/@superset-ui/switchboard/esm/switchboard.js
function isGet(message) {
  return message.switchboardAction === Actions.GET;
}
function isReply(message) {
  return message.switchboardAction === Actions.REPLY;
}
function isEmit(message) {
  return message.switchboardAction === Actions.EMIT;
}
function isError(message) {
  return message.switchboardAction === Actions.ERROR;
}
var Actions, Switchboard, switchboard_default;
var init_switchboard = __esm({
  "node_modules/@superset-ui/switchboard/esm/switchboard.js"() {
    Actions = /* @__PURE__ */ function(Actions2) {
      Actions2["GET"] = "get";
      Actions2["REPLY"] = "reply";
      Actions2["EMIT"] = "emit";
      Actions2["ERROR"] = "error";
      return Actions2;
    }(Actions || {});
    Switchboard = class {
      constructor(params) {
        this.port = void 0;
        this.name = "";
        this.methods = {};
        this.incrementor = 1;
        this.debugMode = void 0;
        this.isInitialised = void 0;
        if (!params) {
          return;
        }
        this.init(params);
      }
      init(params) {
        if (this.isInitialised) {
          this.logError("already initialized");
          return;
        }
        const { port, name = "switchboard", debug = false } = params;
        this.port = port;
        this.name = name;
        this.debugMode = debug;
        port.addEventListener("message", async (event) => {
          this.log("message received", event);
          const message = event.data;
          if (isGet(message)) {
            this.port.postMessage(await this.getMethodResult(message));
          } else if (isEmit(message)) {
            const { method, args } = message;
            const executor = this.methods[method];
            if (executor) {
              executor(args);
            }
          }
        });
        this.isInitialised = true;
      }
      async getMethodResult({
        messageId,
        method,
        args
      }) {
        const executor = this.methods[method];
        if (executor == null) {
          return {
            switchboardAction: Actions.ERROR,
            messageId,
            error: `[${this.name}] Method "${method}" is not defined`
          };
        }
        try {
          const result = await executor(args);
          return {
            switchboardAction: Actions.REPLY,
            messageId,
            result
          };
        } catch (err) {
          this.logError(err);
          return {
            switchboardAction: Actions.ERROR,
            messageId,
            error: `[${this.name}] Method "${method}" threw an error`
          };
        }
      }
      /**
       * Defines a method that can be "called" from the other side by sending an event.
       */
      defineMethod(methodName, executor) {
        this.methods[methodName] = executor;
      }
      /**
       * Calls a method registered on the other side, and returns the result.
       *
       * How this is accomplished:
       * This switchboard sends a "get" message over the channel describing which method to call with which arguments.
       * The other side's switchboard finds a method with that name, and calls it with the arguments.
       * It then packages up the returned value into a "reply" message, sending it back to us across the channel.
       * This switchboard has attached a listener on the channel, which will resolve with the result when a reply is detected.
       *
       * Instead of an arguments list, arguments are supplied as a map.
       *
       * @param method the name of the method to call
       * @param args arguments that will be supplied. Must be serializable, no functions or other nonsense.
       * @returns whatever is returned from the method
       */
      get(method, args = void 0) {
        return new Promise((resolve, reject) => {
          if (!this.isInitialised) {
            reject(new Error("Switchboard not initialised"));
            return;
          }
          const messageId = this.getNewMessageId();
          const listener = (event) => {
            const message2 = event.data;
            if (message2.messageId !== messageId)
              return;
            this.port.removeEventListener("message", listener);
            if (isReply(message2)) {
              resolve(message2.result);
            } else {
              const errStr = isError(message2) ? message2.error : "Unexpected response message";
              reject(new Error(errStr));
            }
          };
          this.port.addEventListener("message", listener);
          this.port.start();
          const message = {
            switchboardAction: Actions.GET,
            method,
            messageId,
            args
          };
          this.port.postMessage(message);
        });
      }
      /**
       * Emit calls a method on the other side just like get does.
       * But emit doesn't wait for a response, it just sends and forgets.
       *
       * @param method
       * @param args
       */
      emit(method, args = void 0) {
        if (!this.isInitialised) {
          this.logError("Switchboard not initialised");
          return;
        }
        const message = {
          switchboardAction: Actions.EMIT,
          method,
          args
        };
        this.port.postMessage(message);
      }
      start() {
        if (!this.isInitialised) {
          this.logError("Switchboard not initialised");
          return;
        }
        this.port.start();
      }
      log(...args) {
        if (this.debugMode) {
          console.debug(`[${this.name}]`, ...args);
        }
      }
      logError(...args) {
        console.error(`[${this.name}]`, ...args);
      }
      getNewMessageId() {
        return `m_${this.name}_${this.incrementor++}`;
      }
    };
    switchboard_default = new Switchboard();
  }
});

// node_modules/@superset-ui/switchboard/esm/index.js
var esm_exports = {};
__export(esm_exports, {
  Switchboard: () => Switchboard,
  default: () => esm_default
});
var esm_default;
var init_esm = __esm({
  "node_modules/@superset-ui/switchboard/esm/index.js"() {
    init_switchboard();
    init_switchboard();
    esm_default = switchboard_default;
  }
});

// node_modules/jwt-decode/build/cjs/index.js
var require_cjs = __commonJS({
  "node_modules/jwt-decode/build/cjs/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.jwtDecode = exports.InvalidTokenError = void 0;
    var InvalidTokenError = class extends Error {
    };
    exports.InvalidTokenError = InvalidTokenError;
    InvalidTokenError.prototype.name = "InvalidTokenError";
    function b64DecodeUnicode(str) {
      return decodeURIComponent(atob(str).replace(/(.)/g, (m, p) => {
        let code = p.charCodeAt(0).toString(16).toUpperCase();
        if (code.length < 2) {
          code = "0" + code;
        }
        return "%" + code;
      }));
    }
    function base64UrlDecode(str) {
      let output = str.replace(/-/g, "+").replace(/_/g, "/");
      switch (output.length % 4) {
        case 0:
          break;
        case 2:
          output += "==";
          break;
        case 3:
          output += "=";
          break;
        default:
          throw new Error("base64 string is not of the correct length");
      }
      try {
        return b64DecodeUnicode(output);
      } catch (err) {
        return atob(output);
      }
    }
    function jwtDecode(token, options) {
      if (typeof token !== "string") {
        throw new InvalidTokenError("Invalid token specified: must be a string");
      }
      options || (options = {});
      const pos = options.header === true ? 0 : 1;
      const part = token.split(".")[pos];
      if (typeof part !== "string") {
        throw new InvalidTokenError(`Invalid token specified: missing part #${pos + 1}`);
      }
      let decoded;
      try {
        decoded = base64UrlDecode(part);
      } catch (e) {
        throw new InvalidTokenError(`Invalid token specified: invalid base64 for part #${pos + 1} (${e.message})`);
      }
      try {
        return JSON.parse(decoded);
      } catch (e) {
        throw new InvalidTokenError(`Invalid token specified: invalid json for part #${pos + 1} (${e.message})`);
      }
    }
    exports.jwtDecode = jwtDecode;
  }
});

// node_modules/@superset-ui/embedded-sdk/lib/guestTokenRefresh.js
var require_guestTokenRefresh = __commonJS({
  "node_modules/@superset-ui/embedded-sdk/lib/guestTokenRefresh.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.REFRESH_TIMING_BUFFER_MS = exports.MIN_REFRESH_WAIT_MS = exports.DEFAULT_TOKEN_EXP_MS = void 0;
    exports.getGuestTokenRefreshTiming = getGuestTokenRefreshTiming;
    var _jwtDecode = require_cjs();
    var REFRESH_TIMING_BUFFER_MS = exports.REFRESH_TIMING_BUFFER_MS = 5e3;
    var MIN_REFRESH_WAIT_MS = exports.MIN_REFRESH_WAIT_MS = 1e4;
    var DEFAULT_TOKEN_EXP_MS = exports.DEFAULT_TOKEN_EXP_MS = 3e5;
    function getGuestTokenRefreshTiming(currentGuestToken) {
      const parsedJwt = (0, _jwtDecode.jwtDecode)(currentGuestToken);
      const exp = new Date(/[^0-9\.]/g.test(parsedJwt.exp) ? parsedJwt.exp : parseFloat(parsedJwt.exp) * 1e3);
      const isValidDate = exp.toString() !== "Invalid Date";
      const ttl = isValidDate ? Math.max(MIN_REFRESH_WAIT_MS, exp.getTime() - Date.now()) : DEFAULT_TOKEN_EXP_MS;
      return ttl - REFRESH_TIMING_BUFFER_MS;
    }
  }
});

// node_modules/@superset-ui/embedded-sdk/lib/index.js
var require_lib = __commonJS({
  "node_modules/@superset-ui/embedded-sdk/lib/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.embedDashboard = embedDashboard2;
    var _const = require_const();
    var _switchboard = (init_esm(), __toCommonJS(esm_exports));
    var _guestTokenRefresh = require_guestTokenRefresh();
    async function embedDashboard2({
      id,
      supersetDomain,
      mountPoint,
      fetchGuestToken: fetchGuestToken2,
      dashboardUiConfig,
      debug = false,
      iframeTitle = "Embedded Dashboard",
      iframeSandboxExtras = []
    }) {
      function log(...info) {
        if (debug) {
          console.debug(`[superset-embedded-sdk][dashboard ${id}]`, ...info);
        }
      }
      log("embedding");
      if (supersetDomain.endsWith("/")) {
        supersetDomain = supersetDomain.slice(0, -1);
      }
      function calculateConfig() {
        let configNumber = 0;
        if (dashboardUiConfig) {
          if (dashboardUiConfig.hideTitle) {
            configNumber += 1;
          }
          if (dashboardUiConfig.hideTab) {
            configNumber += 2;
          }
          if (dashboardUiConfig.hideChartControls) {
            configNumber += 8;
          }
        }
        return configNumber;
      }
      async function mountIframe() {
        return new Promise((resolve) => {
          const iframe = document.createElement("iframe");
          const dashboardConfigUrlParams = dashboardUiConfig ? {
            uiConfig: `${calculateConfig()}`
          } : void 0;
          const filterConfig = dashboardUiConfig?.filters || {};
          const filterConfigKeys = Object.keys(filterConfig);
          const filterConfigUrlParams = Object.fromEntries(filterConfigKeys.map((key) => [_const.DASHBOARD_UI_FILTER_CONFIG_URL_PARAM_KEY[key], filterConfig[key]]));
          const urlParams = {
            ...dashboardConfigUrlParams,
            ...filterConfigUrlParams,
            ...dashboardUiConfig?.urlParams
          };
          const urlParamsString = Object.keys(urlParams).length ? "?" + new URLSearchParams(urlParams).toString() : "";
          iframe.sandbox.add("allow-same-origin");
          iframe.sandbox.add("allow-scripts");
          iframe.sandbox.add("allow-presentation");
          iframe.sandbox.add("allow-downloads");
          iframe.sandbox.add("allow-forms");
          iframe.sandbox.add("allow-popups");
          iframeSandboxExtras.forEach((key) => {
            iframe.sandbox.add(key);
          });
          iframe.addEventListener("load", () => {
            const commsChannel = new MessageChannel();
            const ourPort2 = commsChannel.port1;
            const theirPort = commsChannel.port2;
            iframe.contentWindow.postMessage({
              type: _const.IFRAME_COMMS_MESSAGE_TYPE,
              handshake: "port transfer"
            }, supersetDomain, [theirPort]);
            log("sent message channel to the iframe");
            resolve(new _switchboard.Switchboard({
              port: ourPort2,
              name: "superset-embedded-sdk",
              debug
            }));
          });
          iframe.src = `${supersetDomain}/embedded/${id}${urlParamsString}`;
          iframe.title = iframeTitle;
          mountPoint.replaceChildren(iframe);
          log("placed the iframe");
        });
      }
      const [guestToken, ourPort] = await Promise.all([fetchGuestToken2(), mountIframe()]);
      ourPort.emit("guestToken", {
        guestToken
      });
      log("sent guest token");
      async function refreshGuestToken() {
        const newGuestToken = await fetchGuestToken2();
        ourPort.emit("guestToken", {
          guestToken: newGuestToken
        });
        setTimeout(refreshGuestToken, (0, _guestTokenRefresh.getGuestTokenRefreshTiming)(newGuestToken));
      }
      setTimeout(refreshGuestToken, (0, _guestTokenRefresh.getGuestTokenRefreshTiming)(guestToken));
      function unmount() {
        log("unmounting");
        mountPoint.replaceChildren();
      }
      const getScrollSize = () => ourPort.get("getScrollSize");
      const getDashboardPermalink = (anchor) => ourPort.get("getDashboardPermalink", {
        anchor
      });
      const getActiveTabs = () => ourPort.get("getActiveTabs");
      return {
        getScrollSize,
        unmount,
        getDashboardPermalink,
        getActiveTabs
      };
    }
  }
});

// empty-module:~/utils/superset.server
var require_superset = __commonJS({
  "empty-module:~/utils/superset.server"(exports, module) {
    module.exports = {};
  }
});

// app/routes/dashboard.$ignoredpath.$id.tsx
var import_react2 = __toESM(require_react(), 1);
var import_embedded_sdk = __toESM(require_lib(), 1);
var import_superset = __toESM(require_superset(), 1);
var import_dashboard = __toESM(require_dashboard(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/dashboard.$ignoredpath.$id.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
var _s2 = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/dashboard.$ignoredpath.$id.tsx"
  );
}
var {
  embedDashboard
} = import_embedded_sdk.default;
var links = () => [{
  rel: "stylesheet",
  href: dashboard_default
}];
var meta = () => {
  return [{
    title: "GVA Dashboards"
  }, {
    name: "description",
    content: "Gun Violence Archive - Dashboards"
  }];
};
function DisplayDashboard() {
  _s();
  const {
    token,
    dashboardId,
    dashboardName,
    dashboards
  } = useLoaderData();
  (0, import_react2.useEffect)(() => {
    const embed = async () => {
      embedDashboard({
        id: dashboardId,
        // given by the Superset embedding UI
        supersetDomain: "https://gva-ss.sdev.site",
        mountPoint: document.getElementById("my-superset-container"),
        fetchGuestToken: async () => token,
        dashboardUiConfig: {
          // dashboard UI config: hideTitle, hideTab, hideChartControls, filters.visible, filters.expanded (optional)
          hideTitle: true,
          hideTab: true,
          hideChartControls: false,
          filters: {
            expanded: false,
            visible: true
          }
        }
      });
    };
    if (document.getElementById("my-superset-container") && token !== "") {
      embed();
    }
  }, [dashboardId]);
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(import_jsx_dev_runtime.Fragment, { children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Title, { title: dashboardName }, void 0, false, {
      fileName: "app/routes/dashboard.$ignoredpath.$id.tsx",
      lineNumber: 109,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex grow overflow-auto", children: [
      dashboards.length > 1 ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "h-full w-64 overflow-auto bg-orange-500 p-4 text-white", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("ul", { children: dashboards.map((dashboard) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(NavLink, { className: ({
        isActive
      }) => `block border-b p-4 text-xl ${isActive ? "bg-orange-800" : ""}`, relative: "path", to: `../${dashboard.id}`, children: dashboard.name }, void 0, false, {
        fileName: "app/routes/dashboard.$ignoredpath.$id.tsx",
        lineNumber: 114,
        columnNumber: 10
      }, this) }, dashboard.dashboard_id, false, {
        fileName: "app/routes/dashboard.$ignoredpath.$id.tsx",
        lineNumber: 113,
        columnNumber: 37
      }, this)) }, void 0, false, {
        fileName: "app/routes/dashboard.$ignoredpath.$id.tsx",
        lineNumber: 112,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/dashboard.$ignoredpath.$id.tsx",
        lineNumber: 111,
        columnNumber: 30
      }, this) : null,
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grow", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "my-superset-container", className: "size-full overflow-auto" }, void 0, false, {
        fileName: "app/routes/dashboard.$ignoredpath.$id.tsx",
        lineNumber: 124,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/dashboard.$ignoredpath.$id.tsx",
        lineNumber: 123,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/dashboard.$ignoredpath.$id.tsx",
      lineNumber: 110,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/dashboard.$ignoredpath.$id.tsx",
    lineNumber: 108,
    columnNumber: 10
  }, this);
}
_s(DisplayDashboard, "U7ROpjXXazdULfIv0yBMuxp+oGA=", false, function() {
  return [useLoaderData];
});
_c = DisplayDashboard;
function ErrorBoundary() {
  _s2();
  const error = useRouteError();
  if (error instanceof Error) {
    return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
      "An unexpected error occurred: ",
      error.message
    ] }, void 0, true, {
      fileName: "app/routes/dashboard.$ignoredpath.$id.tsx",
      lineNumber: 137,
      columnNumber: 12
    }, this);
  }
  if (!isRouteErrorResponse(error)) {
    return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h1", { children: "Unknown Error" }, void 0, false, {
      fileName: "app/routes/dashboard.$ignoredpath.$id.tsx",
      lineNumber: 140,
      columnNumber: 12
    }, this);
  }
  if (error.status === 404) {
    return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: "Dashboard not found" }, void 0, false, {
      fileName: "app/routes/dashboard.$ignoredpath.$id.tsx",
      lineNumber: 143,
      columnNumber: 12
    }, this);
  }
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
    "An unexpected error occurred: ",
    error.statusText
  ] }, void 0, true, {
    fileName: "app/routes/dashboard.$ignoredpath.$id.tsx",
    lineNumber: 145,
    columnNumber: 10
  }, this);
}
_s2(ErrorBoundary, "oAgjgbJzsRXlB89+MoVumxMQqKM=", false, function() {
  return [useRouteError];
});
_c2 = ErrorBoundary;
var _c;
var _c2;
$RefreshReg$(_c, "DisplayDashboard");
$RefreshReg$(_c2, "ErrorBoundary");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  ErrorBoundary,
  DisplayDashboard as default,
  links,
  meta
};
//# sourceMappingURL=/build/routes/dashboard.$ignoredpath.$id-WHJETBFZ.js.map
