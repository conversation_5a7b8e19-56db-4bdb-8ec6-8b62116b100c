import {
  <PERSON><PERSON>n<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  move
} from "/build/_shared/chunk-DOCJ2TWD.js";
import "/build/_shared/chunk-5NT3OUFA.js";
import {
  Icon_Admin_Columns_default,
  Icon_Admin_Export_default
} from "/build/_shared/chunk-35JPSIWF.js";
import {
  Icon_Admin_Incidents_default
} from "/build/_shared/chunk-AN3YALPB.js";
import {
  ColumnRegistry,
  require_SearchBuilder
} from "/build/_shared/chunk-E7PJAPPF.js";
import {
  Pagination_default
} from "/build/_shared/chunk-QGERY6II.js";
import "/build/_shared/chunk-ULKIBZZM.js";
import "/build/_shared/chunk-OHSQI4V6.js";
import {
  require_session
} from "/build/_shared/chunk-BK5TXDDP.js";
import "/build/_shared/chunk-AUYLHJJM.js";
import {
  Icon_Admin_Edit_default
} from "/build/_shared/chunk-O4ZMMQVI.js";
import {
  Icon_Admin_Delete_default
} from "/build/_shared/chunk-RUOV274T.js";
import {
  <PERSON><PERSON>,
  Header
} from "/build/_shared/chunk-4556QKWU.js";
import "/build/_shared/chunk-W7JLG7II.js";
import "/build/_shared/chunk-OHIZ2QAR.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import {
  hasPermission,
  ucwords
} from "/build/_shared/chunk-DFTVPORL.js";
import {
  Form,
  Link,
  useLoaderData,
  useSearchParams,
  useSubmit
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  require_node
} from "/build/_shared/chunk-ZP6BZTHN.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/search.$id.result.tsx
var import_react6 = __toESM(require_react(), 1);
var import_node = __toESM(require_node(), 1);
var import_SearchBuilder = __toESM(require_SearchBuilder(), 1);
var import_session = __toESM(require_session(), 1);
var import_auth = __toESM(require_auth(), 1);

// app/components/Pagination.tsx
var import_react = __toESM(require_react(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/components/Pagination.tsx"
  );
  import.meta.hot.lastModified = "1748972656805.2856";
}
var Pagination_default2 = ({ total, current, defaultPageSize, onPageChange }) => {
  const [searchParams] = useSearchParams();
  const [isMounted, setIsMounted] = (0, import_react.useState)(false);
  (0, import_react.useEffect)(() => {
    setIsMounted(true);
  }, []);
  if (!isMounted)
    return null;
  if (onPageChange) {
    return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(
      Pagination_default,
      {
        className: "pagination",
        total,
        current,
        defaultPageSize,
        showTitle: false,
        itemRender: (current2, type, element) => {
          if (type === "prev") {
            return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "button", onClick: () => onPageChange(current2), children: "< PREV" }, void 0, false, {
              fileName: "app/components/Pagination.tsx",
              lineNumber: 37,
              columnNumber: 8
            }, this);
          }
          if (type === "next") {
            return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "button", onClick: () => onPageChange(current2), children: "NEXT >" }, void 0, false, {
              fileName: "app/components/Pagination.tsx",
              lineNumber: 44,
              columnNumber: 8
            }, this);
          }
          if (type === "page") {
            return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "button", onClick: () => onPageChange(current2), children: type === "page" && current2 }, void 0, false, {
              fileName: "app/components/Pagination.tsx",
              lineNumber: 51,
              columnNumber: 8
            }, this);
          }
          if (type === "jump-prev" || type === "jump-next") {
            return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "button", onClick: () => onPageChange(current2), children: "..." }, void 0, false, {
              fileName: "app/components/Pagination.tsx",
              lineNumber: 58,
              columnNumber: 8
            }, this);
          }
          return element;
        },
        showSizeChanger: false
      },
      void 0,
      false,
      {
        fileName: "app/components/Pagination.tsx",
        lineNumber: 28,
        columnNumber: 4
      },
      this
    );
  }
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(
    Pagination_default,
    {
      className: "pagination",
      total,
      defaultPageSize,
      showTitle: false,
      itemRender: (current2, type, element) => {
        if (type === "prev") {
          return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(
            Link,
            {
              to: { search: setSearchParamsString(searchParams, { page: current2 }) },
              preventScrollReset: true,
              children: "< PREV"
            },
            void 0,
            false,
            {
              fileName: "app/components/Pagination.tsx",
              lineNumber: 78,
              columnNumber: 7
            },
            this
          );
        }
        if (type === "next") {
          return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(
            Link,
            {
              to: { search: setSearchParamsString(searchParams, { page: current2 }) },
              preventScrollReset: true,
              children: "NEXT >"
            },
            void 0,
            false,
            {
              fileName: "app/components/Pagination.tsx",
              lineNumber: 88,
              columnNumber: 7
            },
            this
          );
        }
        if (type === "page") {
          return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(
            Link,
            {
              to: { search: setSearchParamsString(searchParams, { page: current2 }) },
              preventScrollReset: true,
              children: type === "page" && current2
            },
            void 0,
            false,
            {
              fileName: "app/components/Pagination.tsx",
              lineNumber: 98,
              columnNumber: 7
            },
            this
          );
        }
        if (type === "jump-prev" || type === "jump-next") {
          return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(
            Link,
            {
              to: { search: setSearchParamsString(searchParams, { page: current2 }) },
              preventScrollReset: true,
              children: "..."
            },
            void 0,
            false,
            {
              fileName: "app/components/Pagination.tsx",
              lineNumber: 108,
              columnNumber: 7
            },
            this
          );
        }
        return element;
      },
      showSizeChanger: false
    },
    void 0,
    false,
    {
      fileName: "app/components/Pagination.tsx",
      lineNumber: 70,
      columnNumber: 3
    },
    this
  );
};
var setSearchParamsString = (searchParams, changes) => {
  const newSearchParams = new URLSearchParams(searchParams);
  for (const [key, value] of Object.entries(changes)) {
    if (value === void 0) {
      newSearchParams.delete(key);
      continue;
    }
    newSearchParams.set(key, String(value));
  }
  return Array.from(newSearchParams.entries()).map(([key, value]) => value ? `${key}=${encodeURIComponent(value)}` : key).join("&");
};

// app/components/search/EditLabelForm.tsx
var import_react3 = __toESM(require_react(), 1);
var import_jsx_dev_runtime2 = __toESM(require_jsx_dev_runtime(), 1);
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/components/search/EditLabelForm.tsx"
  );
  import.meta.hot.lastModified = "1750085655615.364";
}
var EditLabelForm_default = ({ data, onSubmit }) => {
  const [formData, setFormData] = (0, import_react3.useState)({ name: data.name || "", locked: data.locked || false, editlock: data.editlock || false, limitResults: data.limitResults || false, path: data.path || "" });
  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };
  return /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("form", { onSubmit: handleSubmit, children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "rounded-[10px] bg-gray-200 p-10 space-y-5", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("label", { htmlFor: "Label", className: "text-lg font-bold", children: "Label" }, void 0, false, {
          fileName: "app/components/search/EditLabelForm.tsx",
          lineNumber: 31,
          columnNumber: 6
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "mt-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)(
          "input",
          {
            id: "Label",
            type: "text",
            name: "name",
            value: formData.name,
            className: "w-full",
            onChange: (e) => setFormData({ ...formData, name: e.target.value })
          },
          void 0,
          false,
          {
            fileName: "app/components/search/EditLabelForm.tsx",
            lineNumber: 35,
            columnNumber: 7
          },
          this
        ) }, void 0, false, {
          fileName: "app/components/search/EditLabelForm.tsx",
          lineNumber: 34,
          columnNumber: 6
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "mt-1 text-sm text-gray-600", children: "This will be displayed on the results page for this query." }, void 0, false, {
          fileName: "app/components/search/EditLabelForm.tsx",
          lineNumber: 44,
          columnNumber: 6
        }, this)
      ] }, void 0, true, {
        fileName: "app/components/search/EditLabelForm.tsx",
        lineNumber: 30,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "flex items-start", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)(
          "input",
          {
            id: "Locked",
            name: "locked",
            type: "checkbox",
            value: 1,
            checked: formData.locked,
            className: "mr-4 mt-1 size-[20px] accent-orange-500",
            onChange: (e) => setFormData({ ...formData, locked: e.target.checked ? 1 : 0 })
          },
          void 0,
          false,
          {
            fileName: "app/components/search/EditLabelForm.tsx",
            lineNumber: 48,
            columnNumber: 7
          },
          this
        ),
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("label", { htmlFor: "Locked", className: "text-lg font-bold", children: [
          "Locked",
          /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "text-sm text-gray-600 font-normal", children: "If a query is locked, it will never be deleted." }, void 0, false, {
            fileName: "app/components/search/EditLabelForm.tsx",
            lineNumber: 59,
            columnNumber: 8
          }, this)
        ] }, void 0, true, {
          fileName: "app/components/search/EditLabelForm.tsx",
          lineNumber: 57,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/components/search/EditLabelForm.tsx",
        lineNumber: 47,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/components/search/EditLabelForm.tsx",
        lineNumber: 46,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "flex items-start", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)(
          "input",
          {
            id: "Uneditable",
            name: "editlock",
            type: "checkbox",
            value: 1,
            checked: formData.editlock,
            onChange: (e) => setFormData({ ...formData, editlock: e.target.checked ? 1 : 0 }),
            className: "mr-4 mt-1 size-[20px] accent-orange-500"
          },
          void 0,
          false,
          {
            fileName: "app/components/search/EditLabelForm.tsx",
            lineNumber: 65,
            columnNumber: 7
          },
          this
        ),
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("label", { htmlFor: "Uneditable", className: "text-lg font-bold", children: [
          "Uneditable",
          /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "text-sm text-gray-600 font-normal", children: "If a query is edit locked, its search parameters wont be able to be edited." }, void 0, false, {
            fileName: "app/components/search/EditLabelForm.tsx",
            lineNumber: 76,
            columnNumber: 8
          }, this)
        ] }, void 0, true, {
          fileName: "app/components/search/EditLabelForm.tsx",
          lineNumber: 74,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/components/search/EditLabelForm.tsx",
        lineNumber: 64,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/components/search/EditLabelForm.tsx",
        lineNumber: 63,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "flex items-start", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)(
          "input",
          {
            id: "LimitResults",
            name: "limitResults",
            type: "checkbox",
            value: 1,
            checked: formData.limitResults,
            onChange: (e) => setFormData({ ...formData, limitResults: e.target.checked ? 1 : 0 }),
            className: "mr-4 mt-1 size-[20px] accent-orange-500"
          },
          void 0,
          false,
          {
            fileName: "app/components/search/EditLabelForm.tsx",
            lineNumber: 82,
            columnNumber: 7
          },
          this
        ),
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("label", { htmlFor: "LimitResults", className: "text-lg font-bold", children: [
          "Limit Results",
          /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "text-sm text-gray-600 font-normal", children: "Whether or not to limit the results of this query for anonymous users." }, void 0, false, {
            fileName: "app/components/search/EditLabelForm.tsx",
            lineNumber: 93,
            columnNumber: 8
          }, this)
        ] }, void 0, true, {
          fileName: "app/components/search/EditLabelForm.tsx",
          lineNumber: 91,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/components/search/EditLabelForm.tsx",
        lineNumber: 81,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/components/search/EditLabelForm.tsx",
        lineNumber: 80,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("label", { htmlFor: "Path", className: "text-lg font-bold", children: "Path" }, void 0, false, {
          fileName: "app/components/search/EditLabelForm.tsx",
          lineNumber: 98,
          columnNumber: 6
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "mt-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)(
          "input",
          {
            id: "Path",
            name: "path",
            type: "text",
            value: formData.path,
            className: "w-full",
            onChange: (e) => setFormData({ ...formData, path: e.target.value })
          },
          void 0,
          false,
          {
            fileName: "app/components/search/EditLabelForm.tsx",
            lineNumber: 102,
            columnNumber: 7
          },
          this
        ) }, void 0, false, {
          fileName: "app/components/search/EditLabelForm.tsx",
          lineNumber: 101,
          columnNumber: 6
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "mt-1 text-gray-600", children: "The path (without the leading /) to the query." }, void 0, false, {
          fileName: "app/components/search/EditLabelForm.tsx",
          lineNumber: 111,
          columnNumber: 6
        }, this)
      ] }, void 0, true, {
        fileName: "app/components/search/EditLabelForm.tsx",
        lineNumber: 97,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/components/search/EditLabelForm.tsx",
      lineNumber: 29,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "mt-10", children: /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("button", { type: "submit", className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white", children: "Save" }, void 0, false, {
      fileName: "app/components/search/EditLabelForm.tsx",
      lineNumber: 115,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/components/search/EditLabelForm.tsx",
      lineNumber: 114,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/components/search/EditLabelForm.tsx",
    lineNumber: 28,
    columnNumber: 3
  }, this);
};

// app/components/search/ChooseColumnForm.tsx
var import_react5 = __toESM(require_react(), 1);
var import_jsx_dev_runtime3 = __toESM(require_jsx_dev_runtime(), 1);
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/components/search/ChooseColumnForm.tsx"
  );
  import.meta.hot.lastModified = "1750085655615.2866";
}
var ChooseColumnForm_default = ({ enabled, availableColumns, onSubmit }) => {
  const [columnSelector, setColumnSelector] = (0, import_react5.useState)({
    enabled,
    available: availableColumns.filter((s) => !enabled.includes(s.key)).map((s) => s.key)
  });
  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(columnSelector.enabled);
  };
  return /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("form", { onSubmit: handleSubmit, children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: "space-y-5 rounded-[10px] bg-gray-200 p-10", children: /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: "column-selector", children: /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)(
      DragDropProvider,
      {
        onDragOver: (event) => {
          setColumnSelector((columnSelector2) => {
            return move(columnSelector2, event);
          });
        },
        children: /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: "flex gap-5", children: Object.entries(columnSelector).map(([group, items]) => /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)(ColumnContainer, { id: group, title: ucwords(group), children: items.map((id, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)(
          ColumnItem,
          {
            id,
            text: availableColumns.find((o) => o.key == id)?.label || id,
            index,
            group
          },
          id,
          false,
          {
            fileName: "app/components/search/ChooseColumnForm.tsx",
            lineNumber: 51,
            columnNumber: 11
          },
          this
        )) }, group, false, {
          fileName: "app/components/search/ChooseColumnForm.tsx",
          lineNumber: 49,
          columnNumber: 9
        }, this)) }, void 0, false, {
          fileName: "app/components/search/ChooseColumnForm.tsx",
          lineNumber: 47,
          columnNumber: 7
        }, this)
      },
      void 0,
      false,
      {
        fileName: "app/components/search/ChooseColumnForm.tsx",
        lineNumber: 40,
        columnNumber: 6
      },
      this
    ) }, void 0, false, {
      fileName: "app/components/search/ChooseColumnForm.tsx",
      lineNumber: 39,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/components/search/ChooseColumnForm.tsx",
      lineNumber: 38,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: "mt-10", children: /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("button", { type: "submit", className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white", children: "Save" }, void 0, false, {
      fileName: "app/components/search/ChooseColumnForm.tsx",
      lineNumber: 66,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/components/search/ChooseColumnForm.tsx",
      lineNumber: 65,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/components/search/ChooseColumnForm.tsx",
    lineNumber: 37,
    columnNumber: 3
  }, this);
};

// app/routes/search.$id.result.tsx
var import_jsx_dev_runtime4 = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/search.$id.result.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/search.$id.result.tsx"
  );
  import.meta.hot.lastModified = "1750085655617.6033";
}
var PAGE_SIZE = 20;
var getValidId = (id) => {
  if (typeof id === "string") {
    return parseInt(id, 10);
  }
  if (typeof id === "number") {
    return id;
  }
  throw new Error("Invalid ID");
};
function SearchResults() {
  _s();
  const {
    search,
    results,
    totalCount
  } = useLoaderData();
  const submit = useSubmit();
  const availableColumns = ColumnRegistry.getInstance().getAll().map((column) => ({
    key: column.key,
    label: column.label,
    sortable: column.sortable
  }));
  const [tab, setTab] = (0, import_react6.useState)("result");
  const [page, setPage] = (0, import_react6.useState)(1);
  const [pageSize, setPageSize] = (0, import_react6.useState)(PAGE_SIZE);
  const changeSort = (sortColumn) => {
    let sortDirection = "ASC";
    if (search.sortColumn === sortColumn) {
      sortDirection = search.sortDirection === "ASC" ? "DESC" : "ASC";
    }
    window.location.href = `/search/${search.id}/result?sort=${sortColumn}:${sortDirection}`;
  };
  const submitLabelForm = (data) => {
    submit({
      ...data,
      action: "editLabel"
    }, {
      method: "post"
    });
  };
  const submitColumnForm = (enabled) => {
    submit({
      enabled: enabled.join(","),
      action: "chooseColumns"
    }, {
      method: "post"
    });
  };
  const isIncidents = search.entityType === "incidents";
  return /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { className: "min-h-screen", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)(Header, {}, void 0, false, {
      fileName: "app/routes/search.$id.result.tsx",
      lineNumber: 180,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("main", { className: "py-5 md:py-10", children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { className: "container mx-auto", children: [
      search.name && /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { className: "text-center text-xl font-bold uppercase md:text-2xl mb-5", children: search.name }, void 0, false, {
        fileName: "app/routes/search.$id.result.tsx",
        lineNumber: 201,
        columnNumber: 22
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { className: "bg-gray-200 px-10 py-4", children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("ul", { className: "flex space-x-10 text-sm", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("button", { className: "flex items-center hover:text-orange-600", onClick: () => setTab("result"), children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Incidents_default, alt: "", width: 16 }, void 0, false, {
            fileName: "app/routes/search.$id.result.tsx",
            lineNumber: 206,
            columnNumber: 10
          }, this),
          " Incidents"
        ] }, void 0, true, {
          fileName: "app/routes/search.$id.result.tsx",
          lineNumber: 205,
          columnNumber: 9
        }, this) }, void 0, false, {
          fileName: "app/routes/search.$id.result.tsx",
          lineNumber: 204,
          columnNumber: 8
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("button", { className: "flex items-center hover:text-orange-600", onClick: () => setTab("column"), children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Columns_default, alt: "", width: 16 }, void 0, false, {
            fileName: "app/routes/search.$id.result.tsx",
            lineNumber: 211,
            columnNumber: 10
          }, this),
          " Choose Columns"
        ] }, void 0, true, {
          fileName: "app/routes/search.$id.result.tsx",
          lineNumber: 210,
          columnNumber: 9
        }, this) }, void 0, false, {
          fileName: "app/routes/search.$id.result.tsx",
          lineNumber: 209,
          columnNumber: 8
        }, this),
        hasPermission("Admin") && /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)(import_jsx_dev_runtime4.Fragment, { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)(Form, { method: "post", onSubmit: (e) => {
            if (!confirm("Please confirm you want to delete this record.")) {
              e.preventDefault();
            }
          }, children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("input", { type: "hidden", name: "action", value: "delete" }, void 0, false, {
              fileName: "app/routes/search.$id.result.tsx",
              lineNumber: 221,
              columnNumber: 12
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("button", { type: "submit", className: "flex items-center hover:text-orange-600", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Delete_default, alt: "", width: 16 }, void 0, false, {
                fileName: "app/routes/search.$id.result.tsx",
                lineNumber: 223,
                columnNumber: 13
              }, this),
              " Delete"
            ] }, void 0, true, {
              fileName: "app/routes/search.$id.result.tsx",
              lineNumber: 222,
              columnNumber: 12
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/search.$id.result.tsx",
            lineNumber: 216,
            columnNumber: 11
          }, this) }, void 0, false, {
            fileName: "app/routes/search.$id.result.tsx",
            lineNumber: 215,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("button", { className: "flex items-center hover:text-orange-600", onClick: () => setTab("label"), children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Edit_default, alt: "", width: 16 }, void 0, false, {
              fileName: "app/routes/search.$id.result.tsx",
              lineNumber: 229,
              columnNumber: 12
            }, this),
            " Edit Label"
          ] }, void 0, true, {
            fileName: "app/routes/search.$id.result.tsx",
            lineNumber: 228,
            columnNumber: 11
          }, this) }, void 0, false, {
            fileName: "app/routes/search.$id.result.tsx",
            lineNumber: 227,
            columnNumber: 10
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/search.$id.result.tsx",
          lineNumber: 214,
          columnNumber: 35
        }, this),
        (!search.locked || hasPermission("Admin")) && /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)(Link, { className: "flex items-center hover:text-orange-600", to: search.formType == "advanced" ? `/search/advanced/${search.id}` : `/search/${search.id}`, children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Edit_default, alt: "", width: 16 }, void 0, false, {
            fileName: "app/routes/search.$id.result.tsx",
            lineNumber: 235,
            columnNumber: 11
          }, this),
          " Edit Search"
        ] }, void 0, true, {
          fileName: "app/routes/search.$id.result.tsx",
          lineNumber: 234,
          columnNumber: 10
        }, this) }, void 0, false, {
          fileName: "app/routes/search.$id.result.tsx",
          lineNumber: 233,
          columnNumber: 55
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("a", { className: "flex items-center hover:text-orange-600", href: `export`, children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Export_default, alt: "", width: 16 }, void 0, false, {
            fileName: "app/routes/search.$id.result.tsx",
            lineNumber: 240,
            columnNumber: 10
          }, this),
          " Export as CSV"
        ] }, void 0, true, {
          fileName: "app/routes/search.$id.result.tsx",
          lineNumber: 239,
          columnNumber: 9
        }, this) }, void 0, false, {
          fileName: "app/routes/search.$id.result.tsx",
          lineNumber: 238,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/search.$id.result.tsx",
        lineNumber: 203,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/search.$id.result.tsx",
        lineNumber: 202,
        columnNumber: 6
      }, this),
      tab === "result" && /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { className: "mt-5 overflow-x-auto", children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("table", { className: "table-gray w-full table-auto text-sm", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("thead", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("tr", { children: [
            search.columns.map((key) => availableColumns.find((s) => s.key == key)?.sortable ? /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("th", { className: "uppercase cursor-pointer", align: "left", onClick: (e) => changeSort(key), children: [
              key.replace(/_/g, " "),
              " ",
              search.sortColumn === key ? search.sortDirection === "ASC" ? "\u25B2" : "\u25BC" : ""
            ] }, key, true, {
              fileName: "app/routes/search.$id.result.tsx",
              lineNumber: 252,
              columnNumber: 92
            }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("th", { className: "uppercase", align: "left", children: key.replace(/_/g, " ") }, key, false, {
              fileName: "app/routes/search.$id.result.tsx",
              lineNumber: 254,
              columnNumber: 21
            }, this)),
            /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("th", { className: "uppercase", align: "left", children: "Actions" }, void 0, false, {
              fileName: "app/routes/search.$id.result.tsx",
              lineNumber: 257,
              columnNumber: 12
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/search.$id.result.tsx",
            lineNumber: 251,
            columnNumber: 11
          }, this) }, void 0, false, {
            fileName: "app/routes/search.$id.result.tsx",
            lineNumber: 250,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("tbody", { children: [
            results.map((result) => {
              try {
                const itemId = isIncidents ? getValidId(result.incident_id) : getValidId(result.participant_id);
                return /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("tr", { className: "hover:bg-gray-50", children: [
                  search.columns.map((col) => {
                    const value = result[col];
                    return /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("td", { children: typeof value === "object" && value !== null && value.__html ? /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { dangerouslySetInnerHTML: {
                      __html: value.__html
                    } }, void 0, false, {
                      fileName: "app/routes/search.$id.result.tsx",
                      lineNumber: 269,
                      columnNumber: 81
                    }, this) : typeof value === "object" && value !== null ? /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("pre", { children: JSON.stringify(value) }, void 0, false, {
                      fileName: "app/routes/search.$id.result.tsx",
                      lineNumber: 271,
                      columnNumber: 81
                    }, this) : value?.toString() || "" }, `row-${itemId}-${col}`, false, {
                      fileName: "app/routes/search.$id.result.tsx",
                      lineNumber: 268,
                      columnNumber: 32
                    }, this);
                  }),
                  /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("td", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)(Link, { to: isIncidents ? `/incident/${itemId}` : `/participant/${itemId}`, className: "font-medium text-blue-500 hover:text-orange-600", children: "View Details" }, void 0, false, {
                    fileName: "app/routes/search.$id.result.tsx",
                    lineNumber: 275,
                    columnNumber: 16
                  }, this) }, void 0, false, {
                    fileName: "app/routes/search.$id.result.tsx",
                    lineNumber: 274,
                    columnNumber: 15
                  }, this)
                ] }, `row-${itemId}`, true, {
                  fileName: "app/routes/search.$id.result.tsx",
                  lineNumber: 265,
                  columnNumber: 28
                }, this);
              } catch (error) {
                return null;
              }
            }),
            totalCount === 0 && /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("tr", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("td", { colSpan: Object.keys(results[0]).length + 1, children: "No incidents found matching your search criteria." }, void 0, false, {
              fileName: "app/routes/search.$id.result.tsx",
              lineNumber: 287,
              columnNumber: 13
            }, this) }, void 0, false, {
              fileName: "app/routes/search.$id.result.tsx",
              lineNumber: 286,
              columnNumber: 32
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/search.$id.result.tsx",
            lineNumber: 260,
            columnNumber: 10
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/search.$id.result.tsx",
          lineNumber: 249,
          columnNumber: 9
        }, this) }, void 0, false, {
          fileName: "app/routes/search.$id.result.tsx",
          lineNumber: 248,
          columnNumber: 8
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { className: "mt-10 flex justify-center text-sm", children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)(
          Pagination_default2,
          {
            total: totalCount,
            current: page,
            defaultPageSize: pageSize
          },
          void 0,
          false,
          {
            fileName: "app/routes/search.$id.result.tsx",
            lineNumber: 297,
            columnNumber: 9
          },
          this
        ) }, void 0, false, {
          fileName: "app/routes/search.$id.result.tsx",
          lineNumber: 296,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/search.$id.result.tsx",
        lineNumber: 247,
        columnNumber: 27
      }, this),
      tab === "column" && /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { className: "mt-5", children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)(ChooseColumnForm_default, { enabled: search.columns, availableColumns, onSubmit: submitColumnForm }, void 0, false, {
        fileName: "app/routes/search.$id.result.tsx",
        lineNumber: 305,
        columnNumber: 8
      }, this) }, void 0, false, {
        fileName: "app/routes/search.$id.result.tsx",
        lineNumber: 304,
        columnNumber: 27
      }, this),
      tab === "label" && /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { className: "mt-5", children: /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)(EditLabelForm_default, { data: search, onSubmit: submitLabelForm }, void 0, false, {
        fileName: "app/routes/search.$id.result.tsx",
        lineNumber: 310,
        columnNumber: 8
      }, this) }, void 0, false, {
        fileName: "app/routes/search.$id.result.tsx",
        lineNumber: 309,
        columnNumber: 26
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/search.$id.result.tsx",
      lineNumber: 182,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/search.$id.result.tsx",
      lineNumber: 181,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)(Footer, {}, void 0, false, {
      fileName: "app/routes/search.$id.result.tsx",
      lineNumber: 314,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/search.$id.result.tsx",
    lineNumber: 179,
    columnNumber: 10
  }, this);
}
_s(SearchResults, "ytZPLZgdyhFGC6f2SJJGdujhjRk=", false, function() {
  return [useLoaderData, useSubmit];
});
_c = SearchResults;
var _c;
$RefreshReg$(_c, "SearchResults");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  SearchResults as default
};
//# sourceMappingURL=/build/routes/search.$id.result-AC7OM7MZ.js.map
