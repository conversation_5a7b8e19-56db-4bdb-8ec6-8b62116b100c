import {
  require_cookies
} from "/build/_shared/chunk-MUAEGCBW.js";
import {
  Link,
  useLoaderData
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/admin.dashboard._index.tsx
var import_react2 = __toESM(require_react(), 1);
var import_cookies = __toESM(require_cookies(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.dashboard._index.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.dashboard._index.tsx"
  );
  import.meta.hot.lastModified = "1740763710239.5571";
}
function DashboardIndexPage() {
  _s();
  const {
    formSubmitMessage: formSubmitMessage2
  } = useLoaderData();
  const [showMessage, setShowMessage] = (0, import_react2.useState)(!!formSubmitMessage2);
  (0, import_react2.useEffect)(() => {
    if (showMessage) {
      const timeoutId = setTimeout(() => setShowMessage(false), 5e3);
      return () => clearTimeout(timeoutId);
    }
  }, [showMessage]);
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("p", { children: [
    showMessage ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("p", { className: "animate-fade bg-black text-white", children: formSubmitMessage2 }, void 0, false, {
      fileName: "app/routes/admin.dashboard._index.tsx",
      lineNumber: 47,
      columnNumber: 19
    }, this) : null,
    "Select a dashboard on the left, or",
    " ",
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "new", className: "text-orange-500 underline", children: "create a new dashboard." }, void 0, false, {
      fileName: "app/routes/admin.dashboard._index.tsx",
      lineNumber: 49,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.dashboard._index.tsx",
    lineNumber: 46,
    columnNumber: 10
  }, this);
}
_s(DashboardIndexPage, "wnuESngyUfr25U+x/aT3pcLPJ1U=", false, function() {
  return [useLoaderData];
});
_c = DashboardIndexPage;
var _c;
$RefreshReg$(_c, "DashboardIndexPage");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  DashboardIndexPage as default
};
//# sourceMappingURL=/build/routes/admin.dashboard._index-S32MVF3N.js.map
