import {
  require_build
} from "/build/_shared/chunk-E55QCST2.js";
import {
  Icon_Success_default
} from "/build/_shared/chunk-GBO3FEGG.js";
import {
  require_users
} from "/build/_shared/chunk-N5WPI4L5.js";
import {
  Title
} from "/build/_shared/chunk-YPQMQ54K.js";
import {
  Icon_Admin_Delete_default
} from "/build/_shared/chunk-RUOV274T.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import {
  Form,
  useActionData,
  useNavigation,
  useSearchParams
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import "/build/_shared/chunk-QT64XSGC.js";
import {
  require_node
} from "/build/_shared/chunk-ZP6BZTHN.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/user.verify.tsx
var import_node = __toESM(require_node(), 1);
var import_remix_auth = __toESM(require_build(), 1);
var import_auth = __toESM(require_auth(), 1);
var import_users = __toESM(require_users(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/user.verify.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/user.verify.tsx"
  );
  import.meta.hot.lastModified = "1748458133162.8875";
}
var meta = () => {
  return [{
    title: "Gun Violence Archive | Email Validation"
  }];
};
function UserVerify() {
  _s();
  const messages = useActionData();
  const [searchParams] = useSearchParams();
  const redirectTo = searchParams.get("redirectTo") || "/";
  let transition = useNavigation();
  let codeSubmitting = Boolean(transition.state === "submitting" && transition.formData?.get("code"));
  let resendSubmitting = Boolean(transition.state === "submitting" && transition.formData?.get("resend"));
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(import_jsx_dev_runtime.Fragment, { children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Title, { title: "Login Validation" }, void 0, false, {
      fileName: "app/routes/user.verify.tsx",
      lineNumber: 102,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-center", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] bg-gray-200 p-4 sm:w-[480px] xl:p-10", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { method: "post", children: [
        messages?.errors?.message ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-5 flex bg-white px-5 py-3.5 text-sm font-light", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Icon_Admin_Delete_default, alt: "", width: 16 }, void 0, false, {
            fileName: "app/routes/user.verify.tsx",
            lineNumber: 109,
            columnNumber: 12
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "pl-2 text-red-500", children: messages.errors.message }, void 0, false, {
            fileName: "app/routes/user.verify.tsx",
            lineNumber: 110,
            columnNumber: 12
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/user.verify.tsx",
          lineNumber: 108,
          columnNumber: 38
        }, this) : null,
        messages?.messages?.message && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-5 flex bg-white px-5 py-3.5 text-sm font-light", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Icon_Success_default, alt: "", width: 30 }, void 0, false, {
            fileName: "app/routes/user.verify.tsx",
            lineNumber: 113,
            columnNumber: 12
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "pl-2", children: messages.messages.message }, void 0, false, {
            fileName: "app/routes/user.verify.tsx",
            lineNumber: 114,
            columnNumber: 12
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/user.verify.tsx",
          lineNumber: 112,
          columnNumber: 41
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-8", children: "Please check your email for a verification code and enter it below to continue (expires in 5 minutes)." }, void 0, false, {
          fileName: "app/routes/user.verify.tsx",
          lineNumber: 116,
          columnNumber: 9
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-8", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "code", className: "block text-lg font-bold", children: "Code" }, void 0, false, {
            fileName: "app/routes/user.verify.tsx",
            lineNumber: 121,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "text", name: "code", id: "code", required: true, maxLength: 6, inputMode: "numeric", autoComplete: "one-time-code", pattern: "\\d{6}", className: "block w-full rounded-md px-3 py-2 leading-loose" }, void 0, false, {
            fileName: "app/routes/user.verify.tsx",
            lineNumber: 124,
            columnNumber: 10
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/user.verify.tsx",
          lineNumber: 120,
          columnNumber: 9
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "redirectTo", value: redirectTo }, void 0, false, {
          fileName: "app/routes/user.verify.tsx",
          lineNumber: 126,
          columnNumber: 9
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", disabled: codeSubmitting, className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white", children: codeSubmitting ? "Submitting" : "Verify" }, void 0, false, {
            fileName: "app/routes/user.verify.tsx",
            lineNumber: 128,
            columnNumber: 10
          }, this),
          codeSubmitting && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lds-ring", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
              fileName: "app/routes/user.verify.tsx",
              lineNumber: 132,
              columnNumber: 12
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
              fileName: "app/routes/user.verify.tsx",
              lineNumber: 133,
              columnNumber: 12
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
              fileName: "app/routes/user.verify.tsx",
              lineNumber: 134,
              columnNumber: 12
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
              fileName: "app/routes/user.verify.tsx",
              lineNumber: 135,
              columnNumber: 12
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/user.verify.tsx",
            lineNumber: 131,
            columnNumber: 29
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/user.verify.tsx",
          lineNumber: 127,
          columnNumber: 9
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/user.verify.tsx",
        lineNumber: 107,
        columnNumber: 8
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-4", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { method: "POST", preventScrollReset: true, children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "resend", value: "true" }, void 0, false, {
          fileName: "app/routes/user.verify.tsx",
          lineNumber: 141,
          columnNumber: 10
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", disabled: resendSubmitting, className: "text-lg text-blue-500", children: resendSubmitting ? "Sending" : "Resend" }, void 0, false, {
          fileName: "app/routes/user.verify.tsx",
          lineNumber: 142,
          columnNumber: 10
        }, this),
        resendSubmitting && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lds-ring", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
            fileName: "app/routes/user.verify.tsx",
            lineNumber: 146,
            columnNumber: 12
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
            fileName: "app/routes/user.verify.tsx",
            lineNumber: 147,
            columnNumber: 12
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
            fileName: "app/routes/user.verify.tsx",
            lineNumber: 148,
            columnNumber: 12
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
            fileName: "app/routes/user.verify.tsx",
            lineNumber: 149,
            columnNumber: 12
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/user.verify.tsx",
          lineNumber: 145,
          columnNumber: 31
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/user.verify.tsx",
        lineNumber: 140,
        columnNumber: 9
      }, this) }, void 0, false, {
        fileName: "app/routes/user.verify.tsx",
        lineNumber: 139,
        columnNumber: 8
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-4", children: "Didn't get an email? Click resend above to have it sent again. All previous codes sent will become invalid." }, void 0, false, {
        fileName: "app/routes/user.verify.tsx",
        lineNumber: 153,
        columnNumber: 8
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-4 text-lg text-blue-500", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { href: "/user/logout", children: "Back to Sign In" }, void 0, false, {
        fileName: "app/routes/user.verify.tsx",
        lineNumber: 159,
        columnNumber: 9
      }, this) }, void 0, false, {
        fileName: "app/routes/user.verify.tsx",
        lineNumber: 158,
        columnNumber: 8
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/user.verify.tsx",
      lineNumber: 106,
      columnNumber: 7
    }, this) }, void 0, false, {
      fileName: "app/routes/user.verify.tsx",
      lineNumber: 105,
      columnNumber: 6
    }, this) }, void 0, false, {
      fileName: "app/routes/user.verify.tsx",
      lineNumber: 104,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/user.verify.tsx",
      lineNumber: 103,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/user.verify.tsx",
    lineNumber: 101,
    columnNumber: 10
  }, this);
}
_s(UserVerify, "NNg9tiZCa+HMyTfO5N2O3TgKugo=", false, function() {
  return [useActionData, useSearchParams, useNavigation];
});
_c = UserVerify;
var _c;
$RefreshReg$(_c, "UserVerify");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  UserVerify as default,
  meta
};
//# sourceMappingURL=/build/routes/user.verify-VOLPQVHN.js.map
