import {
  require_dashboard
} from "/build/_shared/chunk-LR32EY3Q.js";
import {
  AdminHeader
} from "/build/_shared/chunk-W7JLG7II.js";
import "/build/_shared/chunk-OHIZ2QAR.js";
import {
  useUser
} from "/build/_shared/chunk-DFTVPORL.js";
import {
  Link,
  NavLink,
  Outlet,
  useLoaderData
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/admin.dashboard.tsx
var import_dashboard = __toESM(require_dashboard(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.dashboard.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.dashboard.tsx"
  );
  import.meta.hot.lastModified = "1746467939008.2344";
}
function AdminDashboardsPage() {
  _s();
  const user = useUser();
  if (user.role != "Admin") {
    throw new Error("No permission.");
  }
  const data = useLoaderData();
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "min-h-screen bg-white", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(AdminHeader, {}, void 0, false, {
      fileName: "app/routes/admin.dashboard.tsx",
      lineNumber: 42,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { className: "container mx-auto py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex space-x-10", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "w-[350px] flex-none", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] bg-gray-200 p-10", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "new", className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white", children: "Create Dashboard" }, void 0, false, {
          fileName: "app/routes/admin.dashboard.tsx",
          lineNumber: 48,
          columnNumber: 8
        }, this),
        data.dashboardListItems.length === 0 ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("p", { className: "mt-10", children: "No Dashboards yet" }, void 0, false, {
          fileName: "app/routes/admin.dashboard.tsx",
          lineNumber: 51,
          columnNumber: 48
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("ul", { className: "mt-10 divide-y divide-white border-y border-white text-lg", children: data.dashboardListItems.map((dashboard) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "py-2.5", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(NavLink, { className: ({
          isActive
        }) => `${isActive ? "text-orange-500" : ""}`, to: dashboard.id.toString(), children: dashboard.name }, void 0, false, {
          fileName: "app/routes/admin.dashboard.tsx",
          lineNumber: 53,
          columnNumber: 12
        }, this) }, dashboard.id, false, {
          fileName: "app/routes/admin.dashboard.tsx",
          lineNumber: 52,
          columnNumber: 52
        }, this)) }, void 0, false, {
          fileName: "app/routes/admin.dashboard.tsx",
          lineNumber: 51,
          columnNumber: 93
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.dashboard.tsx",
        lineNumber: 47,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.dashboard.tsx",
        lineNumber: 46,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Outlet, {}, void 0, false, {
        fileName: "app/routes/admin.dashboard.tsx",
        lineNumber: 64,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.dashboard.tsx",
        lineNumber: 63,
        columnNumber: 6
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.dashboard.tsx",
      lineNumber: 45,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/admin.dashboard.tsx",
      lineNumber: 44,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.dashboard.tsx",
    lineNumber: 41,
    columnNumber: 10
  }, this);
}
_s(AdminDashboardsPage, "mNG6CBjAPO0fhbVziK8fnisI310=", false, function() {
  return [useUser, useLoaderData];
});
_c = AdminDashboardsPage;
var _c;
$RefreshReg$(_c, "AdminDashboardsPage");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  AdminDashboardsPage as default
};
//# sourceMappingURL=/build/routes/admin.dashboard-CHWQ5G3D.js.map
