import {
  AddAnotherFilter_default,
  FilterComponent2_default,
  SearchProvider
} from "/build/_shared/chunk-QRPAFGRB.js";
import {
  FilterRegistry,
  require_SearchBuilder
} from "/build/_shared/chunk-E7PJAPPF.js";
import {
  Icon_Admin_Delete_default
} from "/build/_shared/chunk-RUOV274T.js";
import {
  <PERSON><PERSON>,
  <PERSON>er
} from "/build/_shared/chunk-4556QKWU.js";
import "/build/_shared/chunk-W7JLG7II.js";
import {
  Icon_Admin_Add_default
} from "/build/_shared/chunk-OHIZ2QAR.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import "/build/_shared/chunk-DFTVPORL.js";
import {
  useLoaderData,
  useSubmit
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  require_node
} from "/build/_shared/chunk-ZP6BZTHN.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/search.advanced.($id).tsx
var import_react = __toESM(require_react(), 1);
var import_node = __toESM(require_node(), 1);
var import_SearchBuilder = __toESM(require_SearchBuilder(), 1);
var import_auth = __toESM(require_auth(), 1);

// app/components/search/Operator.tsx
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/components/search/Operator.tsx"
  );
  import.meta.hot.lastModified = "1748972656807.184";
}
var Operator_default = ({ or = false }) => {
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "operator", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "andor", children: or ? "Or" : "And" }, void 0, false, {
    fileName: "app/components/search/Operator.tsx",
    lineNumber: 15,
    columnNumber: 4
  }, this) }, void 0, false, {
    fileName: "app/components/search/Operator.tsx",
    lineNumber: 14,
    columnNumber: 3
  }, this);
};

// app/components/search/FilterGroupComponent2.tsx
var import_jsx_dev_runtime2 = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/components/search/FilterGroupComponent2.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/components/search/FilterGroupComponent2.tsx"
  );
  import.meta.hot.lastModified = "1748972656806.923";
}
function FilterGroupComponent({
  group,
  onUpdate,
  onDelete,
  root
}) {
  const addFilter = () => {
    onUpdate({
      ...group,
      filters: [...group.filters, {
        id: Date.now().toString(),
        type: "filter",
        field: "",
        operator: "equals",
        value: ""
      }]
    });
  };
  const addNestedGroup = () => {
    onUpdate({
      ...group,
      filters: [...group.filters, {
        id: Date.now().toString(),
        type: "group",
        operator: "AND",
        filters: []
      }]
    });
  };
  const updateFilter = (index, updatedFilter) => {
    const newFilters = [...group.filters];
    newFilters[index] = updatedFilter;
    onUpdate({
      ...group,
      filters: newFilters
    });
  };
  const deleteFilter = (index) => {
    onUpdate({
      ...group,
      filters: group.filters.filter((_, i) => i !== index)
    });
  };
  const insertFilter = (index) => {
    const newFilters = [...group.filters];
    newFilters.splice(index, 0, {
      id: Date.now().toString(),
      type: "filter",
      field: "",
      operator: "equals",
      value: ""
    });
    onUpdate({
      ...group,
      filters: newFilters
    });
  };
  if (root && group.filters.length === 1) {
    const firstFilter = group.filters[0];
    if (firstFilter.type === "filter") {
      return /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)(
        FilterComponent2_default,
        {
          filter: firstFilter,
          onUpdate: (updated) => updateFilter(0, updated),
          onDelete,
          addFilter
        },
        void 0,
        false,
        {
          fileName: "app/components/search/FilterGroupComponent2.tsx",
          lineNumber: 85,
          columnNumber: 14
        },
        this
      );
    }
  }
  return /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "group", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "anyall flex items-start justify-between", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("select", { value: group.operator, onChange: (e) => onUpdate({
          ...group,
          operator: e.target.value
        }), children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("option", { value: "OR", children: "Any" }, void 0, false, {
            fileName: "app/components/search/FilterGroupComponent2.tsx",
            lineNumber: 96,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("option", { value: "AND", children: "All" }, void 0, false, {
            fileName: "app/components/search/FilterGroupComponent2.tsx",
            lineNumber: 97,
            columnNumber: 7
          }, this)
        ] }, void 0, true, {
          fileName: "app/components/search/FilterGroupComponent2.tsx",
          lineNumber: 92,
          columnNumber: 6
        }, this),
        "of the following are true:"
      ] }, void 0, true, {
        fileName: "app/components/search/FilterGroupComponent2.tsx",
        lineNumber: 91,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("img", { src: Icon_Admin_Delete_default, alt: "Delete", width: 16, className: "cursor-pointer", onClick: onDelete }, void 0, false, {
        fileName: "app/components/search/FilterGroupComponent2.tsx",
        lineNumber: 100,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/components/search/FilterGroupComponent2.tsx",
      lineNumber: 90,
      columnNumber: 4
    }, this),
    group.filters.map((filter, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { children: [
      filter.type === "group" ? /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)(FilterGroupComponent, { group: filter, onUpdate: (updated) => updateFilter(index, updated), onDelete: () => deleteFilter(index) }, void 0, false, {
        fileName: "app/components/search/FilterGroupComponent2.tsx",
        lineNumber: 104,
        columnNumber: 33
      }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)(FilterComponent2_default, { filter, onUpdate: (updated) => updateFilter(index, updated), onDelete: () => deleteFilter(index), addFilter: () => insertFilter(index + 1) }, void 0, false, {
        fileName: "app/components/search/FilterGroupComponent2.tsx",
        lineNumber: 104,
        columnNumber: 163
      }, this),
      index < group.filters.length - 1 && /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)(Operator_default, { or: group.operator === "OR" }, void 0, false, {
        fileName: "app/components/search/FilterGroupComponent2.tsx",
        lineNumber: 105,
        columnNumber: 43
      }, this)
    ] }, filter.id, true, {
      fileName: "app/components/search/FilterGroupComponent2.tsx",
      lineNumber: 103,
      columnNumber: 42
    }, this)),
    /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "addfilter flex gap-5", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("button", { onClick: addFilter, type: "button", className: "flex items-center", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("img", { className: "mr-2.5", src: Icon_Admin_Add_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/components/search/FilterGroupComponent2.tsx",
          lineNumber: 110,
          columnNumber: 6
        }, this),
        "Add Filter"
      ] }, void 0, true, {
        fileName: "app/components/search/FilterGroupComponent2.tsx",
        lineNumber: 109,
        columnNumber: 5
      }, this),
      group.filters.length > 0 && /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("button", { type: "button", onClick: addNestedGroup, className: "flex items-center", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("img", { className: "mr-2.5", src: Icon_Admin_Add_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/components/search/FilterGroupComponent2.tsx",
          lineNumber: 114,
          columnNumber: 7
        }, this),
        "Add Nested Group"
      ] }, void 0, true, {
        fileName: "app/components/search/FilterGroupComponent2.tsx",
        lineNumber: 113,
        columnNumber: 34
      }, this)
    ] }, void 0, true, {
      fileName: "app/components/search/FilterGroupComponent2.tsx",
      lineNumber: 108,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/components/search/FilterGroupComponent2.tsx",
    lineNumber: 89,
    columnNumber: 10
  }, this);
}
_c = FilterGroupComponent;
var _c;
$RefreshReg$(_c, "FilterGroupComponent");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;

// app/routes/search.advanced.($id).tsx
var import_jsx_dev_runtime3 = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/search.advanced.($id).tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/search.advanced.($id).tsx"
  );
  import.meta.hot.lastModified = "1750252761866.6228";
}
function AdvancedSearch() {
  _s();
  const {
    search,
    filterGroups
  } = useLoaderData();
  const submit = useSubmit();
  const availableFilters = FilterRegistry.getInstance().getAllAsMap();
  const [searchState, setSearchState] = (0, import_react.useState)({
    groups: filterGroups,
    page: 1,
    perPage: 20
  });
  const [displayAs, setDisplayAs] = (0, import_react.useState)("incident");
  const [isSearching, setIsSearching] = (0, import_react.useState)(false);
  const addGroup = (field, operator, value) => {
    setSearchState((prev) => ({
      ...prev,
      groups: [...prev.groups, {
        id: Date.now().toString(),
        type: "group",
        operator: "AND",
        filters: [{
          id: Date.now().toString(),
          type: "filter",
          field,
          operator,
          // Cast to correct type if needed
          value
        }]
      }]
    }));
  };
  const deleteGroup = (index) => {
    setSearchState((prev) => ({
      ...prev,
      groups: prev.groups.filter((_, i) => i !== index)
    }));
  };
  const updateGroup = (index, updatedGroup) => {
    setSearchState((prev) => ({
      ...prev,
      groups: prev.groups.map((group, i) => i === index ? updatedGroup : group)
    }));
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    if (searchState.groups[0].filters.length === 0) {
      return;
    }
    setSearchState((prev) => ({
      ...prev,
      page: 1
    }));
    onSearch({
      ...searchState,
      page: 1
    });
  };
  const onSearch = (search2) => {
    setIsSearching(true);
    const filterGroups2 = [];
    search2.groups.forEach((group) => {
      if (group.filters.length > 0) {
        const backendGroup = convertFilterGroup(group);
        filterGroups2.push(backendGroup);
      }
    });
    console.log("search.groups:", JSON.stringify(search2.groups));
    const rootGroup = search2.groups[0];
    if (rootGroup.filters.length > 0) {
      const backendGroup = convertFilterGroup(rootGroup);
      filterGroups2.push(backendGroup);
    }
    console.log("frontend filterGroups:", JSON.stringify(search2.groups));
    console.log("backend filterGroups:", JSON.stringify(filterGroups2));
    submit({
      baseTable: displayAs === "participant" ? "gva_data.incident_participants" : "gva_data.incidents",
      baseAlias: displayAs === "participant" ? "p" : "i",
      filterGroups: filterGroups2,
      columns: displayAs === "participant" ? ["participant_id", "name"] : ["incident_id", "incident_date", "state", "city", "address"],
      userRoles: ["user"]
    }, {
      method: "post",
      encType: "application/json"
    });
  };
  (0, import_react.useEffect)(() => {
    console.log("searchState-------------", JSON.stringify(searchState));
  }, [searchState]);
  return /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)(SearchProvider, { filters: availableFilters, children: /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: "min-h-screen", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)(Header, {}, void 0, false, {
      fileName: "app/routes/search.advanced.($id).tsx",
      lineNumber: 199,
      columnNumber: 5
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("main", { className: "py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: "container mx-auto", children: /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: "querybuilder advanced", children: [
        searchState.groups.map((group, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)(FilterGroupComponent, { group, onUpdate: (updated) => updateGroup(index, updated), onDelete: () => deleteGroup(index), root: true }, group.id, false, {
            fileName: "app/routes/search.advanced.($id).tsx",
            lineNumber: 206,
            columnNumber: 11
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)(Operator_default, {}, void 0, false, {
            fileName: "app/routes/search.advanced.($id).tsx",
            lineNumber: 207,
            columnNumber: 11
          }, this)
        ] }, `group-${group.id}`, true, {
          fileName: "app/routes/search.advanced.($id).tsx",
          lineNumber: 205,
          columnNumber: 51
        }, this)),
        /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)(AddAnotherFilter_default, { onChange: addGroup }, void 0, false, {
          fileName: "app/routes/search.advanced.($id).tsx",
          lineNumber: 209,
          columnNumber: 9
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/search.advanced.($id).tsx",
        lineNumber: 204,
        columnNumber: 8
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: "mt-10 text-lg", children: [
        "Display results as",
        /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("select", { className: "w-full sm:w-fit sm:ml-5 sm:min-w-[200px] bg-gray-200 h-[50px] rounded-[5px] outline-0 px-2 sm:px-3 lg:px-5", value: displayAs, onChange: (e) => setDisplayAs(e.target.value), children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("option", { value: "incident", children: "Incidents" }, void 0, false, {
            fileName: "app/routes/search.advanced.($id).tsx",
            lineNumber: 214,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("option", { value: "participant", children: "Participants" }, void 0, false, {
            fileName: "app/routes/search.advanced.($id).tsx",
            lineNumber: 215,
            columnNumber: 10
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/search.advanced.($id).tsx",
          lineNumber: 213,
          columnNumber: 9
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/search.advanced.($id).tsx",
        lineNumber: 211,
        columnNumber: 8
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: "mt-10", children: [
        !search && /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("button", { type: "button", onClick: isSearching ? null : handleSubmit, className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white", children: isSearching ? "Searching..." : "Search" }, void 0, false, {
          fileName: "app/routes/search.advanced.($id).tsx",
          lineNumber: 219,
          columnNumber: 21
        }, this),
        search && !search.editlock && /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("button", { type: "button", onClick: isSearching ? null : handleSubmit, className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white", children: isSearching ? "Saving..." : "Save" }, void 0, false, {
          fileName: "app/routes/search.advanced.($id).tsx",
          lineNumber: 222,
          columnNumber: 40
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/search.advanced.($id).tsx",
        lineNumber: 218,
        columnNumber: 8
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/search.advanced.($id).tsx",
      lineNumber: 203,
      columnNumber: 7
    }, this) }, void 0, false, {
      fileName: "app/routes/search.advanced.($id).tsx",
      lineNumber: 201,
      columnNumber: 6
    }, this) }, void 0, false, {
      fileName: "app/routes/search.advanced.($id).tsx",
      lineNumber: 200,
      columnNumber: 5
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)(Footer, {}, void 0, false, {
      fileName: "app/routes/search.advanced.($id).tsx",
      lineNumber: 229,
      columnNumber: 5
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/search.advanced.($id).tsx",
    lineNumber: 198,
    columnNumber: 4
  }, this) }, void 0, false, {
    fileName: "app/routes/search.advanced.($id).tsx",
    lineNumber: 197,
    columnNumber: 10
  }, this);
}
_s(AdvancedSearch, "NUPrQEWBEYrvYFCth1mf8+wmIQ8=", false, function() {
  return [useLoaderData, useSubmit];
});
_c2 = AdvancedSearch;
var isFilterGroup = (filter) => {
  return filter.type === "group";
};
var convertFilterGroup = (group) => {
  return {
    operator: group.operator,
    conditions: group.filters.map((filter) => {
      if (isFilterGroup(filter)) {
        return convertFilterGroup(filter);
      } else {
        return {
          id: filter.field,
          value: {
            operator: filter.operator,
            value: filter.value
          }
        };
      }
    })
  };
};
var _c2;
$RefreshReg$(_c2, "AdvancedSearch");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  AdvancedSearch as default
};
//# sourceMappingURL=/build/routes/search.advanced.($id)-TQJRMBSK.js.map
