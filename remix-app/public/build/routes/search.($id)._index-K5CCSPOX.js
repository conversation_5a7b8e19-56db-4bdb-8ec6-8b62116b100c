import {
  AddAnotherFilter_default,
  FilterComponent2_default,
  SearchProvider
} from "/build/_shared/chunk-QRPAFGRB.js";
import {
  FilterRegistry,
  require_SearchBuilder
} from "/build/_shared/chunk-E7PJAPPF.js";
import "/build/_shared/chunk-RUOV274T.js";
import {
  <PERSON><PERSON>,
  <PERSON>er
} from "/build/_shared/chunk-4556QKWU.js";
import "/build/_shared/chunk-W7JLG7II.js";
import "/build/_shared/chunk-OHIZ2QAR.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import "/build/_shared/chunk-DFTVPORL.js";
import {
  useLoaderData,
  useSubmit
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  require_node
} from "/build/_shared/chunk-ZP6BZTHN.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/search.($id)._index.tsx
var import_react = __toESM(require_react(), 1);
var import_node = __toESM(require_node(), 1);
var import_SearchBuilder = __toESM(require_SearchBuilder(), 1);
var import_auth = __toESM(require_auth(), 1);

// app/images/Vector 2.svg
var Vector_2_default = "/build/_assets/Vector 2-L4JNSSI5.svg";

// app/routes/search.($id)._index.tsx
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/search.($id)._index.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/search.($id)._index.tsx"
  );
  import.meta.hot.lastModified = "1750252761866.2292";
}
function Search() {
  _s();
  const {
    search,
    filterGroups
  } = useLoaderData();
  const submit = useSubmit();
  const availableFilters = FilterRegistry.getInstance().getAllAsMap();
  const [searchState, setSearchState] = (0, import_react.useState)({
    groups: filterGroups,
    page: 1,
    perPage: 20
  });
  const [displayAs, setDisplayAs] = (0, import_react.useState)("incident");
  const [isSearching, setIsSearching] = (0, import_react.useState)(false);
  const addFilter = (field, operator, value) => {
    const newFilters = [...searchState.groups[0].filters, {
      id: Date.now().toString(),
      type: "filter",
      field,
      operator,
      value
    }];
    const group = {
      ...searchState.groups[0],
      filters: newFilters
    };
    setSearchState((prev) => ({
      ...prev,
      groups: [group]
    }));
  };
  const updateFilter = (index, updatedFilter) => {
    const newFilters = [...searchState.groups[0].filters];
    newFilters[index] = updatedFilter;
    const group = {
      ...searchState.groups[0],
      filters: newFilters
    };
    setSearchState((prev) => ({
      ...prev,
      groups: [group]
    }));
  };
  const deleteFilter = (index) => {
    const group = {
      ...searchState.groups[0],
      filters: searchState.groups[0].filters.filter((_, i) => i !== index)
    };
    setSearchState((prev) => ({
      ...prev,
      groups: [group]
    }));
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    if (searchState.groups[0].filters.length === 0) {
      return;
    }
    setSearchState((prev) => ({
      ...prev,
      page: 1
    }));
    onSearch({
      ...searchState,
      page: 1
    });
  };
  const onSearch = async (search2) => {
    setIsSearching(true);
    const filterGroups2 = [];
    const rootGroup = search2.groups[0];
    if (rootGroup.filters.length > 0) {
      const backendGroup = convertFilterGroup(rootGroup);
      filterGroups2.push(backendGroup);
    }
    console.log("frontend filterGroups:", JSON.stringify(search2.groups));
    console.log("backend filterGroups:", JSON.stringify(filterGroups2));
    submit({
      baseTable: displayAs === "participant" ? "gva_data.incident_participants" : "gva_data.incidents",
      baseAlias: displayAs === "participant" ? "p" : "i",
      filterGroups: filterGroups2,
      columns: displayAs === "participant" ? ["participant_id", "name"] : ["incident_id", "incident_date", "state", "city", "address"],
      userRoles: ["user"]
    }, {
      method: "post",
      encType: "application/json"
    });
  };
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(SearchProvider, { filters: availableFilters, children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "min-h-screen", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Header, {}, void 0, false, {
      fileName: "app/routes/search.($id)._index.tsx",
      lineNumber: 206,
      columnNumber: 5
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { className: "py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "querybuilder basic", children: [
        searchState.groups[0].filters.map((filter, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(FilterComponent2_default, { filter, onUpdate: (updated) => updateFilter(index, updated), onDelete: () => deleteFilter(index), className: index > 0 ? "mt-[-60px]" : "" }, void 0, false, {
            fileName: "app/routes/search.($id)._index.tsx",
            lineNumber: 213,
            columnNumber: 11
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(AndOrOperator, {}, void 0, false, {
            fileName: "app/routes/search.($id)._index.tsx",
            lineNumber: 214,
            columnNumber: 11
          }, this)
        ] }, filter.id, true, {
          fileName: "app/routes/search.($id)._index.tsx",
          lineNumber: 212,
          columnNumber: 63
        }, this)),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(AddAnotherFilter_default, { className: searchState.groups[0].filters.length > 0 ? "mt-[-60px]" : "ml-[-30px]", onChange: addFilter }, void 0, false, {
          fileName: "app/routes/search.($id)._index.tsx",
          lineNumber: 216,
          columnNumber: 9
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/search.($id)._index.tsx",
        lineNumber: 211,
        columnNumber: 8
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-10 text-lg", children: [
        "Display results as",
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("select", { className: "h-[50px] w-full rounded-[5px] bg-gray-200 px-2 outline-0 sm:ml-5 sm:w-fit sm:min-w-[200px] sm:px-3 lg:px-5", value: displayAs, onChange: (e) => setDisplayAs(e.target.value), children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("option", { value: "incident", children: "Incidents" }, void 0, false, {
            fileName: "app/routes/search.($id)._index.tsx",
            lineNumber: 221,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("option", { value: "participant", children: "Participants" }, void 0, false, {
            fileName: "app/routes/search.($id)._index.tsx",
            lineNumber: 222,
            columnNumber: 10
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/search.($id)._index.tsx",
          lineNumber: 220,
          columnNumber: 9
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/search.($id)._index.tsx",
        lineNumber: 218,
        columnNumber: 8
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-10", children: [
        !search && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "button", onClick: isSearching ? null : handleSubmit, className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white", children: isSearching ? "Searching..." : "Search" }, void 0, false, {
          fileName: "app/routes/search.($id)._index.tsx",
          lineNumber: 226,
          columnNumber: 21
        }, this),
        search && !search.editlock && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "button", onClick: isSearching ? null : handleSubmit, className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white", children: isSearching ? "Saving..." : "Save" }, void 0, false, {
          fileName: "app/routes/search.($id)._index.tsx",
          lineNumber: 229,
          columnNumber: 40
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/search.($id)._index.tsx",
        lineNumber: 225,
        columnNumber: 8
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/search.($id)._index.tsx",
      lineNumber: 210,
      columnNumber: 7
    }, this) }, void 0, false, {
      fileName: "app/routes/search.($id)._index.tsx",
      lineNumber: 208,
      columnNumber: 6
    }, this) }, void 0, false, {
      fileName: "app/routes/search.($id)._index.tsx",
      lineNumber: 207,
      columnNumber: 5
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Footer, {}, void 0, false, {
      fileName: "app/routes/search.($id)._index.tsx",
      lineNumber: 236,
      columnNumber: 5
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/search.($id)._index.tsx",
    lineNumber: 205,
    columnNumber: 4
  }, this) }, void 0, false, {
    fileName: "app/routes/search.($id)._index.tsx",
    lineNumber: 204,
    columnNumber: 10
  }, this);
}
_s(Search, "bhMlSBB/NW0aSOmYZXOEWH5NnX4=", false, function() {
  return [useLoaderData, useSubmit];
});
_c = Search;
var AndOrOperator = ({
  or = false
}) => {
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "operator", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Vector_2_default, alt: "", width: 25 }, void 0, false, {
      fileName: "app/routes/search.($id)._index.tsx",
      lineNumber: 248,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "andor", children: or ? "OR" : "AND" }, void 0, false, {
      fileName: "app/routes/search.($id)._index.tsx",
      lineNumber: 249,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/search.($id)._index.tsx",
    lineNumber: 247,
    columnNumber: 10
  }, this);
};
_c2 = AndOrOperator;
var isFilterGroup = (filter) => {
  return filter.type === "group";
};
var convertFilterGroup = (group) => {
  return {
    operator: group.operator,
    conditions: group.filters.map((filter) => {
      if (isFilterGroup(filter)) {
        return convertFilterGroup(filter);
      } else {
        return {
          id: filter.field,
          value: {
            operator: filter.operator,
            value: filter.value
          }
        };
      }
    })
  };
};
var _c;
var _c2;
$RefreshReg$(_c, "Search");
$RefreshReg$(_c2, "AndOrOperator");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  Search as default
};
//# sourceMappingURL=/build/routes/search.($id)._index-K5CCSPOX.js.map
