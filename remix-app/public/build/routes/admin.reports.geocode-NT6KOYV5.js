import {
  AdminPagination
} from "/build/_shared/chunk-5JSGFT47.js";
import "/build/_shared/chunk-QGERY6II.js";
import "/build/_shared/chunk-ULKIBZZM.js";
import "/build/_shared/chunk-OHSQI4V6.js";
import {
  moment_default
} from "/build/_shared/chunk-XAWK6254.js";
import "/build/_shared/chunk-NPKMIMFJ.js";
import {
  require_cookies
} from "/build/_shared/chunk-MUAEGCBW.js";
import {
  Form,
  Link,
  useLoaderData
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __commonJS,
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// empty-module:~/models/queue.server
var require_queue = __commonJS({
  "empty-module:~/models/queue.server"(exports, module) {
    module.exports = {};
  }
});

// app/routes/admin.reports.geocode.tsx
var import_react2 = __toESM(require_react(), 1);

// node_modules/react-json-view-lite/dist/index.modern.js
var import_react = __toESM(require_react());
var isBoolean = (data) => {
  return typeof data === "boolean" || data instanceof Boolean;
};
var isNumber = (data) => {
  return typeof data === "number" || data instanceof Number;
};
var isBigInt = (data) => {
  return typeof data === "bigint" || data instanceof BigInt;
};
var isDate = (data) => {
  return !!data && data instanceof Date;
};
var isString = (data) => {
  return typeof data === "string" || data instanceof String;
};
var isArray = (data) => {
  return Array.isArray(data);
};
var isObject = (data) => {
  return typeof data === "object" && data !== null;
};
var isFunction = (data) => {
  return !!data && data instanceof Object && typeof data === "function";
};
function useBool(initialValueCreator) {
  const [value, setValue] = (0, import_react.useState)(initialValueCreator());
  const toggle = () => setValue((currentValue) => !currentValue);
  return [value, toggle, setValue];
}
function quoteString(value, quoted) {
  if (quoted === void 0) {
    quoted = false;
  }
  return !value || quoted ? `"${value}"` : value;
}
function quoteStringValue(value, quoted) {
  return quoted ? `"${value}"` : value;
}
function ExpandableObject(_ref) {
  let {
    field,
    value,
    data,
    lastElement,
    openBracket,
    closeBracket,
    level,
    style,
    shouldExpandNode,
    clickToExpandNode,
    outerRef
  } = _ref;
  const shouldExpandNodeCalledRef = (0, import_react.useRef)(false);
  const [expanded, toggleExpanded, setExpanded] = useBool(() => shouldExpandNode(level, value, field));
  const expanderButtonRef = (0, import_react.useRef)(null);
  (0, import_react.useEffect)(() => {
    if (!shouldExpandNodeCalledRef.current) {
      shouldExpandNodeCalledRef.current = true;
    } else {
      setExpanded(shouldExpandNode(level, value, field));
    }
  }, [shouldExpandNode]);
  const expanderIconStyle = expanded ? style.collapseIcon : style.expandIcon;
  const ariaLabel = expanded ? "collapse JSON" : "expand JSON";
  const contentsId = (0, import_react.useId)();
  const childLevel = level + 1;
  const lastIndex = data.length - 1;
  const onKeyDown = (e) => {
    if (e.key === "ArrowRight" || e.key === "ArrowLeft") {
      e.preventDefault();
      setExpanded(e.key === "ArrowRight");
    } else if (e.key === "ArrowUp" || e.key === "ArrowDown") {
      e.preventDefault();
      const direction = e.key === "ArrowUp" ? -1 : 1;
      if (!outerRef.current)
        return;
      const buttonElements = outerRef.current.querySelectorAll("[role=button]");
      let currentIndex = -1;
      for (let i = 0; i < buttonElements.length; i++) {
        if (buttonElements[i].tabIndex === 0) {
          currentIndex = i;
          break;
        }
      }
      if (currentIndex < 0) {
        return;
      }
      const nextIndex = (currentIndex + direction + buttonElements.length) % buttonElements.length;
      buttonElements[currentIndex].tabIndex = -1;
      buttonElements[nextIndex].tabIndex = 0;
      buttonElements[nextIndex].focus();
    }
  };
  const onClick = () => {
    var _outerRef$current;
    toggleExpanded();
    const buttonElement = expanderButtonRef.current;
    if (!buttonElement)
      return;
    const prevButtonElement = (_outerRef$current = outerRef.current) === null || _outerRef$current === void 0 ? void 0 : _outerRef$current.querySelector('[role=button][tabindex="0"]');
    if (prevButtonElement) {
      prevButtonElement.tabIndex = -1;
    }
    buttonElement.tabIndex = 0;
    buttonElement.focus();
  };
  return /* @__PURE__ */ (0, import_react.createElement)("div", {
    className: style.basicChildStyle,
    role: "treeitem",
    "aria-expanded": expanded,
    "aria-selected": void 0
  }, /* @__PURE__ */ (0, import_react.createElement)("span", {
    className: expanderIconStyle,
    onClick,
    onKeyDown,
    role: "button",
    "aria-label": ariaLabel,
    "aria-expanded": expanded,
    "aria-controls": expanded ? contentsId : void 0,
    ref: expanderButtonRef,
    tabIndex: level === 0 ? 0 : -1
  }), (field || field === "") && (clickToExpandNode ? /* @__PURE__ */ (0, import_react.createElement)("span", {
    className: style.clickableLabel,
    onClick,
    onKeyDown
  }, quoteString(field, style.quotesForFieldNames), ":") : /* @__PURE__ */ (0, import_react.createElement)("span", {
    className: style.label
  }, quoteString(field, style.quotesForFieldNames), ":")), /* @__PURE__ */ (0, import_react.createElement)("span", {
    className: style.punctuation
  }, openBracket), expanded ? /* @__PURE__ */ (0, import_react.createElement)("ul", {
    id: contentsId,
    role: "group",
    className: style.childFieldsContainer
  }, data.map((dataElement, index) => /* @__PURE__ */ (0, import_react.createElement)(DataRender, {
    key: dataElement[0] || index,
    field: dataElement[0],
    value: dataElement[1],
    style,
    lastElement: index === lastIndex,
    level: childLevel,
    shouldExpandNode,
    clickToExpandNode,
    outerRef
  }))) : /* @__PURE__ */ (0, import_react.createElement)("span", {
    className: style.collapsedContent,
    onClick,
    onKeyDown
  }), /* @__PURE__ */ (0, import_react.createElement)("span", {
    className: style.punctuation
  }, closeBracket), !lastElement && /* @__PURE__ */ (0, import_react.createElement)("span", {
    className: style.punctuation
  }, ","));
}
function EmptyObject(_ref2) {
  let {
    field,
    openBracket,
    closeBracket,
    lastElement,
    style
  } = _ref2;
  return /* @__PURE__ */ (0, import_react.createElement)("div", {
    className: style.basicChildStyle,
    role: "treeitem",
    "aria-selected": void 0
  }, (field || field === "") && /* @__PURE__ */ (0, import_react.createElement)("span", {
    className: style.label
  }, quoteString(field, style.quotesForFieldNames), ":"), /* @__PURE__ */ (0, import_react.createElement)("span", {
    className: style.punctuation
  }, openBracket), /* @__PURE__ */ (0, import_react.createElement)("span", {
    className: style.punctuation
  }, closeBracket), !lastElement && /* @__PURE__ */ (0, import_react.createElement)("span", {
    className: style.punctuation
  }, ","));
}
function JsonObject(_ref3) {
  let {
    field,
    value,
    style,
    lastElement,
    shouldExpandNode,
    clickToExpandNode,
    level,
    outerRef
  } = _ref3;
  if (Object.keys(value).length === 0) {
    return EmptyObject({
      field,
      openBracket: "{",
      closeBracket: "}",
      lastElement,
      style
    });
  }
  return ExpandableObject({
    field,
    value,
    lastElement: lastElement || false,
    level,
    openBracket: "{",
    closeBracket: "}",
    style,
    shouldExpandNode,
    clickToExpandNode,
    data: Object.keys(value).map((key) => [key, value[key]]),
    outerRef
  });
}
function JsonArray(_ref4) {
  let {
    field,
    value,
    style,
    lastElement,
    level,
    shouldExpandNode,
    clickToExpandNode,
    outerRef
  } = _ref4;
  if (value.length === 0) {
    return EmptyObject({
      field,
      openBracket: "[",
      closeBracket: "]",
      lastElement,
      style
    });
  }
  return ExpandableObject({
    field,
    value,
    lastElement: lastElement || false,
    level,
    openBracket: "[",
    closeBracket: "]",
    style,
    shouldExpandNode,
    clickToExpandNode,
    data: value.map((element) => [void 0, element]),
    outerRef
  });
}
function JsonPrimitiveValue(_ref5) {
  let {
    field,
    value,
    style,
    lastElement
  } = _ref5;
  let stringValue;
  let valueStyle = style.otherValue;
  if (value === null) {
    stringValue = "null";
    valueStyle = style.nullValue;
  } else if (value === void 0) {
    stringValue = "undefined";
    valueStyle = style.undefinedValue;
  } else if (isString(value)) {
    stringValue = quoteStringValue(value, !style.noQuotesForStringValues);
    valueStyle = style.stringValue;
  } else if (isBoolean(value)) {
    stringValue = value ? "true" : "false";
    valueStyle = style.booleanValue;
  } else if (isNumber(value)) {
    stringValue = value.toString();
    valueStyle = style.numberValue;
  } else if (isBigInt(value)) {
    stringValue = `${value.toString()}n`;
    valueStyle = style.numberValue;
  } else if (isDate(value)) {
    stringValue = value.toISOString();
  } else if (isFunction(value)) {
    stringValue = "function() { }";
  } else {
    stringValue = value.toString();
  }
  return /* @__PURE__ */ (0, import_react.createElement)("div", {
    className: style.basicChildStyle,
    role: "treeitem",
    "aria-selected": void 0
  }, (field || field === "") && /* @__PURE__ */ (0, import_react.createElement)("span", {
    className: style.label
  }, quoteString(field, style.quotesForFieldNames), ":"), /* @__PURE__ */ (0, import_react.createElement)("span", {
    className: valueStyle
  }, stringValue), !lastElement && /* @__PURE__ */ (0, import_react.createElement)("span", {
    className: style.punctuation
  }, ","));
}
function DataRender(props) {
  const value = props.value;
  if (isArray(value)) {
    return /* @__PURE__ */ (0, import_react.createElement)(JsonArray, Object.assign({}, props));
  }
  if (isObject(value) && !isDate(value) && !isFunction(value)) {
    return /* @__PURE__ */ (0, import_react.createElement)(JsonObject, Object.assign({}, props));
  }
  return /* @__PURE__ */ (0, import_react.createElement)(JsonPrimitiveValue, Object.assign({}, props));
}
var styles = { "container-base": "_GzYRV", "punctuation-base": "_3eOF8", "pointer": "_1MFti", "expander-base": "_f10Tu _1MFti", "expand-icon": "_1UmXx", "collapse-icon": "_1LId0", "collapsed-content-base": "_1pNG9 _1MFti", "container-light": "_2IvMF _GzYRV", "basic-element-style": "_2bkNM", "child-fields-container": "_1BXBN", "label-light": "_1MGIk", "clickable-label-light": "_2YKJg _1MGIk _1MFti", "punctuation-light": "_3uHL6 _3eOF8", "value-null-light": "_2T6PJ", "value-undefined-light": "_1Gho6", "value-string-light": "_vGjyY", "value-number-light": "_1bQdo", "value-boolean-light": "_3zQKs", "value-other-light": "_1xvuR", "collapse-icon-light": "_oLqym _f10Tu _1MFti _1LId0", "expand-icon-light": "_2AXVT _f10Tu _1MFti _1UmXx", "collapsed-content-light": "_2KJWg _1pNG9 _1MFti", "container-dark": "_11RoI _GzYRV", "expand-icon-dark": "_17H2C _f10Tu _1MFti _1UmXx", "collapse-icon-dark": "_3QHg2 _f10Tu _1MFti _1LId0", "collapsed-content-dark": "_3fDAz _1pNG9 _1MFti", "label-dark": "_2bSDX", "clickable-label-dark": "_1RQEj _2bSDX _1MFti", "punctuation-dark": "_gsbQL _3eOF8", "value-null-dark": "_LaAZe", "value-undefined-dark": "_GTKgm", "value-string-dark": "_Chy1W", "value-number-dark": "_2bveF", "value-boolean-dark": "_2vRm-", "value-other-dark": "_1prJR" };
var defaultStyles = {
  container: styles["container-light"],
  basicChildStyle: styles["basic-element-style"],
  childFieldsContainer: styles["child-fields-container"],
  label: styles["label-light"],
  clickableLabel: styles["clickable-label-light"],
  nullValue: styles["value-null-light"],
  undefinedValue: styles["value-undefined-light"],
  stringValue: styles["value-string-light"],
  booleanValue: styles["value-boolean-light"],
  numberValue: styles["value-number-light"],
  otherValue: styles["value-other-light"],
  punctuation: styles["punctuation-light"],
  collapseIcon: styles["collapse-icon-light"],
  expandIcon: styles["expand-icon-light"],
  collapsedContent: styles["collapsed-content-light"],
  noQuotesForStringValues: false,
  quotesForFieldNames: false
};
var darkStyles = {
  container: styles["container-dark"],
  basicChildStyle: styles["basic-element-style"],
  childFieldsContainer: styles["child-fields-container"],
  label: styles["label-dark"],
  clickableLabel: styles["clickable-label-dark"],
  nullValue: styles["value-null-dark"],
  undefinedValue: styles["value-undefined-dark"],
  stringValue: styles["value-string-dark"],
  booleanValue: styles["value-boolean-dark"],
  numberValue: styles["value-number-dark"],
  otherValue: styles["value-other-dark"],
  punctuation: styles["punctuation-dark"],
  collapseIcon: styles["collapse-icon-dark"],
  expandIcon: styles["expand-icon-dark"],
  collapsedContent: styles["collapsed-content-dark"],
  noQuotesForStringValues: false,
  quotesForFieldNames: false
};
var allExpanded = () => true;
var collapseAllNested = (level) => level < 1;
var JsonView = (_ref) => {
  let {
    data,
    style = defaultStyles,
    shouldExpandNode = allExpanded,
    clickToExpandNode = false,
    ...ariaAttrs
  } = _ref;
  const outerRef = (0, import_react.useRef)(null);
  return /* @__PURE__ */ (0, import_react.createElement)("div", Object.assign({
    "aria-label": "JSON view"
  }, ariaAttrs, {
    className: style.container,
    ref: outerRef,
    role: "tree"
  }), /* @__PURE__ */ (0, import_react.createElement)(DataRender, {
    value: data,
    style: {
      ...defaultStyles,
      ...style
    },
    lastElement: true,
    level: 0,
    shouldExpandNode,
    clickToExpandNode,
    outerRef
  }));
};

// app/routes/admin.reports.geocode.tsx
var import_cookies = __toESM(require_cookies(), 1);
var import_queue = __toESM(require_queue(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.reports.geocode.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.reports.geocode.tsx"
  );
}
var PAGE_SIZE = 25;
function GeocodeIndexPage() {
  _s();
  const {
    logs,
    logsCount,
    filter,
    formSubmitMessage: formSubmitMessage2
  } = useLoaderData();
  const [showMessage, setShowMessage] = (0, import_react2.useState)(!!formSubmitMessage2);
  (0, import_react2.useEffect)(() => {
    if (showMessage) {
      const timeoutId = setTimeout(() => setShowMessage(false), 5e3);
      return () => clearTimeout(timeoutId);
    }
  }, [showMessage]);
  const errorTypes = ["Zipcode", "Congresstional District", "County Name", "Neighborhood", "Metro and Micro Statistical Areas", "Combined Statistical Area", "state-county-tract number"];
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { className: "container mx-auto pb-10 pt-8", children: [
    showMessage ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("p", { className: "animate-fade bg-black text-white", children: formSubmitMessage2 }, void 0, false, {
      fileName: "app/routes/admin.reports.geocode.tsx",
      lineNumber: 67,
      columnNumber: 19
    }, this) : null,
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { id: "search-form", role: "search", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex flex-col space-y-4", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex space-x-4", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("select", { id: "ErrorType", name: "error_type", defaultValue: filter.error_type || "", autoComplete: "off", className: "border-2", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("option", { value: "", children: "-- Error Type --" }, void 0, false, {
          fileName: "app/routes/admin.reports.geocode.tsx",
          lineNumber: 75,
          columnNumber: 9
        }, this),
        errorTypes.map((type) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("option", { value: type, children: type }, `type-${type}`, false, {
          fileName: "app/routes/admin.reports.geocode.tsx",
          lineNumber: 76,
          columnNumber: 33
        }, this))
      ] }, void 0, true, {
        fileName: "app/routes/admin.reports.geocode.tsx",
        lineNumber: 73,
        columnNumber: 8
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.reports.geocode.tsx",
        lineNumber: 72,
        columnNumber: 7
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", className: "rounded-full bg-orange-500 px-5 py-1.5 text-lg text-white hover:bg-orange-800 focus:bg-orange-800", children: "Search" }, void 0, false, {
        fileName: "app/routes/admin.reports.geocode.tsx",
        lineNumber: 82,
        columnNumber: 8
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.reports.geocode.tsx",
        lineNumber: 81,
        columnNumber: 7
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.reports.geocode.tsx",
      lineNumber: 71,
      columnNumber: 6
    }, this) }, void 0, false, {
      fileName: "app/routes/admin.reports.geocode.tsx",
      lineNumber: 70,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/admin.reports.geocode.tsx",
      lineNumber: 69,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("table", { className: "table-gray mt-5 w-full table-auto text-sm", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("thead", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Incident Id" }, void 0, false, {
          fileName: "app/routes/admin.reports.geocode.tsx",
          lineNumber: 94,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Address" }, void 0, false, {
          fileName: "app/routes/admin.reports.geocode.tsx",
          lineNumber: 95,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Date Added" }, void 0, false, {
          fileName: "app/routes/admin.reports.geocode.tsx",
          lineNumber: 96,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Error Type" }, void 0, false, {
          fileName: "app/routes/admin.reports.geocode.tsx",
          lineNumber: 97,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Notes" }, void 0, false, {
          fileName: "app/routes/admin.reports.geocode.tsx",
          lineNumber: 98,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Raw Response" }, void 0, false, {
          fileName: "app/routes/admin.reports.geocode.tsx",
          lineNumber: 99,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.reports.geocode.tsx",
        lineNumber: 93,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.reports.geocode.tsx",
        lineNumber: 92,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tbody", { children: [
        logs.map((log) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "text-blue-500", to: `/incident/${log.entity_id}`, children: log.entity_id }, void 0, false, {
            fileName: "app/routes/admin.reports.geocode.tsx",
            lineNumber: 105,
            columnNumber: 9
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.reports.geocode.tsx",
            lineNumber: 104,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: [
            log.incident?.address,
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("br", {}, void 0, false, {
              fileName: "app/routes/admin.reports.geocode.tsx",
              lineNumber: 111,
              columnNumber: 9
            }, this),
            log.incident?.county,
            " ",
            log.incident?.city_county,
            ", ",
            log.incident?.state?.value
          ] }, void 0, true, {
            fileName: "app/routes/admin.reports.geocode.tsx",
            lineNumber: 109,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: moment_default.unix(log.added).format("MMM D, YYYY") }, void 0, false, {
            fileName: "app/routes/admin.reports.geocode.tsx",
            lineNumber: 114,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: log.error_type }, void 0, false, {
            fileName: "app/routes/admin.reports.geocode.tsx",
            lineNumber: 115,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: log.comment }, void 0, false, {
            fileName: "app/routes/admin.reports.geocode.tsx",
            lineNumber: 116,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: log.json_response && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(import_react2.Fragment, { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(JsonView, { data: JSON.parse(log.json_response), shouldExpandNode: collapseAllNested, style: defaultStyles }, void 0, false, {
            fileName: "app/routes/admin.reports.geocode.tsx",
            lineNumber: 119,
            columnNumber: 11
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.reports.geocode.tsx",
            lineNumber: 118,
            columnNumber: 31
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.reports.geocode.tsx",
            lineNumber: 117,
            columnNumber: 8
          }, this)
        ] }, `log-${log.entity_id}`, true, {
          fileName: "app/routes/admin.reports.geocode.tsx",
          lineNumber: 103,
          columnNumber: 23
        }, this)),
        logs.length === 0 && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { colSpan: 6, children: "No logs yet." }, void 0, false, {
          fileName: "app/routes/admin.reports.geocode.tsx",
          lineNumber: 125,
          columnNumber: 8
        }, this) }, void 0, false, {
          fileName: "app/routes/admin.reports.geocode.tsx",
          lineNumber: 124,
          columnNumber: 28
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.reports.geocode.tsx",
        lineNumber: 102,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.reports.geocode.tsx",
      lineNumber: 91,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "my-10 flex justify-center text-sm", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(AdminPagination, { total: logsCount, defaultPageSize: PAGE_SIZE }, void 0, false, {
      fileName: "app/routes/admin.reports.geocode.tsx",
      lineNumber: 131,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/admin.reports.geocode.tsx",
      lineNumber: 130,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.reports.geocode.tsx",
    lineNumber: 66,
    columnNumber: 10
  }, this);
}
_s(GeocodeIndexPage, "TNWRac2VQlSq9rORbba3FhXSp28=", false, function() {
  return [useLoaderData];
});
_c = GeocodeIndexPage;
var _c;
$RefreshReg$(_c, "GeocodeIndexPage");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  GeocodeIndexPage as default
};
//# sourceMappingURL=/build/routes/admin.reports.geocode-NT6KOYV5.js.map
