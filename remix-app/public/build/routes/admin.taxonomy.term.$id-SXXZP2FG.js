import {
  require_taxonomy
} from "/build/_shared/chunk-AO7MZAIH.js";
import "/build/_shared/chunk-AUYLHJJM.js";
import {
  Icon_Admin_Delete_default
} from "/build/_shared/chunk-RUOV274T.js";
import {
  require_cookies
} from "/build/_shared/chunk-MUAEGCBW.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import "/build/_shared/chunk-DFTVPORL.js";
import {
  Form,
  Link,
  useActionData,
  useLoaderData
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/admin.taxonomy.term.$id.tsx
var import_cookies = __toESM(require_cookies(), 1);
var import_auth = __toESM(require_auth(), 1);
var import_taxonomy = __toESM(require_taxonomy(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.taxonomy.term.$id.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.taxonomy.term.$id.tsx"
  );
  import.meta.hot.lastModified = "1748456820453.9504";
}
function TaxonomyFormPage() {
  _s();
  const actionData = useActionData();
  const {
    term,
    taxonomyOptions,
    parentOptions
  } = useLoaderData();
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { className: "container mx-auto pb-10 pt-8", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[25px] font-bold", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: `/admin/taxonomy`, children: "Manage Taxonomy" }, void 0, false, {
        fileName: "app/routes/admin.taxonomy.term.$id.tsx",
        lineNumber: 138,
        columnNumber: 43
      }, this),
      " - ",
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: `/admin/taxonomy/${term.taxonomy_machine_name}`, children: taxonomyOptions.find((t) => t.id == term.taxonomy_machine_name)?.name }, void 0, false, {
        fileName: "app/routes/admin.taxonomy.term.$id.tsx",
        lineNumber: 138,
        columnNumber: 97
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.taxonomy.term.$id.tsx",
      lineNumber: 138,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-8", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { method: "post", children: [
      actionData && "error" in actionData && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-5 flex border border-red-500 bg-white px-5 py-3.5 text-sm font-light", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Icon_Admin_Delete_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/routes/admin.taxonomy.term.$id.tsx",
          lineNumber: 142,
          columnNumber: 9
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "pl-2 text-red-500", children: actionData.error }, void 0, false, {
          fileName: "app/routes/admin.taxonomy.term.$id.tsx",
          lineNumber: 143,
          columnNumber: 9
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.taxonomy.term.$id.tsx",
        lineNumber: 141,
        columnNumber: 46
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[5px] bg-gray-200 p-10", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid grid-cols-1 gap-x-10 gap-y-8", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "taxonomy_machine_name", defaultValue: term.taxonomy_machine_name }, void 0, false, {
          fileName: "app/routes/admin.taxonomy.term.$id.tsx",
          lineNumber: 147,
          columnNumber: 8
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "Value", className: "text-lg font-bold after:ml-0.5 after:text-orange-500 after:content-['*']", children: "Name" }, void 0, false, {
            fileName: "app/routes/admin.taxonomy.term.$id.tsx",
            lineNumber: 174,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "Value", name: "value", required: true, type: "text", defaultValue: term.value || "", className: "block h-[50px] w-full rounded-[5px] px-5 text-lg outline-0" }, void 0, false, {
            fileName: "app/routes/admin.taxonomy.term.$id.tsx",
            lineNumber: 178,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.taxonomy.term.$id.tsx",
            lineNumber: 177,
            columnNumber: 9
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.taxonomy.term.$id.tsx",
          lineNumber: 173,
          columnNumber: 8
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "Weight", className: "text-lg font-bold after:ml-0.5 after:text-orange-500 after:content-['*']", children: "Weight" }, void 0, false, {
            fileName: "app/routes/admin.taxonomy.term.$id.tsx",
            lineNumber: 182,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "Weight", name: "weight", required: true, type: "number", defaultValue: term.weight || 0, className: "block h-[50px] w-full rounded-[5px] px-5 text-lg outline-0" }, void 0, false, {
            fileName: "app/routes/admin.taxonomy.term.$id.tsx",
            lineNumber: 186,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.taxonomy.term.$id.tsx",
            lineNumber: 185,
            columnNumber: 9
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.taxonomy.term.$id.tsx",
          lineNumber: 181,
          columnNumber: 8
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "Parent", className: "text-lg font-bold", children: "Parent" }, void 0, false, {
            fileName: "app/routes/admin.taxonomy.term.$id.tsx",
            lineNumber: 190,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("select", { id: "Parent", name: "parent", defaultValue: term.parent ?? 0, className: "block h-[50px] w-full rounded-[5px] px-5 text-lg outline-0", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("option", { value: "0", children: "- Select -" }, void 0, false, {
              fileName: "app/routes/admin.taxonomy.term.$id.tsx",
              lineNumber: 195,
              columnNumber: 11
            }, this),
            parentOptions.map((t) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("option", { value: t.id, children: t.name }, `parent-option-${t.id}`, false, {
              fileName: "app/routes/admin.taxonomy.term.$id.tsx",
              lineNumber: 196,
              columnNumber: 35
            }, this))
          ] }, void 0, true, {
            fileName: "app/routes/admin.taxonomy.term.$id.tsx",
            lineNumber: 194,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.taxonomy.term.$id.tsx",
            lineNumber: 193,
            columnNumber: 9
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.taxonomy.term.$id.tsx",
          lineNumber: 189,
          columnNumber: 8
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "Description", className: "text-lg font-bold", children: "Description" }, void 0, false, {
            fileName: "app/routes/admin.taxonomy.term.$id.tsx",
            lineNumber: 203,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("textarea", { id: "Description", name: "description", rows: 4, defaultValue: term.description || "", className: "block w-full rounded-[5px] px-5 py-2.5 text-lg outline-0" }, void 0, false, {
            fileName: "app/routes/admin.taxonomy.term.$id.tsx",
            lineNumber: 207,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.taxonomy.term.$id.tsx",
            lineNumber: 206,
            columnNumber: 9
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.taxonomy.term.$id.tsx",
          lineNumber: 202,
          columnNumber: 8
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "Header", className: "text-lg font-bold", children: "Header" }, void 0, false, {
            fileName: "app/routes/admin.taxonomy.term.$id.tsx",
            lineNumber: 211,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "Header", name: "header", type: "text", defaultValue: term.header || "", className: "block h-[50px] w-full rounded-[5px] px-5 text-lg outline-0" }, void 0, false, {
            fileName: "app/routes/admin.taxonomy.term.$id.tsx",
            lineNumber: 215,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.taxonomy.term.$id.tsx",
            lineNumber: 214,
            columnNumber: 9
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.taxonomy.term.$id.tsx",
          lineNumber: 210,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.taxonomy.term.$id.tsx",
        lineNumber: 146,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.taxonomy.term.$id.tsx",
        lineNumber: 145,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "my-10", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white hover:bg-orange-800 focus:bg-orange-800", children: "Save" }, void 0, false, {
          fileName: "app/routes/admin.taxonomy.term.$id.tsx",
          lineNumber: 222,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: `/admin/taxonomy/${term.taxonomy_machine_name}`, className: "ml-4 inline-block rounded-full bg-gray-500 px-5 py-2 text-lg text-white hover:bg-gray-600 focus:bg-gray-600", children: "Cancel" }, void 0, false, {
          fileName: "app/routes/admin.taxonomy.term.$id.tsx",
          lineNumber: 225,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.taxonomy.term.$id.tsx",
        lineNumber: 221,
        columnNumber: 6
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.taxonomy.term.$id.tsx",
      lineNumber: 140,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/admin.taxonomy.term.$id.tsx",
      lineNumber: 139,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.taxonomy.term.$id.tsx",
    lineNumber: 137,
    columnNumber: 10
  }, this);
}
_s(TaxonomyFormPage, "MMfRsz3MmAm37DUS5hft69hkHI0=", false, function() {
  return [useActionData, useLoaderData];
});
_c = TaxonomyFormPage;
var _c;
$RefreshReg$(_c, "TaxonomyFormPage");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  TaxonomyFormPage as default
};
//# sourceMappingURL=/build/routes/admin.taxonomy.term.$id-SXXZP2FG.js.map
