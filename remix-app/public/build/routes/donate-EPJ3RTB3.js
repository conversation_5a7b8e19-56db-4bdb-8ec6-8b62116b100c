import {
  Title
} from "/build/_shared/chunk-YPQMQ54K.js";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>
} from "/build/_shared/chunk-4556QKWU.js";
import "/build/_shared/chunk-W7JLG7II.js";
import "/build/_shared/chunk-OHIZ2QAR.js";
import "/build/_shared/chunk-DFTVPORL.js";
import {
  Link,
  useLoaderData
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/donate.tsx
var import_react2 = __toESM(require_react(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/donate.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/donate.tsx"
  );
  import.meta.hot.lastModified = "1746467939009.6775";
}
function Donate() {
  _s();
  const data = useLoaderData();
  const donateButtonRef = (0, import_react2.useRef)(null);
  (0, import_react2.useEffect)(() => {
    const scriptId = "givebutter-script";
    if (!document.getElementById(scriptId)) {
      var script = document.createElement("script");
      script.id = scriptId;
      script.async = true;
      script.src = "https://widgets.givebutter.com/latest.umd.cjs?acct=gBBbyIcZHZKShuX8&p=other";
      document.head.appendChild(script);
    }
    const widget = document.createElement("givebutter-widget");
    widget.id = "LWGGJL";
    if (donateButtonRef.current) {
      donateButtonRef.current.appendChild(widget);
    }
    return () => {
      if (donateButtonRef.current) {
        donateButtonRef.current.removeChild(widget);
      }
      const script2 = document.getElementById(scriptId);
      if (script2) {
        document.head.removeChild(script2);
      }
    };
  }, []);
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "min-h-screen", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Header, {}, void 0, false, {
      fileName: "app/routes/donate.tsx",
      lineNumber: 71,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Title, { title: "Donate to Gun Violence Archive" }, void 0, false, {
      fileName: "app/routes/donate.tsx",
      lineNumber: 72,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex flex-col items-center", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "max-w-[1000px] text-center indent-8 font-bold text-4xl md:text-[50px] md:leading-[60px]", children: "Thank you for your consideration to provide a financial contribution to the Gun Violence Archive." }, void 0, false, {
            fileName: "app/routes/donate.tsx",
            lineNumber: 76,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-5 max-w-[1000px] text-center text-[25px] leading-[35px]", children: "Gun Violence Archive is a nonprofit, 501c3 corporation, tax ID# 46-3582959. Contributions or gifts to GVA are tax-deductible." }, void 0, false, {
            fileName: "app/routes/donate.tsx",
            lineNumber: 80,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-5 max-w-[1000px] text-center text-[25px] leading-[35px]", children: [
            "Please",
            " ",
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "/contact", className: "text-blue-500", children: "contact us" }, void 0, false, {
              fileName: "app/routes/donate.tsx",
              lineNumber: 86,
              columnNumber: 8
            }, this),
            " ",
            "if you have contribution questions."
          ] }, void 0, true, {
            fileName: "app/routes/donate.tsx",
            lineNumber: 84,
            columnNumber: 7
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/donate.tsx",
          lineNumber: 75,
          columnNumber: 6
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-center", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { ref: donateButtonRef, className: "mt-10 w-full max-w-md" }, void 0, false, {
          fileName: "app/routes/donate.tsx",
          lineNumber: 93,
          columnNumber: 7
        }, this) }, void 0, false, {
          fileName: "app/routes/donate.tsx",
          lineNumber: 92,
          columnNumber: 6
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/donate.tsx",
        lineNumber: 74,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-gray-200", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid gap-10 text-lg sm:grid-cols-3", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-xl font-bold", children: "Online Donations" }, void 0, false, {
            fileName: "app/routes/donate.tsx",
            lineNumber: 100,
            columnNumber: 9
          }, this),
          "Gun Violence Archive uses a platform called Givebutter for donations. Givebutter uses the world's best payment encryption technology and security."
        ] }, void 0, true, {
          fileName: "app/routes/donate.tsx",
          lineNumber: 99,
          columnNumber: 8
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-xl font-bold", children: "Check" }, void 0, false, {
            fileName: "app/routes/donate.tsx",
            lineNumber: 105,
            columnNumber: 9
          }, this),
          "Please mail your donation checks to:",
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("br", {}, void 0, false, {
            fileName: "app/routes/donate.tsx",
            lineNumber: 107,
            columnNumber: 9
          }, this),
          "Gun Violence Archive",
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("br", {}, void 0, false, {
            fileName: "app/routes/donate.tsx",
            lineNumber: 109,
            columnNumber: 9
          }, this),
          "PO Box 22221",
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("br", {}, void 0, false, {
            fileName: "app/routes/donate.tsx",
            lineNumber: 111,
            columnNumber: 9
          }, this),
          "Lexington, KY 40522-2221"
        ] }, void 0, true, {
          fileName: "app/routes/donate.tsx",
          lineNumber: 104,
          columnNumber: 8
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-xl font-bold", children: "Bank Transfer" }, void 0, false, {
            fileName: "app/routes/donate.tsx",
            lineNumber: 115,
            columnNumber: 9
          }, this),
          "Bank transfers are an option for supporter contributions. Please",
          " ",
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "/contact", className: "text-blue-500", children: "contact us" }, void 0, false, {
            fileName: "app/routes/donate.tsx",
            lineNumber: 117,
            columnNumber: 9
          }, this),
          " ",
          "directly on how to use ACH for your donations to GVA."
        ] }, void 0, true, {
          fileName: "app/routes/donate.tsx",
          lineNumber: 114,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/donate.tsx",
        lineNumber: 98,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/donate.tsx",
        lineNumber: 97,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/donate.tsx",
        lineNumber: 96,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/donate.tsx",
      lineNumber: 73,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Footer, {}, void 0, false, {
      fileName: "app/routes/donate.tsx",
      lineNumber: 126,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/donate.tsx",
    lineNumber: 70,
    columnNumber: 10
  }, this);
}
_s(Donate, "dzL6izIqUOdsI/HYr4yZLCp8QhU=", false, function() {
  return [useLoaderData];
});
_c = Donate;
var _c;
$RefreshReg$(_c, "Donate");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  Donate as default
};
//# sourceMappingURL=/build/routes/donate-EPJ3RTB3.js.map
