import "/build/_shared/chunk-AUYLHJJM.js";
import {
  require_config
} from "/build/_shared/chunk-3PRZVPKO.js";
import {
  Icon_Admin_Delete_default
} from "/build/_shared/chunk-RUOV274T.js";
import {
  require_cookies
} from "/build/_shared/chunk-MUAEGCBW.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import "/build/_shared/chunk-DFTVPORL.js";
import {
  Form,
  Link,
  isRouteErrorResponse,
  useActionData,
  useLoaderData,
  useRouteError
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import "/build/_shared/chunk-QT64XSGC.js";
import {
  require_node
} from "/build/_shared/chunk-ZP6BZTHN.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/admin.configuration.$id.tsx
var import_node = __toESM(require_node(), 1);
var import_config = __toESM(require_config(), 1);
var import_cookies = __toESM(require_cookies(), 1);
var import_auth = __toESM(require_auth(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.configuration.$id.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
var _s2 = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.configuration.$id.tsx"
  );
  import.meta.hot.lastModified = "1748456608108.736";
}
function ConfigurationFormPage() {
  _s();
  const actionData = useActionData();
  const {
    config
  } = useLoaderData();
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { className: "container mx-auto pb-10 pt-8", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[25px] font-bold", children: "Manage Configurations" }, void 0, false, {
      fileName: "app/routes/admin.configuration.$id.tsx",
      lineNumber: 110,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-8", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { method: "post", children: [
      actionData && "error" in actionData && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-5 flex border border-red-500 bg-white px-5 py-3.5 text-sm font-light", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Icon_Admin_Delete_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/routes/admin.configuration.$id.tsx",
          lineNumber: 114,
          columnNumber: 9
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "pl-2 text-red-500", children: actionData.error }, void 0, false, {
          fileName: "app/routes/admin.configuration.$id.tsx",
          lineNumber: 115,
          columnNumber: 9
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.configuration.$id.tsx",
        lineNumber: 113,
        columnNumber: 46
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[5px] bg-gray-200 p-10", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid grid-cols-1 gap-x-10 gap-y-8", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "Key", className: "text-lg font-bold after:ml-0.5 after:text-orange-500 after:content-['*']", children: "Key" }, void 0, false, {
            fileName: "app/routes/admin.configuration.$id.tsx",
            lineNumber: 120,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "Key", name: "key", required: true, type: "text", disabled: !!config.id, defaultValue: config.key, className: "block h-[50px] w-full rounded-[5px] px-5 text-lg outline-0" }, void 0, false, {
            fileName: "app/routes/admin.configuration.$id.tsx",
            lineNumber: 124,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.configuration.$id.tsx",
            lineNumber: 123,
            columnNumber: 9
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.configuration.$id.tsx",
          lineNumber: 119,
          columnNumber: 8
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "Values", className: "text-lg font-bold", children: "Values" }, void 0, false, {
            fileName: "app/routes/admin.configuration.$id.tsx",
            lineNumber: 128,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("textarea", { id: "Values", name: "values", rows: 4, defaultValue: config.values || "", className: "block w-full rounded-[5px] px-5 py-2.5 text-lg outline-0" }, void 0, false, {
            fileName: "app/routes/admin.configuration.$id.tsx",
            lineNumber: 132,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.configuration.$id.tsx",
            lineNumber: 131,
            columnNumber: 9
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.configuration.$id.tsx",
          lineNumber: 127,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.configuration.$id.tsx",
        lineNumber: 118,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.configuration.$id.tsx",
        lineNumber: 117,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "my-10", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white hover:bg-orange-800 focus:bg-orange-800", children: "Save" }, void 0, false, {
          fileName: "app/routes/admin.configuration.$id.tsx",
          lineNumber: 139,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "/admin/configuration", className: "ml-4 inline-block rounded-full bg-gray-500 px-5 py-2 text-lg text-white hover:bg-gray-600 focus:bg-gray-600", children: "Cancel" }, void 0, false, {
          fileName: "app/routes/admin.configuration.$id.tsx",
          lineNumber: 142,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.configuration.$id.tsx",
        lineNumber: 138,
        columnNumber: 6
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.configuration.$id.tsx",
      lineNumber: 112,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/admin.configuration.$id.tsx",
      lineNumber: 111,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.configuration.$id.tsx",
    lineNumber: 109,
    columnNumber: 10
  }, this);
}
_s(ConfigurationFormPage, "nfo4QTKDgcpmMUINJUCt5K0sWwU=", false, function() {
  return [useActionData, useLoaderData];
});
_c = ConfigurationFormPage;
function ErrorBoundary() {
  _s2();
  const error = useRouteError();
  if (error instanceof Error) {
    return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
      "An unexpected error occurred: ",
      error.message
    ] }, void 0, true, {
      fileName: "app/routes/admin.configuration.$id.tsx",
      lineNumber: 158,
      columnNumber: 12
    }, this);
  }
  if (!isRouteErrorResponse(error)) {
    return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h1", { children: "Unknown Error" }, void 0, false, {
      fileName: "app/routes/admin.configuration.$id.tsx",
      lineNumber: 161,
      columnNumber: 12
    }, this);
  }
  if (error.status === 404) {
    return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: "Configuration not found" }, void 0, false, {
      fileName: "app/routes/admin.configuration.$id.tsx",
      lineNumber: 164,
      columnNumber: 12
    }, this);
  }
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
    "An unexpected error occurred: ",
    error.statusText
  ] }, void 0, true, {
    fileName: "app/routes/admin.configuration.$id.tsx",
    lineNumber: 166,
    columnNumber: 10
  }, this);
}
_s2(ErrorBoundary, "oAgjgbJzsRXlB89+MoVumxMQqKM=", false, function() {
  return [useRouteError];
});
_c2 = ErrorBoundary;
var _c;
var _c2;
$RefreshReg$(_c, "ConfigurationFormPage");
$RefreshReg$(_c2, "ErrorBoundary");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  ErrorBoundary,
  ConfigurationFormPage as default
};
//# sourceMappingURL=/build/routes/admin.configuration.$id-DPLQPJOP.js.map
