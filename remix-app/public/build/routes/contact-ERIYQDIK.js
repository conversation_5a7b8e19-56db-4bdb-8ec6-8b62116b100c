import {
  require_jsx_runtime
} from "/build/_shared/chunk-5NT3OUFA.js";
import {
  Icon_Success_default
} from "/build/_shared/chunk-GBO3FEGG.js";
import {
  require_cloudflare
} from "/build/_shared/chunk-WMMS5YEM.js";
import {
  Title
} from "/build/_shared/chunk-YPQMQ54K.js";
import {
  <PERSON><PERSON>,
  <PERSON>er
} from "/build/_shared/chunk-4556QKWU.js";
import "/build/_shared/chunk-W7JLG7II.js";
import "/build/_shared/chunk-OHIZ2QAR.js";
import "/build/_shared/chunk-DFTVPORL.js";
import {
  Form,
  Link,
  useActionData,
  useLoaderData,
  useNavigation
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __commonJS,
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// empty-module:~/utils/mail.server
var require_mail = __commonJS({
  "empty-module:~/utils/mail.server"(exports, module) {
    module.exports = {};
  }
});

// app/routes/contact.tsx
var import_react2 = __toESM(require_react(), 1);

// node_modules/@marsidev/react-turnstile/dist/index.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
"use client";
var Component = ({ as: Element = "div", ...props }, ref) => {
  return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(Element, { ...props, ref });
};
var Container = (0, import_react.forwardRef)(Component);
var SCRIPT_URL = "https://challenges.cloudflare.com/turnstile/v0/api.js";
var DEFAULT_SCRIPT_ID = "cf-turnstile-script";
var DEFAULT_CONTAINER_ID = "cf-turnstile";
var DEFAULT_ONLOAD_NAME = "onloadTurnstileCallback";
var checkElementExistence = (id) => !!document.getElementById(id);
var injectTurnstileScript = ({
  render = "explicit",
  onLoadCallbackName = DEFAULT_ONLOAD_NAME,
  scriptOptions: {
    nonce = "",
    defer = true,
    async = true,
    id = "",
    appendTo,
    onError,
    crossOrigin = ""
  } = {}
}) => {
  const scriptId = id || DEFAULT_SCRIPT_ID;
  if (checkElementExistence(scriptId)) {
    return;
  }
  const script = document.createElement("script");
  script.id = scriptId;
  script.src = `${SCRIPT_URL}?onload=${onLoadCallbackName}&render=${render}`;
  if (document.querySelector(`script[src="${script.src}"]`)) {
    return;
  }
  script.defer = !!defer;
  script.async = !!async;
  if (nonce) {
    script.nonce = nonce;
  }
  if (crossOrigin) {
    script.crossOrigin = crossOrigin;
  }
  if (onError) {
    script.onerror = onError;
  }
  const parentEl = appendTo === "body" ? document.body : document.getElementsByTagName("head")[0];
  parentEl.appendChild(script);
};
var CONTAINER_STYLE_SET = {
  normal: {
    width: 300,
    height: 65
  },
  compact: {
    width: 130,
    height: 120
  },
  invisible: {
    width: 0,
    height: 0,
    overflow: "hidden"
  },
  interactionOnly: {
    width: "fit-content",
    height: "auto"
  }
};
function getTurnstileSizeOpts(size) {
  let result;
  if (size !== "invisible") {
    result = size;
  }
  return result;
}
function useObserveScript(scriptId = DEFAULT_SCRIPT_ID) {
  const [scriptLoaded, setScriptLoaded] = (0, import_react.useState)(false);
  (0, import_react.useEffect)(() => {
    const checkScriptExists = () => {
      if (checkElementExistence(scriptId)) {
        setScriptLoaded(true);
      }
    };
    const observer = new MutationObserver(checkScriptExists);
    observer.observe(document, { childList: true, subtree: true });
    checkScriptExists();
    return () => {
      observer.disconnect();
    };
  }, [scriptId]);
  return scriptLoaded;
}
var Turnstile = (0, import_react.forwardRef)((props, ref) => {
  const {
    scriptOptions,
    options = {},
    siteKey,
    onWidgetLoad,
    onSuccess,
    onExpire,
    onError,
    onBeforeInteractive,
    onAfterInteractive,
    onUnsupported,
    onLoadScript,
    id,
    style,
    as = "div",
    injectScript = true,
    ...divProps
  } = props;
  const widgetSize = options.size ?? "normal";
  const [containerStyle, setContainerStyle] = (0, import_react.useState)(
    options.execution === "execute" ? CONTAINER_STYLE_SET.invisible : options.appearance === "interaction-only" ? CONTAINER_STYLE_SET.interactionOnly : CONTAINER_STYLE_SET[widgetSize]
  );
  const containerRef = (0, import_react.useRef)(null);
  const firstRendered = (0, import_react.useRef)(false);
  const [widgetId, setWidgetId] = (0, import_react.useState)();
  const [turnstileLoaded, setTurnstileLoaded] = (0, import_react.useState)(false);
  const containerId = id ?? DEFAULT_CONTAINER_ID;
  const scriptId = injectScript ? scriptOptions?.id || `${DEFAULT_SCRIPT_ID}__${containerId}` : scriptOptions?.id || DEFAULT_SCRIPT_ID;
  const scriptLoaded = useObserveScript(scriptId);
  const onLoadCallbackName = scriptOptions?.onLoadCallbackName ? `${scriptOptions.onLoadCallbackName}__${containerId}` : `${DEFAULT_ONLOAD_NAME}__${containerId}`;
  const renderConfig = (0, import_react.useMemo)(
    () => ({
      sitekey: siteKey,
      action: options.action,
      cData: options.cData,
      callback: onSuccess,
      "error-callback": onError,
      "expired-callback": onExpire,
      "before-interactive-callback": onBeforeInteractive,
      "after-interactive-callback": onAfterInteractive,
      "unsupported-callback": onUnsupported,
      theme: options.theme ?? "auto",
      language: options.language ?? "auto",
      tabindex: options.tabIndex,
      "response-field": options.responseField,
      "response-field-name": options.responseFieldName,
      size: getTurnstileSizeOpts(widgetSize),
      retry: options.retry ?? "auto",
      "retry-interval": options.retryInterval ?? 8e3,
      "refresh-expired": options.refreshExpired ?? "auto",
      execution: options.execution ?? "render",
      appearance: options.appearance ?? "always"
    }),
    [
      siteKey,
      options,
      onSuccess,
      onError,
      onExpire,
      widgetSize,
      onBeforeInteractive,
      onAfterInteractive,
      onUnsupported
    ]
  );
  const renderConfigStringified = (0, import_react.useMemo)(() => JSON.stringify(renderConfig), [renderConfig]);
  (0, import_react.useImperativeHandle)(
    ref,
    () => {
      if (typeof window === "undefined" || !scriptLoaded) {
        return;
      }
      const { turnstile } = window;
      return {
        getResponse() {
          if (!turnstile?.getResponse || !widgetId) {
            console.warn("Turnstile has not been loaded");
            return;
          }
          return turnstile.getResponse(widgetId);
        },
        reset() {
          if (!turnstile?.reset || !widgetId) {
            console.warn("Turnstile has not been loaded");
            return;
          }
          if (options.execution === "execute") {
            setContainerStyle(CONTAINER_STYLE_SET.invisible);
          }
          try {
            turnstile.reset(widgetId);
          } catch (error) {
            console.warn(`Failed to reset Turnstile widget ${widgetId}`, error);
          }
        },
        remove() {
          if (!turnstile?.remove || !widgetId) {
            console.warn("Turnstile has not been loaded");
            return;
          }
          setWidgetId("");
          setContainerStyle(CONTAINER_STYLE_SET.invisible);
          turnstile.remove(widgetId);
        },
        render() {
          if (!turnstile?.render || !containerRef.current || widgetId) {
            console.warn("Turnstile has not been loaded or widget already rendered");
            return;
          }
          const id2 = turnstile.render(containerRef.current, renderConfig);
          setWidgetId(id2);
          if (options.execution !== "execute") {
            setContainerStyle(CONTAINER_STYLE_SET[widgetSize]);
          }
          return id2;
        },
        execute() {
          if (options.execution !== "execute") {
            return;
          }
          if (!turnstile?.execute || !containerRef.current || !widgetId) {
            console.warn("Turnstile has not been loaded or widget has not been rendered");
            return;
          }
          turnstile.execute(containerRef.current, renderConfig);
          setContainerStyle(CONTAINER_STYLE_SET[widgetSize]);
        },
        isExpired() {
          if (!turnstile?.isExpired || !widgetId) {
            console.warn("Turnstile has not been loaded");
            return;
          }
          return turnstile.isExpired(widgetId);
        }
      };
    },
    [scriptLoaded, widgetId, options.execution, widgetSize, renderConfig, containerRef]
  );
  (0, import_react.useEffect)(() => {
    window[onLoadCallbackName] = () => setTurnstileLoaded(true);
    return () => {
      delete window[onLoadCallbackName];
    };
  }, [onLoadCallbackName]);
  (0, import_react.useEffect)(() => {
    if (injectScript && !turnstileLoaded) {
      injectTurnstileScript({
        onLoadCallbackName,
        scriptOptions: {
          ...scriptOptions,
          id: scriptId
        }
      });
    }
  }, [injectScript, turnstileLoaded, onLoadCallbackName, scriptOptions, scriptId]);
  (0, import_react.useEffect)(() => {
    if (scriptLoaded && !turnstileLoaded && window.turnstile) {
      setTurnstileLoaded(true);
    }
  }, [turnstileLoaded, scriptLoaded]);
  (0, import_react.useEffect)(() => {
    if (!siteKey) {
      console.warn("sitekey was not provided");
      return;
    }
    if (!scriptLoaded || !containerRef.current || !turnstileLoaded || firstRendered.current) {
      return;
    }
    const id2 = window.turnstile.render(containerRef.current, renderConfig);
    setWidgetId(id2);
    firstRendered.current = true;
  }, [scriptLoaded, siteKey, renderConfig, firstRendered, turnstileLoaded]);
  (0, import_react.useEffect)(() => {
    if (!window.turnstile)
      return;
    if (containerRef.current && widgetId) {
      if (checkElementExistence(widgetId)) {
        window.turnstile.remove(widgetId);
      }
      const newWidgetId = window.turnstile.render(containerRef.current, renderConfig);
      setWidgetId(newWidgetId);
      firstRendered.current = true;
    }
  }, [renderConfigStringified, siteKey]);
  (0, import_react.useEffect)(() => {
    if (!window.turnstile)
      return;
    if (!widgetId)
      return;
    if (!checkElementExistence(widgetId))
      return;
    onWidgetLoad?.(widgetId);
    return () => {
      window.turnstile.remove(widgetId);
    };
  }, [widgetId, onWidgetLoad]);
  (0, import_react.useEffect)(() => {
    setContainerStyle(
      options.execution === "execute" ? CONTAINER_STYLE_SET.invisible : renderConfig.appearance === "interaction-only" ? CONTAINER_STYLE_SET.interactionOnly : CONTAINER_STYLE_SET[widgetSize]
    );
  }, [options.execution, widgetSize, renderConfig.appearance]);
  (0, import_react.useEffect)(() => {
    if (!scriptLoaded || typeof onLoadScript !== "function")
      return;
    onLoadScript();
  }, [scriptLoaded, onLoadScript]);
  return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
    Container,
    {
      ref: containerRef,
      as,
      id: containerId,
      style: { ...containerStyle, ...style },
      ...divProps
    }
  );
});
Turnstile.displayName = "Turnstile";

// app/routes/contact.tsx
var import_cloudflare = __toESM(require_cloudflare(), 1);
var import_mail = __toESM(require_mail(), 1);

// app/images/Icon-Contact-Mail-Address.svg
var Icon_Contact_Mail_Address_default = "/build/_assets/Icon-Contact-Mail-Address-6N2ID4VC.svg";

// app/images/Icon-Contact-Email-Address.svg
var Icon_Contact_Email_Address_default = "/build/_assets/Icon-Contact-Email-Address-KCQPOQWE.svg";

// app/images/Icon-Contact-Social-Media.svg
var Icon_Contact_Social_Media_default = "/build/_assets/Icon-Contact-Social-Media-ZRAH7RR7.svg";

// app/routes/contact.tsx
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/contact.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/contact.tsx"
  );
  import.meta.hot.lastModified = "1746467939009.517";
}
function Contact() {
  _s();
  const [canSubmit, setCanSubmit] = (0, import_react2.useState)(false);
  const data = useLoaderData();
  const actionData = useActionData();
  const navigation = useNavigation();
  const isSending = navigation.formAction === "/contact";
  const formRef = (0, import_react2.useRef)(null);
  const turnstileRef = (0, import_react2.useRef)(null);
  (0, import_react2.useEffect)(() => {
    if (actionData?.result && !canSubmit) {
      formRef.current?.reset();
    }
  }, [actionData?.result, canSubmit]);
  (0, import_react2.useEffect)(() => {
    if (!isSending) {
      turnstileRef.current?.reset();
      setCanSubmit(false);
    }
  }, [isSending]);
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "min-h-screen", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Header, {}, void 0, false, {
      fileName: "app/routes/contact.tsx",
      lineNumber: 105,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Title, { title: "Contact Gun Violence Archive" }, void 0, false, {
      fileName: "app/routes/contact.tsx",
      lineNumber: 106,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "md:flex md:space-x-10", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "basis-1/4", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "sm:flex sm:flex-row md:flex-col md:space-y-8", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-4 sm:basis-1/3 md:mb-0 md:basis-full", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Icon_Contact_Mail_Address_default, alt: "", width: 60 }, void 0, false, {
            fileName: "app/routes/contact.tsx",
            lineNumber: 113,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3 text-lg", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "font-bold", children: "Mail Address" }, void 0, false, {
              fileName: "app/routes/contact.tsx",
              lineNumber: 115,
              columnNumber: 11
            }, this),
            "Gun Violence Archive",
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("br", {}, void 0, false, {
              fileName: "app/routes/contact.tsx",
              lineNumber: 117,
              columnNumber: 11
            }, this),
            "PO Box 22221",
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("br", {}, void 0, false, {
              fileName: "app/routes/contact.tsx",
              lineNumber: 119,
              columnNumber: 11
            }, this),
            "Lexington, KY 40522-2221"
          ] }, void 0, true, {
            fileName: "app/routes/contact.tsx",
            lineNumber: 114,
            columnNumber: 10
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/contact.tsx",
          lineNumber: 112,
          columnNumber: 9
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-4 sm:basis-1/3 md:mb-0 md:basis-full", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Icon_Contact_Email_Address_default, alt: "", width: 60 }, void 0, false, {
            fileName: "app/routes/contact.tsx",
            lineNumber: 124,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3 text-lg", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "font-bold", children: "Email Address" }, void 0, false, {
              fileName: "app/routes/contact.tsx",
              lineNumber: 126,
              columnNumber: 11
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { className: "block text-blue-500", href: "mailto:<EMAIL>", children: "<EMAIL>" }, void 0, false, {
              fileName: "app/routes/contact.tsx",
              lineNumber: 127,
              columnNumber: 11
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/contact.tsx",
            lineNumber: 125,
            columnNumber: 10
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/contact.tsx",
          lineNumber: 123,
          columnNumber: 9
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-4 sm:basis-1/3 md:mb-0 md:basis-full", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Icon_Contact_Social_Media_default, alt: "", width: 60 }, void 0, false, {
            fileName: "app/routes/contact.tsx",
            lineNumber: 133,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3 text-lg", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "font-bold", children: "Social Media" }, void 0, false, {
              fileName: "app/routes/contact.tsx",
              lineNumber: 135,
              columnNumber: 11
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "block text-blue-500", to: `https://twitter.com/gundeaths`, target: "_blank", children: "X / Twitter" }, void 0, false, {
              fileName: "app/routes/contact.tsx",
              lineNumber: 136,
              columnNumber: 11
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "block text-blue-500", to: `https://facebook.com/gunviolencearchive`, target: "_blank", children: "Facebook" }, void 0, false, {
              fileName: "app/routes/contact.tsx",
              lineNumber: 139,
              columnNumber: 11
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/contact.tsx",
            lineNumber: 134,
            columnNumber: 10
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/contact.tsx",
          lineNumber: 132,
          columnNumber: 9
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/contact.tsx",
        lineNumber: 111,
        columnNumber: 8
      }, this) }, void 0, false, {
        fileName: "app/routes/contact.tsx",
        lineNumber: 110,
        columnNumber: 7
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "basis-3/4", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] bg-gray-200 p-4 xl:p-14", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-6 text-lg", children: "Please complete and submit the web form below. All form elements are required. A Gun Violence Archive representative will respond as quickly as possible." }, void 0, false, {
          fileName: "app/routes/contact.tsx",
          lineNumber: 148,
          columnNumber: 9
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { id: "contact-form", method: "post", ref: formRef, children: [
          actionData?.result && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-5 bg-white px-5 py-3.5 text-sm font-light", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "inline-block align-middle", src: Icon_Success_default, alt: "", width: 30 }, void 0, false, {
              fileName: "app/routes/contact.tsx",
              lineNumber: 154,
              columnNumber: 12
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "inline-block pl-2", children: "Contact form was submitted!" }, void 0, false, {
              fileName: "app/routes/contact.tsx",
              lineNumber: 155,
              columnNumber: 12
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/contact.tsx",
            lineNumber: 153,
            columnNumber: 33
          }, this),
          actionData?.errors && Object.keys(actionData?.errors).length > 0 && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-5 bg-white px-5 py-3.5 text-sm font-light", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "inline-block pl-2 text-red-500", children: Object.values(actionData.errors).map((error, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: error }, `error-${index}`, false, {
            fileName: "app/routes/contact.tsx",
            lineNumber: 160,
            columnNumber: 69
          }, this)) }, void 0, false, {
            fileName: "app/routes/contact.tsx",
            lineNumber: 159,
            columnNumber: 12
          }, this) }, void 0, false, {
            fileName: "app/routes/contact.tsx",
            lineNumber: 157,
            columnNumber: 79
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid gap-x-4 gap-y-8 lg:grid-cols-3 xl:gap-x-10", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "name", className: "mb-3 block text-lg font-bold", children: "First & Last Name" }, void 0, false, {
                fileName: "app/routes/contact.tsx",
                lineNumber: 165,
                columnNumber: 12
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "name", name: "name", type: "text", required: true, className: "block w-full rounded-md px-3 py-2 leading-loose" }, void 0, false, {
                fileName: "app/routes/contact.tsx",
                lineNumber: 168,
                columnNumber: 12
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/contact.tsx",
              lineNumber: 164,
              columnNumber: 11
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "email", className: "mb-3 block text-lg font-bold", children: "Email Address" }, void 0, false, {
                fileName: "app/routes/contact.tsx",
                lineNumber: 171,
                columnNumber: 12
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "email", name: "email", type: "email", required: true, className: "block w-full rounded-md px-3 py-2 leading-loose" }, void 0, false, {
                fileName: "app/routes/contact.tsx",
                lineNumber: 174,
                columnNumber: 12
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/contact.tsx",
              lineNumber: 170,
              columnNumber: 11
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "organization", className: "mb-3 block text-lg font-bold", children: "Organization" }, void 0, false, {
                fileName: "app/routes/contact.tsx",
                lineNumber: 177,
                columnNumber: 12
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "organization", name: "organization", type: "text", required: true, className: "block w-full rounded-md px-3 py-2 leading-loose" }, void 0, false, {
                fileName: "app/routes/contact.tsx",
                lineNumber: 180,
                columnNumber: 12
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/contact.tsx",
              lineNumber: 176,
              columnNumber: 11
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "col-span-full", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "comments", className: "mb-3 block text-lg font-bold", children: "Comments" }, void 0, false, {
                fileName: "app/routes/contact.tsx",
                lineNumber: 183,
                columnNumber: 12
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("textarea", { id: "comments", name: "comments", rows: 5, className: "block w-full rounded-md px-3 py-2" }, void 0, false, {
                fileName: "app/routes/contact.tsx",
                lineNumber: 186,
                columnNumber: 12
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/contact.tsx",
              lineNumber: 182,
              columnNumber: 11
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "col-span-full", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Turnstile, { ref: turnstileRef, siteKey: data.cfTurnstileSiteKey, onSuccess: () => setCanSubmit(true), onExpire: () => {
              setCanSubmit(false);
              turnstileRef.current?.reset();
            } }, void 0, false, {
              fileName: "app/routes/contact.tsx",
              lineNumber: 189,
              columnNumber: 12
            }, this) }, void 0, false, {
              fileName: "app/routes/contact.tsx",
              lineNumber: 188,
              columnNumber: 11
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "col-span-full", children: isSending ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "button", disabled: true, className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white", children: "Sending" }, void 0, false, {
              fileName: "app/routes/contact.tsx",
              lineNumber: 207,
              columnNumber: 25
            }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", disabled: canSubmit ? false : true, className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white", children: "Send" }, void 0, false, {
              fileName: "app/routes/contact.tsx",
              lineNumber: 209,
              columnNumber: 25
            }, this) }, void 0, false, {
              fileName: "app/routes/contact.tsx",
              lineNumber: 206,
              columnNumber: 11
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/contact.tsx",
            lineNumber: 163,
            columnNumber: 10
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/contact.tsx",
          lineNumber: 152,
          columnNumber: 9
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/contact.tsx",
        lineNumber: 147,
        columnNumber: 8
      }, this) }, void 0, false, {
        fileName: "app/routes/contact.tsx",
        lineNumber: 146,
        columnNumber: 7
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/contact.tsx",
      lineNumber: 109,
      columnNumber: 6
    }, this) }, void 0, false, {
      fileName: "app/routes/contact.tsx",
      lineNumber: 108,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/contact.tsx",
      lineNumber: 107,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Footer, {}, void 0, false, {
      fileName: "app/routes/contact.tsx",
      lineNumber: 220,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/contact.tsx",
    lineNumber: 104,
    columnNumber: 10
  }, this);
}
_s(Contact, "UjJMbeDPtfBcOPruIOMGsHSWmMA=", false, function() {
  return [useLoaderData, useActionData, useNavigation];
});
_c = Contact;
var _c;
$RefreshReg$(_c, "Contact");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  Contact as default
};
//# sourceMappingURL=/build/routes/contact-ERIYQDIK.js.map
