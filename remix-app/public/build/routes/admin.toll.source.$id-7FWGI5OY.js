import "/build/_shared/chunk-AUYLHJJM.js";
import {
  require_cookies
} from "/build/_shared/chunk-MUAEGCBW.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import "/build/_shared/chunk-DFTVPORL.js";
import {
  Form,
  Link,
  isRouteErrorResponse,
  useLoaderData,
  useRouteError
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  require_node
} from "/build/_shared/chunk-ZP6BZTHN.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/admin.toll.source.$id.tsx
var import_react = __toESM(require_react(), 1);
var import_node = __toESM(require_node(), 1);
var import_cookies = __toESM(require_cookies(), 1);
var import_auth = __toESM(require_auth(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.toll.source.$id.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
var _s2 = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.toll.source.$id.tsx"
  );
  import.meta.hot.lastModified = "1748456987358.9348";
}
function TollSourceFormPage() {
  _s();
  const {
    source,
    modificationOptions
  } = useLoaderData();
  const [type, setType] = (0, import_react.useState)(source.type);
  const [year, setYear] = (0, import_react.useState)(source.year);
  const endYear = (/* @__PURE__ */ new Date()).getFullYear() + 5;
  const yearOptions = Array(endYear - 2014 + 1).fill(2014).map((e, i) => 2014 + i);
  const typeOptions = ["National", "State", "City"];
  const ledgerOptions = year < 2019 ? Object.entries(modificationOptions.past) : Object.entries(modificationOptions.new);
  const modification = source.modification ? JSON.parse(source.modification) : {};
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { className: "container mx-auto pb-10 pt-8", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[25px] font-bold", children: "Manage Toll" }, void 0, false, {
      fileName: "app/routes/admin.toll.source.$id.tsx",
      lineNumber: 127,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-8", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { method: "post", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[5px] bg-gray-200 p-10", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid grid-cols-4 gap-10", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "Title", className: "text-lg font-bold after:ml-0.5 after:text-orange-500 after:content-['*']", children: "Title" }, void 0, false, {
              fileName: "app/routes/admin.toll.source.$id.tsx",
              lineNumber: 133,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "Title", name: "title", required: true, type: "text", defaultValue: source.title, className: "block h-[50px] w-full rounded-[5px] px-5 text-lg outline-0" }, void 0, false, {
              fileName: "app/routes/admin.toll.source.$id.tsx",
              lineNumber: 137,
              columnNumber: 10
            }, this) }, void 0, false, {
              fileName: "app/routes/admin.toll.source.$id.tsx",
              lineNumber: 136,
              columnNumber: 9
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.toll.source.$id.tsx",
            lineNumber: 132,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "Year", className: "text-lg font-bold after:ml-0.5 after:text-orange-500 after:content-['*']", children: "Year" }, void 0, false, {
              fileName: "app/routes/admin.toll.source.$id.tsx",
              lineNumber: 141,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("select", { id: "Year", name: "year", required: true, defaultValue: source.year, onChange: (e) => setYear(Number(e.target.value)), className: "block h-[50px] w-full rounded-[5px] px-5 text-lg outline-0", children: yearOptions.map((y) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("option", { value: y, children: y }, `year-option-${y}`, false, {
              fileName: "app/routes/admin.toll.source.$id.tsx",
              lineNumber: 146,
              columnNumber: 33
            }, this)) }, void 0, false, {
              fileName: "app/routes/admin.toll.source.$id.tsx",
              lineNumber: 145,
              columnNumber: 10
            }, this) }, void 0, false, {
              fileName: "app/routes/admin.toll.source.$id.tsx",
              lineNumber: 144,
              columnNumber: 9
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.toll.source.$id.tsx",
            lineNumber: 140,
            columnNumber: 8
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.toll.source.$id.tsx",
          lineNumber: 131,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-8 grid grid-cols-4 gap-10", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "Type", className: "text-lg font-bold after:ml-0.5 after:text-orange-500 after:content-['*']", children: "Type" }, void 0, false, {
              fileName: "app/routes/admin.toll.source.$id.tsx",
              lineNumber: 155,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("select", { id: "Type", name: "type", required: true, value: type, onChange: (e) => setType(e.target.value), className: "block h-[50px] w-full rounded-[5px] px-5 text-lg outline-0", children: typeOptions.map((t) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("option", { value: t, children: t }, `type-option-${t}`, false, {
              fileName: "app/routes/admin.toll.source.$id.tsx",
              lineNumber: 160,
              columnNumber: 33
            }, this)) }, void 0, false, {
              fileName: "app/routes/admin.toll.source.$id.tsx",
              lineNumber: 159,
              columnNumber: 10
            }, this) }, void 0, false, {
              fileName: "app/routes/admin.toll.source.$id.tsx",
              lineNumber: 158,
              columnNumber: 9
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.toll.source.$id.tsx",
            lineNumber: 154,
            columnNumber: 8
          }, this),
          (type == "State" || type == "City") && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "State", className: "text-lg font-bold", children: "State" }, void 0, false, {
              fileName: "app/routes/admin.toll.source.$id.tsx",
              lineNumber: 167,
              columnNumber: 10
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "State", name: "state", type: "text", defaultValue: source.state || "", className: "block h-[50px] w-full rounded-[5px] px-5 text-lg outline-0" }, void 0, false, {
              fileName: "app/routes/admin.toll.source.$id.tsx",
              lineNumber: 171,
              columnNumber: 11
            }, this) }, void 0, false, {
              fileName: "app/routes/admin.toll.source.$id.tsx",
              lineNumber: 170,
              columnNumber: 10
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.toll.source.$id.tsx",
            lineNumber: 166,
            columnNumber: 48
          }, this),
          type == "City" && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "City", className: "text-lg font-bold", children: "City" }, void 0, false, {
              fileName: "app/routes/admin.toll.source.$id.tsx",
              lineNumber: 175,
              columnNumber: 10
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "City", name: "city", type: "text", defaultValue: source.city || "", className: "block h-[50px] w-full rounded-[5px] px-5 text-lg outline-0" }, void 0, false, {
              fileName: "app/routes/admin.toll.source.$id.tsx",
              lineNumber: 179,
              columnNumber: 11
            }, this) }, void 0, false, {
              fileName: "app/routes/admin.toll.source.$id.tsx",
              lineNumber: 178,
              columnNumber: 10
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.toll.source.$id.tsx",
            lineNumber: 174,
            columnNumber: 27
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.toll.source.$id.tsx",
          lineNumber: 153,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.toll.source.$id.tsx",
        lineNumber: 130,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-10 rounded-[5px] bg-gray-200 p-10", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid grid-cols-4 gap-10", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { className: "text-lg font-bold", children: "Ledger Item" }, void 0, false, {
            fileName: "app/routes/admin.toll.source.$id.tsx",
            lineNumber: 188,
            columnNumber: 9
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.toll.source.$id.tsx",
            lineNumber: 187,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { className: "text-lg font-bold", children: "Modification" }, void 0, false, {
            fileName: "app/routes/admin.toll.source.$id.tsx",
            lineNumber: 191,
            columnNumber: 9
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.toll.source.$id.tsx",
            lineNumber: 190,
            columnNumber: 8
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.toll.source.$id.tsx",
          lineNumber: 186,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3 flex flex-col space-y-5", children: ledgerOptions.map(([key, title]) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid grid-cols-4 gap-10", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: `modification-value-${key}`, dangerouslySetInnerHTML: {
            __html: title
          } }, void 0, false, {
            fileName: "app/routes/admin.toll.source.$id.tsx",
            lineNumber: 197,
            columnNumber: 11
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: `modification-value-${key}`, name: `modification[${key}]`, type: "number", defaultValue: modification[key] || "", className: "block h-[40px] w-full rounded-[5px] px-5 text-lg outline-0" }, void 0, false, {
            fileName: "app/routes/admin.toll.source.$id.tsx",
            lineNumber: 201,
            columnNumber: 12
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.toll.source.$id.tsx",
            lineNumber: 200,
            columnNumber: 11
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.toll.source.$id.tsx",
          lineNumber: 196,
          columnNumber: 10
        }, this) }, `modification-${key}`, false, {
          fileName: "app/routes/admin.toll.source.$id.tsx",
          lineNumber: 195,
          columnNumber: 45
        }, this)) }, void 0, false, {
          fileName: "app/routes/admin.toll.source.$id.tsx",
          lineNumber: 194,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("ul", { className: "mt-3", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: "1. Number of source verified deaths and injuries" }, void 0, false, {
            fileName: "app/routes/admin.toll.source.$id.tsx",
            lineNumber: 207,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: "2. Number of INCIDENTS reported and verified" }, void 0, false, {
            fileName: "app/routes/admin.toll.source.$id.tsx",
            lineNumber: 208,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: "3. Calculation based on CDC Suicide Data" }, void 0, false, {
            fileName: "app/routes/admin.toll.source.$id.tsx",
            lineNumber: 209,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: "4. Actual total of all non-suicide deaths plus daily calculated suicide deaths" }, void 0, false, {
            fileName: "app/routes/admin.toll.source.$id.tsx",
            lineNumber: 210,
            columnNumber: 8
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.toll.source.$id.tsx",
          lineNumber: 206,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.toll.source.$id.tsx",
        lineNumber: 185,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-10 rounded-[5px] bg-gray-200 p-10", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "Notes", className: "text-lg font-bold", children: "Notes" }, void 0, false, {
          fileName: "app/routes/admin.toll.source.$id.tsx",
          lineNumber: 215,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("textarea", { id: "Notes", name: "notes", rows: 4, defaultValue: source.notes || "", className: "block w-full rounded-[5px] px-5 py-2.5 text-lg outline-0" }, void 0, false, {
          fileName: "app/routes/admin.toll.source.$id.tsx",
          lineNumber: 219,
          columnNumber: 8
        }, this) }, void 0, false, {
          fileName: "app/routes/admin.toll.source.$id.tsx",
          lineNumber: 218,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.toll.source.$id.tsx",
        lineNumber: 214,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "my-10", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white hover:bg-orange-800 focus:bg-orange-800", children: "Save" }, void 0, false, {
          fileName: "app/routes/admin.toll.source.$id.tsx",
          lineNumber: 224,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "/admin/toll", className: "ml-4 inline-block rounded-full bg-gray-500 px-5 py-2 text-lg text-white hover:bg-gray-600 focus:bg-gray-600", children: "Cancel" }, void 0, false, {
          fileName: "app/routes/admin.toll.source.$id.tsx",
          lineNumber: 227,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.toll.source.$id.tsx",
        lineNumber: 223,
        columnNumber: 6
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.toll.source.$id.tsx",
      lineNumber: 129,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/admin.toll.source.$id.tsx",
      lineNumber: 128,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.toll.source.$id.tsx",
    lineNumber: 126,
    columnNumber: 10
  }, this);
}
_s(TollSourceFormPage, "wTCtS+BQbl4PT8vxNw0pSbQJxZA=", false, function() {
  return [useLoaderData];
});
_c = TollSourceFormPage;
function ErrorBoundary() {
  _s2();
  const error = useRouteError();
  if (error instanceof Error) {
    return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
      "An unexpected error occurred: ",
      error.message
    ] }, void 0, true, {
      fileName: "app/routes/admin.toll.source.$id.tsx",
      lineNumber: 243,
      columnNumber: 12
    }, this);
  }
  if (!isRouteErrorResponse(error)) {
    return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h1", { children: "Unknown Error" }, void 0, false, {
      fileName: "app/routes/admin.toll.source.$id.tsx",
      lineNumber: 246,
      columnNumber: 12
    }, this);
  }
  if (error.status === 404) {
    return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: "Toll not found" }, void 0, false, {
      fileName: "app/routes/admin.toll.source.$id.tsx",
      lineNumber: 249,
      columnNumber: 12
    }, this);
  }
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
    "An unexpected error occurred: ",
    error.statusText
  ] }, void 0, true, {
    fileName: "app/routes/admin.toll.source.$id.tsx",
    lineNumber: 251,
    columnNumber: 10
  }, this);
}
_s2(ErrorBoundary, "oAgjgbJzsRXlB89+MoVumxMQqKM=", false, function() {
  return [useRouteError];
});
_c2 = ErrorBoundary;
var _c;
var _c2;
$RefreshReg$(_c, "TollSourceFormPage");
$RefreshReg$(_c2, "ErrorBoundary");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  ErrorBoundary,
  TollSourceFormPage as default
};
//# sourceMappingURL=/build/routes/admin.toll.source.$id-7FWGI5OY.js.map
