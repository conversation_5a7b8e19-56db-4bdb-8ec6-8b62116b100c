import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  move
} from "/build/_shared/chunk-DOCJ2TWD.js";
import "/build/_shared/chunk-5NT3OUFA.js";
import {
  require_column_templates
} from "/build/_shared/chunk-PUZHN2JW.js";
import "/build/_shared/chunk-AUYLHJJM.js";
import {
  Icon_Admin_Delete_default
} from "/build/_shared/chunk-RUOV274T.js";
import {
  require_cookies
} from "/build/_shared/chunk-MUAEGCBW.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import {
  ucwords
} from "/build/_shared/chunk-DFTVPORL.js";
import {
  Form,
  Link,
  isRouteErrorResponse,
  useActionData,
  useLoaderData,
  useRouteError
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  require_node
} from "/build/_shared/chunk-ZP6BZTHN.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __commonJS,
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// empty-module:~/services/entry/columns/columns.server
var require_columns = __commonJS({
  "empty-module:~/services/entry/columns/columns.server"(exports, module) {
    module.exports = {};
  }
});

// app/routes/admin.column.$id.tsx
var import_react = __toESM(require_react(), 1);
var import_node = __toESM(require_node(), 1);
var import_column_templates = __toESM(require_column_templates(), 1);
var import_cookies = __toESM(require_cookies(), 1);
var import_auth = __toESM(require_auth(), 1);
var import_columns = __toESM(require_columns(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.column.$id.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
var _s2 = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.column.$id.tsx"
  );
  import.meta.hot.lastModified = "1748456131823.544";
}
function ColumnTemplateFormPage() {
  _s();
  const actionData = useActionData();
  const {
    template,
    columnOptions
  } = useLoaderData();
  const [type, setType] = (0, import_react.useState)(template.type);
  const [columns, setColumns] = (0, import_react.useState)(template.columns || "");
  const [columnSelector, setColumnSelector] = (0, import_react.useState)({
    enabled: template.columns ? JSON.parse(template.columns).filter((s) => columnOptions.findIndex((o) => o.name == s.name) !== -1).sort((a, b) => a.weight - b.weight).map((s) => s.name) : [],
    available: []
  });
  (0, import_react.useEffect)(() => {
    if (type) {
      const optionsByType = columnOptions.filter((o) => o.queryTypes.includes(type));
      const enabled = columnSelector.enabled.filter((name) => optionsByType.findIndex((o) => o.name == name) !== -1);
      const available = optionsByType.filter((o) => !columnSelector.enabled.includes(o.name)).map((s) => s.name);
      setColumnSelector({
        enabled,
        available
      });
    } else {
      setColumnSelector({
        enabled: [],
        available: []
      });
    }
  }, [type]);
  (0, import_react.useEffect)(() => {
    const newColumns = [];
    let weight = 0;
    for (const columnName of columnSelector.enabled) {
      newColumns.push({
        name: columnName,
        weight: weight++
      });
    }
    setColumns(JSON.stringify(newColumns));
  }, [columnSelector.enabled]);
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { className: "admin container mx-auto pb-10 pt-8", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[25px] font-bold", children: "Manage Column Templates" }, void 0, false, {
      fileName: "app/routes/admin.column.$id.tsx",
      lineNumber: 143,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-8", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, {
      method: "post",
      // @ts-ignore
      children: [
        actionData?.error && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-5 flex border border-red-500 bg-white px-5 py-3.5 text-sm font-light", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Icon_Admin_Delete_default, alt: "", width: 16 }, void 0, false, {
            fileName: "app/routes/admin.column.$id.tsx",
            lineNumber: 149,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "pl-2 text-red-500", children: actionData.error }, void 0, false, {
            fileName: "app/routes/admin.column.$id.tsx",
            lineNumber: 150,
            columnNumber: 9
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.column.$id.tsx",
          lineNumber: 148,
          columnNumber: 30
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[5px] bg-gray-200 p-10", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid grid-cols-1 gap-x-10 gap-y-8", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "source", defaultValue: template.source || "" }, void 0, false, {
            fileName: "app/routes/admin.column.$id.tsx",
            lineNumber: 154,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "columns", value: columns }, void 0, false, {
            fileName: "app/routes/admin.column.$id.tsx",
            lineNumber: 155,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "Name", className: "text-lg font-bold after:ml-0.5 after:text-orange-500 after:content-['*']", children: "Name" }, void 0, false, {
              fileName: "app/routes/admin.column.$id.tsx",
              lineNumber: 157,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "Name", name: "name", required: true, disabled: template.source == "hidden", type: "text", defaultValue: template.name, className: "block h-[50px] w-full rounded-[5px] px-5 text-lg outline-0" }, void 0, false, {
              fileName: "app/routes/admin.column.$id.tsx",
              lineNumber: 161,
              columnNumber: 10
            }, this) }, void 0, false, {
              fileName: "app/routes/admin.column.$id.tsx",
              lineNumber: 160,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: "The name of the column template. This name must be unique." }, void 0, false, {
              fileName: "app/routes/admin.column.$id.tsx",
              lineNumber: 163,
              columnNumber: 9
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.column.$id.tsx",
            lineNumber: 156,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "Type", className: "text-lg font-bold after:ml-0.5 after:text-orange-500 after:content-['*']", children: "Type" }, void 0, false, {
              fileName: "app/routes/admin.column.$id.tsx",
              lineNumber: 166,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3", children: [
              template.ctuuid && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { name: "type", type: "hidden", defaultValue: template.type }, void 0, false, {
                  fileName: "app/routes/admin.column.$id.tsx",
                  lineNumber: 171,
                  columnNumber: 12
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "Type", required: true, readOnly: true, type: "text", defaultValue: ucwords(template.type), className: "block h-[50px] w-full rounded-[5px] px-5 text-lg outline-0" }, void 0, false, {
                  fileName: "app/routes/admin.column.$id.tsx",
                  lineNumber: 172,
                  columnNumber: 12
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.column.$id.tsx",
                lineNumber: 170,
                columnNumber: 30
              }, this),
              !template.ctuuid && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("select", { id: "Type", name: "type", required: true, defaultValue: template.type ?? "", onChange: (e) => setType(e.target.value), className: "block h-[50px] w-full rounded-[5px] px-5 text-lg outline-0", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("option", { value: "", children: "- Select -" }, void 0, false, {
                  fileName: "app/routes/admin.column.$id.tsx",
                  lineNumber: 175,
                  columnNumber: 12
                }, this),
                ["incidents", "participants"].map((t) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("option", { value: t, children: ucwords(t) }, `type-option-${t}`, false, {
                  fileName: "app/routes/admin.column.$id.tsx",
                  lineNumber: 176,
                  columnNumber: 52
                }, this))
              ] }, void 0, true, {
                fileName: "app/routes/admin.column.$id.tsx",
                lineNumber: 174,
                columnNumber: 31
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.column.$id.tsx",
              lineNumber: 169,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: "The type of search query this column template is for." }, void 0, false, {
              fileName: "app/routes/admin.column.$id.tsx",
              lineNumber: 181,
              columnNumber: 9
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.column.$id.tsx",
            lineNumber: 165,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "SupportedExportTypes", className: "text-lg font-bold after:ml-0.5 after:text-orange-500 after:content-['*']", children: "Supported Export Types" }, void 0, false, {
              fileName: "app/routes/admin.column.$id.tsx",
              lineNumber: 184,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3 flex flex-col space-y-3", children: ["CSV", "Table"].map((s) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex items-center", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: `export-type-chk-${s}`, name: "supported_export_types[]", type: "checkbox", value: s, defaultChecked: !template.supported_export_types || template.supported_export_types.includes(s), className: "mr-4 size-[20px] accent-orange-500" }, void 0, false, {
                fileName: "app/routes/admin.column.$id.tsx",
                lineNumber: 189,
                columnNumber: 12
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: `export-type-chk-${s}`, className: "text-lg", children: s }, void 0, false, {
                fileName: "app/routes/admin.column.$id.tsx",
                lineNumber: 190,
                columnNumber: 12
              }, this)
            ] }, `export-type-${s}`, true, {
              fileName: "app/routes/admin.column.$id.tsx",
              lineNumber: 188,
              columnNumber: 37
            }, this)) }, void 0, false, {
              fileName: "app/routes/admin.column.$id.tsx",
              lineNumber: 187,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: "Select the exporters that can use this column template." }, void 0, false, {
              fileName: "app/routes/admin.column.$id.tsx",
              lineNumber: 195,
              columnNumber: 9
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.column.$id.tsx",
            lineNumber: 183,
            columnNumber: 8
          }, this),
          type && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "column-selector", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(DragDropProvider, { onDragOver: (event) => {
            setColumnSelector((columnSelector2) => {
              return move(columnSelector2, event);
            });
          }, children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex gap-5", children: Object.entries(columnSelector).map(([group, items]) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(ColumnContainer, { id: group, title: ucwords(group), children: items.map((id, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(ColumnItem, { id, text: columnOptions.find((o) => o.name == id)?.label || id, index, group }, id, false, {
            fileName: "app/routes/admin.column.$id.tsx",
            lineNumber: 205,
            columnNumber: 40
          }, this)) }, group, false, {
            fileName: "app/routes/admin.column.$id.tsx",
            lineNumber: 204,
            columnNumber: 68
          }, this)) }, void 0, false, {
            fileName: "app/routes/admin.column.$id.tsx",
            lineNumber: 203,
            columnNumber: 11
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.column.$id.tsx",
            lineNumber: 198,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.column.$id.tsx",
            lineNumber: 197,
            columnNumber: 17
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.column.$id.tsx",
          lineNumber: 153,
          columnNumber: 7
        }, this) }, void 0, false, {
          fileName: "app/routes/admin.column.$id.tsx",
          lineNumber: 152,
          columnNumber: 6
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "my-10", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white hover:bg-orange-800 focus:bg-orange-800", children: "Save" }, void 0, false, {
            fileName: "app/routes/admin.column.$id.tsx",
            lineNumber: 214,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "/admin/column", className: "ml-4 inline-block rounded-full bg-gray-500 px-5 py-2 text-lg text-white hover:bg-gray-600 focus:bg-gray-600", children: "Cancel" }, void 0, false, {
            fileName: "app/routes/admin.column.$id.tsx",
            lineNumber: 217,
            columnNumber: 7
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.column.$id.tsx",
          lineNumber: 213,
          columnNumber: 6
        }, this)
      ]
    }, void 0, true, {
      fileName: "app/routes/admin.column.$id.tsx",
      lineNumber: 145,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/admin.column.$id.tsx",
      lineNumber: 144,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.column.$id.tsx",
    lineNumber: 142,
    columnNumber: 10
  }, this);
}
_s(ColumnTemplateFormPage, "VqXZOReFSaM/u0wdb9Oq2AKLJIE=", false, function() {
  return [useActionData, useLoaderData];
});
_c = ColumnTemplateFormPage;
function ErrorBoundary() {
  _s2();
  const error = useRouteError();
  if (error instanceof Error) {
    return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
      "An unexpected error occurred: ",
      error.message
    ] }, void 0, true, {
      fileName: "app/routes/admin.column.$id.tsx",
      lineNumber: 233,
      columnNumber: 12
    }, this);
  }
  if (!isRouteErrorResponse(error)) {
    return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h1", { children: "Unknown Error" }, void 0, false, {
      fileName: "app/routes/admin.column.$id.tsx",
      lineNumber: 236,
      columnNumber: 12
    }, this);
  }
  if (error.status === 404) {
    return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: "Column Template not found" }, void 0, false, {
      fileName: "app/routes/admin.column.$id.tsx",
      lineNumber: 239,
      columnNumber: 12
    }, this);
  }
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
    "An unexpected error occurred: ",
    error.statusText
  ] }, void 0, true, {
    fileName: "app/routes/admin.column.$id.tsx",
    lineNumber: 241,
    columnNumber: 10
  }, this);
}
_s2(ErrorBoundary, "oAgjgbJzsRXlB89+MoVumxMQqKM=", false, function() {
  return [useRouteError];
});
_c2 = ErrorBoundary;
var _c;
var _c2;
$RefreshReg$(_c, "ColumnTemplateFormPage");
$RefreshReg$(_c2, "ErrorBoundary");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  ErrorBoundary,
  ColumnTemplateFormPage as default
};
//# sourceMappingURL=/build/routes/admin.column.$id-U7BLXP2T.js.map
