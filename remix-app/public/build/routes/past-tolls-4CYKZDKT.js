import {
  require_toll
} from "/build/_shared/chunk-OQS22UZD.js";
import {
  esm_default
} from "/build/_shared/chunk-WBY37MGY.js";
import {
  Title
} from "/build/_shared/chunk-YPQMQ54K.js";
import {
  Gun_Violence_Archive_Logo_Reverse_default
} from "/build/_shared/chunk-MPHBVVM4.js";
import {
  moment_default
} from "/build/_shared/chunk-XAWK6254.js";
import "/build/_shared/chunk-NPKMIMFJ.js";
import {
  require_config
} from "/build/_shared/chunk-3PRZVPKO.js";
import {
  Footer,
  Header
} from "/build/_shared/chunk-4556QKWU.js";
import "/build/_shared/chunk-W7JLG7II.js";
import "/build/_shared/chunk-OHIZ2QAR.js";
import "/build/_shared/chunk-DFTVPORL.js";
import {
  useLoaderData
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/past-tolls.tsx
var import_config = __toESM(require_config(), 1);
var import_toll = __toESM(require_toll(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/past-tolls.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/past-tolls.tsx"
  );
  import.meta.hot.lastModified = "1742571036285.2444";
}
function PastTolls() {
  _s();
  const data = useLoaderData();
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "min-h-screen", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Header, {}, void 0, false, {
      fileName: "app/routes/past-tolls.tsx",
      lineNumber: 54,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Title, { title: "Past Years" }, void 0, false, {
      fileName: "app/routes/past-tolls.tsx",
      lineNumber: 55,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { className: "bg-gray-500 py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid grid-cols-1 gap-5 md:grid-cols-2 xl:grid-cols-3 xl:gap-10", children: data.pastYearsData.map((yearData) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(YearReview, { data: yearData }, void 0, false, {
      fileName: "app/routes/past-tolls.tsx",
      lineNumber: 60,
      columnNumber: 9
    }, this) }, `toll-${yearData.year_range?.str}`, false, {
      fileName: "app/routes/past-tolls.tsx",
      lineNumber: 59,
      columnNumber: 43
    }, this)) }, void 0, false, {
      fileName: "app/routes/past-tolls.tsx",
      lineNumber: 58,
      columnNumber: 6
    }, this) }, void 0, false, {
      fileName: "app/routes/past-tolls.tsx",
      lineNumber: 57,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/past-tolls.tsx",
      lineNumber: 56,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Footer, {}, void 0, false, {
      fileName: "app/routes/past-tolls.tsx",
      lineNumber: 65,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/past-tolls.tsx",
    lineNumber: 53,
    columnNumber: 10
  }, this);
}
_s(PastTolls, "5thj+e1edPyRpKif1JmVRC6KArE=", false, function() {
  return [useLoaderData];
});
_c = PastTolls;
var YearReview = ({
  data
}) => {
  const tolls = data.values ? Object.entries(data.values) : [];
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] bg-gray-800 py-9 text-white", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "px-9 pb-4", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[25px] font-bold", children: [
        "Gun Violence Archive",
        " ",
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: data.year_range?.str.includes("-") ? "block" : "inline", children: data.year_range?.str }, void 0, false, {
          fileName: "app/routes/past-tolls.tsx",
          lineNumber: 80,
          columnNumber: 6
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/past-tolls.tsx",
        lineNumber: 78,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[15px]", children: [
        "Publish Date: ",
        moment_default().format("MMMM DD, YYYY")
      ] }, void 0, true, {
        fileName: "app/routes/past-tolls.tsx",
        lineNumber: 84,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/past-tolls.tsx",
      lineNumber: 77,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-sm", children: tolls.filter(([k, v]) => !v.parent).map(([key, item], index) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "px-9 py-3.5 space-y-1.5" + (index % 2 == 0 ? " bg-black" : ""), children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex space-x-3 justify-between", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1", children: !item.hideLabel && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { href: item.report || "#", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { dangerouslySetInnerHTML: {
          __html: item.label
        } }, void 0, false, {
          fileName: "app/routes/past-tolls.tsx",
          lineNumber: 92,
          columnNumber: 12
        }, this) }, void 0, false, {
          fileName: "app/routes/past-tolls.tsx",
          lineNumber: 91,
          columnNumber: 30
        }, this) }, void 0, false, {
          fileName: "app/routes/past-tolls.tsx",
          lineNumber: 90,
          columnNumber: 9
        }, this),
        item.valueLabel && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between w-[100px] lg:w-28", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { dangerouslySetInnerHTML: {
            __html: item.valueLabel
          } }, void 0, false, {
            fileName: "app/routes/past-tolls.tsx",
            lineNumber: 98,
            columnNumber: 11
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { href: item.report || "#", children: item.value }, void 0, false, {
            fileName: "app/routes/past-tolls.tsx",
            lineNumber: 101,
            columnNumber: 11
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/past-tolls.tsx",
          lineNumber: 97,
          columnNumber: 29
        }, this),
        !item.valueLabel && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { href: item.report || "#", children: item.value }, void 0, false, {
          fileName: "app/routes/past-tolls.tsx",
          lineNumber: 104,
          columnNumber: 11
        }, this) }, void 0, false, {
          fileName: "app/routes/past-tolls.tsx",
          lineNumber: 103,
          columnNumber: 30
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/past-tolls.tsx",
        lineNumber: 89,
        columnNumber: 8
      }, this),
      tolls.filter(([k, v]) => v.parent == key).map(([subkey, subitem]) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex space-x-3 justify-between", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1", children: !subitem.hideLabel && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { href: subitem.report || "#", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { dangerouslySetInnerHTML: {
          __html: subitem.label
        } }, void 0, false, {
          fileName: "app/routes/past-tolls.tsx",
          lineNumber: 110,
          columnNumber: 14
        }, this) }, void 0, false, {
          fileName: "app/routes/past-tolls.tsx",
          lineNumber: 109,
          columnNumber: 35
        }, this) }, void 0, false, {
          fileName: "app/routes/past-tolls.tsx",
          lineNumber: 108,
          columnNumber: 11
        }, this),
        subitem.valueLabel && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between w-[100px] lg:w-28", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { dangerouslySetInnerHTML: {
            __html: subitem.valueLabel
          } }, void 0, false, {
            fileName: "app/routes/past-tolls.tsx",
            lineNumber: 116,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { href: subitem.report || "#", children: subitem.value }, void 0, false, {
            fileName: "app/routes/past-tolls.tsx",
            lineNumber: 119,
            columnNumber: 13
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/past-tolls.tsx",
          lineNumber: 115,
          columnNumber: 34
        }, this),
        !subitem.valueLabel && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { href: subitem.report || "#", children: subitem.value }, void 0, false, {
          fileName: "app/routes/past-tolls.tsx",
          lineNumber: 122,
          columnNumber: 13
        }, this) }, void 0, false, {
          fileName: "app/routes/past-tolls.tsx",
          lineNumber: 121,
          columnNumber: 35
        }, this)
      ] }, `toll-${key}-sub-${subkey}`, true, {
        fileName: "app/routes/past-tolls.tsx",
        lineNumber: 107,
        columnNumber: 78
      }, this))
    ] }, `toll-${key}`, true, {
      fileName: "app/routes/past-tolls.tsx",
      lineNumber: 88,
      columnNumber: 70
    }, this)) }, void 0, false, {
      fileName: "app/routes/past-tolls.tsx",
      lineNumber: 87,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "px-9 pt-4 text-xs", children: [
      esm_default(data.footer),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-4 font-bold text-sm lg:text-xl", children: [
        "Data Sources Verified: ",
        data.date_validated
      ] }, void 0, true, {
        fileName: "app/routes/past-tolls.tsx",
        lineNumber: 130,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-4 lg:flex items-end", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Gun_Violence_Archive_Logo_Reverse_default, alt: "Gun Violence Archive", width: 230 }, void 0, false, {
          fileName: "app/routes/past-tolls.tsx",
          lineNumber: 132,
          columnNumber: 6
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg:pl-1 pb-0.5 text-[10px]", children: [
          "\xA9 2013-",
          (/* @__PURE__ */ new Date()).getFullYear()
        ] }, void 0, true, {
          fileName: "app/routes/past-tolls.tsx",
          lineNumber: 133,
          columnNumber: 6
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/past-tolls.tsx",
        lineNumber: 131,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/past-tolls.tsx",
      lineNumber: 128,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/past-tolls.tsx",
    lineNumber: 76,
    columnNumber: 10
  }, this);
};
_c2 = YearReview;
var _c;
var _c2;
$RefreshReg$(_c, "PastTolls");
$RefreshReg$(_c2, "YearReview");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  PastTolls as default
};
//# sourceMappingURL=/build/routes/past-tolls-4CYKZDKT.js.map
