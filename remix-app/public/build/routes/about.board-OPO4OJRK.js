import {
  require_range
} from "/build/_shared/chunk-XEU2OR47.js";
import {
  require_about,
  require_js,
  require_reduce
} from "/build/_shared/chunk-O7YZF2UJ.js";
import {
  Editor,
  htmlFrom,
  require_browser,
  tinyConfig
} from "/build/_shared/chunk-SIGMETZO.js";
import "/build/_shared/chunk-WBY37MGY.js";
import "/build/_shared/chunk-IVTPFYOU.js";
import {
  Title
} from "/build/_shared/chunk-YPQMQ54K.js";
import {
  Foot<PERSON>,
  Header
} from "/build/_shared/chunk-4556QKWU.js";
import "/build/_shared/chunk-W7JLG7II.js";
import "/build/_shared/chunk-OHIZ2QAR.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import {
  useOptionalUser
} from "/build/_shared/chunk-DFTVPORL.js";
import {
  useLoaderData,
  useNavigate
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/about.board.tsx
var import_react = __toESM(require_react(), 1);
var import_about = __toESM(require_about(), 1);
var import_auth = __toESM(require_auth(), 1);
var import_react_multi_ref = __toESM(require_js(), 1);
var import_isomorphic_dompurify = __toESM(require_browser(), 1);
var import_reduce = __toESM(require_reduce(), 1);
var import_range = __toESM(require_range(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/about.board.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/about.board.tsx"
  );
  import.meta.hot.lastModified = "1748453662435.184";
}
function AboutBoard() {
  _s();
  const data = useLoaderData();
  const user = useOptionalUser();
  const navigate = useNavigate();
  const [editorRefs] = (0, import_react.useState)(() => new import_react_multi_ref.default());
  const [editing, setEditing] = (0, import_react.useState)(false);
  const editingRef = (0, import_react.useRef)(editing);
  const alertUser = (e) => {
    if (editingRef.current) {
      e.preventDefault();
    }
  };
  (0, import_react.useEffect)(() => {
    editingRef.current = editing;
  }, [editing]);
  (0, import_react.useEffect)(() => {
    window.addEventListener("beforeunload", alertUser);
    return () => {
      window.removeEventListener("beforeunload", alertUser);
    };
  }, []);
  const save = async (body) => {
    await fetch("/about/board", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(body)
    });
    navigate(".", {
      replace: true
    });
  };
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "min-h-screen", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Header, {}, void 0, false, {
      fileName: "app/routes/about.board.tsx",
      lineNumber: 92,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Title, { title: "Gun Violence Archive Board of Directors" }, void 0, false, {
      fileName: "app/routes/about.board.tsx",
      lineNumber: 93,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { children: [
      user && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "fixed bottom-0 left-0 border-2 bg-white px-5 py-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { className: "text-xl", onClick: () => {
        if (editing) {
          save([...(0, import_range.default)(1, 11).map((value) => ({
            contentid: "about-board-container-" + value,
            content: editorRefs.map.get("about-board-container-" + value)?.getContent() || ""
          }))]).then(() => setEditing(false));
        } else {
          setEditing(true);
        }
      }, children: editing ? "Save" : "Edit" }, void 0, false, {
        fileName: "app/routes/about.board.tsx",
        lineNumber: 96,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.board.tsx",
        lineNumber: 95,
        columnNumber: 14
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid gap-10 lg:grid-cols-2 xl:grid-cols-3", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "w-[80px] flex-none pt-2 sm:w-[120px]", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
            ...tinyConfig
          }, onInit: (_evt, instance) => editorRefs.map.set("about-board-container-1", instance), initialValue: data.content["about-board-container-1"] }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 113,
            columnNumber: 20
          }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "about-board-container-1", children: htmlFrom(data.content["about-board-container-1"]) }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 115,
            columnNumber: 163
          }, this) }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 112,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 pl-4 sm:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
            ...tinyConfig
          }, onInit: (_evt, instance) => editorRefs.map.set("about-board-container-2", instance), initialValue: data.content["about-board-container-2"] }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 120,
            columnNumber: 20
          }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "about-board-container-2", children: htmlFrom(data.content["about-board-container-2"]) }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 122,
            columnNumber: 163
          }, this) }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 119,
            columnNumber: 8
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/about.board.tsx",
          lineNumber: 111,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "w-[80px] flex-none pt-2 sm:w-[120px]", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
            ...tinyConfig
          }, onInit: (_evt, instance) => editorRefs.map.set("about-board-container-3", instance), initialValue: data.content["about-board-container-3"] }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 129,
            columnNumber: 20
          }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "about-board-container-3", children: htmlFrom(data.content["about-board-container-3"]) }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 131,
            columnNumber: 163
          }, this) }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 128,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 pl-4 sm:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
            ...tinyConfig
          }, onInit: (_evt, instance) => editorRefs.map.set("about-board-container-4", instance), initialValue: data.content["about-board-container-4"] }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 136,
            columnNumber: 20
          }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "about-board-container-4", children: htmlFrom(data.content["about-board-container-4"]) }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 138,
            columnNumber: 163
          }, this) }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 135,
            columnNumber: 8
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/about.board.tsx",
          lineNumber: 127,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "w-[80px] flex-none pt-2 sm:w-[120px]", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
            ...tinyConfig
          }, onInit: (_evt, instance) => editorRefs.map.set("about-board-container-5", instance), initialValue: data.content["about-board-container-5"] }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 145,
            columnNumber: 20
          }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "about-board-container-5", children: htmlFrom(data.content["about-board-container-5"]) }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 147,
            columnNumber: 163
          }, this) }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 144,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 pl-4 sm:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
            ...tinyConfig
          }, onInit: (_evt, instance) => editorRefs.map.set("about-board-container-6", instance), initialValue: data.content["about-board-container-6"] }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 152,
            columnNumber: 20
          }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "about-board-container-6", children: htmlFrom(data.content["about-board-container-6"]) }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 154,
            columnNumber: 163
          }, this) }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 151,
            columnNumber: 8
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/about.board.tsx",
          lineNumber: 143,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "w-[80px] flex-none pt-2 sm:w-[120px]", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
            ...tinyConfig
          }, onInit: (_evt, instance) => editorRefs.map.set("about-board-container-7", instance), initialValue: data.content["about-board-container-7"] }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 161,
            columnNumber: 20
          }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "about-board-container-7", children: htmlFrom(data.content["about-board-container-7"]) }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 163,
            columnNumber: 163
          }, this) }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 160,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 pl-4 sm:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
            ...tinyConfig
          }, onInit: (_evt, instance) => editorRefs.map.set("about-board-container-8", instance), initialValue: data.content["about-board-container-8"] }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 168,
            columnNumber: 20
          }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "about-board-container-8", children: htmlFrom(data.content["about-board-container-8"]) }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 170,
            columnNumber: 163
          }, this) }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 167,
            columnNumber: 8
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/about.board.tsx",
          lineNumber: 159,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "w-[80px] flex-none pt-2 sm:w-[120px]", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
            ...tinyConfig
          }, onInit: (_evt, instance) => editorRefs.map.set("about-board-container-9", instance), initialValue: data.content["about-board-container-9"] }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 177,
            columnNumber: 20
          }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "about-board-container-9", children: htmlFrom(data.content["about-board-container-9"]) }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 179,
            columnNumber: 163
          }, this) }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 176,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 pl-4 sm:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
            ...tinyConfig
          }, onInit: (_evt, instance) => editorRefs.map.set("about-board-container-10", instance), initialValue: data.content["about-board-container-10"] }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 184,
            columnNumber: 20
          }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "about-board-container-10", children: htmlFrom(data.content["about-board-container-10"]) }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 186,
            columnNumber: 165
          }, this) }, void 0, false, {
            fileName: "app/routes/about.board.tsx",
            lineNumber: 183,
            columnNumber: 8
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/about.board.tsx",
          lineNumber: 175,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.board.tsx",
        lineNumber: 110,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.board.tsx",
        lineNumber: 109,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/about.board.tsx",
      lineNumber: 94,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Footer, {}, void 0, false, {
      fileName: "app/routes/about.board.tsx",
      lineNumber: 194,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/about.board.tsx",
    lineNumber: 91,
    columnNumber: 10
  }, this);
}
_s(AboutBoard, "78e72DpIv4Z4DnUhCpWGp5ZspBI=", false, function() {
  return [useLoaderData, useOptionalUser, useNavigate];
});
_c = AboutBoard;
var _c;
$RefreshReg$(_c, "AboutBoard");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  AboutBoard as default
};
//# sourceMappingURL=/build/routes/about.board-OPO4OJRK.js.map
