import {
  require_session
} from "/build/_shared/chunk-BK5TXDDP.js";
import {
  require_incidents
} from "/build/_shared/chunk-J24HANUS.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import "/build/_shared/chunk-QT64XSGC.js";
import {
  require_node
} from "/build/_shared/chunk-ZP6BZTHN.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/admin.twitter.tweet.$id.tsx
var import_node = __toESM(require_node(), 1);
var import_session = __toESM(require_session(), 1);
var import_incidents = __toESM(require_incidents(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.twitter.tweet.$id.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.twitter.tweet.$id.tsx"
  );
  import.meta.hot.lastModified = "1751305673937.1404";
}
function TwitterTweet() {
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h1", { children: "Tweet about Incident" }, void 0, false, {
      fileName: "app/routes/admin.twitter.tweet.$id.tsx",
      lineNumber: 79,
      columnNumber: 7
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("p", { children: "This is a placeholder for the tweet form." }, void 0, false, {
      fileName: "app/routes/admin.twitter.tweet.$id.tsx",
      lineNumber: 80,
      columnNumber: 7
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.twitter.tweet.$id.tsx",
    lineNumber: 78,
    columnNumber: 10
  }, this);
}
_c = TwitterTweet;
var _c;
$RefreshReg$(_c, "TwitterTweet");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  TwitterTweet as default
};
//# sourceMappingURL=/build/routes/admin.twitter.tweet.$id-2TKOBFKM.js.map
