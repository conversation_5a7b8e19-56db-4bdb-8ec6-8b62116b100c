import {
  require_range
} from "/build/_shared/chunk-XEU2OR47.js";
import {
  require_about,
  require_js,
  require_reduce
} from "/build/_shared/chunk-O7YZF2UJ.js";
import {
  Editor,
  htmlFrom,
  require_browser,
  tinyConfig
} from "/build/_shared/chunk-SIGMETZO.js";
import "/build/_shared/chunk-WBY37MGY.js";
import "/build/_shared/chunk-IVTPFYOU.js";
import {
  Title
} from "/build/_shared/chunk-YPQMQ54K.js";
import {
  Footer,
  Gun_Violence_Archive_Logo_default,
  Header
} from "/build/_shared/chunk-4556QKWU.js";
import "/build/_shared/chunk-W7JLG7II.js";
import "/build/_shared/chunk-OHIZ2QAR.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import {
  useOptionalUser
} from "/build/_shared/chunk-DFTVPORL.js";
import {
  Link,
  useLoaderData,
  useNavigate
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/about._index.tsx
var import_react = __toESM(require_react(), 1);
var import_about = __toESM(require_about(), 1);
var import_auth = __toESM(require_auth(), 1);
var import_react_multi_ref = __toESM(require_js(), 1);
var import_isomorphic_dompurify = __toESM(require_browser(), 1);
var import_reduce = __toESM(require_reduce(), 1);
var import_range = __toESM(require_range(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/about._index.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/about._index.tsx"
  );
  import.meta.hot.lastModified = "1748453482177.3438";
}
function About() {
  _s();
  const data = useLoaderData();
  const user = useOptionalUser();
  const navigate = useNavigate();
  const [editorRefs] = (0, import_react.useState)(() => new import_react_multi_ref.default());
  const [editing, setEditing] = (0, import_react.useState)(false);
  const editingRef = (0, import_react.useRef)(editing);
  const alertUser = (e) => {
    if (editingRef.current) {
      e.preventDefault();
    }
  };
  (0, import_react.useEffect)(() => {
    editingRef.current = editing;
  }, [editing]);
  (0, import_react.useEffect)(() => {
    window.addEventListener("beforeunload", alertUser);
    return () => {
      window.removeEventListener("beforeunload", alertUser);
    };
  }, []);
  const save = async (body) => {
    await fetch("/about", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(body)
    });
    navigate(".", {
      replace: true
    });
  };
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "min-h-screen", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Header, {}, void 0, false, {
      fileName: "app/routes/about._index.tsx",
      lineNumber: 93,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Title, { title: "About Gun Violence Archive" }, void 0, false, {
      fileName: "app/routes/about._index.tsx",
      lineNumber: 94,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { children: [
      user && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "fixed bottom-0 left-0 border-2 bg-white px-5 py-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { className: "text-xl", onClick: () => {
        if (editing) {
          save((0, import_range.default)(1, 7).map((value) => {
            const editor = editorRefs.map.get("about-overview-container-" + value);
            return {
              contentid: "about-overview-container-" + value,
              content: editor ? editor.getContent() : ""
            };
          })).then(() => setEditing(false));
        } else {
          setEditing(true);
        }
      }, children: editing ? "Save" : "Edit" }, void 0, false, {
        fileName: "app/routes/about._index.tsx",
        lineNumber: 97,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about._index.tsx",
        lineNumber: 96,
        columnNumber: 14
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto bg-[url(~/images/BG-Gradient-Orange-to-Transparent.jpg)] bg-no-repeat py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex flex-col items-center", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Gun_Violence_Archive_Logo_default, alt: "Gun Violence Archive", width: 500 }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 115,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-16 max-w-[1000px] text-center font-bold text-4xl md:text-[50px] md:leading-[60px]", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          cleanup: false,
          verify_html: false,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-overview-container-1", instance), initialValue: data.content["about-overview-container-1"] }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 117,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "about-overview-container-1", children: htmlFrom(data.content["about-overview-container-1"]) }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 121,
          columnNumber: 167
        }, this) }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 116,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-7", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "inline-block rounded-full bg-orange-500 px-5 py-2 text-lg text-white", to: "/contact", children: "Contact GVA" }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 126,
          columnNumber: 8
        }, this) }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 125,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about._index.tsx",
        lineNumber: 114,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about._index.tsx",
        lineNumber: 113,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-gray-200", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid gap-10 text-lg sm:grid-cols-2 lg:grid-cols-4", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          cleanup: false,
          verify_html: false,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-overview-container-2", instance), initialValue: data.content["about-overview-container-2"] }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 136,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "about-overview-container-2", children: htmlFrom(data.content["about-overview-container-2"]) }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 140,
          columnNumber: 169
        }, this) }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 135,
          columnNumber: 8
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-overview-container-3", instance), initialValue: data.content["about-overview-container-3"] }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 145,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "about-overview-container-3", children: htmlFrom(data.content["about-overview-container-3"]) }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 147,
          columnNumber: 169
        }, this) }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 144,
          columnNumber: 8
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-overview-container-4", instance), initialValue: data.content["about-overview-container-4"] }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 152,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "about-overview-container-4", children: htmlFrom(data.content["about-overview-container-4"]) }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 154,
          columnNumber: 169
        }, this) }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 151,
          columnNumber: 8
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-overview-container-5", instance), initialValue: data.content["about-overview-container-5"] }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 159,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "about-overview-container-5", children: htmlFrom(data.content["about-overview-container-5"]) }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 161,
          columnNumber: 169
        }, this) }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 158,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about._index.tsx",
        lineNumber: 134,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about._index.tsx",
        lineNumber: 133,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about._index.tsx",
        lineNumber: 132,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-center", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-overview-container-6", instance), initialValue: data.content["about-overview-container-6"] }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 171,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "about-overview-container-6", children: htmlFrom(data.content["about-overview-container-6"]) }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 173,
          columnNumber: 167
        }, this) }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 170,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-7", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "inline-block rounded-full bg-orange-500 px-5 py-2 text-lg text-white", to: "/donate", children: "Donate to GVA" }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 178,
          columnNumber: 8
        }, this) }, void 0, false, {
          fileName: "app/routes/about._index.tsx",
          lineNumber: 177,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about._index.tsx",
        lineNumber: 169,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about._index.tsx",
        lineNumber: 168,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/about._index.tsx",
      lineNumber: 95,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Footer, {}, void 0, false, {
      fileName: "app/routes/about._index.tsx",
      lineNumber: 185,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/about._index.tsx",
    lineNumber: 92,
    columnNumber: 10
  }, this);
}
_s(About, "TBqYx9hN0H7O36quMgIX3Ab+WvU=", false, function() {
  return [useLoaderData, useOptionalUser, useNavigate];
});
_c = About;
var _c;
$RefreshReg$(_c, "About");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  About as default
};
//# sourceMappingURL=/build/routes/about._index-LI4T6FRN.js.map
