import {
  require_node
} from "/build/_shared/chunk-ZP6BZTHN.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __commonJS,
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// empty-module:~/models/import-queue.server
var require_import_queue = __commonJS({
  "empty-module:~/models/import-queue.server"(exports, module) {
    module.exports = {};
  }
});

// app/routes/admin.import.status.$filename.tsx
var import_node = __toESM(require_node(), 1);
var import_import_queue = __toESM(require_import_queue(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.import.status.$filename.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.import.status.$filename.tsx"
  );
  import.meta.hot.lastModified = "1751305542758.1995";
}
function ImportStatus() {
  return null;
}
_c = ImportStatus;
var _c;
$RefreshReg$(_c, "ImportStatus");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  ImportStatus as default
};
//# sourceMappingURL=/build/routes/admin.import.status.$filename-UK4H5DHE.js.map
