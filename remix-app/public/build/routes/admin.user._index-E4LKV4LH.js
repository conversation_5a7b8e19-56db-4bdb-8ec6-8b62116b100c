import {
  require_users
} from "/build/_shared/chunk-N5WPI4L5.js";
import {
  AdminPagination
} from "/build/_shared/chunk-5JSGFT47.js";
import "/build/_shared/chunk-QGERY6II.js";
import "/build/_shared/chunk-ULKIBZZM.js";
import "/build/_shared/chunk-OHSQI4V6.js";
import {
  require_cookies
} from "/build/_shared/chunk-MUAEGCBW.js";
import {
  Form,
  useFetcher,
  useLoaderData
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __commonJS,
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// empty-module:~/models/roles.server
var require_roles = __commonJS({
  "empty-module:~/models/roles.server"(exports, module) {
    module.exports = {};
  }
});

// app/routes/admin.user._index.tsx
var import_react = __toESM(require_react(), 1);
var import_cookies = __toESM(require_cookies(), 1);
var import_users = __toESM(require_users(), 1);
var import_roles = __toESM(require_roles(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.user._index.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
var _s2 = $RefreshSig$();
var _s3 = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.user._index.tsx"
  );
  import.meta.hot.lastModified = "1748457855905.0713";
}
var PAGE_SIZE = 25;
function UserIndexPage() {
  _s();
  const {
    users,
    usersCount,
    filter,
    roles,
    formSubmitMessage: formSubmitMessage2
  } = useLoaderData();
  const [showMessage, setShowMessage] = (0, import_react.useState)(!!formSubmitMessage2);
  (0, import_react.useEffect)(() => {
    if (showMessage) {
      const timeoutId = setTimeout(() => setShowMessage(false), 5e3);
      return () => clearTimeout(timeoutId);
    }
  }, [showMessage]);
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { className: "container mx-auto py-10", children: [
    showMessage ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("p", { className: "animate-fade bg-black text-white", children: formSubmitMessage2 }, void 0, false, {
      fileName: "app/routes/admin.user._index.tsx",
      lineNumber: 88,
      columnNumber: 19
    }, this) : null,
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { id: "search-form", role: "search", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex flex-col space-y-4", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex space-x-4", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "name", name: "name", type: "text", defaultValue: filter.name || "", placeholder: "Enter Name or Email to Search", autoComplete: "off", className: "border-2 w-80" }, void 0, false, {
          fileName: "app/routes/admin.user._index.tsx",
          lineNumber: 94,
          columnNumber: 8
        }, this) }, void 0, false, {
          fileName: "app/routes/admin.user._index.tsx",
          lineNumber: 93,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("select", { id: "role", name: "role", defaultValue: filter.rid || "", autoComplete: "off", className: "border-2", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("option", { value: "", children: "-- Role --" }, void 0, false, {
            fileName: "app/routes/admin.user._index.tsx",
            lineNumber: 98,
            columnNumber: 9
          }, this),
          roles.map((role) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("option", { value: role.id, children: role.name }, `role-${role.id}`, false, {
            fileName: "app/routes/admin.user._index.tsx",
            lineNumber: 99,
            columnNumber: 28
          }, this))
        ] }, void 0, true, {
          fileName: "app/routes/admin.user._index.tsx",
          lineNumber: 97,
          columnNumber: 8
        }, this) }, void 0, false, {
          fileName: "app/routes/admin.user._index.tsx",
          lineNumber: 96,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", className: "rounded-full bg-orange-500 px-5 py-1.5 text-lg text-white hover:bg-orange-800 focus:bg-orange-800", children: "Search" }, void 0, false, {
          fileName: "app/routes/admin.user._index.tsx",
          lineNumber: 105,
          columnNumber: 8
        }, this) }, void 0, false, {
          fileName: "app/routes/admin.user._index.tsx",
          lineNumber: 104,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.user._index.tsx",
        lineNumber: 92,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex items-center", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "disabled", name: "disabled", type: "checkbox", value: 1, defaultChecked: filter.status === false, autoComplete: "off", className: "mr-4 size-[20px] accent-orange-500" }, void 0, false, {
          fileName: "app/routes/admin.user._index.tsx",
          lineNumber: 112,
          columnNumber: 8
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "disabled", children: "Disabled Users" }, void 0, false, {
          fileName: "app/routes/admin.user._index.tsx",
          lineNumber: 113,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.user._index.tsx",
        lineNumber: 111,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.user._index.tsx",
        lineNumber: 110,
        columnNumber: 6
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.user._index.tsx",
      lineNumber: 91,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/admin.user._index.tsx",
      lineNumber: 90,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("table", { className: "table-gray mt-5 w-full table-auto text-sm", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("thead", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "ID" }, void 0, false, {
          fileName: "app/routes/admin.user._index.tsx",
          lineNumber: 122,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "User" }, void 0, false, {
          fileName: "app/routes/admin.user._index.tsx",
          lineNumber: 123,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Email" }, void 0, false, {
          fileName: "app/routes/admin.user._index.tsx",
          lineNumber: 124,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Role" }, void 0, false, {
          fileName: "app/routes/admin.user._index.tsx",
          lineNumber: 125,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Active" }, void 0, false, {
          fileName: "app/routes/admin.user._index.tsx",
          lineNumber: 126,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.user._index.tsx",
        lineNumber: 121,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.user._index.tsx",
        lineNumber: 120,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tbody", { children: [
        users.map((user) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: user.id }, void 0, false, {
            fileName: "app/routes/admin.user._index.tsx",
            lineNumber: 131,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: user.name }, void 0, false, {
            fileName: "app/routes/admin.user._index.tsx",
            lineNumber: 132,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: user.email }, void 0, false, {
            fileName: "app/routes/admin.user._index.tsx",
            lineNumber: 133,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex space-x-10", children: roles.map((role) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(RoleCheckbox, { user, role }, `user-${user.id}-role-${role.id}`, false, {
            fileName: "app/routes/admin.user._index.tsx",
            lineNumber: 136,
            columnNumber: 29
          }, this)) }, void 0, false, {
            fileName: "app/routes/admin.user._index.tsx",
            lineNumber: 135,
            columnNumber: 9
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.user._index.tsx",
            lineNumber: 134,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(StatusCheckbox, { user }, void 0, false, {
            fileName: "app/routes/admin.user._index.tsx",
            lineNumber: 140,
            columnNumber: 9
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.user._index.tsx",
            lineNumber: 139,
            columnNumber: 8
          }, this)
        ] }, `user-${user.id}`, true, {
          fileName: "app/routes/admin.user._index.tsx",
          lineNumber: 130,
          columnNumber: 25
        }, this)),
        users.length === 0 && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { colSpan: 5, children: "No users yet." }, void 0, false, {
          fileName: "app/routes/admin.user._index.tsx",
          lineNumber: 144,
          columnNumber: 8
        }, this) }, void 0, false, {
          fileName: "app/routes/admin.user._index.tsx",
          lineNumber: 143,
          columnNumber: 29
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.user._index.tsx",
        lineNumber: 129,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.user._index.tsx",
      lineNumber: 119,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "my-10 flex justify-center text-sm", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(AdminPagination, { total: usersCount, defaultPageSize: PAGE_SIZE }, void 0, false, {
      fileName: "app/routes/admin.user._index.tsx",
      lineNumber: 150,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/admin.user._index.tsx",
      lineNumber: 149,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.user._index.tsx",
    lineNumber: 87,
    columnNumber: 10
  }, this);
}
_s(UserIndexPage, "t/hiuGgy4ttHBAB5MefgDsAaSJ0=", false, function() {
  return [useLoaderData];
});
_c = UserIndexPage;
var RoleCheckbox = ({
  user,
  role
}) => {
  _s2();
  const fetcher = useFetcher();
  const onClick = () => {
    if (user.users_roles.findIndex((r) => r.rid === role.id) !== -1)
      return;
    const formData = new FormData();
    formData.append("user_id", user.id.toString());
    formData.append("role_id", role.id.toString());
    formData.append("action", "role");
    fetcher.submit(formData, {
      method: "POST"
    });
  };
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(fetcher.Form, { method: "post", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex items-center", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: `user-${user.id}-chk-role-${role.id}`, type: "checkbox", value: role.id, checked: user.users_roles.findIndex((r) => r.rid === role.id) !== -1, onClick, onChange: () => null, className: "mr-4 size-[20px] accent-orange-500" }, void 0, false, {
      fileName: "app/routes/admin.user._index.tsx",
      lineNumber: 176,
      columnNumber: 5
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: `user-${user.id}-chk-role-${role.id}`, children: role.name }, void 0, false, {
      fileName: "app/routes/admin.user._index.tsx",
      lineNumber: 177,
      columnNumber: 5
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.user._index.tsx",
    lineNumber: 175,
    columnNumber: 4
  }, this) }, void 0, false, {
    fileName: "app/routes/admin.user._index.tsx",
    lineNumber: 174,
    columnNumber: 10
  }, this);
};
_s2(RoleCheckbox, "2WHaGQTcUOgkXDaibwUgjUp1MBY=", false, function() {
  return [useFetcher];
});
_c2 = RoleCheckbox;
var StatusCheckbox = ({
  user
}) => {
  _s3();
  const fetcher = useFetcher();
  const onChange = (e) => {
    const formData = new FormData();
    formData.append("user_id", user.id.toString());
    formData.append("status", e.target.checked ? "1" : "0");
    formData.append("action", "status");
    fetcher.submit(formData, {
      method: "POST"
    });
  };
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(fetcher.Form, { method: "post", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex items-center", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: `user-${user.id}-chk-status`, type: "checkbox", value: 1, checked: user.status === true, onChange: (e) => onChange(e), className: "size-[20px] accent-orange-500" }, void 0, false, {
    fileName: "app/routes/admin.user._index.tsx",
    lineNumber: 201,
    columnNumber: 5
  }, this) }, void 0, false, {
    fileName: "app/routes/admin.user._index.tsx",
    lineNumber: 200,
    columnNumber: 4
  }, this) }, void 0, false, {
    fileName: "app/routes/admin.user._index.tsx",
    lineNumber: 199,
    columnNumber: 10
  }, this);
};
_s3(StatusCheckbox, "2WHaGQTcUOgkXDaibwUgjUp1MBY=", false, function() {
  return [useFetcher];
});
_c3 = StatusCheckbox;
var _c;
var _c2;
var _c3;
$RefreshReg$(_c, "UserIndexPage");
$RefreshReg$(_c2, "RoleCheckbox");
$RefreshReg$(_c3, "StatusCheckbox");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  UserIndexPage as default
};
//# sourceMappingURL=/build/routes/admin.user._index-E4LKV4LH.js.map
