import {
  require_dashboard
} from "/build/_shared/chunk-LR32EY3Q.js";
import "/build/_shared/chunk-AUYLHJJM.js";
import {
  require_cookies
} from "/build/_shared/chunk-MUAEGCBW.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import {
  Form,
  Link,
  isRouteErrorResponse,
  useActionData,
  useLoaderData,
  useRouteError
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  require_node
} from "/build/_shared/chunk-ZP6BZTHN.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/admin.dashboard.$id.tsx
var import_node = __toESM(require_node(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_dashboard = __toESM(require_dashboard(), 1);
var import_auth = __toESM(require_auth(), 1);
var import_cookies = __toESM(require_cookies(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.dashboard.$id.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
var _s2 = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.dashboard.$id.tsx"
  );
  import.meta.hot.lastModified = "1747752412821.7705";
}
function DashboardDetailsPage() {
  _s();
  const loaderData = useLoaderData();
  const actionData = useActionData();
  const nameRef = (0, import_react2.useRef)(null);
  const pathRef = (0, import_react2.useRef)(null);
  const dashboardIdRef = (0, import_react2.useRef)(null);
  const [name, setName] = (0, import_react2.useState)(loaderData.dashboard.name);
  const [path, setPath] = (0, import_react2.useState)(loaderData.dashboard.path);
  const [dashboardId, setDashboardId] = (0, import_react2.useState)(loaderData.dashboard.dashboard_id);
  const [weight, setWeight] = (0, import_react2.useState)(loaderData.dashboard.weight);
  const [isActive, setIsActive] = (0, import_react2.useState)(loaderData.dashboard.is_active);
  const [onMainMenu, setOnMainMenu] = (0, import_react2.useState)(loaderData.dashboard.on_main_menu);
  (0, import_react2.useEffect)(() => {
    setName(loaderData.dashboard.name);
    setPath(loaderData.dashboard.path);
    setDashboardId(loaderData.dashboard.dashboard_id);
    setWeight(loaderData.dashboard.weight);
    setIsActive(loaderData.dashboard.is_active);
    setOnMainMenu(loaderData.dashboard.on_main_menu);
    if (actionData?.errors?.name) {
      nameRef.current?.focus();
    } else if (actionData?.errors?.path) {
      pathRef.current?.focus();
    } else if (actionData?.errors?.dashboardId) {
      dashboardIdRef.current?.focus();
    }
  }, [actionData, loaderData.dashboard.name, setPath, setDashboardId, setWeight, setIsActive]);
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { method: "post", className: "flex w-1/2 flex-col gap-5", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex w-full flex-col gap-1", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "name", className: "text-lg font-bold after:ml-0.5 after:text-orange-500 after:content-['*']", children: "Name" }, void 0, false, {
            fileName: "app/routes/admin.dashboard.$id.tsx",
            lineNumber: 188,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { ref: nameRef, id: "name", name: "name", value: name, onChange: (e) => setName(e.target.value), className: "flex-1 rounded-md border-2 border-gray-500 px-5 py-2 text-lg leading-loose", "aria-invalid": actionData?.errors?.name ? true : void 0, "aria-errormessage": actionData?.errors?.name ? "name-error" : void 0 }, void 0, false, {
            fileName: "app/routes/admin.dashboard.$id.tsx",
            lineNumber: 191,
            columnNumber: 7
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.dashboard.$id.tsx",
          lineNumber: 187,
          columnNumber: 6
        }, this),
        actionData?.errors?.name ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "pt-1 text-red-700", id: "name-error", children: actionData?.errors?.name || null }, void 0, false, {
          fileName: "app/routes/admin.dashboard.$id.tsx",
          lineNumber: 193,
          columnNumber: 34
        }, this) : null
      ] }, void 0, true, {
        fileName: "app/routes/admin.dashboard.$id.tsx",
        lineNumber: 186,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex w-full flex-col gap-1", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "path", className: "text-lg font-bold after:ml-0.5 after:text-orange-500 after:content-['*']", children: "Path" }, void 0, false, {
            fileName: "app/routes/admin.dashboard.$id.tsx",
            lineNumber: 199,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { ref: pathRef, id: "path", name: "path", value: path, onChange: (e) => setPath(e.target.value), className: "w-full flex-1 rounded-md border-2 border-gray-500 px-5 py-2 text-lg leading-6", "aria-invalid": actionData?.errors?.path ? true : void 0, "aria-errormessage": actionData?.errors?.path ? "path-error" : void 0 }, void 0, false, {
            fileName: "app/routes/admin.dashboard.$id.tsx",
            lineNumber: 202,
            columnNumber: 7
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.dashboard.$id.tsx",
          lineNumber: 198,
          columnNumber: 6
        }, this),
        actionData?.errors?.path ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "pt-1 text-red-700", id: "path-error", children: actionData?.errors?.path || null }, void 0, false, {
          fileName: "app/routes/admin.dashboard.$id.tsx",
          lineNumber: 204,
          columnNumber: 34
        }, this) : null
      ] }, void 0, true, {
        fileName: "app/routes/admin.dashboard.$id.tsx",
        lineNumber: 197,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex w-full flex-col gap-1", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "dashboardId", className: "text-lg font-bold after:ml-0.5 after:text-orange-500 after:content-['*']", children: "Dashboard ID" }, void 0, false, {
            fileName: "app/routes/admin.dashboard.$id.tsx",
            lineNumber: 211,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { ref: dashboardIdRef, id: "dashboardId", name: "dashboardId", value: dashboardId, onChange: (e) => setDashboardId(e.target.value), className: "w-full flex-1 rounded-md border-2 border-gray-500 px-3 py-2 text-lg leading-6", "aria-invalid": actionData?.errors?.dashboardId ? true : void 0, "aria-errormessage": actionData?.errors?.dashboardId ? "dashboardId-error" : void 0 }, void 0, false, {
            fileName: "app/routes/admin.dashboard.$id.tsx",
            lineNumber: 214,
            columnNumber: 7
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.dashboard.$id.tsx",
          lineNumber: 210,
          columnNumber: 6
        }, this),
        actionData?.errors?.dashboardId ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "pt-1 text-red-700", id: "path-error", children: actionData?.errors?.dashboardId || null }, void 0, false, {
          fileName: "app/routes/admin.dashboard.$id.tsx",
          lineNumber: 216,
          columnNumber: 41
        }, this) : null
      ] }, void 0, true, {
        fileName: "app/routes/admin.dashboard.$id.tsx",
        lineNumber: 209,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex w-full flex-col gap-1", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "weight", className: "text-lg font-bold", children: "Weight" }, void 0, false, {
          fileName: "app/routes/admin.dashboard.$id.tsx",
          lineNumber: 223,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "weight", name: "weight", value: weight, onChange: (e) => setWeight(e.target.value), className: "w-full flex-1 rounded-md border-2 border-gray-500 px-3 py-2 text-lg leading-6", pattern: "^\\d*$", title: "Enter a number or leave this field empty to default to 0" }, void 0, false, {
          fileName: "app/routes/admin.dashboard.$id.tsx",
          lineNumber: 226,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.dashboard.$id.tsx",
        lineNumber: 222,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.dashboard.$id.tsx",
        lineNumber: 221,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex items-center", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "checkbox", id: "isActive", name: "isActive", checked: isActive, onChange: (e) => setIsActive(e.target.checked), className: "mr-4 size-[30px] accent-orange-500" }, void 0, false, {
          fileName: "app/routes/admin.dashboard.$id.tsx",
          lineNumber: 232,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "isActive", className: "text-lg", children: "Is Active" }, void 0, false, {
          fileName: "app/routes/admin.dashboard.$id.tsx",
          lineNumber: 233,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.dashboard.$id.tsx",
        lineNumber: 231,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.dashboard.$id.tsx",
        lineNumber: 230,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex items-center", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "checkbox", id: "onMainMenu", name: "onMainMenu", checked: onMainMenu, onChange: (e) => setOnMainMenu(e.target.checked), className: "mr-4 size-[30px] accent-orange-500" }, void 0, false, {
          fileName: "app/routes/admin.dashboard.$id.tsx",
          lineNumber: 241,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "onMainMenu", className: "text-lg", children: "Display on Main Menu" }, void 0, false, {
          fileName: "app/routes/admin.dashboard.$id.tsx",
          lineNumber: 242,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.dashboard.$id.tsx",
        lineNumber: 240,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.dashboard.$id.tsx",
        lineNumber: 239,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-left", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white hover:bg-orange-800 focus:bg-orange-800", children: "Save" }, void 0, false, {
          fileName: "app/routes/admin.dashboard.$id.tsx",
          lineNumber: 249,
          columnNumber: 6
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "/admin/dashboard/", className: "ml-4 inline-block rounded-full bg-gray-500 px-5 py-2 text-lg text-white hover:bg-gray-600 focus:bg-gray-600", children: "Cancel" }, void 0, false, {
          fileName: "app/routes/admin.dashboard.$id.tsx",
          lineNumber: 252,
          columnNumber: 6
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.dashboard.$id.tsx",
        lineNumber: 248,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.dashboard.$id.tsx",
      lineNumber: 185,
      columnNumber: 4
    }, this),
    loaderData.dashboard.id && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { method: "post", className: "mt-5", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "actionType", value: "delete" }, void 0, false, {
        fileName: "app/routes/admin.dashboard.$id.tsx",
        lineNumber: 258,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", className: "rounded-full bg-red-500 px-5 py-2 text-lg text-white hover:bg-red-600 focus:bg-red-600", onClick: (e) => {
        if (!window.confirm("Are you sure you want to delete this dashboard?")) {
          e.preventDefault();
        }
      }, children: "Delete" }, void 0, false, {
        fileName: "app/routes/admin.dashboard.$id.tsx",
        lineNumber: 259,
        columnNumber: 6
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.dashboard.$id.tsx",
      lineNumber: 257,
      columnNumber: 32
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.dashboard.$id.tsx",
    lineNumber: 184,
    columnNumber: 10
  }, this);
}
_s(DashboardDetailsPage, "XokyQm5jUv1vJulRZJKZN7kUhG8=", false, function() {
  return [useLoaderData, useActionData];
});
_c = DashboardDetailsPage;
function ErrorBoundary() {
  _s2();
  const error = useRouteError();
  if (error instanceof Error) {
    return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
      "An unexpected error occurred: ",
      error.message
    ] }, void 0, true, {
      fileName: "app/routes/admin.dashboard.$id.tsx",
      lineNumber: 277,
      columnNumber: 12
    }, this);
  }
  if (!isRouteErrorResponse(error)) {
    return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h1", { children: "Unknown Error" }, void 0, false, {
      fileName: "app/routes/admin.dashboard.$id.tsx",
      lineNumber: 280,
      columnNumber: 12
    }, this);
  }
  if (error.status === 404) {
    return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: "Dashboard not found" }, void 0, false, {
      fileName: "app/routes/admin.dashboard.$id.tsx",
      lineNumber: 283,
      columnNumber: 12
    }, this);
  }
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
    "An unexpected error occurred: ",
    error.statusText
  ] }, void 0, true, {
    fileName: "app/routes/admin.dashboard.$id.tsx",
    lineNumber: 285,
    columnNumber: 10
  }, this);
}
_s2(ErrorBoundary, "oAgjgbJzsRXlB89+MoVumxMQqKM=", false, function() {
  return [useRouteError];
});
_c2 = ErrorBoundary;
var _c;
var _c2;
$RefreshReg$(_c, "DashboardDetailsPage");
$RefreshReg$(_c2, "ErrorBoundary");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  ErrorBoundary,
  DashboardDetailsPage as default
};
//# sourceMappingURL=/build/routes/admin.dashboard.$id-QWXBEOEU.js.map
