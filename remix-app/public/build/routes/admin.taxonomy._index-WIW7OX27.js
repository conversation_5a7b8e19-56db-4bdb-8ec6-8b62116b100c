import {
  Icon_Admin_Incidents_default
} from "/build/_shared/chunk-AN3YALPB.js";
import {
  require_taxonomy
} from "/build/_shared/chunk-AO7MZAIH.js";
import {
  Icon_Admin_Edit_default
} from "/build/_shared/chunk-O4ZMMQVI.js";
import {
  require_cookies
} from "/build/_shared/chunk-MUAEGCBW.js";
import {
  Link,
  useLoaderData
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/admin.taxonomy._index.tsx
var import_react = __toESM(require_react(), 1);
var import_cookies = __toESM(require_cookies(), 1);
var import_taxonomy = __toESM(require_taxonomy(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.taxonomy._index.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.taxonomy._index.tsx"
  );
  import.meta.hot.lastModified = "1742571036284.1782";
}
function TaxonomyIndexPage() {
  _s();
  const {
    taxonomyList,
    formSubmitMessage: formSubmitMessage2
  } = useLoaderData();
  const [showMessage, setShowMessage] = (0, import_react.useState)(!!formSubmitMessage2);
  (0, import_react.useEffect)(() => {
    if (showMessage) {
      const timeoutId = setTimeout(() => setShowMessage(false), 5e3);
      return () => clearTimeout(timeoutId);
    }
  }, [showMessage]);
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { className: "container mx-auto pb-10 pt-8", children: [
    showMessage ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("p", { className: "animate-fade bg-black text-white", children: formSubmitMessage2 }, void 0, false, {
      fileName: "app/routes/admin.taxonomy._index.tsx",
      lineNumber: 53,
      columnNumber: 19
    }, this) : null,
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[25px] font-bold", children: "Manage Taxonomy" }, void 0, false, {
      fileName: "app/routes/admin.taxonomy._index.tsx",
      lineNumber: 55,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("table", { className: "table-gray mt-8 w-full table-auto text-sm", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("thead", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Taxonomy" }, void 0, false, {
          fileName: "app/routes/admin.taxonomy._index.tsx",
          lineNumber: 60,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "\xA0" }, void 0, false, {
          fileName: "app/routes/admin.taxonomy._index.tsx",
          lineNumber: 61,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.taxonomy._index.tsx",
        lineNumber: 59,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.taxonomy._index.tsx",
        lineNumber: 58,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tbody", { children: [
        taxonomyList.map((taxonomy) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: taxonomy.taxonomy_name }, void 0, false, {
            fileName: "app/routes/admin.taxonomy._index.tsx",
            lineNumber: 66,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex space-x-10", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: `/admin/taxonomy/${taxonomy.taxonomy_machine_name}`, className: "flex items-center font-bold text-blue-500", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Incidents_default, alt: "", width: 16 }, void 0, false, {
                fileName: "app/routes/admin.taxonomy._index.tsx",
                lineNumber: 70,
                columnNumber: 11
              }, this),
              " List Terms"
            ] }, void 0, true, {
              fileName: "app/routes/admin.taxonomy._index.tsx",
              lineNumber: 69,
              columnNumber: 10
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: `/admin/taxonomy/term/new?vocabulary=${taxonomy.taxonomy_machine_name}`, className: "flex items-center font-bold text-blue-500", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Edit_default, alt: "", width: 16 }, void 0, false, {
                fileName: "app/routes/admin.taxonomy._index.tsx",
                lineNumber: 73,
                columnNumber: 11
              }, this),
              " Add Term"
            ] }, void 0, true, {
              fileName: "app/routes/admin.taxonomy._index.tsx",
              lineNumber: 72,
              columnNumber: 10
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.taxonomy._index.tsx",
            lineNumber: 68,
            columnNumber: 9
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.taxonomy._index.tsx",
            lineNumber: 67,
            columnNumber: 8
          }, this)
        ] }, `taxonomy-${taxonomy.taxonomy_machine_name}`, true, {
          fileName: "app/routes/admin.taxonomy._index.tsx",
          lineNumber: 65,
          columnNumber: 36
        }, this)),
        taxonomyList.length === 0 && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { colSpan: 6, children: "No taxonomy yet." }, void 0, false, {
          fileName: "app/routes/admin.taxonomy._index.tsx",
          lineNumber: 79,
          columnNumber: 8
        }, this) }, void 0, false, {
          fileName: "app/routes/admin.taxonomy._index.tsx",
          lineNumber: 78,
          columnNumber: 36
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.taxonomy._index.tsx",
        lineNumber: 64,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.taxonomy._index.tsx",
      lineNumber: 57,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.taxonomy._index.tsx",
    lineNumber: 52,
    columnNumber: 10
  }, this);
}
_s(TaxonomyIndexPage, "zH8i40FH6jHyUxfDbw7H5e/1Ga4=", false, function() {
  return [useLoaderData];
});
_c = TaxonomyIndexPage;
var _c;
$RefreshReg$(_c, "TaxonomyIndexPage");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  TaxonomyIndexPage as default
};
//# sourceMappingURL=/build/routes/admin.taxonomy._index-WIW7OX27.js.map
