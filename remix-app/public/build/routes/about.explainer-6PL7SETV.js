import {
  require_range
} from "/build/_shared/chunk-XEU2OR47.js";
import {
  require_about,
  require_js,
  require_reduce
} from "/build/_shared/chunk-O7YZF2UJ.js";
import {
  Editor,
  htmlFrom,
  require_browser,
  tinyConfig
} from "/build/_shared/chunk-SIGMETZO.js";
import "/build/_shared/chunk-WBY37MGY.js";
import "/build/_shared/chunk-IVTPFYOU.js";
import {
  Title
} from "/build/_shared/chunk-YPQMQ54K.js";
import {
  Foot<PERSON>,
  Header
} from "/build/_shared/chunk-4556QKWU.js";
import "/build/_shared/chunk-W7JLG7II.js";
import "/build/_shared/chunk-OHIZ2QAR.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import {
  useOptionalUser
} from "/build/_shared/chunk-DFTVPORL.js";
import {
  Link,
  useLoaderData,
  useNavigate
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/about.explainer.tsx
var import_react = __toESM(require_react(), 1);
var import_about = __toESM(require_about(), 1);
var import_auth = __toESM(require_auth(), 1);
var import_react_multi_ref = __toESM(require_js(), 1);
var import_isomorphic_dompurify = __toESM(require_browser(), 1);
var import_reduce = __toESM(require_reduce(), 1);
var import_range = __toESM(require_range(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/about.explainer.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/about.explainer.tsx"
  );
  import.meta.hot.lastModified = "1748454242344.255";
}
function AboutExplainer() {
  _s();
  const data = useLoaderData();
  const user = useOptionalUser();
  const navigate = useNavigate();
  const [editorRefs] = (0, import_react.useState)(() => new import_react_multi_ref.default());
  const [editing, setEditing] = (0, import_react.useState)(false);
  const editingRef = (0, import_react.useRef)(editing);
  const alertUser = (e) => {
    if (editingRef.current) {
      e.preventDefault();
    }
  };
  (0, import_react.useEffect)(() => {
    editingRef.current = editing;
  }, [editing]);
  (0, import_react.useEffect)(() => {
    window.addEventListener("beforeunload", alertUser);
    return () => {
      window.removeEventListener("beforeunload", alertUser);
    };
  }, []);
  const save = async (body) => {
    await fetch("/about/explainer", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(body)
    });
    navigate(".", {
      replace: true
    });
  };
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "min-h-screen", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Header, {}, void 0, false, {
      fileName: "app/routes/about.explainer.tsx",
      lineNumber: 98,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Title, { title: "Gun Violence Archive Explainer" }, void 0, false, {
      fileName: "app/routes/about.explainer.tsx",
      lineNumber: 99,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { children: [
      user && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "fixed bottom-0 left-0 border-2 bg-white px-5 py-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { className: "text-xl", onClick: () => {
        if (editing) {
          save([...(0, import_range.default)(1, 14).map((value) => ({
            contentid: "about-explainer-container-" + value,
            content: editorRefs.map.get("about-explainer-container-" + value)?.getContent() || ""
          }))]).then(() => setEditing(false));
        } else {
          setEditing(true);
        }
      }, children: editing ? "Save" : "Edit" }, void 0, false, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 102,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 101,
        columnNumber: 14
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex flex-col items-center", children: [
        editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          ...tinyConfig
        }, id: "about-explainer-container-1", onInit: (_evt, instance) => editorRefs.map.set("about-explainer-container-1", instance), initialValue: data.content["about-explainer-container-1"] }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 117,
          columnNumber: 18
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "max-w-[1000px] text-center font-bold text-4xl md:text-[50px] md:leading-[60px]", id: "about-explainer-container-1", children: htmlFrom(data.content["about-explainer-container-1"]) }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 119,
          columnNumber: 200
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-7", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "inline-block rounded-full bg-orange-500 px-5 py-2 text-lg text-white", to: "/contact", children: "Contact GVA" }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 123,
          columnNumber: 8
        }, this) }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 122,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 116,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 115,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-gray-200", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg:flex", children: [
        editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          width: 350,
          ...tinyConfig
        }, id: "about-explainer-container-2", onInit: (_evt, instance) => editorRefs.map.set("about-explainer-container-2", instance), initialValue: data.content["about-explainer-container-2"] }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 132,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-none lg:w-[350px]", id: "about-explainer-container-2", children: htmlFrom(data.content["about-explainer-container-2"]) }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 135,
          columnNumber: 202
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 lg:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          ...tinyConfig
        }, id: "about-explainer-container-3", onInit: (_evt, instance) => editorRefs.map.set("about-explainer-container-3", instance), initialValue: data.content["about-explainer-container-3"] }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 139,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "paragraph", id: "about-explainer-container-3", children: htmlFrom(data.content["about-explainer-container-3"]) }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 141,
          columnNumber: 204
        }, this) }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 138,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 131,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 130,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 129,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-white", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg:flex", children: [
        editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          width: 350,
          ...tinyConfig
        }, id: "about-explainer-container-4", onInit: (_evt, instance) => editorRefs.map.set("about-explainer-container-4", instance), initialValue: data.content["about-explainer-container-4"] }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 151,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-none lg:w-[350px]", id: "about-explainer-container-4", children: htmlFrom(data.content["about-explainer-container-4"]) }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 154,
          columnNumber: 202
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 lg:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          ...tinyConfig
        }, id: "about-explainer-container-5", onInit: (_evt, instance) => editorRefs.map.set("about-explainer-container-5", instance), initialValue: data.content["about-explainer-container-5"] }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 158,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "paragraph", id: "about-explainer-container-5", children: htmlFrom(data.content["about-explainer-container-5"]) }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 160,
          columnNumber: 204
        }, this) }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 157,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 150,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 149,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 148,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-gray-200", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg:flex", children: [
        editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          width: 350,
          ...tinyConfig
        }, id: "about-explainer-container-6", onInit: (_evt, instance) => editorRefs.map.set("about-explainer-container-6", instance), initialValue: data.content["about-explainer-container-6"] }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 170,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-none lg:w-[350px]", id: "about-explainer-container-6", children: htmlFrom(data.content["about-explainer-container-6"]) }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 173,
          columnNumber: 202
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 lg:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          ...tinyConfig
        }, id: "about-explainer-container-7", onInit: (_evt, instance) => editorRefs.map.set("about-explainer-container-7", instance), initialValue: data.content["about-explainer-container-7"] }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 177,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "paragraph", id: "about-explainer-container-7", children: htmlFrom(data.content["about-explainer-container-7"]) }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 179,
          columnNumber: 204
        }, this) }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 176,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 169,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 168,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 167,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-white", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg:flex", children: [
        editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          width: 350,
          ...tinyConfig
        }, id: "about-explainer-container-8", onInit: (_evt, instance) => editorRefs.map.set("about-explainer-container-8", instance), initialValue: data.content["about-explainer-container-8"] }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 189,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-none lg:w-[350px]", id: "about-explainer-container-8", children: htmlFrom(data.content["about-explainer-container-8"]) }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 192,
          columnNumber: 202
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 lg:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          ...tinyConfig
        }, id: "about-explainer-container-9", onInit: (_evt, instance) => editorRefs.map.set("about-explainer-container-9", instance), initialValue: data.content["about-explainer-container-9"] }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 196,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "paragraph", id: "about-explainer-container-9", children: htmlFrom(data.content["about-explainer-container-9"]) }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 198,
          columnNumber: 204
        }, this) }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 195,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 188,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 187,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 186,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-gray-200", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg:flex", children: [
        editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          width: 350,
          ...tinyConfig
        }, id: "about-explainer-container-10", onInit: (_evt, instance) => editorRefs.map.set("about-explainer-container-10", instance), initialValue: data.content["about-explainer-container-10"] }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 208,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-none lg:w-[350px]", id: "about-explainer-container-10", children: htmlFrom(data.content["about-explainer-container-10"]) }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 211,
          columnNumber: 205
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 lg:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          ...tinyConfig
        }, id: "about-explainer-container-11", onInit: (_evt, instance) => editorRefs.map.set("about-explainer-container-11", instance), initialValue: data.content["about-explainer-container-11"] }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 215,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "paragraph", id: "about-explainer-container-11", children: htmlFrom(data.content["about-explainer-container-11"]) }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 217,
          columnNumber: 207
        }, this) }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 214,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 207,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 206,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 205,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-white", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg:flex", children: [
        editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          width: 350,
          ...tinyConfig
        }, id: "about-explainer-container-12", onInit: (_evt, instance) => editorRefs.map.set("about-explainer-container-12", instance), initialValue: data.content["about-explainer-container-12"] }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 227,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-none lg:w-[350px]", id: "about-explainer-container-12", children: htmlFrom(data.content["about-explainer-container-12"]) }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 230,
          columnNumber: 205
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 lg:pl-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          ...tinyConfig
        }, id: "about-explainer-container-13", onInit: (_evt, instance) => editorRefs.map.set("about-explainer-container-13", instance), initialValue: data.content["about-explainer-container-13"] }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 234,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "paragraph", id: "about-explainer-13", children: htmlFrom(data.content["about-explainer-container-13"]) }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 236,
          columnNumber: 207
        }, this) }, void 0, false, {
          fileName: "app/routes/about.explainer.tsx",
          lineNumber: 233,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 226,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 225,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.explainer.tsx",
        lineNumber: 224,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/about.explainer.tsx",
      lineNumber: 100,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Footer, {}, void 0, false, {
      fileName: "app/routes/about.explainer.tsx",
      lineNumber: 244,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/about.explainer.tsx",
    lineNumber: 97,
    columnNumber: 10
  }, this);
}
_s(AboutExplainer, "SBuWdnW64+wOeAma5mJSiqFM3Y0=", false, function() {
  return [useLoaderData, useOptionalUser, useNavigate];
});
_c = AboutExplainer;
var _c;
$RefreshReg$(_c, "AboutExplainer");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  AboutExplainer as default
};
//# sourceMappingURL=/build/routes/about.explainer-6PL7SETV.js.map
