import {
  require_build
} from "/build/_shared/chunk-E55QCST2.js";
import {
  Icon_Success_default
} from "/build/_shared/chunk-GBO3FEGG.js";
import {
  require_users
} from "/build/_shared/chunk-N5WPI4L5.js";
import {
  Title
} from "/build/_shared/chunk-YPQMQ54K.js";
import {
  Icon_Admin_Delete_default
} from "/build/_shared/chunk-RUOV274T.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import {
  Form,
  useActionData,
  useNavigation
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/user.reset.tsx
var import_remix_auth = __toESM(require_build(), 1);
var import_auth = __toESM(require_auth(), 1);
var import_users = __toESM(require_users(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/user.reset.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/user.reset.tsx"
  );
  import.meta.hot.lastModified = "1748458718565.8125";
}
var meta = () => {
  return [{
    title: "Gun Violence Archive | Password Reset"
  }];
};
function UserReset() {
  _s();
  const notices = useActionData();
  const transition = useNavigation();
  const isSubmitting = Boolean(transition.state == "submitting");
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(import_jsx_dev_runtime.Fragment, { children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Title, { title: "Forgot Password" }, void 0, false, {
      fileName: "app/routes/user.reset.tsx",
      lineNumber: 85,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-center", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] bg-gray-200 p-4 sm:w-[480px] xl:p-10", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { method: "post", id: "reset", children: [
        notices && "success" in notices && notices.success && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-5 flex bg-white px-5 py-3.5 text-sm font-light", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Icon_Success_default, alt: "", width: 30 }, void 0, false, {
            fileName: "app/routes/user.reset.tsx",
            lineNumber: 92,
            columnNumber: 12
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "pl-2", children: notices.success }, void 0, false, {
            fileName: "app/routes/user.reset.tsx",
            lineNumber: 93,
            columnNumber: 12
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/user.reset.tsx",
          lineNumber: 91,
          columnNumber: 64
        }, this),
        notices && "message" in notices && notices.message && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-5 flex bg-white px-5 py-3.5 text-sm font-light", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Icon_Admin_Delete_default, alt: "", width: 16 }, void 0, false, {
            fileName: "app/routes/user.reset.tsx",
            lineNumber: 96,
            columnNumber: 12
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "pl-2 text-red-500", children: notices.message }, void 0, false, {
            fileName: "app/routes/user.reset.tsx",
            lineNumber: 97,
            columnNumber: 12
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/user.reset.tsx",
          lineNumber: 95,
          columnNumber: 64
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-8", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "email", className: "block text-lg font-bold", children: "Email" }, void 0, false, {
            fileName: "app/routes/user.reset.tsx",
            lineNumber: 100,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-3.5 text-[15px]", children: "Enter your Gun Violence Archive email." }, void 0, false, {
            fileName: "app/routes/user.reset.tsx",
            lineNumber: 103,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "email", name: "email", type: "email", autoComplete: "username", required: true, className: "block w-full rounded-md px-3 py-2 leading-loose" }, void 0, false, {
            fileName: "app/routes/user.reset.tsx",
            lineNumber: 104,
            columnNumber: 10
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/user.reset.tsx",
          lineNumber: 99,
          columnNumber: 9
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", disabled: isSubmitting, className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white", children: isSubmitting ? "Submitting" : "Submit" }, void 0, false, {
            fileName: "app/routes/user.reset.tsx",
            lineNumber: 107,
            columnNumber: 10
          }, this),
          isSubmitting && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lds-ring", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
              fileName: "app/routes/user.reset.tsx",
              lineNumber: 111,
              columnNumber: 12
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
              fileName: "app/routes/user.reset.tsx",
              lineNumber: 112,
              columnNumber: 12
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
              fileName: "app/routes/user.reset.tsx",
              lineNumber: 113,
              columnNumber: 12
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
              fileName: "app/routes/user.reset.tsx",
              lineNumber: 114,
              columnNumber: 12
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/user.reset.tsx",
            lineNumber: 110,
            columnNumber: 27
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/user.reset.tsx",
          lineNumber: 106,
          columnNumber: 9
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/user.reset.tsx",
        lineNumber: 90,
        columnNumber: 8
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-4 text-lg text-blue-500", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { href: "/user/login", children: "Back to Sign In" }, void 0, false, {
        fileName: "app/routes/user.reset.tsx",
        lineNumber: 119,
        columnNumber: 9
      }, this) }, void 0, false, {
        fileName: "app/routes/user.reset.tsx",
        lineNumber: 118,
        columnNumber: 8
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/user.reset.tsx",
      lineNumber: 89,
      columnNumber: 7
    }, this) }, void 0, false, {
      fileName: "app/routes/user.reset.tsx",
      lineNumber: 88,
      columnNumber: 6
    }, this) }, void 0, false, {
      fileName: "app/routes/user.reset.tsx",
      lineNumber: 87,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/user.reset.tsx",
      lineNumber: 86,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/user.reset.tsx",
    lineNumber: 84,
    columnNumber: 10
  }, this);
}
_s(UserReset, "aNgK6vHpzvtHPH8j8NOAZXIGR4E=", false, function() {
  return [useActionData, useNavigation];
});
_c = UserReset;
var _c;
$RefreshReg$(_c, "UserReset");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  UserReset as default,
  meta
};
//# sourceMappingURL=/build/routes/user.reset-PW3UDG2F.js.map
