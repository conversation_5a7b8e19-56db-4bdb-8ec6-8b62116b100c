import {
  require_range
} from "/build/_shared/chunk-XEU2OR47.js";
import {
  require_about,
  require_js,
  require_reduce
} from "/build/_shared/chunk-O7YZF2UJ.js";
import {
  Editor,
  htmlFrom,
  require_browser,
  tinyConfig
} from "/build/_shared/chunk-SIGMETZO.js";
import "/build/_shared/chunk-WBY37MGY.js";
import "/build/_shared/chunk-IVTPFYOU.js";
import {
  Title
} from "/build/_shared/chunk-YPQMQ54K.js";
import {
  Foot<PERSON>,
  Header
} from "/build/_shared/chunk-4556QKWU.js";
import "/build/_shared/chunk-W7JLG7II.js";
import "/build/_shared/chunk-OHIZ2QAR.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import {
  useOptionalUser
} from "/build/_shared/chunk-DFTVPORL.js";
import {
  Link,
  useLoaderData,
  useNavigate
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/about.faq.tsx
var import_react = __toESM(require_react(), 1);
var import_about = __toESM(require_about(), 1);
var import_auth = __toESM(require_auth(), 1);
var import_react_multi_ref = __toESM(require_js(), 1);
var import_isomorphic_dompurify = __toESM(require_browser(), 1);
var import_reduce = __toESM(require_reduce(), 1);
var import_range = __toESM(require_range(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/about.faq.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/about.faq.tsx"
  );
  import.meta.hot.lastModified = "1748454706502.0586";
}
function AboutFaq() {
  _s();
  const data = useLoaderData();
  const user = useOptionalUser();
  const navigate = useNavigate();
  const [editorRefs] = (0, import_react.useState)(() => new import_react_multi_ref.default());
  const [editing, setEditing] = (0, import_react.useState)(false);
  const editingRef = (0, import_react.useRef)(editing);
  const alertUser = (e) => {
    if (editingRef.current) {
      e.preventDefault();
    }
  };
  (0, import_react.useEffect)(() => {
    editingRef.current = editing;
  }, [editing]);
  (0, import_react.useEffect)(() => {
    window.addEventListener("beforeunload", alertUser);
    return () => {
      window.removeEventListener("beforeunload", alertUser);
    };
  }, []);
  const save = async (body) => {
    await fetch("/about/faq", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(body)
    });
    navigate(".", {
      replace: true
    });
  };
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "min-h-screen", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Header, {}, void 0, false, {
      fileName: "app/routes/about.faq.tsx",
      lineNumber: 92,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Title, { title: "GVA Frequently Asked Questions" }, void 0, false, {
      fileName: "app/routes/about.faq.tsx",
      lineNumber: 93,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { children: [
      user && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "fixed bottom-0 left-0 border-2 bg-white px-5 py-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { className: "text-xl", onClick: () => {
        if (editing) {
          save([...(0, import_range.default)(1, 12).map((value) => ({
            contentid: "about-faq-container-" + value,
            content: editorRefs.map.get("about-faq-container-" + value)?.getContent() || ""
          }))]).then(() => setEditing(false));
        } else {
          setEditing(true);
        }
      }, children: editing ? "Save" : "Edit" }, void 0, false, {
        fileName: "app/routes/about.faq.tsx",
        lineNumber: 96,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.faq.tsx",
        lineNumber: 95,
        columnNumber: 14
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex flex-col items-center", children: [
        editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-faq-container-1",
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-faq-container-1", instance), initialValue: data.content["about-faq-container-1"] }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 111,
          columnNumber: 18
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "max-w-[1000px] text-center font-bold text-4xl md:text-[50px] md:leading-[60px]", id: "about-faq-container-1", children: htmlFrom(data.content["about-faq-container-1"]) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 114,
          columnNumber: 155
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-7", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "inline-block rounded-full bg-orange-500 px-5 py-2 text-lg text-white", to: "/contact", children: "Contact GVA" }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 118,
          columnNumber: 8
        }, this) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 117,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.faq.tsx",
        lineNumber: 110,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.faq.tsx",
        lineNumber: 109,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex flex-col space-y-10 pb-20", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] bg-gray-200 p-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-faq-container-2",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-faq-container-2", instance), initialValue: data.content["about-faq-container-2"] }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 127,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg flex-none", id: "about-faq-container-2", children: htmlFrom(data.content["about-faq-container-2"]) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 131,
          columnNumber: 157
        }, this) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 126,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] bg-gray-200 p-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-faq-container-3",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-faq-container-3", instance), initialValue: data.content["about-faq-container-3"] }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 136,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg flex-none", id: "about-faq-container-3", children: htmlFrom(data.content["about-faq-container-3"]) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 140,
          columnNumber: 157
        }, this) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 135,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] bg-gray-200 p-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-faq-container-4",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-faq-container-4", instance), initialValue: data.content["about-faq-container-4"] }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 145,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg flex-none", id: "about-faq-container-4", children: htmlFrom(data.content["about-faq-container-4"]) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 149,
          columnNumber: 157
        }, this) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 144,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] bg-gray-200 p-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-faq-container-5",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-faq-container-5", instance), initialValue: data.content["about-faq-container-5"] }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 154,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg flex-none", id: "about-faq-container-5", children: htmlFrom(data.content["about-faq-container-5"]) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 158,
          columnNumber: 157
        }, this) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 153,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] bg-gray-200 p-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-faq-container-6",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-faq-container-6", instance), initialValue: data.content["about-faq-container-6"] }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 163,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg flex-none", id: "about-faq-container-6", children: htmlFrom(data.content["about-faq-container-6"]) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 167,
          columnNumber: 157
        }, this) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 162,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] bg-gray-200 p-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-faq-container-7",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-faq-container-7", instance), initialValue: data.content["about-faq-container-7"] }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 172,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg flex-none", id: "about-faq-container-7", children: htmlFrom(data.content["about-faq-container-7"]) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 176,
          columnNumber: 157
        }, this) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 171,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] bg-gray-200 p-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-faq-container-8",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-faq-container-8", instance), initialValue: data.content["about-faq-container-8"] }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 181,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg flex-none", id: "about-faq-container-8", children: htmlFrom(data.content["about-faq-container-8"]) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 185,
          columnNumber: 157
        }, this) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 180,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] bg-gray-200 p-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-faq-container-9",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-faq-container-9", instance), initialValue: data.content["about-faq-container-9"] }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 190,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg flex-none", id: "about-faq-container-9", children: htmlFrom(data.content["about-faq-container-9"]) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 194,
          columnNumber: 157
        }, this) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 189,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] bg-gray-200 p-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-faq-container-10",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-faq-container-10", instance), initialValue: data.content["about-faq-container-10"] }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 199,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg flex-none", id: "about-faq-container-10", children: htmlFrom(data.content["about-faq-container-10"]) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 203,
          columnNumber: 159
        }, this) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 198,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] bg-gray-200 p-10", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-faq-container-11",
          width: 350,
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-faq-container-11", instance), initialValue: data.content["about-faq-container-11"] }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 208,
          columnNumber: 19
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg flex-none", id: "about-faq-container-11", children: htmlFrom(data.content["about-faq-container-11"]) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 212,
          columnNumber: 159
        }, this) }, void 0, false, {
          fileName: "app/routes/about.faq.tsx",
          lineNumber: 207,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.faq.tsx",
        lineNumber: 125,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.faq.tsx",
        lineNumber: 124,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/about.faq.tsx",
      lineNumber: 94,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Footer, {}, void 0, false, {
      fileName: "app/routes/about.faq.tsx",
      lineNumber: 219,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/about.faq.tsx",
    lineNumber: 91,
    columnNumber: 10
  }, this);
}
_s(AboutFaq, "zVyzhMJ5DkvqVWYPz1OnDlPPbmY=", false, function() {
  return [useLoaderData, useOptionalUser, useNavigate];
});
_c = AboutFaq;
var _c;
$RefreshReg$(_c, "AboutFaq");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  AboutFaq as default
};
//# sourceMappingURL=/build/routes/about.faq-WSAPAX3C.js.map
