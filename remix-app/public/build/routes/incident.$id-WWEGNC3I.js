import {
  require_s3
} from "/build/_shared/chunk-AXOFG3UT.js";
import {
  Title
} from "/build/_shared/chunk-YPQMQ54K.js";
import {
  require_incidents
} from "/build/_shared/chunk-J24HANUS.js";
import {
  require_taxonomy
} from "/build/_shared/chunk-AO7MZAIH.js";
import "/build/_shared/chunk-AUYLHJJM.js";
import {
  require_moment
} from "/build/_shared/chunk-NPKMIMFJ.js";
import {
  leaflet_default
} from "/build/_shared/chunk-WD3B53AS.js";
import {
  Footer,
  Header
} from "/build/_shared/chunk-4556QKWU.js";
import "/build/_shared/chunk-W7JLG7II.js";
import "/build/_shared/chunk-OHIZ2QAR.js";
import {
  ucwords
} from "/build/_shared/chunk-DFTVPORL.js";
import {
  useLoaderData
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/incident.$id.tsx
var import_react = __toESM(require_react(), 1);
var import_moment = __toESM(require_moment(), 1);
var import_s3 = __toESM(require_s3(), 1);
var import_incidents = __toESM(require_incidents(), 1);
var import_taxonomy = __toESM(require_taxonomy(), 1);

// app/images/Placeholder-Image.png
var Placeholder_Image_default = "/build/_assets/Placeholder-Image-ZP2XKEDO.png";

// app/routes/incident.$id.tsx
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/incident.$id.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/incident.$id.tsx"
  );
}
var links = () => {
  return [{
    rel: "stylesheet",
    href: leaflet_default
  }];
};
function DisplayIncident() {
  _s();
  const data = useLoaderData();
  const locationRef = (0, import_react.useRef)(null);
  const [mapWidth, setMapWidth] = (0, import_react.useState)(0);
  (0, import_react.useEffect)(() => {
    setMapWidth(locationRef.current?.offsetWidth || 0);
  }, []);
  const incident = data.incident;
  const titleSegments = [];
  if (incident.incident_date) {
    titleSegments.push(import_moment.default.unix(incident.incident_date).format("M-D-YYYY"));
  }
  if (incident.state) {
    titleSegments.push(incident.state.value);
  }
  if (incident.city_county) {
    titleSegments.push(incident.city_county);
  }
  if (incident.county) {
    titleSegments.push(incident.county);
  }
  titleSegments.push(`${incident.incident_participants.filter((s) => s.participant_type === "victim").length}-${incident.incident_participants.filter((s) => s.participant_type === "perpetrator").length}`);
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "min-h-screen", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Header, {}, void 0, false, {
      fileName: "app/routes/incident.$id.tsx",
      lineNumber: 140,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Title, { title: "Incident" }, void 0, false, {
      fileName: "app/routes/incident.$id.tsx",
      lineNumber: 141,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { className: "incident", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto text-center md:py-8", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "py-10", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[50px] font-bold leading-[60px]", children: titleSegments.join(" ") }, void 0, false, {
          fileName: "app/routes/incident.$id.tsx",
          lineNumber: 145,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-5 text-lg", children: incident.incident_date ? import_moment.default.unix(incident.incident_date).format("MMMM D, YYYY") : "" }, void 0, false, {
          fileName: "app/routes/incident.$id.tsx",
          lineNumber: 146,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/incident.$id.tsx",
        lineNumber: 144,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/incident.$id.tsx",
        lineNumber: 143,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto pb-10 md:pb-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid gap-10 text-lg md:grid-cols-2", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex flex-col space-y-10", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] border-2 border-gray-200 px-10 py-8", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[25px] font-bold leading-[35px]", ref: locationRef, children: "Location" }, void 0, false, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 156,
              columnNumber: 10
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-1", children: [
              incident.address,
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("br", {}, void 0, false, {
                fileName: "app/routes/incident.$id.tsx",
                lineNumber: 161,
                columnNumber: 11
              }, this),
              [incident.city_county, incident.state?.value].join(", ")
            ] }, void 0, true, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 159,
              columnNumber: 10
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/incident.$id.tsx",
            lineNumber: 155,
            columnNumber: 9
          }, this),
          incident.incident_types.length > 0 && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-8", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[25px] font-bold leading-[35px]", children: "Incident Characteristics" }, void 0, false, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 180,
              columnNumber: 11
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("ul", { className: "mt-1 list-disc pl-6", children: incident.incident_types.map((item) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: item.type?.value }, `characteristic-${item.type_tid}`, false, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 184,
              columnNumber: 49
            }, this)) }, void 0, false, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 183,
              columnNumber: 11
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/incident.$id.tsx",
            lineNumber: 179,
            columnNumber: 48
          }, this),
          incident.notes && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-6", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[25px] font-bold leading-[35px]", children: "Notes" }, void 0, false, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 188,
              columnNumber: 11
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-1", children: incident.notes }, void 0, false, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 189,
              columnNumber: 11
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/incident.$id.tsx",
            lineNumber: 187,
            columnNumber: 28
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-6", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[25px] font-bold leading-[35px]", children: "District Information" }, void 0, false, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 192,
              columnNumber: 10
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-2.5 rounded-[5px] bg-gray-200 px-5 py-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-row", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell text-right font-bold", children: "Congressional District:" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 196,
                  columnNumber: 13
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell pl-2.5", children: incident.congressional_district }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 199,
                  columnNumber: 13
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/incident.$id.tsx",
                lineNumber: 195,
                columnNumber: 12
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-row", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell text-right font-bold", children: "State Senate District:" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 204,
                  columnNumber: 13
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell pl-2.5", children: incident.state_senate_district }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 207,
                  columnNumber: 13
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/incident.$id.tsx",
                lineNumber: 203,
                columnNumber: 12
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-row", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell text-right font-bold", children: "State House District:" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 212,
                  columnNumber: 13
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell pl-2.5", children: incident.state_house_district }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 215,
                  columnNumber: 13
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/incident.$id.tsx",
                lineNumber: 211,
                columnNumber: 12
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 194,
              columnNumber: 11
            }, this) }, void 0, false, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 193,
              columnNumber: 10
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/incident.$id.tsx",
            lineNumber: 191,
            columnNumber: 9
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/incident.$id.tsx",
          lineNumber: 154,
          columnNumber: 8
        }, this) }, void 0, false, {
          fileName: "app/routes/incident.$id.tsx",
          lineNumber: 153,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex flex-col space-y-10", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] border-2 border-gray-200 px-10 py-8", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[25px] font-bold leading-[35px]", children: "Victims" }, void 0, false, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 224,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-8 space-y-5", children: incident.incident_participants.filter((s) => s.participant_type === "victim").map((participant) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[5px] bg-gray-200 px-5 py-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table", children: [
              participant.name && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-row", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell text-right font-bold", children: "Name:" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 230,
                  columnNumber: 16
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell pl-2.5", children: participant.name }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 231,
                  columnNumber: 16
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/incident.$id.tsx",
                lineNumber: 229,
                columnNumber: 35
              }, this),
              participant.age !== null && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-row", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell text-right font-bold", children: "Age:" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 234,
                  columnNumber: 16
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell pl-2.5", children: participant.age }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 235,
                  columnNumber: 16
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/incident.$id.tsx",
                lineNumber: 233,
                columnNumber: 43
              }, this),
              participant.age_group && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-row", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell text-right font-bold", children: "Age Group:" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 238,
                  columnNumber: 16
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell pl-2.5", children: participant.age_group.value }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 241,
                  columnNumber: 16
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/incident.$id.tsx",
                lineNumber: 237,
                columnNumber: 40
              }, this),
              participant.gender && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-row", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell text-right font-bold", children: "Gender:" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 246,
                  columnNumber: 16
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell pl-2.5", children: ucwords(participant.gender) }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 249,
                  columnNumber: 16
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/incident.$id.tsx",
                lineNumber: 245,
                columnNumber: 37
              }, this),
              participant.participants_characteristic_tid && data.participantCharacteristicsTerms.find((s) => s.tid == participant.participants_characteristic_tid)?.value == "LE" && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-row", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell text-right font-bold", children: "LEO Status:" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 254,
                  columnNumber: 17
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell pl-2.5", children: "Yes" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 257,
                  columnNumber: 17
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/incident.$id.tsx",
                lineNumber: 253,
                columnNumber: 181
              }, this),
              participant.participant_status_tid && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-row", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell text-right font-bold", children: "Status:" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 262,
                  columnNumber: 16
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell pl-2.5", children: participant.participant_status_tid.split(",").map((tid) => data.participantStatusTerms.find((s) => s.tid == Number(tid))?.value).join("/") }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 265,
                  columnNumber: 16
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/incident.$id.tsx",
                lineNumber: 261,
                columnNumber: 53
              }, this),
              participant.relationship_tid && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-row", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell text-right font-bold", children: "Relationship:" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 270,
                  columnNumber: 16
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell pl-2.5", children: data.participantStatusTerms.find((s) => s.tid == participant.relationship_tid)?.value }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 273,
                  columnNumber: 16
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/incident.$id.tsx",
                lineNumber: 269,
                columnNumber: 47
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 228,
              columnNumber: 13
            }, this) }, `victim-${participant.participant_id}`, false, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 226,
              columnNumber: 106
            }, this)) }, void 0, false, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 225,
              columnNumber: 9
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/incident.$id.tsx",
            lineNumber: 223,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] border-2 border-gray-200 px-10 py-8", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[25px] font-bold leading-[35px]", children: "Subjects-Suspects" }, void 0, false, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 282,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-8 space-y-5", children: incident.incident_participants.filter((s) => s.participant_type === "perpetrator").map((participant) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[5px] bg-gray-200 px-5 py-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-row", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell text-right font-bold", children: "Name:" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 288,
                  columnNumber: 15
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell pl-2.5", children: participant.name }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 289,
                  columnNumber: 15
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/incident.$id.tsx",
                lineNumber: 287,
                columnNumber: 14
              }, this),
              participant.age !== null && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-row", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell text-right font-bold", children: "Age:" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 292,
                  columnNumber: 16
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell pl-2.5", children: participant.age }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 293,
                  columnNumber: 16
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/incident.$id.tsx",
                lineNumber: 291,
                columnNumber: 43
              }, this),
              participant.age_group && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-row", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell text-right font-bold", children: "Age Group:" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 296,
                  columnNumber: 16
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell pl-2.5", children: participant.age_group.value }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 299,
                  columnNumber: 16
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/incident.$id.tsx",
                lineNumber: 295,
                columnNumber: 40
              }, this),
              participant.gender && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-row", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell text-right font-bold", children: "Gender:" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 304,
                  columnNumber: 16
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell pl-2.5", children: ucwords(participant.gender) }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 307,
                  columnNumber: 16
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/incident.$id.tsx",
                lineNumber: 303,
                columnNumber: 37
              }, this),
              participant.participants_characteristic_tid && data.participantCharacteristicsTerms.find((s) => s.tid == participant.participants_characteristic_tid)?.value == "LE" && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-row", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell text-right font-bold", children: "LEO Status:" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 312,
                  columnNumber: 17
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell pl-2.5", children: "Yes" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 315,
                  columnNumber: 17
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/incident.$id.tsx",
                lineNumber: 311,
                columnNumber: 181
              }, this),
              participant.participant_status_tid && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-row", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell text-right font-bold", children: "Status:" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 320,
                  columnNumber: 16
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell pl-2.5", children: participant.participant_status_tid.split(",").map((tid) => data.participantStatusTerms.find((s) => s.tid == Number(tid))?.value).join("/") }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 323,
                  columnNumber: 16
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/incident.$id.tsx",
                lineNumber: 319,
                columnNumber: 53
              }, this),
              participant.relationship_tid && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-row", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell text-right font-bold", children: "Relationship:" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 328,
                  columnNumber: 16
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "table-cell pl-2.5", children: data.participantStatusTerms.find((s) => s.tid == participant.relationship_tid)?.value }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 331,
                  columnNumber: 16
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/incident.$id.tsx",
                lineNumber: 327,
                columnNumber: 47
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 286,
              columnNumber: 13
            }, this) }, `perpetrator-${participant.participant_id}`, false, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 284,
              columnNumber: 111
            }, this)) }, void 0, false, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 283,
              columnNumber: 9
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/incident.$id.tsx",
            lineNumber: 281,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] border-2 border-gray-200 px-10 py-8", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[25px] font-bold leading-[35px]", children: "Sources" }, void 0, false, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 340,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-8 space-y-10", children: incident.incident_sources.map((source) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex space-x-10", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "h-[120px] w-[180px] flex-none overflow-hidden", children: [
                source.imageFile && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { className: "break-all", href: source.imageFile.preview, target: "_blank", rel: "noreferrer", children: [
                  source.imageFile.filemime?.includes("image") && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: source.imageFile.preview, alt: source.image_alt || "", title: source.image_title || "" }, void 0, false, {
                    fileName: "app/routes/incident.$id.tsx",
                    lineNumber: 346,
                    columnNumber: 64
                  }, this),
                  !source.imageFile.filemime?.includes("image") && source.imageFile.filename
                ] }, void 0, true, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 344,
                  columnNumber: 34
                }, this),
                !source.imageFile && source.image_uri && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { href: source.image_uri.replace("public://", data.imagePublicUrl), target: "_blank", rel: "noreferrer", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: source.image_uri.replace("public://", data.imagePublicUrl), alt: source.image_alt || "", title: source.image_title || "" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 351,
                  columnNumber: 15
                }, this) }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 349,
                  columnNumber: 55
                }, this),
                !source.image_uri && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Placeholder_Image_default, alt: source.image_alt || "", title: source.image_title || "" }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 354,
                  columnNumber: 35
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/incident.$id.tsx",
                lineNumber: 343,
                columnNumber: 12
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 content-center", children: [
                source.source_name && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "font-bold", children: source.source_name }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 357,
                  columnNumber: 36
                }, this),
                source.source_url && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { className: "break-all text-blue-500", href: source.source_url, target: "_blank", rel: "noreferrer", children: source.source_url }, void 0, false, {
                  fileName: "app/routes/incident.$id.tsx",
                  lineNumber: 358,
                  columnNumber: 35
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/incident.$id.tsx",
                lineNumber: 356,
                columnNumber: 12
              }, this)
            ] }, `source-${source.source_entity_id}`, true, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 342,
              columnNumber: 51
            }, this)) }, void 0, false, {
              fileName: "app/routes/incident.$id.tsx",
              lineNumber: 341,
              columnNumber: 9
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/incident.$id.tsx",
            lineNumber: 339,
            columnNumber: 8
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/incident.$id.tsx",
          lineNumber: 222,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/incident.$id.tsx",
        lineNumber: 152,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/incident.$id.tsx",
        lineNumber: 151,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/incident.$id.tsx",
      lineNumber: 142,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Footer, {}, void 0, false, {
      fileName: "app/routes/incident.$id.tsx",
      lineNumber: 370,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/incident.$id.tsx",
    lineNumber: 139,
    columnNumber: 10
  }, this);
}
_s(DisplayIncident, "u4+sf2ue48zVm33xaX5SwHbE8yM=", false, function() {
  return [useLoaderData];
});
_c = DisplayIncident;
var _c;
$RefreshReg$(_c, "DisplayIncident");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  DisplayIncident as default,
  links
};
//# sourceMappingURL=/build/routes/incident.$id-WWEGNC3I.js.map
