import {
  Icon_Success_default
} from "/build/_shared/chunk-GBO3FEGG.js";
import {
  Editor,
  tinyConfig
} from "/build/_shared/chunk-SIGMETZO.js";
import "/build/_shared/chunk-WBY37MGY.js";
import "/build/_shared/chunk-IVTPFYOU.js";
import {
  Icon_Admin_Edit_default
} from "/build/_shared/chunk-O4ZMMQVI.js";
import {
  require_config
} from "/build/_shared/chunk-3PRZVPKO.js";
import {
  Icon_Admin_Delete_default
} from "/build/_shared/chunk-RUOV274T.js";
import {
  require_cookies
} from "/build/_shared/chunk-MUAEGCBW.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import {
  Form,
  Link,
  useActionData,
  useLoaderData
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/admin.toll._index.tsx
var import_react = __toESM(require_react(), 1);
var import_cookies = __toESM(require_cookies(), 1);
var import_auth = __toESM(require_auth(), 1);
var import_config = __toESM(require_config(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.toll._index.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.toll._index.tsx"
  );
  import.meta.hot.lastModified = "1747752412822.333";
}
function TollIndexPage() {
  _s();
  const {
    sourceList,
    formSubmitMessage: formSubmitMessage2,
    b42019Footer,
    nafter2019Footer,
    yearReviewFooter,
    pastTollsStartingYear
  } = useLoaderData();
  const [showMessage, setShowMessage] = (0, import_react.useState)(!!formSubmitMessage2);
  const [footer1, setFooter1] = (0, import_react.useState)(b42019Footer ?? "");
  const [footer2, setFooter2] = (0, import_react.useState)(nafter2019Footer ?? "");
  const [footer3, setFooter3] = (0, import_react.useState)(yearReviewFooter ?? "");
  const actionData = useActionData();
  (0, import_react.useEffect)(() => {
    if (showMessage) {
      const timeoutId = setTimeout(() => setShowMessage(false), 5e3);
      return () => clearTimeout(timeoutId);
    }
  }, [showMessage]);
  const yearOptions = Array((/* @__PURE__ */ new Date()).getFullYear() - 2014 + 1).fill(2014).map((e, i) => 2014 + i);
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { className: "container mx-auto pb-10 pt-8", children: [
    showMessage ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("p", { className: "animate-fade bg-black text-white", children: formSubmitMessage2 }, void 0, false, {
      fileName: "app/routes/admin.toll._index.tsx",
      lineNumber: 104,
      columnNumber: 19
    }, this) : null,
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[25px] font-bold", children: "Manage Toll" }, void 0, false, {
      fileName: "app/routes/admin.toll._index.tsx",
      lineNumber: 106,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-8", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "source/new", className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white hover:bg-orange-800 focus:bg-orange-800", children: "Add Source" }, void 0, false, {
      fileName: "app/routes/admin.toll._index.tsx",
      lineNumber: 108,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/admin.toll._index.tsx",
      lineNumber: 107,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("table", { className: "table-gray mt-5 w-full table-auto text-sm", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("thead", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Title" }, void 0, false, {
          fileName: "app/routes/admin.toll._index.tsx",
          lineNumber: 115,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Year" }, void 0, false, {
          fileName: "app/routes/admin.toll._index.tsx",
          lineNumber: 116,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Type" }, void 0, false, {
          fileName: "app/routes/admin.toll._index.tsx",
          lineNumber: 117,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "State" }, void 0, false, {
          fileName: "app/routes/admin.toll._index.tsx",
          lineNumber: 118,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "City" }, void 0, false, {
          fileName: "app/routes/admin.toll._index.tsx",
          lineNumber: 119,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "\xA0" }, void 0, false, {
          fileName: "app/routes/admin.toll._index.tsx",
          lineNumber: 120,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.toll._index.tsx",
        lineNumber: 114,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.toll._index.tsx",
        lineNumber: 113,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tbody", { children: [
        sourceList.map((source) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: source.title }, void 0, false, {
            fileName: "app/routes/admin.toll._index.tsx",
            lineNumber: 125,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: source.year }, void 0, false, {
            fileName: "app/routes/admin.toll._index.tsx",
            lineNumber: 126,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: source.type }, void 0, false, {
            fileName: "app/routes/admin.toll._index.tsx",
            lineNumber: 127,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: source.state }, void 0, false, {
            fileName: "app/routes/admin.toll._index.tsx",
            lineNumber: 128,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: source.city }, void 0, false, {
            fileName: "app/routes/admin.toll._index.tsx",
            lineNumber: 129,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex space-x-10", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: `/admin/toll/source/${source.id}`, className: "flex items-center font-bold text-blue-500", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Edit_default, alt: "", width: 16 }, void 0, false, {
                fileName: "app/routes/admin.toll._index.tsx",
                lineNumber: 133,
                columnNumber: 11
              }, this),
              " Edit"
            ] }, void 0, true, {
              fileName: "app/routes/admin.toll._index.tsx",
              lineNumber: 132,
              columnNumber: 10
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { action: `/admin/toll/source/${source.id}/destroy`, method: "post", onSubmit: (e) => {
              if (!confirm("Please confirm you want to delete this record.")) {
                e.preventDefault();
              }
            }, children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", className: "flex items-center font-bold text-blue-500", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Delete_default, alt: "", width: 16 }, void 0, false, {
                fileName: "app/routes/admin.toll._index.tsx",
                lineNumber: 141,
                columnNumber: 12
              }, this),
              " Delete"
            ] }, void 0, true, {
              fileName: "app/routes/admin.toll._index.tsx",
              lineNumber: 140,
              columnNumber: 11
            }, this) }, void 0, false, {
              fileName: "app/routes/admin.toll._index.tsx",
              lineNumber: 135,
              columnNumber: 10
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.toll._index.tsx",
            lineNumber: 131,
            columnNumber: 9
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.toll._index.tsx",
            lineNumber: 130,
            columnNumber: 8
          }, this)
        ] }, `source-${source.id}`, true, {
          fileName: "app/routes/admin.toll._index.tsx",
          lineNumber: 124,
          columnNumber: 32
        }, this)),
        sourceList.length === 0 && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { colSpan: 6, children: "No source modifications yet." }, void 0, false, {
          fileName: "app/routes/admin.toll._index.tsx",
          lineNumber: 148,
          columnNumber: 8
        }, this) }, void 0, false, {
          fileName: "app/routes/admin.toll._index.tsx",
          lineNumber: 147,
          columnNumber: 34
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.toll._index.tsx",
        lineNumber: 123,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.toll._index.tsx",
      lineNumber: 112,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {
      className: "mt-8 rounded-[5px] bg-gray-200 p-10",
      // @ts-ignore
      children: [
        actionData?.message && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-5 flex bg-white px-5 py-3.5 text-sm font-light", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Icon_Success_default, alt: "", width: 30 }, void 0, false, {
            fileName: "app/routes/admin.toll._index.tsx",
            lineNumber: 156,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "pl-2", children: actionData.message }, void 0, false, {
            fileName: "app/routes/admin.toll._index.tsx",
            lineNumber: 157,
            columnNumber: 8
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.toll._index.tsx",
          lineNumber: 155,
          columnNumber: 30
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { reloadDocument: true, method: "post", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "Footer1", className: "text-lg font-bold", children: "Toll Footer for prior 2019" }, void 0, false, {
              fileName: "app/routes/admin.toll._index.tsx",
              lineNumber: 161,
              columnNumber: 7
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", initialValue: b42019Footer ?? "", value: footer1, init: {
                ...tinyConfig,
                inline: false
              }, onEditorChange: (newValue, editor) => setFooter1(newValue) }, void 0, false, {
                fileName: "app/routes/admin.toll._index.tsx",
                lineNumber: 165,
                columnNumber: 8
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "b42019Footer", value: footer1 }, void 0, false, {
                fileName: "app/routes/admin.toll._index.tsx",
                lineNumber: 169,
                columnNumber: 8
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.toll._index.tsx",
              lineNumber: 164,
              columnNumber: 7
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.toll._index.tsx",
            lineNumber: 160,
            columnNumber: 6
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-8", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "Footer2", className: "text-lg font-bold", children: "Toll Footer for 2019 and after" }, void 0, false, {
              fileName: "app/routes/admin.toll._index.tsx",
              lineNumber: 173,
              columnNumber: 7
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", initialValue: nafter2019Footer ?? "", value: footer2, init: {
                ...tinyConfig,
                inline: false
              }, onEditorChange: (newValue, editor) => setFooter2(newValue) }, void 0, false, {
                fileName: "app/routes/admin.toll._index.tsx",
                lineNumber: 177,
                columnNumber: 8
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "nafter2019Footer", value: footer2 }, void 0, false, {
                fileName: "app/routes/admin.toll._index.tsx",
                lineNumber: 181,
                columnNumber: 8
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.toll._index.tsx",
              lineNumber: 176,
              columnNumber: 7
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.toll._index.tsx",
            lineNumber: 172,
            columnNumber: 6
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-8", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "Footer3", className: "text-lg font-bold", children: "10 Year Review Footer" }, void 0, false, {
              fileName: "app/routes/admin.toll._index.tsx",
              lineNumber: 185,
              columnNumber: 7
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", initialValue: yearReviewFooter ?? "", value: footer3, init: {
                ...tinyConfig,
                inline: false
              }, onEditorChange: (newValue, editor) => setFooter3(newValue) }, void 0, false, {
                fileName: "app/routes/admin.toll._index.tsx",
                lineNumber: 189,
                columnNumber: 8
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "yearReviewFooter", value: footer3 }, void 0, false, {
                fileName: "app/routes/admin.toll._index.tsx",
                lineNumber: 193,
                columnNumber: 8
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.toll._index.tsx",
              lineNumber: 188,
              columnNumber: 7
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.toll._index.tsx",
            lineNumber: 184,
            columnNumber: 6
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-8", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "PastTollsStartingYear", className: "text-lg font-bold", children: "Past Tolls Starting Year" }, void 0, false, {
              fileName: "app/routes/admin.toll._index.tsx",
              lineNumber: 197,
              columnNumber: 7
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("select", { id: "PastTollsStartingYear", name: "pastTollsStartingYear", required: true, defaultValue: pastTollsStartingYear, children: yearOptions.map((y) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("option", { value: y, children: y }, `past-year-option-${y}`, false, {
              fileName: "app/routes/admin.toll._index.tsx",
              lineNumber: 202,
              columnNumber: 31
            }, this)) }, void 0, false, {
              fileName: "app/routes/admin.toll._index.tsx",
              lineNumber: 201,
              columnNumber: 8
            }, this) }, void 0, false, {
              fileName: "app/routes/admin.toll._index.tsx",
              lineNumber: 200,
              columnNumber: 7
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.toll._index.tsx",
            lineNumber: 196,
            columnNumber: 6
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-8", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white hover:bg-orange-800 focus:bg-orange-800", children: "Save" }, void 0, false, {
            fileName: "app/routes/admin.toll._index.tsx",
            lineNumber: 209,
            columnNumber: 7
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.toll._index.tsx",
            lineNumber: 208,
            columnNumber: 6
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.toll._index.tsx",
          lineNumber: 159,
          columnNumber: 5
        }, this)
      ]
    }, void 0, true, {
      fileName: "app/routes/admin.toll._index.tsx",
      lineNumber: 152,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.toll._index.tsx",
    lineNumber: 103,
    columnNumber: 10
  }, this);
}
_s(TollIndexPage, "E7+T7v/PgLWkj95kxuEdVvnsV2Q=", false, function() {
  return [useLoaderData, useActionData];
});
_c = TollIndexPage;
var _c;
$RefreshReg$(_c, "TollIndexPage");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  TollIndexPage as default
};
//# sourceMappingURL=/build/routes/admin.toll._index-ZW4HD6N7.js.map
