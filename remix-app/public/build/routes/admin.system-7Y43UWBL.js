import {
  AdminHeader
} from "/build/_shared/chunk-W7JLG7II.js";
import "/build/_shared/chunk-OHIZ2QAR.js";
import {
  useUser
} from "/build/_shared/chunk-DFTVPORL.js";
import {
  NavLink,
  Outlet,
  useLocation
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/admin.system.tsx
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.system.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.system.tsx"
  );
  import.meta.hot.lastModified = "1748981831849.79";
}
function AdminSystemPage() {
  _s();
  const user = useUser();
  const location = useLocation();
  if (user.role != "Admin") {
    throw new Error("No permission.");
  }
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "min-h-screen bg-white", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(AdminHeader, {}, void 0, false, {
      fileName: "app/routes/admin.system.tsx",
      lineNumber: 38,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { className: "container mx-auto py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex space-x-10", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "w-[350px] flex-none", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] bg-gray-200 p-10", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h2", { className: "mb-5 text-2xl font-bold", children: "System" }, void 0, false, {
          fileName: "app/routes/admin.system.tsx",
          lineNumber: 44,
          columnNumber: 8
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("ul", { className: "mt-5 divide-y divide-white border-y border-white text-lg", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "py-2.5", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(NavLink, { className: ({
            isActive
          }) => `${isActive ? "text-orange-500" : ""}`, to: "/admin/system/aws", children: "AWS Stats" }, void 0, false, {
            fileName: "app/routes/admin.system.tsx",
            lineNumber: 47,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.system.tsx",
            lineNumber: 46,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "py-2.5", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(NavLink, { className: ({
            isActive
          }) => `${isActive ? "text-orange-500" : ""}`, to: "/admin/system/health", children: "Site Health" }, void 0, false, {
            fileName: "app/routes/admin.system.tsx",
            lineNumber: 54,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.system.tsx",
            lineNumber: 53,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "py-2.5", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(NavLink, { className: ({
            isActive
          }) => `${isActive ? "text-orange-500" : ""}`, to: "/admin/system/migrations", children: "Database Migrations" }, void 0, false, {
            fileName: "app/routes/admin.system.tsx",
            lineNumber: 61,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.system.tsx",
            lineNumber: 60,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "py-2.5", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(NavLink, { className: ({
            isActive
          }) => `${isActive ? "text-orange-500" : ""}`, to: "/admin/system/errors", children: "Error Logs" }, void 0, false, {
            fileName: "app/routes/admin.system.tsx",
            lineNumber: 68,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.system.tsx",
            lineNumber: 67,
            columnNumber: 9
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.system.tsx",
          lineNumber: 45,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.system.tsx",
        lineNumber: 43,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.system.tsx",
        lineNumber: 42,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Outlet, {}, location.pathname + location.search, false, {
        fileName: "app/routes/admin.system.tsx",
        lineNumber: 79,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.system.tsx",
        lineNumber: 78,
        columnNumber: 6
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.system.tsx",
      lineNumber: 41,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/admin.system.tsx",
      lineNumber: 40,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.system.tsx",
    lineNumber: 37,
    columnNumber: 10
  }, this);
}
_s(AdminSystemPage, "SuAQlDjLYUAfiZG3TwKOF970A8M=", false, function() {
  return [useUser, useLocation];
});
_c = AdminSystemPage;
var _c;
$RefreshReg$(_c, "AdminSystemPage");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  AdminSystemPage as default
};
//# sourceMappingURL=/build/routes/admin.system-7Y43UWBL.js.map
