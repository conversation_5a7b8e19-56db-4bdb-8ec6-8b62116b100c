import {
  Icon_Admin_Edit_default
} from "/build/_shared/chunk-O4ZMMQVI.js";
import {
  require_config
} from "/build/_shared/chunk-3PRZVPKO.js";
import {
  Icon_Admin_Delete_default
} from "/build/_shared/chunk-RUOV274T.js";
import {
  require_cookies
} from "/build/_shared/chunk-MUAEGCBW.js";
import {
  Form,
  Link,
  useLoaderData
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/admin.configuration._index.tsx
var import_react = __toESM(require_react(), 1);
var import_cookies = __toESM(require_cookies(), 1);
var import_config = __toESM(require_config(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.configuration._index.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.configuration._index.tsx"
  );
  import.meta.hot.lastModified = "1740763710238.8691";
}
function ConfigurationIndexPage() {
  _s();
  const {
    configList,
    formSubmitMessage: formSubmitMessage2
  } = useLoaderData();
  const [showMessage, setShowMessage] = (0, import_react.useState)(!!formSubmitMessage2);
  (0, import_react.useEffect)(() => {
    if (showMessage) {
      const timeoutId = setTimeout(() => setShowMessage(false), 5e3);
      return () => clearTimeout(timeoutId);
    }
  }, [showMessage]);
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { className: "container mx-auto pb-10 pt-8", children: [
    showMessage ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("p", { className: "animate-fade bg-black text-white", children: formSubmitMessage2 }, void 0, false, {
      fileName: "app/routes/admin.configuration._index.tsx",
      lineNumber: 53,
      columnNumber: 19
    }, this) : null,
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[25px] font-bold", children: "Manage Configurations" }, void 0, false, {
      fileName: "app/routes/admin.configuration._index.tsx",
      lineNumber: 55,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-8", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "new", className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white hover:bg-orange-800 focus:bg-orange-800", children: "Add Configuration" }, void 0, false, {
      fileName: "app/routes/admin.configuration._index.tsx",
      lineNumber: 57,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/admin.configuration._index.tsx",
      lineNumber: 56,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("table", { className: "table-gray mt-5 w-full table-auto text-sm", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("thead", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Key" }, void 0, false, {
          fileName: "app/routes/admin.configuration._index.tsx",
          lineNumber: 64,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "Values" }, void 0, false, {
          fileName: "app/routes/admin.configuration._index.tsx",
          lineNumber: 65,
          columnNumber: 7
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("th", { align: "left", children: "\xA0" }, void 0, false, {
          fileName: "app/routes/admin.configuration._index.tsx",
          lineNumber: 66,
          columnNumber: 7
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.configuration._index.tsx",
        lineNumber: 63,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.configuration._index.tsx",
        lineNumber: 62,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tbody", { children: [
        configList.map((config) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: config.key }, void 0, false, {
            fileName: "app/routes/admin.configuration._index.tsx",
            lineNumber: 71,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: config.values }, void 0, false, {
            fileName: "app/routes/admin.configuration._index.tsx",
            lineNumber: 72,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex space-x-10", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: `/admin/configuration/${config.id}`, className: "flex items-center font-bold text-blue-500", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Edit_default, alt: "", width: 16 }, void 0, false, {
                fileName: "app/routes/admin.configuration._index.tsx",
                lineNumber: 76,
                columnNumber: 11
              }, this),
              " Edit"
            ] }, void 0, true, {
              fileName: "app/routes/admin.configuration._index.tsx",
              lineNumber: 75,
              columnNumber: 10
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { action: `/admin/configuration/${config.id}/destroy`, method: "post", onSubmit: (e) => {
              if (!confirm("Please confirm you want to delete this record.")) {
                e.preventDefault();
              }
            }, children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", className: "flex items-center font-bold text-blue-500", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Admin_Delete_default, alt: "", width: 16 }, void 0, false, {
                fileName: "app/routes/admin.configuration._index.tsx",
                lineNumber: 84,
                columnNumber: 12
              }, this),
              " Delete"
            ] }, void 0, true, {
              fileName: "app/routes/admin.configuration._index.tsx",
              lineNumber: 83,
              columnNumber: 11
            }, this) }, void 0, false, {
              fileName: "app/routes/admin.configuration._index.tsx",
              lineNumber: 78,
              columnNumber: 10
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.configuration._index.tsx",
            lineNumber: 74,
            columnNumber: 9
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.configuration._index.tsx",
            lineNumber: 73,
            columnNumber: 8
          }, this)
        ] }, `configuration-${config.id}`, true, {
          fileName: "app/routes/admin.configuration._index.tsx",
          lineNumber: 70,
          columnNumber: 32
        }, this)),
        configList.length === 0 && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("tr", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("td", { colSpan: 6, children: "No configurations yet." }, void 0, false, {
          fileName: "app/routes/admin.configuration._index.tsx",
          lineNumber: 91,
          columnNumber: 8
        }, this) }, void 0, false, {
          fileName: "app/routes/admin.configuration._index.tsx",
          lineNumber: 90,
          columnNumber: 34
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.configuration._index.tsx",
        lineNumber: 69,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.configuration._index.tsx",
      lineNumber: 61,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.configuration._index.tsx",
    lineNumber: 52,
    columnNumber: 10
  }, this);
}
_s(ConfigurationIndexPage, "nJYBZImWga0s3/3sDMSGM1OZUFE=", false, function() {
  return [useLoaderData];
});
_c = ConfigurationIndexPage;
var _c;
$RefreshReg$(_c, "ConfigurationIndexPage");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  ConfigurationIndexPage as default
};
//# sourceMappingURL=/build/routes/admin.configuration._index-TJIC3WZV.js.map
