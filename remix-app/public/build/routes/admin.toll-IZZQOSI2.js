import {
  AdminHeader
} from "/build/_shared/chunk-W7JLG7II.js";
import "/build/_shared/chunk-OHIZ2QAR.js";
import {
  useUser
} from "/build/_shared/chunk-DFTVPORL.js";
import {
  Outlet
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/admin.toll.tsx
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.toll.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.toll.tsx"
  );
  import.meta.hot.lastModified = "1736974048103.9746";
}
function AdminTollPage() {
  _s();
  const user = useUser();
  if (user.role != "Admin") {
    throw new Error("No permission.");
  }
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "min-h-screen bg-white", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(AdminHeader, {}, void 0, false, {
      fileName: "app/routes/admin.toll.tsx",
      lineNumber: 32,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Outlet, {}, void 0, false, {
      fileName: "app/routes/admin.toll.tsx",
      lineNumber: 34,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.toll.tsx",
    lineNumber: 31,
    columnNumber: 10
  }, this);
}
_s(AdminTollPage, "BPnln+wUpxLjLAxQmw7xYz9C+QI=", false, function() {
  return [useUser];
});
_c = AdminTollPage;
var _c;
$RefreshReg$(_c, "AdminTollPage");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  AdminTollPage as default
};
//# sourceMappingURL=/build/routes/admin.toll-IZZQOSI2.js.map
