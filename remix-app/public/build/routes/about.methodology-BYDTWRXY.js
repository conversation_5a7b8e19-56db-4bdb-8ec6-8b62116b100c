import {
  require_about,
  require_js,
  require_reduce
} from "/build/_shared/chunk-O7YZF2UJ.js";
import {
  Editor,
  htmlFrom,
  require_browser,
  tinyConfig
} from "/build/_shared/chunk-SIGMETZO.js";
import "/build/_shared/chunk-WBY37MGY.js";
import "/build/_shared/chunk-IVTPFYOU.js";
import {
  Title
} from "/build/_shared/chunk-YPQMQ54K.js";
import {
  <PERSON><PERSON>,
  <PERSON>er
} from "/build/_shared/chunk-4556QKWU.js";
import "/build/_shared/chunk-W7JLG7II.js";
import "/build/_shared/chunk-OHIZ2QAR.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import {
  useOptionalUser
} from "/build/_shared/chunk-DFTVPORL.js";
import {
  Link,
  useLoaderData,
  useNavigate
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/about.methodology.tsx
var import_react = __toESM(require_react(), 1);
var import_about = __toESM(require_about(), 1);
var import_auth = __toESM(require_auth(), 1);
var import_react_multi_ref = __toESM(require_js(), 1);
var import_isomorphic_dompurify = __toESM(require_browser(), 1);
var import_reduce = __toESM(require_reduce(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/about.methodology.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/about.methodology.tsx"
  );
  import.meta.hot.lastModified = "1748456083098.8823";
}
function AboutMethodology() {
  _s();
  const data = useLoaderData();
  const user = useOptionalUser();
  const navigate = useNavigate();
  const [editorRefs] = (0, import_react.useState)(() => new import_react_multi_ref.default());
  const [editing, setEditing] = (0, import_react.useState)(false);
  const editingRef = (0, import_react.useRef)(editing);
  const alertUser = (e) => {
    if (editingRef.current) {
      e.preventDefault();
    }
  };
  (0, import_react.useEffect)(() => {
    editingRef.current = editing;
  }, [editing]);
  (0, import_react.useEffect)(() => {
    window.addEventListener("beforeunload", alertUser);
    return () => {
      window.removeEventListener("beforeunload", alertUser);
    };
  }, []);
  const save = async (body) => {
    await fetch("/about/methodology", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(body)
    });
    navigate(".", {
      replace: true
    });
  };
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "min-h-screen", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Header, {}, void 0, false, {
      fileName: "app/routes/about.methodology.tsx",
      lineNumber: 91,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Title, { title: "Gun Violence Archive Methodology" }, void 0, false, {
      fileName: "app/routes/about.methodology.tsx",
      lineNumber: 92,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { children: [
      user && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "fixed bottom-0 left-0 border-2 bg-white px-5 py-3", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { className: "text-xl", onClick: () => {
        if (editing) {
          save([{
            contentid: "about-methodology-container-1",
            content: editorRefs.map.get("about-methodology-container-1").getContent()
          }, {
            contentid: "about-methodology-container-2",
            content: editorRefs.map.get("about-methodology-container-2").getContent()
          }]).then(() => setEditing(false));
        } else {
          setEditing(true);
        }
      }, children: editing ? "Save" : "Edit" }, void 0, false, {
        fileName: "app/routes/about.methodology.tsx",
        lineNumber: 95,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.methodology.tsx",
        lineNumber: 94,
        columnNumber: 14
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex flex-col space-y-10", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lg:flex", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1 lg:pr-10", children: [
          editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
            id: "about-methodology-container-1",
            ...tinyConfig
          }, onInit: (_evt, instance) => editorRefs.map.set("about-methodology-container-1", instance), initialValue: data.content["about-methodology-container-1"] || "" }, void 0, false, {
            fileName: "app/routes/about.methodology.tsx",
            lineNumber: 115,
            columnNumber: 20
          }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "about-methodology-container-1", children: htmlFrom(data.content["about-methodology-container-1"] || "") }, void 0, false, {
            fileName: "app/routes/about.methodology.tsx",
            lineNumber: 118,
            columnNumber: 181
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-5", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "inline-block rounded-full bg-orange-500 px-5 py-2 text-lg text-white", to: "/contact", children: "Contact GVA" }, void 0, false, {
            fileName: "app/routes/about.methodology.tsx",
            lineNumber: 122,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/about.methodology.tsx",
            lineNumber: 121,
            columnNumber: 9
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/about.methodology.tsx",
          lineNumber: 114,
          columnNumber: 8
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-none rounded-[10px] bg-gray-200 p-10 lg:w-[480px]", children: editing ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Editor, { tinymceScriptSrc: "/tinymce/tinymce.min.js", licenseKey: "gpl", init: {
          id: "about-methodology-container-2",
          ...tinyConfig
        }, onInit: (_evt, instance) => editorRefs.map.set("about-methodology-container-2", instance), initialValue: data.content["about-methodology-container-2"] || "" }, void 0, false, {
          fileName: "app/routes/about.methodology.tsx",
          lineNumber: 128,
          columnNumber: 20
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "about-methodology-container-2", children: htmlFrom(data.content["about-methodology-container-2"]) || "" }, void 0, false, {
          fileName: "app/routes/about.methodology.tsx",
          lineNumber: 131,
          columnNumber: 181
        }, this) }, void 0, false, {
          fileName: "app/routes/about.methodology.tsx",
          lineNumber: 127,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/about.methodology.tsx",
        lineNumber: 113,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/about.methodology.tsx",
        lineNumber: 112,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/about.methodology.tsx",
        lineNumber: 111,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/about.methodology.tsx",
      lineNumber: 93,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Footer, {}, void 0, false, {
      fileName: "app/routes/about.methodology.tsx",
      lineNumber: 139,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/about.methodology.tsx",
    lineNumber: 90,
    columnNumber: 10
  }, this);
}
_s(AboutMethodology, "zVyzhMJ5DkvqVWYPz1OnDlPPbmY=", false, function() {
  return [useLoaderData, useOptionalUser, useNavigate];
});
_c = AboutMethodology;
var _c;
$RefreshReg$(_c, "AboutMethodology");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  AboutMethodology as default
};
//# sourceMappingURL=/build/routes/about.methodology-BYDTWRXY.js.map
