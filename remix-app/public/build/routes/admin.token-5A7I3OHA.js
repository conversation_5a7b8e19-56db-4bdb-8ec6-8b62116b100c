import {
  require_users
} from "/build/_shared/chunk-N5WPI4L5.js";
import {
  AdminHeader
} from "/build/_shared/chunk-W7JLG7II.js";
import "/build/_shared/chunk-OHIZ2QAR.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import {
  useUser
} from "/build/_shared/chunk-DFTVPORL.js";
import {
  useLoaderData
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __commonJS,
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// empty-module:~/utils/jwt.server
var require_jwt = __commonJS({
  "empty-module:~/utils/jwt.server"(exports, module) {
    module.exports = {};
  }
});

// app/routes/admin.token.tsx
var import_users = __toESM(require_users(), 1);
var import_auth = __toESM(require_auth(), 1);
var import_jwt = __toESM(require_jwt(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.token.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.token.tsx"
  );
  import.meta.hot.lastModified = "1748456858571.4446";
}
function AdminTokenPage() {
  _s();
  const user = useUser();
  if (user.role != "Admin") {
    throw new Error("No permission.");
  }
  const data = useLoaderData();
  const {
    token
  } = data;
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "min-h-screen bg-white", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(AdminHeader, {}, void 0, false, {
      fileName: "app/routes/admin.token.tsx",
      lineNumber: 62,
      columnNumber: 13
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { className: "container mx-auto pb-10 pt-8", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-[25px] font-bold", children: "Generate Token" }, void 0, false, {
        fileName: "app/routes/admin.token.tsx",
        lineNumber: 65,
        columnNumber: 17
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-8", children: token }, void 0, false, {
        fileName: "app/routes/admin.token.tsx",
        lineNumber: 66,
        columnNumber: 17
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.token.tsx",
      lineNumber: 64,
      columnNumber: 13
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.token.tsx",
    lineNumber: 61,
    columnNumber: 10
  }, this);
}
_s(AdminTokenPage, "mNG6CBjAPO0fhbVziK8fnisI310=", false, function() {
  return [useUser, useLoaderData];
});
_c = AdminTokenPage;
var _c;
$RefreshReg$(_c, "AdminTokenPage");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  AdminTokenPage as default
};
//# sourceMappingURL=/build/routes/admin.token-5A7I3OHA.js.map
