import {
  Icon_Success_default
} from "/build/_shared/chunk-GBO3FEGG.js";
import {
  Title
} from "/build/_shared/chunk-YPQMQ54K.js";
import {
  require_session
} from "/build/_shared/chunk-BK5TXDDP.js";
import {
  Icon_Admin_Delete_default
} from "/build/_shared/chunk-RUOV274T.js";
import {
  require_auth
} from "/build/_shared/chunk-6XGTNG2E.js";
import {
  Form,
  useLoaderData,
  useNavigation,
  useSearchParams
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/user.login.tsx
var import_auth = __toESM(require_auth(), 1);
var import_session = __toESM(require_session(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/user.login.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/user.login.tsx"
  );
  import.meta.hot.lastModified = "1746467939010.2402";
}
var meta = () => {
  return [{
    title: "Gun Violence Archive | Sign In"
  }];
};
function UserLogin() {
  _s();
  let {
    authError
  } = useLoaderData();
  const [searchParams] = useSearchParams();
  const redirectTo = searchParams.get("redirectTo") || "/";
  const transition = useNavigation();
  const isSubmitting = Boolean(transition.state == "submitting");
  const newFlag = searchParams.has("new");
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(import_jsx_dev_runtime.Fragment, { children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Title, { title: "Sign In" }, void 0, false, {
      fileName: "app/routes/user.login.tsx",
      lineNumber: 79,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-center", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-[10px] bg-gray-200 p-4 sm:w-[480px] xl:p-10", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, {
        method: "post",
        id: "login",
        // @ts-ignore
        children: [
          newFlag && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-5 flex bg-white px-5 py-3.5 text-sm font-light", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Icon_Success_default, alt: "", width: 30 }, void 0, false, {
              fileName: "app/routes/user.login.tsx",
              lineNumber: 88,
              columnNumber: 12
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "pl-2", children: "An account has been created. You can now login below." }, void 0, false, {
              fileName: "app/routes/user.login.tsx",
              lineNumber: 89,
              columnNumber: 12
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/user.login.tsx",
            lineNumber: 87,
            columnNumber: 26
          }, this),
          // @ts-ignore
          authError?.message && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-5 flex bg-white px-5 py-3.5 text-sm font-light", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Icon_Admin_Delete_default, alt: "", width: 16 }, void 0, false, {
              fileName: "app/routes/user.login.tsx",
              lineNumber: 96,
              columnNumber: 12
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "pl-2 text-red-500", children: authError.message }, void 0, false, {
              fileName: "app/routes/user.login.tsx",
              lineNumber: 97,
              columnNumber: 12
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/user.login.tsx",
            lineNumber: 95,
            columnNumber: 37
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-8", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "email", className: "block text-lg font-bold", children: "Email" }, void 0, false, {
              fileName: "app/routes/user.login.tsx",
              lineNumber: 100,
              columnNumber: 10
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-3.5 text-[15px]", children: "Enter your Gun Violence Archive email." }, void 0, false, {
              fileName: "app/routes/user.login.tsx",
              lineNumber: 103,
              columnNumber: 10
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "email", name: "email", type: "email", autoComplete: "username", required: true, className: "block w-full rounded-md px-3 py-2 leading-loose" }, void 0, false, {
              fileName: "app/routes/user.login.tsx",
              lineNumber: 104,
              columnNumber: 10
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/user.login.tsx",
            lineNumber: 99,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-2", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { htmlFor: "password", className: "block text-lg font-bold", children: "Password" }, void 0, false, {
              fileName: "app/routes/user.login.tsx",
              lineNumber: 107,
              columnNumber: 10
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-3.5 text-[15px]", children: "Enter the password that accompanies your email." }, void 0, false, {
              fileName: "app/routes/user.login.tsx",
              lineNumber: 110,
              columnNumber: 10
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { id: "password", name: "password", type: "password", autoComplete: "current-password", required: true, className: "block w-full rounded-md px-3 py-2 leading-loose" }, void 0, false, {
              fileName: "app/routes/user.login.tsx",
              lineNumber: 113,
              columnNumber: 10
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/user.login.tsx",
            lineNumber: 106,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-8 text-right text-sm text-blue-500", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { href: "/user/reset", children: "Request New Password" }, void 0, false, {
            fileName: "app/routes/user.login.tsx",
            lineNumber: 116,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/user.login.tsx",
            lineNumber: 115,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-8", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", disabled: isSubmitting, className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white", children: isSubmitting ? "Submitting" : "Sign In" }, void 0, false, {
              fileName: "app/routes/user.login.tsx",
              lineNumber: 119,
              columnNumber: 10
            }, this),
            isSubmitting && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "lds-ring", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
                fileName: "app/routes/user.login.tsx",
                lineNumber: 123,
                columnNumber: 12
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
                fileName: "app/routes/user.login.tsx",
                lineNumber: 124,
                columnNumber: 12
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
                fileName: "app/routes/user.login.tsx",
                lineNumber: 125,
                columnNumber: 12
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", {}, void 0, false, {
                fileName: "app/routes/user.login.tsx",
                lineNumber: 126,
                columnNumber: 12
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/user.login.tsx",
              lineNumber: 122,
              columnNumber: 27
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/user.login.tsx",
            lineNumber: 118,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "redirectTo", value: redirectTo }, void 0, false, {
            fileName: "app/routes/user.login.tsx",
            lineNumber: 129,
            columnNumber: 9
          }, this)
        ]
      }, void 0, true, {
        fileName: "app/routes/user.login.tsx",
        lineNumber: 84,
        columnNumber: 8
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-4 text-center text-lg text-blue-500", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h2", { className: "mb-4", children: "No Account?" }, void 0, false, {
          fileName: "app/routes/user.login.tsx",
          lineNumber: 132,
          columnNumber: 9
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { className: "rounded-full bg-orange-500 px-5 py-2 text-lg text-white", href: `/user/register?redirectTo=${redirectTo}`, children: "Sign Up" }, void 0, false, {
          fileName: "app/routes/user.login.tsx",
          lineNumber: 133,
          columnNumber: 9
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/user.login.tsx",
        lineNumber: 131,
        columnNumber: 8
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/user.login.tsx",
      lineNumber: 83,
      columnNumber: 7
    }, this) }, void 0, false, {
      fileName: "app/routes/user.login.tsx",
      lineNumber: 82,
      columnNumber: 6
    }, this) }, void 0, false, {
      fileName: "app/routes/user.login.tsx",
      lineNumber: 81,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/user.login.tsx",
      lineNumber: 80,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/user.login.tsx",
    lineNumber: 78,
    columnNumber: 10
  }, this);
}
_s(UserLogin, "Qw7BIg5/XzLrlNLr67R7ruo0lYI=", false, function() {
  return [useLoaderData, useSearchParams, useNavigation];
});
_c = UserLogin;
var _c;
$RefreshReg$(_c, "UserLogin");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  UserLogin as default,
  meta
};
//# sourceMappingURL=/build/routes/user.login-DSQOHQAS.js.map
