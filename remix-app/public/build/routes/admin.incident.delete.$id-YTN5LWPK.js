import {
  require_session
} from "/build/_shared/chunk-BK5TXDDP.js";
import {
  require_incidents
} from "/build/_shared/chunk-J24HANUS.js";
import {
  require_node
} from "/build/_shared/chunk-ZP6BZTHN.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/admin.incident.delete.$id.tsx
var import_node = __toESM(require_node(), 1);
var import_incidents = __toESM(require_incidents(), 1);
var import_session = __toESM(require_session(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.incident.delete.$id.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.incident.delete.$id.tsx"
  );
  import.meta.hot.lastModified = "1751305578382.074";
}
function IncidentDelete() {
  return null;
}
_c = IncidentDelete;
var _c;
$RefreshReg$(_c, "IncidentDelete");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  IncidentDelete as default
};
//# sourceMappingURL=/build/routes/admin.incident.delete.$id-YTN5LWPK.js.map
