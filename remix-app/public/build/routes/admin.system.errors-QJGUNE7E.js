import {
  useUser
} from "/build/_shared/chunk-DFTVPORL.js";
import {
  Form,
  Link,
  useActionData,
  useLoaderData,
  useNavigation
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  require_node
} from "/build/_shared/chunk-ZP6BZTHN.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/admin.system.errors.tsx
var import_node = __toESM(require_node(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.system.errors.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.system.errors.tsx"
  );
  import.meta.hot.lastModified = "1748982388621.867";
}
var SentryApiClient = class {
  constructor() {
    this.apiToken = process.env.SENTRY_API_TOKEN || "mock-token";
    this.organizationSlug = process.env.SENTRY_ORG || "organization";
    this.projectSlug = process.env.SENTRY_PROJECT || "project";
    const sentryDsn = process.env.SENTRY_DSN || "";
    try {
      const url = new URL(sentryDsn);
      this.sentryBaseUrl = `${url.protocol}//${url.host}`;
    } catch (e) {
      this.sentryBaseUrl = "https://sentry.ableengine.site";
    }
  }
  // In a real implementation, this would make actual API calls to Sentry
  async getIssues() {
    return [{
      id: "issue1",
      title: 'TypeError: Cannot read property "x" of undefined',
      culprit: "app/routes/search.tsx",
      level: "error",
      status: "unresolved",
      firstSeen: "2023-06-15T10:30:00Z",
      lastSeen: "2023-06-15T14:45:00Z",
      count: 42,
      userCount: 15,
      assignedTo: null,
      permalink: `${this.sentryBaseUrl}/organizations/org/issues/issue1/`
    }, {
      id: "issue2",
      title: "ReferenceError: fetch is not defined",
      culprit: "app/routes/api.incidents.tsx",
      level: "error",
      status: "resolved",
      firstSeen: "2023-06-10T08:20:00Z",
      lastSeen: "2023-06-10T09:15:00Z",
      count: 3,
      userCount: 2,
      assignedTo: "<EMAIL>",
      permalink: `${this.sentryBaseUrl}/organizations/org/issues/issue2/`
    }, {
      id: "issue3",
      title: "Failed to fetch: Network error",
      culprit: "app/routes/dashboard.tsx",
      level: "warning",
      status: "unresolved",
      firstSeen: "2023-06-14T16:40:00Z",
      lastSeen: "2023-06-15T17:30:00Z",
      count: 28,
      userCount: 12,
      assignedTo: null,
      permalink: `${this.sentryBaseUrl}/organizations/org/issues/issue3/`
    }];
  }
  async getIssueDetails(issueId) {
    const issues = {
      issue1: {
        id: "issue1",
        title: 'TypeError: Cannot read property "x" of undefined',
        culprit: "app/routes/search.tsx",
        level: "error",
        status: "unresolved",
        firstSeen: "2023-06-15T10:30:00Z",
        lastSeen: "2023-06-15T14:45:00Z",
        count: 42,
        userCount: 15,
        assignedTo: null,
        permalink: `${this.sentryBaseUrl}/organizations/org/issues/issue1/`,
        events: [{
          id: "event1",
          timestamp: "2023-06-15T14:45:00Z",
          user: {
            id: "user123",
            email: "<EMAIL>",
            ip_address: "***********"
          },
          tags: {
            browser: "Chrome",
            device: "Desktop",
            os: "Windows"
          },
          exception: {
            values: [{
              type: "TypeError",
              value: 'Cannot read property "x" of undefined',
              stacktrace: {
                frames: [{
                  filename: "app/routes/search.tsx",
                  function: "handleSearch",
                  lineno: 42,
                  colno: 15
                }, {
                  filename: "app/routes/search.tsx",
                  function: "SearchPage",
                  lineno: 30,
                  colno: 10
                }]
              }
            }]
          }
        }]
      },
      issue2: {
        id: "issue2",
        title: "ReferenceError: fetch is not defined",
        culprit: "app/routes/api.incidents.tsx",
        level: "error",
        status: "resolved",
        firstSeen: "2023-06-10T08:20:00Z",
        lastSeen: "2023-06-10T09:15:00Z",
        count: 3,
        userCount: 2,
        assignedTo: "<EMAIL>",
        permalink: `${this.sentryBaseUrl}/organizations/org/issues/issue2/`,
        events: [{
          id: "event2",
          timestamp: "2023-06-10T09:15:00Z",
          user: {
            id: "user456",
            email: "<EMAIL>",
            ip_address: "***********"
          },
          tags: {
            browser: "Firefox",
            device: "Desktop",
            os: "MacOS"
          },
          exception: {
            values: [{
              type: "ReferenceError",
              value: "fetch is not defined",
              stacktrace: {
                frames: [{
                  filename: "app/routes/api.incidents.tsx",
                  function: "fetchIncidents",
                  lineno: 15,
                  colno: 8
                }, {
                  filename: "app/routes/api.incidents.tsx",
                  function: "loader",
                  lineno: 10,
                  colno: 5
                }]
              }
            }]
          }
        }]
      },
      issue3: {
        id: "issue3",
        title: "Failed to fetch: Network error",
        culprit: "app/routes/dashboard.tsx",
        level: "warning",
        status: "unresolved",
        firstSeen: "2023-06-14T16:40:00Z",
        lastSeen: "2023-06-15T17:30:00Z",
        count: 28,
        userCount: 12,
        assignedTo: null,
        permalink: `${this.sentryBaseUrl}/organizations/org/issues/issue3/`,
        events: [{
          id: "event3",
          timestamp: "2023-06-15T17:30:00Z",
          user: {
            id: "user789",
            email: "<EMAIL>",
            ip_address: "***********"
          },
          tags: {
            browser: "Safari",
            device: "Mobile",
            os: "iOS"
          },
          exception: {
            values: [{
              type: "Error",
              value: "Failed to fetch: Network error",
              stacktrace: {
                frames: [{
                  filename: "app/routes/dashboard.tsx",
                  function: "fetchDashboardData",
                  lineno: 78,
                  colno: 22
                }, {
                  filename: "app/routes/dashboard.tsx",
                  function: "DashboardPage",
                  lineno: 65,
                  colno: 18
                }]
              }
            }]
          }
        }]
      }
    };
    return issues[issueId] || null;
  }
  async updateIssueStatus(issueId, status) {
    console.log(`Updating issue ${issueId} status to ${status}`);
    return {
      success: true
    };
  }
  async assignIssue(issueId, assignee) {
    console.log(`Assigning issue ${issueId} to ${assignee || "no one"}`);
    return {
      success: true
    };
  }
};
var sentryClient = new SentryApiClient();
function ErrorLogsPage() {
  _s();
  const user = useUser();
  if (user.role !== "Admin") {
    throw new Error("No permission.");
  }
  const data = useLoaderData();
  const actionData = useActionData();
  const navigation = useNavigation();
  const [assigneeInput, setAssigneeInput] = (0, import_react2.useState)("");
  const isSubmitting = navigation.state === "submitting";
  const getLevelColor = (level) => {
    switch (level) {
      case "error":
        return "bg-red-100 text-red-800";
      case "warning":
        return "bg-yellow-100 text-yellow-800";
      case "info":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };
  const getStatusColor = (status) => {
    switch (status) {
      case "resolved":
        return "bg-green-100 text-green-800";
      case "unresolved":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "p-8", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h1", { className: "mb-8 text-3xl font-bold", children: "Error Logs" }, void 0, false, {
      fileName: "app/routes/admin.system.errors.tsx",
      lineNumber: 350,
      columnNumber: 4
    }, this),
    actionData?.message && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: `mb-4 rounded p-4 ${actionData.success ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`, children: [
      actionData.message,
      actionData.error && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-2 text-sm", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("strong", { children: "Error:" }, void 0, false, {
          fileName: "app/routes/admin.system.errors.tsx",
          lineNumber: 355,
          columnNumber: 8
        }, this),
        " ",
        actionData.error
      ] }, void 0, true, {
        fileName: "app/routes/admin.system.errors.tsx",
        lineNumber: 354,
        columnNumber: 27
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.system.errors.tsx",
      lineNumber: 352,
      columnNumber: 28
    }, this),
    data.issueDetails ? (
      // Issue details view
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-4", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "/admin/system/errors", className: "rounded bg-gray-500 px-3 py-1 text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 inline-block", children: "Back to Issues" }, void 0, false, {
          fileName: "app/routes/admin.system.errors.tsx",
          lineNumber: 363,
          columnNumber: 7
        }, this) }, void 0, false, {
          fileName: "app/routes/admin.system.errors.tsx",
          lineNumber: 362,
          columnNumber: 6
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-8 overflow-hidden bg-white shadow sm:rounded-lg", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "px-4 py-5 sm:px-6", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h2", { className: "text-xl font-semibold", children: data.issueDetails.title }, void 0, false, {
              fileName: "app/routes/admin.system.errors.tsx",
              lineNumber: 370,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-2 flex flex-wrap gap-2", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: `rounded-full px-2 py-1 text-xs ${getLevelColor(data.issueDetails.level)}`, children: data.issueDetails.level }, void 0, false, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 372,
                columnNumber: 9
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: `rounded-full px-2 py-1 text-xs ${getStatusColor(data.issueDetails.status)}`, children: data.issueDetails.status }, void 0, false, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 375,
                columnNumber: 9
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.errors.tsx",
              lineNumber: 371,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.errors.tsx",
            lineNumber: 369,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "border-t border-gray-200 px-4 py-5 sm:px-6", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("dl", { className: "grid grid-cols-1 gap-4 sm:grid-cols-2", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "sm:col-span-1", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("dt", { className: "text-sm font-medium text-gray-500", children: "First Seen" }, void 0, false, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 384,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("dd", { className: "mt-1 text-sm text-gray-900", children: formatDate(data.issueDetails.firstSeen) }, void 0, false, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 385,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.errors.tsx",
              lineNumber: 383,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "sm:col-span-1", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("dt", { className: "text-sm font-medium text-gray-500", children: "Last Seen" }, void 0, false, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 390,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("dd", { className: "mt-1 text-sm text-gray-900", children: formatDate(data.issueDetails.lastSeen) }, void 0, false, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 391,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.errors.tsx",
              lineNumber: 389,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "sm:col-span-1", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("dt", { className: "text-sm font-medium text-gray-500", children: "Occurrences" }, void 0, false, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 396,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("dd", { className: "mt-1 text-sm text-gray-900", children: data.issueDetails.count }, void 0, false, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 397,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.errors.tsx",
              lineNumber: 395,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "sm:col-span-1", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("dt", { className: "text-sm font-medium text-gray-500", children: "Users Affected" }, void 0, false, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 400,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("dd", { className: "mt-1 text-sm text-gray-900", children: data.issueDetails.userCount }, void 0, false, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 401,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.errors.tsx",
              lineNumber: 399,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "sm:col-span-1", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("dt", { className: "text-sm font-medium text-gray-500", children: "Culprit" }, void 0, false, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 404,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("dd", { className: "mt-1 text-sm text-gray-900", children: data.issueDetails.culprit }, void 0, false, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 405,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.errors.tsx",
              lineNumber: 403,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "sm:col-span-1", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("dt", { className: "text-sm font-medium text-gray-500", children: "Assigned To" }, void 0, false, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 408,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("dd", { className: "mt-1 text-sm text-gray-900", children: data.issueDetails.assignedTo || "Unassigned" }, void 0, false, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 409,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.errors.tsx",
              lineNumber: 407,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "sm:col-span-2", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("dt", { className: "text-sm font-medium text-gray-500", children: "Permalink" }, void 0, false, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 414,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("dd", { className: "mt-1 text-sm text-gray-900", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { href: data.issueDetails.permalink, target: "_blank", rel: "noopener noreferrer", className: "text-blue-600 hover:text-blue-800", children: "View in Sentry" }, void 0, false, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 416,
                columnNumber: 11
              }, this) }, void 0, false, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 415,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.errors.tsx",
              lineNumber: 413,
              columnNumber: 9
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.errors.tsx",
            lineNumber: 382,
            columnNumber: 8
          }, this) }, void 0, false, {
            fileName: "app/routes/admin.system.errors.tsx",
            lineNumber: 381,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "border-t border-gray-200 px-4 py-5 sm:px-6", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h3", { className: "text-lg font-medium text-gray-900", children: "Actions" }, void 0, false, {
              fileName: "app/routes/admin.system.errors.tsx",
              lineNumber: 425,
              columnNumber: 8
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-4 flex flex-wrap gap-2", children: [
              data.issueDetails.status === "unresolved" ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { method: "post", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "action", value: "resolve" }, void 0, false, {
                  fileName: "app/routes/admin.system.errors.tsx",
                  lineNumber: 428,
                  columnNumber: 11
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "issueId", value: data.issueDetails.id }, void 0, false, {
                  fileName: "app/routes/admin.system.errors.tsx",
                  lineNumber: 429,
                  columnNumber: 11
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", className: "rounded bg-green-500 px-3 py-1 text-white hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50", disabled: isSubmitting, children: isSubmitting && navigation.formData?.get("action") === "resolve" ? "Resolving..." : "Resolve" }, void 0, false, {
                  fileName: "app/routes/admin.system.errors.tsx",
                  lineNumber: 430,
                  columnNumber: 11
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 427,
                columnNumber: 54
              }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { method: "post", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "action", value: "unresolve" }, void 0, false, {
                  fileName: "app/routes/admin.system.errors.tsx",
                  lineNumber: 434,
                  columnNumber: 11
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "issueId", value: data.issueDetails.id }, void 0, false, {
                  fileName: "app/routes/admin.system.errors.tsx",
                  lineNumber: 435,
                  columnNumber: 11
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", className: "rounded bg-yellow-500 px-3 py-1 text-white hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-opacity-50", disabled: isSubmitting, children: isSubmitting && navigation.formData?.get("action") === "unresolve" ? "Unresolving..." : "Unresolve" }, void 0, false, {
                  fileName: "app/routes/admin.system.errors.tsx",
                  lineNumber: 436,
                  columnNumber: 11
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 433,
                columnNumber: 20
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { method: "post", className: "flex items-center gap-2", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "action", value: "assign" }, void 0, false, {
                  fileName: "app/routes/admin.system.errors.tsx",
                  lineNumber: 442,
                  columnNumber: 10
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "issueId", value: data.issueDetails.id }, void 0, false, {
                  fileName: "app/routes/admin.system.errors.tsx",
                  lineNumber: 443,
                  columnNumber: 10
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "text", name: "assignee", placeholder: "Email address", value: assigneeInput, onChange: (e) => setAssigneeInput(e.target.value), className: "rounded border border-gray-300 px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50" }, void 0, false, {
                  fileName: "app/routes/admin.system.errors.tsx",
                  lineNumber: 444,
                  columnNumber: 10
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", className: "rounded bg-blue-500 px-3 py-1 text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50", disabled: isSubmitting, children: isSubmitting && navigation.formData?.get("action") === "assign" ? "Assigning..." : "Assign" }, void 0, false, {
                  fileName: "app/routes/admin.system.errors.tsx",
                  lineNumber: 445,
                  columnNumber: 10
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 441,
                columnNumber: 9
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.errors.tsx",
              lineNumber: 426,
              columnNumber: 8
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.errors.tsx",
            lineNumber: 424,
            columnNumber: 7
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "border-t border-gray-200 px-4 py-5 sm:px-6", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h3", { className: "mb-4 text-lg font-medium text-gray-900", children: "Latest Event" }, void 0, false, {
              fileName: "app/routes/admin.system.errors.tsx",
              lineNumber: 453,
              columnNumber: 8
            }, this),
            data.issueDetails.events && data.issueDetails.events.length > 0 && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-4", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h4", { className: "text-md font-medium text-gray-700", children: "Event Information" }, void 0, false, {
                  fileName: "app/routes/admin.system.errors.tsx",
                  lineNumber: 456,
                  columnNumber: 11
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-2 grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2", children: [
                  /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "sm:col-span-1", children: [
                    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "text-sm font-medium text-gray-500", children: "Timestamp:" }, void 0, false, {
                      fileName: "app/routes/admin.system.errors.tsx",
                      lineNumber: 459,
                      columnNumber: 13
                    }, this),
                    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "ml-2 text-sm text-gray-900", children: formatDate(data.issueDetails.events[0].timestamp) }, void 0, false, {
                      fileName: "app/routes/admin.system.errors.tsx",
                      lineNumber: 460,
                      columnNumber: 13
                    }, this)
                  ] }, void 0, true, {
                    fileName: "app/routes/admin.system.errors.tsx",
                    lineNumber: 458,
                    columnNumber: 12
                  }, this),
                  /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "sm:col-span-1", children: [
                    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "text-sm font-medium text-gray-500", children: "User:" }, void 0, false, {
                      fileName: "app/routes/admin.system.errors.tsx",
                      lineNumber: 465,
                      columnNumber: 13
                    }, this),
                    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "ml-2 text-sm text-gray-900", children: data.issueDetails.events[0].user.email }, void 0, false, {
                      fileName: "app/routes/admin.system.errors.tsx",
                      lineNumber: 466,
                      columnNumber: 13
                    }, this)
                  ] }, void 0, true, {
                    fileName: "app/routes/admin.system.errors.tsx",
                    lineNumber: 464,
                    columnNumber: 12
                  }, this),
                  /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "sm:col-span-1", children: [
                    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "text-sm font-medium text-gray-500", children: "IP Address:" }, void 0, false, {
                      fileName: "app/routes/admin.system.errors.tsx",
                      lineNumber: 471,
                      columnNumber: 13
                    }, this),
                    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "ml-2 text-sm text-gray-900", children: data.issueDetails.events[0].user.ip_address }, void 0, false, {
                      fileName: "app/routes/admin.system.errors.tsx",
                      lineNumber: 472,
                      columnNumber: 13
                    }, this)
                  ] }, void 0, true, {
                    fileName: "app/routes/admin.system.errors.tsx",
                    lineNumber: 470,
                    columnNumber: 12
                  }, this)
                ] }, void 0, true, {
                  fileName: "app/routes/admin.system.errors.tsx",
                  lineNumber: 457,
                  columnNumber: 11
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 455,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-4", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h4", { className: "text-md font-medium text-gray-700", children: "Tags" }, void 0, false, {
                  fileName: "app/routes/admin.system.errors.tsx",
                  lineNumber: 480,
                  columnNumber: 11
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-2 flex flex-wrap gap-2", children: Object.entries(data.issueDetails.events[0].tags).map(([key, value]) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "rounded-full bg-gray-100 px-2 py-1 text-xs", children: [
                  key,
                  ": ",
                  String(value)
                ] }, key, true, {
                  fileName: "app/routes/admin.system.errors.tsx",
                  lineNumber: 482,
                  columnNumber: 84
                }, this)) }, void 0, false, {
                  fileName: "app/routes/admin.system.errors.tsx",
                  lineNumber: 481,
                  columnNumber: 11
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 479,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h4", { className: "text-md font-medium text-gray-700", children: "Exception" }, void 0, false, {
                  fileName: "app/routes/admin.system.errors.tsx",
                  lineNumber: 489,
                  columnNumber: 11
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-2", children: [
                  /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-sm", children: [
                    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: [
                      data.issueDetails.events[0].exception.values[0].type,
                      ":"
                    ] }, void 0, true, {
                      fileName: "app/routes/admin.system.errors.tsx",
                      lineNumber: 492,
                      columnNumber: 13
                    }, this),
                    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "ml-1", children: data.issueDetails.events[0].exception.values[0].value }, void 0, false, {
                      fileName: "app/routes/admin.system.errors.tsx",
                      lineNumber: 495,
                      columnNumber: 13
                    }, this)
                  ] }, void 0, true, {
                    fileName: "app/routes/admin.system.errors.tsx",
                    lineNumber: 491,
                    columnNumber: 12
                  }, this),
                  /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-2", children: [
                    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h5", { className: "text-sm font-medium text-gray-700", children: "Stack Trace" }, void 0, false, {
                      fileName: "app/routes/admin.system.errors.tsx",
                      lineNumber: 501,
                      columnNumber: 13
                    }, this),
                    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-1 max-h-96 overflow-auto rounded bg-gray-50 p-4", children: data.issueDetails.events[0].exception.values[0].stacktrace.frames.map((frame, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "font-mono mb-1 text-xs", children: [
                      "at ",
                      frame.function,
                      " (",
                      frame.filename,
                      ":",
                      frame.lineno,
                      ":",
                      frame.colno,
                      ")"
                    ] }, index, true, {
                      fileName: "app/routes/admin.system.errors.tsx",
                      lineNumber: 503,
                      columnNumber: 103
                    }, this)) }, void 0, false, {
                      fileName: "app/routes/admin.system.errors.tsx",
                      lineNumber: 502,
                      columnNumber: 13
                    }, this)
                  ] }, void 0, true, {
                    fileName: "app/routes/admin.system.errors.tsx",
                    lineNumber: 500,
                    columnNumber: 12
                  }, this)
                ] }, void 0, true, {
                  fileName: "app/routes/admin.system.errors.tsx",
                  lineNumber: 490,
                  columnNumber: 11
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.errors.tsx",
                lineNumber: 488,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.errors.tsx",
              lineNumber: 454,
              columnNumber: 76
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.errors.tsx",
            lineNumber: 452,
            columnNumber: 7
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.system.errors.tsx",
          lineNumber: 368,
          columnNumber: 6
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.system.errors.tsx",
        lineNumber: 361,
        columnNumber: 5
      }, this)
    ) : (
      // Issues list view
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "overflow-hidden bg-white shadow sm:rounded-md", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("ul", { className: "divide-y divide-gray-200", children: data.issues && data.issues.length > 0 ? data.issues.map((issue) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "px-6 py-4", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex items-center justify-between", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex-1", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex items-center", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: `/admin/system/errors?issueId=${issue.id}`, className: "font-medium text-blue-600 hover:text-blue-800", children: issue.title }, void 0, false, {
              fileName: "app/routes/admin.system.errors.tsx",
              lineNumber: 522,
              columnNumber: 13
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: `ml-2 rounded-full px-2 py-1 text-xs ${getLevelColor(issue.level)}`, children: issue.level }, void 0, false, {
              fileName: "app/routes/admin.system.errors.tsx",
              lineNumber: 525,
              columnNumber: 13
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: `ml-2 rounded-full px-2 py-1 text-xs ${getStatusColor(issue.status)}`, children: issue.status }, void 0, false, {
              fileName: "app/routes/admin.system.errors.tsx",
              lineNumber: 528,
              columnNumber: 13
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.errors.tsx",
            lineNumber: 521,
            columnNumber: 12
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-1 text-sm text-gray-500", children: [
            issue.culprit,
            " \u2022 ",
            formatDate(issue.lastSeen),
            " \u2022 ",
            issue.count,
            " ",
            "occurrences \u2022 ",
            issue.userCount,
            " users affected"
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.errors.tsx",
            lineNumber: 532,
            columnNumber: 12
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.system.errors.tsx",
          lineNumber: 520,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex space-x-2", children: issue.status === "unresolved" ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { method: "post", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "action", value: "resolve" }, void 0, false, {
            fileName: "app/routes/admin.system.errors.tsx",
            lineNumber: 539,
            columnNumber: 14
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "issueId", value: issue.id }, void 0, false, {
            fileName: "app/routes/admin.system.errors.tsx",
            lineNumber: 540,
            columnNumber: 14
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", className: "rounded bg-green-500 px-3 py-1 text-white hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50", disabled: isSubmitting, children: isSubmitting && navigation.formData?.get("issueId") === issue.id && navigation.formData?.get("action") === "resolve" ? "Resolving..." : "Resolve" }, void 0, false, {
            fileName: "app/routes/admin.system.errors.tsx",
            lineNumber: 541,
            columnNumber: 14
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.system.errors.tsx",
          lineNumber: 538,
          columnNumber: 45
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { method: "post", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "action", value: "unresolve" }, void 0, false, {
            fileName: "app/routes/admin.system.errors.tsx",
            lineNumber: 545,
            columnNumber: 14
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "hidden", name: "issueId", value: issue.id }, void 0, false, {
            fileName: "app/routes/admin.system.errors.tsx",
            lineNumber: 546,
            columnNumber: 14
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "submit", className: "rounded bg-yellow-500 px-3 py-1 text-white hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-opacity-50", disabled: isSubmitting, children: isSubmitting && navigation.formData?.get("issueId") === issue.id && navigation.formData?.get("action") === "unresolve" ? "Unresolving..." : "Unresolve" }, void 0, false, {
            fileName: "app/routes/admin.system.errors.tsx",
            lineNumber: 547,
            columnNumber: 14
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.system.errors.tsx",
          lineNumber: 544,
          columnNumber: 23
        }, this) }, void 0, false, {
          fileName: "app/routes/admin.system.errors.tsx",
          lineNumber: 537,
          columnNumber: 11
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.system.errors.tsx",
        lineNumber: 519,
        columnNumber: 10
      }, this) }, issue.id, false, {
        fileName: "app/routes/admin.system.errors.tsx",
        lineNumber: 518,
        columnNumber: 73
      }, this)) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "px-6 py-4 text-center text-gray-500", children: "No issues found" }, void 0, false, {
        fileName: "app/routes/admin.system.errors.tsx",
        lineNumber: 553,
        columnNumber: 18
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.system.errors.tsx",
        lineNumber: 517,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/admin.system.errors.tsx",
        lineNumber: 516,
        columnNumber: 5
      }, this)
    )
  ] }, void 0, true, {
    fileName: "app/routes/admin.system.errors.tsx",
    lineNumber: 349,
    columnNumber: 10
  }, this);
}
_s(ErrorLogsPage, "Bq/z+rBdRin5zZ4O2fuq82ofGcs=", false, function() {
  return [useUser, useLoaderData, useActionData, useNavigation];
});
_c = ErrorLogsPage;
var _c;
$RefreshReg$(_c, "ErrorLogsPage");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  ErrorLogsPage as default
};
//# sourceMappingURL=/build/routes/admin.system.errors-QJGUNE7E.js.map
