import {
  useUser
} from "/build/_shared/chunk-DFTVPORL.js";
import {
  useLoaderData
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import "/build/_shared/chunk-QT64XSGC.js";
import {
  require_node
} from "/build/_shared/chunk-ZP6BZTHN.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __commonJS,
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// empty-module:~/utils/aws.server
var require_aws = __commonJS({
  "empty-module:~/utils/aws.server"(exports, module) {
    module.exports = {};
  }
});

// app/routes/admin.system.aws.tsx
var import_node = __toESM(require_node(), 1);
var import_aws = __toESM(require_aws(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/admin.system.aws.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/admin.system.aws.tsx"
  );
  import.meta.hot.lastModified = "1748980550223.2378";
}
function AWSStatsPage() {
  _s();
  const user = useUser();
  if (user.role !== "Admin") {
    throw new Error("No permission.");
  }
  const data = useLoaderData();
  const {
    awsStats
  } = data;
  const formatDate = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };
  const formatBytes = (bytes) => {
    if (!bytes)
      return "N/A";
    const gb = bytes / (1024 * 1024 * 1024);
    return `${gb.toFixed(2)} GB`;
  };
  const formatPercentage = (value) => {
    if (value === void 0 || value === null)
      return "N/A";
    return `${value.toFixed(2)}%`;
  };
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "p-8", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h1", { className: "mb-8 text-3xl font-bold", children: "AWS Deployment Stats" }, void 0, false, {
      fileName: "app/routes/admin.system.aws.tsx",
      lineNumber: 65,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid grid-cols-1 gap-8 md:grid-cols-2", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-lg bg-gray-100 p-6 shadow md:col-span-2", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h2", { className: "mb-4 text-xl font-semibold", children: "ECS Cluster Information" }, void 0, false, {
          fileName: "app/routes/admin.system.aws.tsx",
          lineNumber: 70,
          columnNumber: 6
        }, this),
        awsStats.ecsCluster ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-2", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Cluster Name:" }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 73,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: awsStats.ecsCluster.clusterName }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 74,
              columnNumber: 9
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.aws.tsx",
            lineNumber: 72,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Status:" }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 77,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: awsStats.ecsCluster.status }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 78,
              columnNumber: 9
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.aws.tsx",
            lineNumber: 76,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Running Tasks:" }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 81,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: awsStats.ecsCluster.runningTasksCount }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 82,
              columnNumber: 9
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.aws.tsx",
            lineNumber: 80,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Pending Tasks:" }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 85,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: awsStats.ecsCluster.pendingTasksCount }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 86,
              columnNumber: 9
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.aws.tsx",
            lineNumber: 84,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Active Services:" }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 89,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: awsStats.ecsCluster.activeServicesCount }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 90,
              columnNumber: 9
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.aws.tsx",
            lineNumber: 88,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Registered Container Instances:" }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 93,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: awsStats.ecsCluster.registeredContainerInstancesCount }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 94,
              columnNumber: 9
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.aws.tsx",
            lineNumber: 92,
            columnNumber: 8
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.system.aws.tsx",
          lineNumber: 71,
          columnNumber: 29
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-gray-500", children: "No ECS cluster information available" }, void 0, false, {
          fileName: "app/routes/admin.system.aws.tsx",
          lineNumber: 96,
          columnNumber: 16
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.system.aws.tsx",
        lineNumber: 69,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-lg bg-gray-100 p-6 shadow md:col-span-2", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h2", { className: "mb-4 text-xl font-semibold", children: "Current Deployment Information" }, void 0, false, {
          fileName: "app/routes/admin.system.aws.tsx",
          lineNumber: 101,
          columnNumber: 6
        }, this),
        awsStats.ecsService ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-4", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-2", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Service Name:" }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 105,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: awsStats.ecsService.serviceName }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 106,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 104,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Status:" }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 109,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: awsStats.ecsService.status }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 110,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 108,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Desired Count:" }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 113,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: awsStats.ecsService.desiredCount }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 114,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 112,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Running Count:" }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 117,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: awsStats.ecsService.runningCount }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 118,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 116,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Pending Count:" }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 121,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: awsStats.ecsService.pendingCount }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 122,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 120,
              columnNumber: 9
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.aws.tsx",
            lineNumber: 103,
            columnNumber: 8
          }, this),
          awsStats.ecsService.deployments && awsStats.ecsService.deployments.length > 0 && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h3", { className: "mb-3 text-lg font-medium", children: "Deployments" }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 127,
              columnNumber: 10
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-4", children: awsStats.ecsService.deployments.map((deployment, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded border border-gray-200 p-3", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Status:" }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 131,
                  columnNumber: 14
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: deployment.status }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 132,
                  columnNumber: 14
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 130,
                columnNumber: 13
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Created At:" }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 135,
                  columnNumber: 14
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: formatDate(deployment.createdAt) }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 136,
                  columnNumber: 14
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 134,
                columnNumber: 13
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Updated At:" }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 139,
                  columnNumber: 14
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: formatDate(deployment.updatedAt) }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 140,
                  columnNumber: 14
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 138,
                columnNumber: 13
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Desired Count:" }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 143,
                  columnNumber: 14
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: deployment.desiredCount }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 144,
                  columnNumber: 14
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 142,
                columnNumber: 13
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Running Count:" }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 147,
                  columnNumber: 14
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: deployment.runningCount }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 148,
                  columnNumber: 14
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 146,
                columnNumber: 13
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Pending Count:" }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 151,
                  columnNumber: 14
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: deployment.pendingCount }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 152,
                  columnNumber: 14
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 150,
                columnNumber: 13
              }, this)
            ] }, index, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 129,
              columnNumber: 71
            }, this)) }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 128,
              columnNumber: 10
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.aws.tsx",
            lineNumber: 126,
            columnNumber: 90
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.system.aws.tsx",
          lineNumber: 102,
          columnNumber: 29
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-gray-500", children: "No deployment information available" }, void 0, false, {
          fileName: "app/routes/admin.system.aws.tsx",
          lineNumber: 157,
          columnNumber: 16
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.system.aws.tsx",
        lineNumber: 100,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-lg bg-gray-100 p-6 shadow md:col-span-2", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h2", { className: "mb-4 text-xl font-semibold", children: "Task Definition Information" }, void 0, false, {
          fileName: "app/routes/admin.system.aws.tsx",
          lineNumber: 162,
          columnNumber: 6
        }, this),
        awsStats.ecsTaskDefinition ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-4", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-2", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Family:" }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 166,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: awsStats.ecsTaskDefinition.family }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 167,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 165,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Revision:" }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 170,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: awsStats.ecsTaskDefinition.revision }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 171,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 169,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Status:" }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 174,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: awsStats.ecsTaskDefinition.status }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 175,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 173,
              columnNumber: 9
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.aws.tsx",
            lineNumber: 164,
            columnNumber: 8
          }, this),
          awsStats.ecsTaskDefinition.containerDefinitions && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h3", { className: "mb-3 text-lg font-medium", children: "Container Definitions" }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 180,
              columnNumber: 10
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-4", children: awsStats.ecsTaskDefinition.containerDefinitions.map((container, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded border border-gray-200 p-3", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Name:" }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 184,
                  columnNumber: 14
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: container.name }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 185,
                  columnNumber: 14
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 183,
                columnNumber: 13
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Image:" }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 188,
                  columnNumber: 14
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "max-w-md truncate", children: container.image }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 189,
                  columnNumber: 14
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 187,
                columnNumber: 13
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "CPU:" }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 192,
                  columnNumber: 14
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: (() => {
                  if (container.cpu === void 0 || container.cpu === null) {
                    return "N/A";
                  }
                  const cpuValue = Number(container.cpu);
                  if (isNaN(cpuValue)) {
                    return container.cpu;
                  } else {
                    return `${cpuValue} units (${(cpuValue / 1024).toFixed(2)} vCPU)`;
                  }
                })() }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 193,
                  columnNumber: 14
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 191,
                columnNumber: 13
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Memory:" }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 211,
                  columnNumber: 14
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: (() => {
                  if (container.memory === void 0 || container.memory === null) {
                    return "N/A";
                  }
                  const memoryValue = Number(container.memory);
                  if (isNaN(memoryValue)) {
                    return container.memory;
                  } else {
                    return `${memoryValue} MB (${(memoryValue / 1024).toFixed(2)} GB)`;
                  }
                })() }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 212,
                  columnNumber: 14
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 210,
                columnNumber: 13
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Essential:" }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 230,
                  columnNumber: 14
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: container.essential ? "Yes" : "No" }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 231,
                  columnNumber: 14
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 229,
                columnNumber: 13
              }, this)
            ] }, index, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 182,
              columnNumber: 86
            }, this)) }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 181,
              columnNumber: 10
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.aws.tsx",
            lineNumber: 179,
            columnNumber: 60
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.system.aws.tsx",
          lineNumber: 163,
          columnNumber: 36
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-gray-500", children: "No task definition information available" }, void 0, false, {
          fileName: "app/routes/admin.system.aws.tsx",
          lineNumber: 236,
          columnNumber: 16
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.system.aws.tsx",
        lineNumber: 161,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-lg bg-gray-100 p-6 shadow md:col-span-2", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h2", { className: "mb-4 text-xl font-semibold", children: "Container Stats" }, void 0, false, {
          fileName: "app/routes/admin.system.aws.tsx",
          lineNumber: 241,
          columnNumber: 6
        }, this),
        awsStats.ecsTasks && awsStats.ecsTasks.length > 0 ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-6", children: awsStats.ecsTasks.map((task, taskIndex) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded border border-gray-200 p-4", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h3", { className: "mb-3 text-lg font-medium", children: [
            "Task: ",
            task.taskArn.split("/").pop()
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.aws.tsx",
            lineNumber: 244,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-3 space-y-2", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Status:" }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 250,
                columnNumber: 12
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: task.lastStatus }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 251,
                columnNumber: 12
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 249,
              columnNumber: 11
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Started At:" }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 254,
                columnNumber: 12
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: task.startedAt ? formatDate(task.startedAt) : "N/A" }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 255,
                columnNumber: 12
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 253,
              columnNumber: 11
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.aws.tsx",
            lineNumber: 248,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-4", children: task.containers.map((container, containerIndex) => {
            const containerMetrics = awsStats.containerMetrics.find((metric) => metric.taskId === task.taskArn.split("/").pop() && metric.containerName === container.name);
            return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded border border-gray-200 p-3", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h4", { className: "mb-2 font-medium", children: [
                "Container: ",
                container.name
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 264,
                columnNumber: 14
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mb-3 space-y-2", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Status:" }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 268,
                  columnNumber: 16
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: container.lastStatus }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 269,
                  columnNumber: 16
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 267,
                columnNumber: 15
              }, this) }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 266,
                columnNumber: 14
              }, this),
              containerMetrics && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3 grid grid-cols-1 gap-4 md:grid-cols-2", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded border border-gray-200 p-3", children: [
                  /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h5", { className: "mb-2 text-sm font-medium", children: "CPU Utilization" }, void 0, false, {
                    fileName: "app/routes/admin.system.aws.tsx",
                    lineNumber: 276,
                    columnNumber: 17
                  }, this),
                  containerMetrics.cpuUtilization.length > 0 ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-1", children: containerMetrics.cpuUtilization.sort((a, b) => new Date(b.Timestamp).getTime() - new Date(a.Timestamp).getTime()).slice(0, 5).map((point, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between text-sm", children: [
                    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: new Date(point.Timestamp).toLocaleTimeString() }, void 0, false, {
                      fileName: "app/routes/admin.system.aws.tsx",
                      lineNumber: 279,
                      columnNumber: 22
                    }, this),
                    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: formatPercentage(point.Average) }, void 0, false, {
                      fileName: "app/routes/admin.system.aws.tsx",
                      lineNumber: 282,
                      columnNumber: 22
                    }, this)
                  ] }, index, true, {
                    fileName: "app/routes/admin.system.aws.tsx",
                    lineNumber: 278,
                    columnNumber: 168
                  }, this)) }, void 0, false, {
                    fileName: "app/routes/admin.system.aws.tsx",
                    lineNumber: 277,
                    columnNumber: 63
                  }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-gray-500 text-sm", children: "No CPU data available" }, void 0, false, {
                    fileName: "app/routes/admin.system.aws.tsx",
                    lineNumber: 284,
                    columnNumber: 27
                  }, this)
                ] }, void 0, true, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 275,
                  columnNumber: 16
                }, this),
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded border border-gray-200 p-3", children: [
                  /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h5", { className: "mb-2 text-sm font-medium", children: "Memory Utilization" }, void 0, false, {
                    fileName: "app/routes/admin.system.aws.tsx",
                    lineNumber: 289,
                    columnNumber: 17
                  }, this),
                  containerMetrics.memoryUtilization.length > 0 ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-1", children: containerMetrics.memoryUtilization.sort((a, b) => new Date(b.Timestamp).getTime() - new Date(a.Timestamp).getTime()).slice(0, 5).map((point, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between text-sm", children: [
                    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: new Date(point.Timestamp).toLocaleTimeString() }, void 0, false, {
                      fileName: "app/routes/admin.system.aws.tsx",
                      lineNumber: 292,
                      columnNumber: 22
                    }, this),
                    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: formatPercentage(point.Average) }, void 0, false, {
                      fileName: "app/routes/admin.system.aws.tsx",
                      lineNumber: 295,
                      columnNumber: 22
                    }, this)
                  ] }, index, true, {
                    fileName: "app/routes/admin.system.aws.tsx",
                    lineNumber: 291,
                    columnNumber: 171
                  }, this)) }, void 0, false, {
                    fileName: "app/routes/admin.system.aws.tsx",
                    lineNumber: 290,
                    columnNumber: 66
                  }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-gray-500 text-sm", children: "No memory data available" }, void 0, false, {
                    fileName: "app/routes/admin.system.aws.tsx",
                    lineNumber: 297,
                    columnNumber: 27
                  }, this)
                ] }, void 0, true, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 288,
                  columnNumber: 16
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 273,
                columnNumber: 35
              }, this)
            ] }, containerIndex, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 263,
              columnNumber: 24
            }, this);
          }) }, void 0, false, {
            fileName: "app/routes/admin.system.aws.tsx",
            lineNumber: 259,
            columnNumber: 10
          }, this)
        ] }, taskIndex, true, {
          fileName: "app/routes/admin.system.aws.tsx",
          lineNumber: 243,
          columnNumber: 52
        }, this)) }, void 0, false, {
          fileName: "app/routes/admin.system.aws.tsx",
          lineNumber: 242,
          columnNumber: 59
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-gray-500", children: "No running tasks found" }, void 0, false, {
          fileName: "app/routes/admin.system.aws.tsx",
          lineNumber: 304,
          columnNumber: 16
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.system.aws.tsx",
        lineNumber: 240,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-lg bg-gray-100 p-6 shadow md:col-span-2", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h2", { className: "mb-4 text-xl font-semibold", children: "RDS Database Information" }, void 0, false, {
          fileName: "app/routes/admin.system.aws.tsx",
          lineNumber: 309,
          columnNumber: 6
        }, this),
        awsStats.rdsInstance ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-4", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-2", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "DB Instance Identifier:" }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 313,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: awsStats.rdsInstance.DBInstanceIdentifier }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 314,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 312,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Engine:" }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 317,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: [
                awsStats.rdsInstance.Engine,
                " ",
                awsStats.rdsInstance.EngineVersion
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 318,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 316,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Status:" }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 323,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: awsStats.rdsInstance.DBInstanceStatus }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 324,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 322,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Instance Class:" }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 327,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: awsStats.rdsInstance.DBInstanceClass }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 328,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 326,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Storage:" }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 331,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: [
                awsStats.rdsInstance.AllocatedStorage,
                " GB"
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 332,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 330,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Multi-AZ:" }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 335,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: awsStats.rdsInstance.MultiAZ ? "Yes" : "No" }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 336,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 334,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Created:" }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 339,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: formatDate(awsStats.rdsInstance.InstanceCreateTime) }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 340,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 338,
              columnNumber: 9
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.aws.tsx",
            lineNumber: 311,
            columnNumber: 8
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h3", { className: "mb-3 text-lg font-medium", children: "Database Metrics (Last 24 Hours)" }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 346,
              columnNumber: 9
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded border border-gray-200 p-3", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h4", { className: "mb-2 font-medium", children: "CPU Utilization" }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 350,
                  columnNumber: 11
                }, this),
                awsStats.rdsMetrics.cpuUtilization.length > 0 ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-1", children: awsStats.rdsMetrics.cpuUtilization.sort((a, b) => new Date(b.Timestamp).getTime() - new Date(a.Timestamp).getTime()).slice(0, 6).map((point, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between text-sm", children: [
                  /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: new Date(point.Timestamp).toLocaleTimeString() }, void 0, false, {
                    fileName: "app/routes/admin.system.aws.tsx",
                    lineNumber: 353,
                    columnNumber: 16
                  }, this),
                  /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: formatPercentage(point.Average) }, void 0, false, {
                    fileName: "app/routes/admin.system.aws.tsx",
                    lineNumber: 356,
                    columnNumber: 16
                  }, this)
                ] }, index, true, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 352,
                  columnNumber: 165
                }, this)) }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 351,
                  columnNumber: 60
                }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-gray-500", children: "No CPU data available" }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 358,
                  columnNumber: 21
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 349,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded border border-gray-200 p-3", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h4", { className: "mb-2 font-medium", children: "Free Storage Space" }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 363,
                  columnNumber: 11
                }, this),
                awsStats.rdsMetrics.freeStorageSpace.length > 0 ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-1", children: awsStats.rdsMetrics.freeStorageSpace.sort((a, b) => new Date(b.Timestamp).getTime() - new Date(a.Timestamp).getTime()).slice(0, 6).map((point, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between text-sm", children: [
                  /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: new Date(point.Timestamp).toLocaleTimeString() }, void 0, false, {
                    fileName: "app/routes/admin.system.aws.tsx",
                    lineNumber: 366,
                    columnNumber: 16
                  }, this),
                  /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: formatBytes(point.Average) }, void 0, false, {
                    fileName: "app/routes/admin.system.aws.tsx",
                    lineNumber: 369,
                    columnNumber: 16
                  }, this)
                ] }, index, true, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 365,
                  columnNumber: 167
                }, this)) }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 364,
                  columnNumber: 62
                }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-gray-500", children: "No storage data available" }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 371,
                  columnNumber: 21
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 362,
                columnNumber: 10
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded border border-gray-200 p-3 md:col-span-2", children: [
                /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h4", { className: "mb-2 font-medium", children: "Database Connections" }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 376,
                  columnNumber: 11
                }, this),
                awsStats.rdsMetrics.databaseConnections.length > 0 ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-1", children: awsStats.rdsMetrics.databaseConnections.sort((a, b) => new Date(b.Timestamp).getTime() - new Date(a.Timestamp).getTime()).slice(0, 6).map((point, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between text-sm", children: [
                  /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: new Date(point.Timestamp).toLocaleTimeString() }, void 0, false, {
                    fileName: "app/routes/admin.system.aws.tsx",
                    lineNumber: 379,
                    columnNumber: 16
                  }, this),
                  /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: [
                    Math.round(point.Average),
                    " connections"
                  ] }, void 0, true, {
                    fileName: "app/routes/admin.system.aws.tsx",
                    lineNumber: 382,
                    columnNumber: 16
                  }, this)
                ] }, index, true, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 378,
                  columnNumber: 170
                }, this)) }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 377,
                  columnNumber: 65
                }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-gray-500", children: "No connection data available" }, void 0, false, {
                  fileName: "app/routes/admin.system.aws.tsx",
                  lineNumber: 384,
                  columnNumber: 21
                }, this)
              ] }, void 0, true, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 375,
                columnNumber: 10
              }, this)
            ] }, void 0, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 347,
              columnNumber: 9
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.aws.tsx",
            lineNumber: 345,
            columnNumber: 8
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/admin.system.aws.tsx",
          lineNumber: 310,
          columnNumber: 30
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-gray-500", children: "No RDS instance information available" }, void 0, false, {
          fileName: "app/routes/admin.system.aws.tsx",
          lineNumber: 388,
          columnNumber: 16
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.system.aws.tsx",
        lineNumber: 308,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded-lg bg-gray-100 p-6 shadow md:col-span-2", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h2", { className: "mb-4 text-xl font-semibold", children: "CodePipeline Status" }, void 0, false, {
          fileName: "app/routes/admin.system.aws.tsx",
          lineNumber: 393,
          columnNumber: 6
        }, this),
        awsStats.codePipeline && awsStats.codePipeline.length > 0 ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-4", children: awsStats.codePipeline.map((stage, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "rounded border border-gray-200 p-3", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Stage Name:" }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 397,
              columnNumber: 11
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: stage.stageName }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 398,
              columnNumber: 11
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.aws.tsx",
            lineNumber: 396,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Status:" }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 401,
              columnNumber: 11
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: `font-medium ${stage.latestExecution?.status === "Succeeded" ? "text-green-600" : stage.latestExecution?.status === "Failed" ? "text-red-600" : stage.latestExecution?.status === "InProgress" ? "text-blue-600" : ""}`, children: stage.latestExecution?.status || "Unknown" }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 402,
              columnNumber: 11
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.aws.tsx",
            lineNumber: 400,
            columnNumber: 10
          }, this),
          stage.latestExecution?.lastStatusChange && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "font-medium", children: "Last Updated:" }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 407,
              columnNumber: 12
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: formatDate(stage.latestExecution.lastStatusChange) }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 408,
              columnNumber: 12
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.aws.tsx",
            lineNumber: 406,
            columnNumber: 54
          }, this),
          stage.actionStates && stage.actionStates.length > 0 && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-2", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h4", { className: "mb-1 font-medium", children: "Actions:" }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 411,
              columnNumber: 12
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "space-y-1", children: stage.actionStates.map((action, actionIndex) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-between text-sm", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { children: action.actionName }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 414,
                columnNumber: 15
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: `${action.latestExecution?.status === "Succeeded" ? "text-green-600" : action.latestExecution?.status === "Failed" ? "text-red-600" : action.latestExecution?.status === "InProgress" ? "text-blue-600" : ""}`, children: action.latestExecution?.status || "Unknown" }, void 0, false, {
                fileName: "app/routes/admin.system.aws.tsx",
                lineNumber: 415,
                columnNumber: 15
              }, this)
            ] }, actionIndex, true, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 413,
              columnNumber: 62
            }, this)) }, void 0, false, {
              fileName: "app/routes/admin.system.aws.tsx",
              lineNumber: 412,
              columnNumber: 12
            }, this)
          ] }, void 0, true, {
            fileName: "app/routes/admin.system.aws.tsx",
            lineNumber: 410,
            columnNumber: 66
          }, this)
        ] }, index, true, {
          fileName: "app/routes/admin.system.aws.tsx",
          lineNumber: 395,
          columnNumber: 53
        }, this)) }, void 0, false, {
          fileName: "app/routes/admin.system.aws.tsx",
          lineNumber: 394,
          columnNumber: 67
        }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-gray-500", children: "No CodePipeline information available" }, void 0, false, {
          fileName: "app/routes/admin.system.aws.tsx",
          lineNumber: 422,
          columnNumber: 16
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/admin.system.aws.tsx",
        lineNumber: 392,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/admin.system.aws.tsx",
      lineNumber: 67,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/admin.system.aws.tsx",
    lineNumber: 64,
    columnNumber: 10
  }, this);
}
_s(AWSStatsPage, "mNG6CBjAPO0fhbVziK8fnisI310=", false, function() {
  return [useUser, useLoaderData];
});
_c = AWSStatsPage;
var _c;
$RefreshReg$(_c, "AWSStatsPage");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  AWSStatsPage as default
};
//# sourceMappingURL=/build/routes/admin.system.aws-J6XEIVI6.js.map
