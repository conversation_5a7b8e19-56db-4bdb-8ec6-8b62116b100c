import {
  Gun_Violence_Archive_Logo_Reverse_default
} from "/build/_shared/chunk-MPHBVVM4.js";
import {
  moment_default
} from "/build/_shared/chunk-XAWK6254.js";
import "/build/_shared/chunk-NPKMIMFJ.js";
import {
  useLoaderData
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/charts-and-maps.html.$quuid.tsx
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/charts-and-maps.html.$quuid.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/charts-and-maps.html.$quuid.tsx"
  );
}
var links = () => [{
  rel: "stylesheet",
  href: "/styles/framed-map.css"
}];
function ChartsMapsHtml() {
  _s();
  const {
    title,
    image
  } = useLoaderData();
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(import_jsx_dev_runtime.Fragment, { children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "map-frame-above", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "first", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "branding-logo", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Gun_Violence_Archive_Logo_Reverse_default, alt: "Gun Violence Archive" }, void 0, false, {
        fileName: "app/routes/charts-and-maps.html.$quuid.tsx",
        lineNumber: 49,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/charts-and-maps.html.$quuid.tsx",
        lineNumber: 48,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/charts-and-maps.html.$quuid.tsx",
        lineNumber: 47,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "map-frame-right", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("h1", { id: "map-title", children: title }, void 0, false, {
        fileName: "app/routes/charts-and-maps.html.$quuid.tsx",
        lineNumber: 53,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/charts-and-maps.html.$quuid.tsx",
        lineNumber: 52,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/charts-and-maps.html.$quuid.tsx",
      lineNumber: 46,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "map", src: image, alt: "Map" }, void 0, false, {
      fileName: "app/routes/charts-and-maps.html.$quuid.tsx",
      lineNumber: 56,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "map-frame-below", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "date", children: [
        "January 1 - ",
        moment_default().format("MMMM D, YYYY")
      ] }, void 0, true, {
        fileName: "app/routes/charts-and-maps.html.$quuid.tsx",
        lineNumber: 59,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/charts-and-maps.html.$quuid.tsx",
        lineNumber: 58,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "map-frame-right", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("span", { className: "url", children: "gunviolencearchive.org" }, void 0, false, {
        fileName: "app/routes/charts-and-maps.html.$quuid.tsx",
        lineNumber: 62,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/routes/charts-and-maps.html.$quuid.tsx",
        lineNumber: 61,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/charts-and-maps.html.$quuid.tsx",
      lineNumber: 57,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/charts-and-maps.html.$quuid.tsx",
    lineNumber: 45,
    columnNumber: 10
  }, this);
}
_s(ChartsMapsHtml, "NtbHcKEnwx7yneLDCbdvub0OI3w=", false, function() {
  return [useLoaderData];
});
_c = ChartsMapsHtml;
var _c;
$RefreshReg$(_c, "ChartsMapsHtml");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  ChartsMapsHtml as default,
  links
};
//# sourceMappingURL=/build/routes/charts-and-maps.html.$quuid-BCQZUELW.js.map
