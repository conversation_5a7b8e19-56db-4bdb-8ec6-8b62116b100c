import {
  Title
} from "/build/_shared/chunk-YPQMQ54K.js";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>
} from "/build/_shared/chunk-4556QKWU.js";
import "/build/_shared/chunk-W7JLG7II.js";
import "/build/_shared/chunk-OHIZ2QAR.js";
import "/build/_shared/chunk-DFTVPORL.js";
import {
  Link,
  useLoaderData
} from "/build/_shared/chunk-R4KMRLXS.js";
import "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import "/build/_shared/chunk-5GUXQVXG.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/routes/reports.tsx
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/routes/reports.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/routes/reports.tsx"
  );
  import.meta.hot.lastModified = "1741371722336.3608";
}
function Reports() {
  _s();
  const data = useLoaderData();
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "min-h-screen", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Header, {}, void 0, false, {
      fileName: "app/routes/reports.tsx",
      lineNumber: 33,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Title, { title: "Reports" }, void 0, false, {
      fileName: "app/routes/reports.tsx",
      lineNumber: 34,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("main", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto py-10 md:py-20", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-center text-xl font-bold uppercase md:text-2xl", children: "Standard Reports" }, void 0, false, {
        fileName: "app/routes/reports.tsx",
        lineNumber: 37,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-10 flex justify-center", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "w-full max-w-[1000px] text-lg md:columns-2", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("ul", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Children Killed" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 42,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 41,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Children Injured" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 45,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 44,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Teens Killed" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 48,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 47,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Teens Injured" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 51,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 50,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Accidental Deaths" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 54,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 53,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Accidental Injuries" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 57,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 56,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Accidental Deaths (Children Ages 0-11)" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 60,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 59,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Accidental Injuries (Children Ages 0-11)" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 63,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 62,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Accidental Deaths (Teens Ages 12-17)" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 66,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 65,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Accidental Injuries (Teens Ages 12-17)" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 69,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 68,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Officer Involved Shootings" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 72,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 71,
            columnNumber: 9
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/reports.tsx",
          lineNumber: 40,
          columnNumber: 8
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("ul", { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Mass Shootings - All Years" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 77,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 76,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Mass Shootings in 2014" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 80,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 79,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Mass Shootings in 2015" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 83,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 82,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Mass Shootings in 2016" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 86,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 85,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Mass Shootings in 2017" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 89,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 88,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Mass Shootings in 2018" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 92,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 91,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Mass Shootings in 2019" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 95,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 94,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Mass Shootings in 2020" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 98,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 97,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Mass Shootings in 2021" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 101,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 100,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Mass Shootings in 2022" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 104,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 103,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: "#", children: "Mass Shootings in 2023" }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 107,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/routes/reports.tsx",
            lineNumber: 106,
            columnNumber: 9
          }, this)
        ] }, void 0, true, {
          fileName: "app/routes/reports.tsx",
          lineNumber: 75,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/routes/reports.tsx",
        lineNumber: 39,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/routes/reports.tsx",
        lineNumber: 38,
        columnNumber: 6
      }, this)
    ] }, void 0, true, {
      fileName: "app/routes/reports.tsx",
      lineNumber: 36,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/routes/reports.tsx",
      lineNumber: 35,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Footer, {}, void 0, false, {
      fileName: "app/routes/reports.tsx",
      lineNumber: 114,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/routes/reports.tsx",
    lineNumber: 32,
    columnNumber: 10
  }, this);
}
_s(Reports, "5thj+e1edPyRpKif1JmVRC6KArE=", false, function() {
  return [useLoaderData];
});
_c = Reports;
var _c;
$RefreshReg$(_c, "Reports");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;
export {
  Reports as default
};
//# sourceMappingURL=/build/routes/reports-XQSC7AIY.js.map
