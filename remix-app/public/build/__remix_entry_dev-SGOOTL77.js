import "/build/_shared/chunk-73CLBT4D.js";

// node_modules/@remix-run/dev/dist/config/defaults/entry.dev.ts
var entry_dev_default = () => {
  import("/build/_shared/react-I7HMVZNP.js");
  import("/build/_shared/jsx-dev-runtime-FI256N76.js");
  import("/build/_shared/jsx-runtime-NFFGVTXP.js");
  import("/build/_shared/react-dom-ZGLSFBB2.js");
  import("/build/_shared/client-XYWZD2SW.js");
  import("/build/_shared/runtime-NHRKRT5P.js");
  import("/build/_shared/esm-ECBSRYL2.js");
  import("/build/_shared/remix_hmr-QIGHEVJL.js");
};
export {
  entry_dev_default as default
};
//# sourceMappingURL=/build/__remix_entry_dev-SGOOTL77.js.map
