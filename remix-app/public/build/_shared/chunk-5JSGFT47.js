import {
  Pagination_default
} from "/build/_shared/chunk-QGERY6II.js";
import {
  Link,
  useSearchParams
} from "/build/_shared/chunk-R4KMRLXS.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/components/admin/Pagination.tsx
var import_react = __toESM(require_react(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/components/admin/Pagination.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/components/admin/Pagination.tsx"
  );
  import.meta.hot.lastModified = "1736974048092.173";
}
var AdminPagination = ({
  total,
  defaultPageSize
}) => {
  _s();
  const [searchParams] = useSearchParams();
  const [isMounted, setIsMounted] = (0, import_react.useState)(false);
  (0, import_react.useEffect)(() => {
    setIsMounted(true);
  }, []);
  if (!isMounted)
    return null;
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Pagination_default, { className: "pagination", total, defaultPageSize, showTitle: false, itemRender: (current, type, element) => {
    if (type === "prev") {
      return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: {
        search: setSearchParamsString(searchParams, {
          page: current
        })
      }, preventScrollReset: true, children: "< PREV" }, void 0, false, {
        fileName: "app/components/admin/Pagination.tsx",
        lineNumber: 38,
        columnNumber: 14
      }, this);
    }
    if (type === "next") {
      return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: {
        search: setSearchParamsString(searchParams, {
          page: current
        })
      }, preventScrollReset: true, children: "NEXT >" }, void 0, false, {
        fileName: "app/components/admin/Pagination.tsx",
        lineNumber: 47,
        columnNumber: 14
      }, this);
    }
    if (type === "page") {
      return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: {
        search: setSearchParamsString(searchParams, {
          page: current
        })
      }, preventScrollReset: true, children: type === "page" && current }, void 0, false, {
        fileName: "app/components/admin/Pagination.tsx",
        lineNumber: 56,
        columnNumber: 14
      }, this);
    }
    if (type === "jump-prev" || type === "jump-next") {
      return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: {
        search: setSearchParamsString(searchParams, {
          page: current
        })
      }, preventScrollReset: true, children: "..." }, void 0, false, {
        fileName: "app/components/admin/Pagination.tsx",
        lineNumber: 65,
        columnNumber: 14
      }, this);
    }
    return element;
  }, showSizeChanger: false }, void 0, false, {
    fileName: "app/components/admin/Pagination.tsx",
    lineNumber: 36,
    columnNumber: 10
  }, this);
};
_s(AdminPagination, "b2lv8rhyvE3uWBVNKau37L+HBxE=", false, function() {
  return [useSearchParams];
});
_c = AdminPagination;
var setSearchParamsString = (searchParams, changes) => {
  const newSearchParams = new URLSearchParams(searchParams);
  for (const [key, value] of Object.entries(changes)) {
    if (value === void 0) {
      newSearchParams.delete(key);
      continue;
    }
    newSearchParams.set(key, String(value));
  }
  return Array.from(newSearchParams.entries()).map(([key, value]) => value ? `${key}=${encodeURIComponent(value)}` : key).join("&");
};
var _c;
$RefreshReg$(_c, "AdminPagination");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;

export {
  AdminPagination
};
//# sourceMappingURL=/build/_shared/chunk-5JSGFT47.js.map
