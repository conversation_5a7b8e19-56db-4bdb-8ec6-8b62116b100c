import {
  useMatches
} from "/build/_shared/chunk-R4KMRLXS.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/utils/client-utils.ts
var import_react2 = __toESM(require_react(), 1);
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/utils/client-utils.ts"
  );
  import.meta.hot.lastModified = "1750085655624.913";
}
function useMatchesData(id) {
  const matchingRoutes = useMatches();
  const route = (0, import_react2.useMemo)(() => matchingRoutes.find((route2) => route2.id === id), [matchingRoutes, id]);
  return route?.data;
}
function isUser(user) {
  return user != null && typeof user === "object" && "email" in user && typeof user.email === "string";
}
function useOptionalUser() {
  const data = useMatchesData("root");
  if (!data || !isUser(data.user)) {
    return void 0;
  }
  return data.user;
}
function useUser() {
  const maybeUser = useOptionalUser();
  if (!maybeUser) {
    throw new Error(
      "No user found in root loader, but user is required by useUser. If user is optional, try useOptionalUser instead."
    );
  }
  return maybeUser;
}
function ucwords(str) {
  return str.replace(/(^[a-z])|(\s+[a-z])/g, (txt) => txt.toUpperCase());
}
function hasPermission(role) {
  const user = useOptionalUser();
  switch (role.toLowerCase()) {
    case "admin":
      return user?.role?.toLowerCase() === "admin";
    case "editor":
      return user?.role?.toLowerCase() === "admin" || user?.role?.toLowerCase() === "editor";
    case "viewer":
      return user?.role?.toLowerCase() === "admin" || user?.role?.toLowerCase() === "editor" || user?.role?.toLowerCase() === "viewer";
    case "anonymous":
      return true;
    default:
      throw new Error(
        "Invalid User Role Provided."
      );
  }
}

export {
  useMatchesData,
  useOptionalUser,
  useUser,
  ucwords,
  hasPermission
};
//# sourceMappingURL=/build/_shared/chunk-DFTVPORL.js.map
