import {
  esm_default
} from "/build/_shared/chunk-WBY37MGY.js";
import {
  require_prop_types
} from "/build/_shared/chunk-IVTPFYOU.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import {
  __commonJS,
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// node_modules/dompurify/dist/purify.cjs.js
var require_purify_cjs = __commonJS({
  "node_modules/dompurify/dist/purify.cjs.js"(exports, module) {
    "use strict";
    var {
      entries,
      setPrototypeOf,
      isFrozen,
      getPrototypeOf,
      getOwnPropertyDescriptor
    } = Object;
    var {
      freeze,
      seal,
      create
    } = Object;
    var {
      apply,
      construct
    } = typeof Reflect !== "undefined" && Reflect;
    if (!freeze) {
      freeze = function freeze2(x) {
        return x;
      };
    }
    if (!seal) {
      seal = function seal2(x) {
        return x;
      };
    }
    if (!apply) {
      apply = function apply2(fun, thisValue, args) {
        return fun.apply(thisValue, args);
      };
    }
    if (!construct) {
      construct = function construct2(Func, args) {
        return new Func(...args);
      };
    }
    var arrayForEach = unapply(Array.prototype.forEach);
    var arrayLastIndexOf = unapply(Array.prototype.lastIndexOf);
    var arrayPop = unapply(Array.prototype.pop);
    var arrayPush = unapply(Array.prototype.push);
    var arraySplice = unapply(Array.prototype.splice);
    var stringToLowerCase = unapply(String.prototype.toLowerCase);
    var stringToString = unapply(String.prototype.toString);
    var stringMatch = unapply(String.prototype.match);
    var stringReplace = unapply(String.prototype.replace);
    var stringIndexOf = unapply(String.prototype.indexOf);
    var stringTrim = unapply(String.prototype.trim);
    var objectHasOwnProperty = unapply(Object.prototype.hasOwnProperty);
    var regExpTest = unapply(RegExp.prototype.test);
    var typeErrorCreate = unconstruct(TypeError);
    function unapply(func2) {
      return function(thisArg) {
        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
          args[_key - 1] = arguments[_key];
        }
        return apply(func2, thisArg, args);
      };
    }
    function unconstruct(func2) {
      return function() {
        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
          args[_key2] = arguments[_key2];
        }
        return construct(func2, args);
      };
    }
    function addToSet(set, array2) {
      let transformCaseFunc = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : stringToLowerCase;
      if (setPrototypeOf) {
        setPrototypeOf(set, null);
      }
      let l = array2.length;
      while (l--) {
        let element = array2[l];
        if (typeof element === "string") {
          const lcElement = transformCaseFunc(element);
          if (lcElement !== element) {
            if (!isFrozen(array2)) {
              array2[l] = lcElement;
            }
            element = lcElement;
          }
        }
        set[element] = true;
      }
      return set;
    }
    function cleanArray(array2) {
      for (let index = 0; index < array2.length; index++) {
        const isPropertyExist = objectHasOwnProperty(array2, index);
        if (!isPropertyExist) {
          array2[index] = null;
        }
      }
      return array2;
    }
    function clone(object2) {
      const newObject = create(null);
      for (const [property, value] of entries(object2)) {
        const isPropertyExist = objectHasOwnProperty(object2, property);
        if (isPropertyExist) {
          if (Array.isArray(value)) {
            newObject[property] = cleanArray(value);
          } else if (value && typeof value === "object" && value.constructor === Object) {
            newObject[property] = clone(value);
          } else {
            newObject[property] = value;
          }
        }
      }
      return newObject;
    }
    function lookupGetter(object2, prop) {
      while (object2 !== null) {
        const desc = getOwnPropertyDescriptor(object2, prop);
        if (desc) {
          if (desc.get) {
            return unapply(desc.get);
          }
          if (typeof desc.value === "function") {
            return unapply(desc.value);
          }
        }
        object2 = getPrototypeOf(object2);
      }
      function fallbackValue() {
        return null;
      }
      return fallbackValue;
    }
    var html$1 = freeze(["a", "abbr", "acronym", "address", "area", "article", "aside", "audio", "b", "bdi", "bdo", "big", "blink", "blockquote", "body", "br", "button", "canvas", "caption", "center", "cite", "code", "col", "colgroup", "content", "data", "datalist", "dd", "decorator", "del", "details", "dfn", "dialog", "dir", "div", "dl", "dt", "element", "em", "fieldset", "figcaption", "figure", "font", "footer", "form", "h1", "h2", "h3", "h4", "h5", "h6", "head", "header", "hgroup", "hr", "html", "i", "img", "input", "ins", "kbd", "label", "legend", "li", "main", "map", "mark", "marquee", "menu", "menuitem", "meter", "nav", "nobr", "ol", "optgroup", "option", "output", "p", "picture", "pre", "progress", "q", "rp", "rt", "ruby", "s", "samp", "section", "select", "shadow", "small", "source", "spacer", "span", "strike", "strong", "style", "sub", "summary", "sup", "table", "tbody", "td", "template", "textarea", "tfoot", "th", "thead", "time", "tr", "track", "tt", "u", "ul", "var", "video", "wbr"]);
    var svg$1 = freeze(["svg", "a", "altglyph", "altglyphdef", "altglyphitem", "animatecolor", "animatemotion", "animatetransform", "circle", "clippath", "defs", "desc", "ellipse", "filter", "font", "g", "glyph", "glyphref", "hkern", "image", "line", "lineargradient", "marker", "mask", "metadata", "mpath", "path", "pattern", "polygon", "polyline", "radialgradient", "rect", "stop", "style", "switch", "symbol", "text", "textpath", "title", "tref", "tspan", "view", "vkern"]);
    var svgFilters = freeze(["feBlend", "feColorMatrix", "feComponentTransfer", "feComposite", "feConvolveMatrix", "feDiffuseLighting", "feDisplacementMap", "feDistantLight", "feDropShadow", "feFlood", "feFuncA", "feFuncB", "feFuncG", "feFuncR", "feGaussianBlur", "feImage", "feMerge", "feMergeNode", "feMorphology", "feOffset", "fePointLight", "feSpecularLighting", "feSpotLight", "feTile", "feTurbulence"]);
    var svgDisallowed = freeze(["animate", "color-profile", "cursor", "discard", "font-face", "font-face-format", "font-face-name", "font-face-src", "font-face-uri", "foreignobject", "hatch", "hatchpath", "mesh", "meshgradient", "meshpatch", "meshrow", "missing-glyph", "script", "set", "solidcolor", "unknown", "use"]);
    var mathMl$1 = freeze(["math", "menclose", "merror", "mfenced", "mfrac", "mglyph", "mi", "mlabeledtr", "mmultiscripts", "mn", "mo", "mover", "mpadded", "mphantom", "mroot", "mrow", "ms", "mspace", "msqrt", "mstyle", "msub", "msup", "msubsup", "mtable", "mtd", "mtext", "mtr", "munder", "munderover", "mprescripts"]);
    var mathMlDisallowed = freeze(["maction", "maligngroup", "malignmark", "mlongdiv", "mscarries", "mscarry", "msgroup", "mstack", "msline", "msrow", "semantics", "annotation", "annotation-xml", "mprescripts", "none"]);
    var text = freeze(["#text"]);
    var html = freeze(["accept", "action", "align", "alt", "autocapitalize", "autocomplete", "autopictureinpicture", "autoplay", "background", "bgcolor", "border", "capture", "cellpadding", "cellspacing", "checked", "cite", "class", "clear", "color", "cols", "colspan", "controls", "controlslist", "coords", "crossorigin", "datetime", "decoding", "default", "dir", "disabled", "disablepictureinpicture", "disableremoteplayback", "download", "draggable", "enctype", "enterkeyhint", "face", "for", "headers", "height", "hidden", "high", "href", "hreflang", "id", "inputmode", "integrity", "ismap", "kind", "label", "lang", "list", "loading", "loop", "low", "max", "maxlength", "media", "method", "min", "minlength", "multiple", "muted", "name", "nonce", "noshade", "novalidate", "nowrap", "open", "optimum", "pattern", "placeholder", "playsinline", "popover", "popovertarget", "popovertargetaction", "poster", "preload", "pubdate", "radiogroup", "readonly", "rel", "required", "rev", "reversed", "role", "rows", "rowspan", "spellcheck", "scope", "selected", "shape", "size", "sizes", "span", "srclang", "start", "src", "srcset", "step", "style", "summary", "tabindex", "title", "translate", "type", "usemap", "valign", "value", "width", "wrap", "xmlns", "slot"]);
    var svg = freeze(["accent-height", "accumulate", "additive", "alignment-baseline", "amplitude", "ascent", "attributename", "attributetype", "azimuth", "basefrequency", "baseline-shift", "begin", "bias", "by", "class", "clip", "clippathunits", "clip-path", "clip-rule", "color", "color-interpolation", "color-interpolation-filters", "color-profile", "color-rendering", "cx", "cy", "d", "dx", "dy", "diffuseconstant", "direction", "display", "divisor", "dur", "edgemode", "elevation", "end", "exponent", "fill", "fill-opacity", "fill-rule", "filter", "filterunits", "flood-color", "flood-opacity", "font-family", "font-size", "font-size-adjust", "font-stretch", "font-style", "font-variant", "font-weight", "fx", "fy", "g1", "g2", "glyph-name", "glyphref", "gradientunits", "gradienttransform", "height", "href", "id", "image-rendering", "in", "in2", "intercept", "k", "k1", "k2", "k3", "k4", "kerning", "keypoints", "keysplines", "keytimes", "lang", "lengthadjust", "letter-spacing", "kernelmatrix", "kernelunitlength", "lighting-color", "local", "marker-end", "marker-mid", "marker-start", "markerheight", "markerunits", "markerwidth", "maskcontentunits", "maskunits", "max", "mask", "media", "method", "mode", "min", "name", "numoctaves", "offset", "operator", "opacity", "order", "orient", "orientation", "origin", "overflow", "paint-order", "path", "pathlength", "patterncontentunits", "patterntransform", "patternunits", "points", "preservealpha", "preserveaspectratio", "primitiveunits", "r", "rx", "ry", "radius", "refx", "refy", "repeatcount", "repeatdur", "restart", "result", "rotate", "scale", "seed", "shape-rendering", "slope", "specularconstant", "specularexponent", "spreadmethod", "startoffset", "stddeviation", "stitchtiles", "stop-color", "stop-opacity", "stroke-dasharray", "stroke-dashoffset", "stroke-linecap", "stroke-linejoin", "stroke-miterlimit", "stroke-opacity", "stroke", "stroke-width", "style", "surfacescale", "systemlanguage", "tabindex", "tablevalues", "targetx", "targety", "transform", "transform-origin", "text-anchor", "text-decoration", "text-rendering", "textlength", "type", "u1", "u2", "unicode", "values", "viewbox", "visibility", "version", "vert-adv-y", "vert-origin-x", "vert-origin-y", "width", "word-spacing", "wrap", "writing-mode", "xchannelselector", "ychannelselector", "x", "x1", "x2", "xmlns", "y", "y1", "y2", "z", "zoomandpan"]);
    var mathMl = freeze(["accent", "accentunder", "align", "bevelled", "close", "columnsalign", "columnlines", "columnspan", "denomalign", "depth", "dir", "display", "displaystyle", "encoding", "fence", "frame", "height", "href", "id", "largeop", "length", "linethickness", "lspace", "lquote", "mathbackground", "mathcolor", "mathsize", "mathvariant", "maxsize", "minsize", "movablelimits", "notation", "numalign", "open", "rowalign", "rowlines", "rowspacing", "rowspan", "rspace", "rquote", "scriptlevel", "scriptminsize", "scriptsizemultiplier", "selection", "separator", "separators", "stretchy", "subscriptshift", "supscriptshift", "symmetric", "voffset", "width", "xmlns"]);
    var xml = freeze(["xlink:href", "xml:id", "xlink:title", "xml:space", "xmlns:xlink"]);
    var MUSTACHE_EXPR = seal(/\{\{[\w\W]*|[\w\W]*\}\}/gm);
    var ERB_EXPR = seal(/<%[\w\W]*|[\w\W]*%>/gm);
    var TMPLIT_EXPR = seal(/\$\{[\w\W]*/gm);
    var DATA_ATTR = seal(/^data-[\-\w.\u00B7-\uFFFF]+$/);
    var ARIA_ATTR = seal(/^aria-[\-\w]+$/);
    var IS_ALLOWED_URI = seal(
      /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i
      // eslint-disable-line no-useless-escape
    );
    var IS_SCRIPT_OR_DATA = seal(/^(?:\w+script|data):/i);
    var ATTR_WHITESPACE = seal(
      /[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g
      // eslint-disable-line no-control-regex
    );
    var DOCTYPE_NAME = seal(/^html$/i);
    var CUSTOM_ELEMENT = seal(/^[a-z][.\w]*(-[.\w]+)+$/i);
    var EXPRESSIONS = /* @__PURE__ */ Object.freeze({
      __proto__: null,
      ARIA_ATTR,
      ATTR_WHITESPACE,
      CUSTOM_ELEMENT,
      DATA_ATTR,
      DOCTYPE_NAME,
      ERB_EXPR,
      IS_ALLOWED_URI,
      IS_SCRIPT_OR_DATA,
      MUSTACHE_EXPR,
      TMPLIT_EXPR
    });
    var NODE_TYPE = {
      element: 1,
      attribute: 2,
      text: 3,
      cdataSection: 4,
      entityReference: 5,
      // Deprecated
      entityNode: 6,
      // Deprecated
      progressingInstruction: 7,
      comment: 8,
      document: 9,
      documentType: 10,
      documentFragment: 11,
      notation: 12
      // Deprecated
    };
    var getGlobal = function getGlobal2() {
      return typeof window === "undefined" ? null : window;
    };
    var _createTrustedTypesPolicy = function _createTrustedTypesPolicy2(trustedTypes, purifyHostElement) {
      if (typeof trustedTypes !== "object" || typeof trustedTypes.createPolicy !== "function") {
        return null;
      }
      let suffix = null;
      const ATTR_NAME = "data-tt-policy-suffix";
      if (purifyHostElement && purifyHostElement.hasAttribute(ATTR_NAME)) {
        suffix = purifyHostElement.getAttribute(ATTR_NAME);
      }
      const policyName = "dompurify" + (suffix ? "#" + suffix : "");
      try {
        return trustedTypes.createPolicy(policyName, {
          createHTML(html2) {
            return html2;
          },
          createScriptURL(scriptUrl) {
            return scriptUrl;
          }
        });
      } catch (_) {
        console.warn("TrustedTypes policy " + policyName + " could not be created.");
        return null;
      }
    };
    var _createHooksMap = function _createHooksMap2() {
      return {
        afterSanitizeAttributes: [],
        afterSanitizeElements: [],
        afterSanitizeShadowDOM: [],
        beforeSanitizeAttributes: [],
        beforeSanitizeElements: [],
        beforeSanitizeShadowDOM: [],
        uponSanitizeAttribute: [],
        uponSanitizeElement: [],
        uponSanitizeShadowNode: []
      };
    };
    function createDOMPurify() {
      let window2 = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : getGlobal();
      const DOMPurify2 = (root) => createDOMPurify(root);
      DOMPurify2.version = "3.2.4";
      DOMPurify2.removed = [];
      if (!window2 || !window2.document || window2.document.nodeType !== NODE_TYPE.document || !window2.Element) {
        DOMPurify2.isSupported = false;
        return DOMPurify2;
      }
      let {
        document
      } = window2;
      const originalDocument = document;
      const currentScript = originalDocument.currentScript;
      const {
        DocumentFragment,
        HTMLTemplateElement,
        Node: Node2,
        Element,
        NodeFilter,
        NamedNodeMap = window2.NamedNodeMap || window2.MozNamedAttrMap,
        HTMLFormElement,
        DOMParser,
        trustedTypes
      } = window2;
      const ElementPrototype = Element.prototype;
      const cloneNode = lookupGetter(ElementPrototype, "cloneNode");
      const remove = lookupGetter(ElementPrototype, "remove");
      const getNextSibling = lookupGetter(ElementPrototype, "nextSibling");
      const getChildNodes = lookupGetter(ElementPrototype, "childNodes");
      const getParentNode = lookupGetter(ElementPrototype, "parentNode");
      if (typeof HTMLTemplateElement === "function") {
        const template = document.createElement("template");
        if (template.content && template.content.ownerDocument) {
          document = template.content.ownerDocument;
        }
      }
      let trustedTypesPolicy;
      let emptyHTML = "";
      const {
        implementation,
        createNodeIterator,
        createDocumentFragment,
        getElementsByTagName
      } = document;
      const {
        importNode
      } = originalDocument;
      let hooks = _createHooksMap();
      DOMPurify2.isSupported = typeof entries === "function" && typeof getParentNode === "function" && implementation && implementation.createHTMLDocument !== void 0;
      const {
        MUSTACHE_EXPR: MUSTACHE_EXPR2,
        ERB_EXPR: ERB_EXPR2,
        TMPLIT_EXPR: TMPLIT_EXPR2,
        DATA_ATTR: DATA_ATTR2,
        ARIA_ATTR: ARIA_ATTR2,
        IS_SCRIPT_OR_DATA: IS_SCRIPT_OR_DATA2,
        ATTR_WHITESPACE: ATTR_WHITESPACE2,
        CUSTOM_ELEMENT: CUSTOM_ELEMENT2
      } = EXPRESSIONS;
      let {
        IS_ALLOWED_URI: IS_ALLOWED_URI$1
      } = EXPRESSIONS;
      let ALLOWED_TAGS = null;
      const DEFAULT_ALLOWED_TAGS = addToSet({}, [...html$1, ...svg$1, ...svgFilters, ...mathMl$1, ...text]);
      let ALLOWED_ATTR = null;
      const DEFAULT_ALLOWED_ATTR = addToSet({}, [...html, ...svg, ...mathMl, ...xml]);
      let CUSTOM_ELEMENT_HANDLING = Object.seal(create(null, {
        tagNameCheck: {
          writable: true,
          configurable: false,
          enumerable: true,
          value: null
        },
        attributeNameCheck: {
          writable: true,
          configurable: false,
          enumerable: true,
          value: null
        },
        allowCustomizedBuiltInElements: {
          writable: true,
          configurable: false,
          enumerable: true,
          value: false
        }
      }));
      let FORBID_TAGS = null;
      let FORBID_ATTR = null;
      let ALLOW_ARIA_ATTR = true;
      let ALLOW_DATA_ATTR = true;
      let ALLOW_UNKNOWN_PROTOCOLS = false;
      let ALLOW_SELF_CLOSE_IN_ATTR = true;
      let SAFE_FOR_TEMPLATES = false;
      let SAFE_FOR_XML = true;
      let WHOLE_DOCUMENT = false;
      let SET_CONFIG = false;
      let FORCE_BODY = false;
      let RETURN_DOM = false;
      let RETURN_DOM_FRAGMENT = false;
      let RETURN_TRUSTED_TYPE = false;
      let SANITIZE_DOM = true;
      let SANITIZE_NAMED_PROPS = false;
      const SANITIZE_NAMED_PROPS_PREFIX = "user-content-";
      let KEEP_CONTENT = true;
      let IN_PLACE = false;
      let USE_PROFILES = {};
      let FORBID_CONTENTS = null;
      const DEFAULT_FORBID_CONTENTS = addToSet({}, ["annotation-xml", "audio", "colgroup", "desc", "foreignobject", "head", "iframe", "math", "mi", "mn", "mo", "ms", "mtext", "noembed", "noframes", "noscript", "plaintext", "script", "style", "svg", "template", "thead", "title", "video", "xmp"]);
      let DATA_URI_TAGS = null;
      const DEFAULT_DATA_URI_TAGS = addToSet({}, ["audio", "video", "img", "source", "image", "track"]);
      let URI_SAFE_ATTRIBUTES = null;
      const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, ["alt", "class", "for", "id", "label", "name", "pattern", "placeholder", "role", "summary", "title", "value", "style", "xmlns"]);
      const MATHML_NAMESPACE = "http://www.w3.org/1998/Math/MathML";
      const SVG_NAMESPACE = "http://www.w3.org/2000/svg";
      const HTML_NAMESPACE = "http://www.w3.org/1999/xhtml";
      let NAMESPACE = HTML_NAMESPACE;
      let IS_EMPTY_INPUT = false;
      let ALLOWED_NAMESPACES = null;
      const DEFAULT_ALLOWED_NAMESPACES = addToSet({}, [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE], stringToString);
      let MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, ["mi", "mo", "mn", "ms", "mtext"]);
      let HTML_INTEGRATION_POINTS = addToSet({}, ["annotation-xml"]);
      const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, ["title", "style", "font", "a", "script"]);
      let PARSER_MEDIA_TYPE = null;
      const SUPPORTED_PARSER_MEDIA_TYPES = ["application/xhtml+xml", "text/html"];
      const DEFAULT_PARSER_MEDIA_TYPE = "text/html";
      let transformCaseFunc = null;
      let CONFIG = null;
      const formElement = document.createElement("form");
      const isRegexOrFunction = function isRegexOrFunction2(testValue) {
        return testValue instanceof RegExp || testValue instanceof Function;
      };
      const _parseConfig = function _parseConfig2() {
        let cfg = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
        if (CONFIG && CONFIG === cfg) {
          return;
        }
        if (!cfg || typeof cfg !== "object") {
          cfg = {};
        }
        cfg = clone(cfg);
        PARSER_MEDIA_TYPE = // eslint-disable-next-line unicorn/prefer-includes
        SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1 ? DEFAULT_PARSER_MEDIA_TYPE : cfg.PARSER_MEDIA_TYPE;
        transformCaseFunc = PARSER_MEDIA_TYPE === "application/xhtml+xml" ? stringToString : stringToLowerCase;
        ALLOWED_TAGS = objectHasOwnProperty(cfg, "ALLOWED_TAGS") ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc) : DEFAULT_ALLOWED_TAGS;
        ALLOWED_ATTR = objectHasOwnProperty(cfg, "ALLOWED_ATTR") ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc) : DEFAULT_ALLOWED_ATTR;
        ALLOWED_NAMESPACES = objectHasOwnProperty(cfg, "ALLOWED_NAMESPACES") ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString) : DEFAULT_ALLOWED_NAMESPACES;
        URI_SAFE_ATTRIBUTES = objectHasOwnProperty(cfg, "ADD_URI_SAFE_ATTR") ? addToSet(clone(DEFAULT_URI_SAFE_ATTRIBUTES), cfg.ADD_URI_SAFE_ATTR, transformCaseFunc) : DEFAULT_URI_SAFE_ATTRIBUTES;
        DATA_URI_TAGS = objectHasOwnProperty(cfg, "ADD_DATA_URI_TAGS") ? addToSet(clone(DEFAULT_DATA_URI_TAGS), cfg.ADD_DATA_URI_TAGS, transformCaseFunc) : DEFAULT_DATA_URI_TAGS;
        FORBID_CONTENTS = objectHasOwnProperty(cfg, "FORBID_CONTENTS") ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc) : DEFAULT_FORBID_CONTENTS;
        FORBID_TAGS = objectHasOwnProperty(cfg, "FORBID_TAGS") ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc) : {};
        FORBID_ATTR = objectHasOwnProperty(cfg, "FORBID_ATTR") ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc) : {};
        USE_PROFILES = objectHasOwnProperty(cfg, "USE_PROFILES") ? cfg.USE_PROFILES : false;
        ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false;
        ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false;
        ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false;
        ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false;
        SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false;
        SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false;
        WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false;
        RETURN_DOM = cfg.RETURN_DOM || false;
        RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false;
        RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false;
        FORCE_BODY = cfg.FORCE_BODY || false;
        SANITIZE_DOM = cfg.SANITIZE_DOM !== false;
        SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false;
        KEEP_CONTENT = cfg.KEEP_CONTENT !== false;
        IN_PLACE = cfg.IN_PLACE || false;
        IS_ALLOWED_URI$1 = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI;
        NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;
        MATHML_TEXT_INTEGRATION_POINTS = cfg.MATHML_TEXT_INTEGRATION_POINTS || MATHML_TEXT_INTEGRATION_POINTS;
        HTML_INTEGRATION_POINTS = cfg.HTML_INTEGRATION_POINTS || HTML_INTEGRATION_POINTS;
        CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};
        if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)) {
          CUSTOM_ELEMENT_HANDLING.tagNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;
        }
        if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)) {
          CUSTOM_ELEMENT_HANDLING.attributeNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;
        }
        if (cfg.CUSTOM_ELEMENT_HANDLING && typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements === "boolean") {
          CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements = cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;
        }
        if (SAFE_FOR_TEMPLATES) {
          ALLOW_DATA_ATTR = false;
        }
        if (RETURN_DOM_FRAGMENT) {
          RETURN_DOM = true;
        }
        if (USE_PROFILES) {
          ALLOWED_TAGS = addToSet({}, text);
          ALLOWED_ATTR = [];
          if (USE_PROFILES.html === true) {
            addToSet(ALLOWED_TAGS, html$1);
            addToSet(ALLOWED_ATTR, html);
          }
          if (USE_PROFILES.svg === true) {
            addToSet(ALLOWED_TAGS, svg$1);
            addToSet(ALLOWED_ATTR, svg);
            addToSet(ALLOWED_ATTR, xml);
          }
          if (USE_PROFILES.svgFilters === true) {
            addToSet(ALLOWED_TAGS, svgFilters);
            addToSet(ALLOWED_ATTR, svg);
            addToSet(ALLOWED_ATTR, xml);
          }
          if (USE_PROFILES.mathMl === true) {
            addToSet(ALLOWED_TAGS, mathMl$1);
            addToSet(ALLOWED_ATTR, mathMl);
            addToSet(ALLOWED_ATTR, xml);
          }
        }
        if (cfg.ADD_TAGS) {
          if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {
            ALLOWED_TAGS = clone(ALLOWED_TAGS);
          }
          addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);
        }
        if (cfg.ADD_ATTR) {
          if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {
            ALLOWED_ATTR = clone(ALLOWED_ATTR);
          }
          addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);
        }
        if (cfg.ADD_URI_SAFE_ATTR) {
          addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);
        }
        if (cfg.FORBID_CONTENTS) {
          if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {
            FORBID_CONTENTS = clone(FORBID_CONTENTS);
          }
          addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);
        }
        if (KEEP_CONTENT) {
          ALLOWED_TAGS["#text"] = true;
        }
        if (WHOLE_DOCUMENT) {
          addToSet(ALLOWED_TAGS, ["html", "head", "body"]);
        }
        if (ALLOWED_TAGS.table) {
          addToSet(ALLOWED_TAGS, ["tbody"]);
          delete FORBID_TAGS.tbody;
        }
        if (cfg.TRUSTED_TYPES_POLICY) {
          if (typeof cfg.TRUSTED_TYPES_POLICY.createHTML !== "function") {
            throw typeErrorCreate('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');
          }
          if (typeof cfg.TRUSTED_TYPES_POLICY.createScriptURL !== "function") {
            throw typeErrorCreate('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');
          }
          trustedTypesPolicy = cfg.TRUSTED_TYPES_POLICY;
          emptyHTML = trustedTypesPolicy.createHTML("");
        } else {
          if (trustedTypesPolicy === void 0) {
            trustedTypesPolicy = _createTrustedTypesPolicy(trustedTypes, currentScript);
          }
          if (trustedTypesPolicy !== null && typeof emptyHTML === "string") {
            emptyHTML = trustedTypesPolicy.createHTML("");
          }
        }
        if (freeze) {
          freeze(cfg);
        }
        CONFIG = cfg;
      };
      const ALL_SVG_TAGS = addToSet({}, [...svg$1, ...svgFilters, ...svgDisallowed]);
      const ALL_MATHML_TAGS = addToSet({}, [...mathMl$1, ...mathMlDisallowed]);
      const _checkValidNamespace = function _checkValidNamespace2(element) {
        let parent = getParentNode(element);
        if (!parent || !parent.tagName) {
          parent = {
            namespaceURI: NAMESPACE,
            tagName: "template"
          };
        }
        const tagName = stringToLowerCase(element.tagName);
        const parentTagName = stringToLowerCase(parent.tagName);
        if (!ALLOWED_NAMESPACES[element.namespaceURI]) {
          return false;
        }
        if (element.namespaceURI === SVG_NAMESPACE) {
          if (parent.namespaceURI === HTML_NAMESPACE) {
            return tagName === "svg";
          }
          if (parent.namespaceURI === MATHML_NAMESPACE) {
            return tagName === "svg" && (parentTagName === "annotation-xml" || MATHML_TEXT_INTEGRATION_POINTS[parentTagName]);
          }
          return Boolean(ALL_SVG_TAGS[tagName]);
        }
        if (element.namespaceURI === MATHML_NAMESPACE) {
          if (parent.namespaceURI === HTML_NAMESPACE) {
            return tagName === "math";
          }
          if (parent.namespaceURI === SVG_NAMESPACE) {
            return tagName === "math" && HTML_INTEGRATION_POINTS[parentTagName];
          }
          return Boolean(ALL_MATHML_TAGS[tagName]);
        }
        if (element.namespaceURI === HTML_NAMESPACE) {
          if (parent.namespaceURI === SVG_NAMESPACE && !HTML_INTEGRATION_POINTS[parentTagName]) {
            return false;
          }
          if (parent.namespaceURI === MATHML_NAMESPACE && !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]) {
            return false;
          }
          return !ALL_MATHML_TAGS[tagName] && (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName]);
        }
        if (PARSER_MEDIA_TYPE === "application/xhtml+xml" && ALLOWED_NAMESPACES[element.namespaceURI]) {
          return true;
        }
        return false;
      };
      const _forceRemove = function _forceRemove2(node) {
        arrayPush(DOMPurify2.removed, {
          element: node
        });
        try {
          getParentNode(node).removeChild(node);
        } catch (_) {
          remove(node);
        }
      };
      const _removeAttribute = function _removeAttribute2(name, element) {
        try {
          arrayPush(DOMPurify2.removed, {
            attribute: element.getAttributeNode(name),
            from: element
          });
        } catch (_) {
          arrayPush(DOMPurify2.removed, {
            attribute: null,
            from: element
          });
        }
        element.removeAttribute(name);
        if (name === "is") {
          if (RETURN_DOM || RETURN_DOM_FRAGMENT) {
            try {
              _forceRemove(element);
            } catch (_) {
            }
          } else {
            try {
              element.setAttribute(name, "");
            } catch (_) {
            }
          }
        }
      };
      const _initDocument = function _initDocument2(dirty) {
        let doc = null;
        let leadingWhitespace = null;
        if (FORCE_BODY) {
          dirty = "<remove></remove>" + dirty;
        } else {
          const matches = stringMatch(dirty, /^[\r\n\t ]+/);
          leadingWhitespace = matches && matches[0];
        }
        if (PARSER_MEDIA_TYPE === "application/xhtml+xml" && NAMESPACE === HTML_NAMESPACE) {
          dirty = '<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>' + dirty + "</body></html>";
        }
        const dirtyPayload = trustedTypesPolicy ? trustedTypesPolicy.createHTML(dirty) : dirty;
        if (NAMESPACE === HTML_NAMESPACE) {
          try {
            doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);
          } catch (_) {
          }
        }
        if (!doc || !doc.documentElement) {
          doc = implementation.createDocument(NAMESPACE, "template", null);
          try {
            doc.documentElement.innerHTML = IS_EMPTY_INPUT ? emptyHTML : dirtyPayload;
          } catch (_) {
          }
        }
        const body = doc.body || doc.documentElement;
        if (dirty && leadingWhitespace) {
          body.insertBefore(document.createTextNode(leadingWhitespace), body.childNodes[0] || null);
        }
        if (NAMESPACE === HTML_NAMESPACE) {
          return getElementsByTagName.call(doc, WHOLE_DOCUMENT ? "html" : "body")[0];
        }
        return WHOLE_DOCUMENT ? doc.documentElement : body;
      };
      const _createNodeIterator = function _createNodeIterator2(root) {
        return createNodeIterator.call(
          root.ownerDocument || root,
          root,
          // eslint-disable-next-line no-bitwise
          NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_COMMENT | NodeFilter.SHOW_TEXT | NodeFilter.SHOW_PROCESSING_INSTRUCTION | NodeFilter.SHOW_CDATA_SECTION,
          null
        );
      };
      const _isClobbered = function _isClobbered2(element) {
        return element instanceof HTMLFormElement && (typeof element.nodeName !== "string" || typeof element.textContent !== "string" || typeof element.removeChild !== "function" || !(element.attributes instanceof NamedNodeMap) || typeof element.removeAttribute !== "function" || typeof element.setAttribute !== "function" || typeof element.namespaceURI !== "string" || typeof element.insertBefore !== "function" || typeof element.hasChildNodes !== "function");
      };
      const _isNode = function _isNode2(value) {
        return typeof Node2 === "function" && value instanceof Node2;
      };
      function _executeHooks(hooks2, currentNode, data) {
        arrayForEach(hooks2, (hook) => {
          hook.call(DOMPurify2, currentNode, data, CONFIG);
        });
      }
      const _sanitizeElements = function _sanitizeElements2(currentNode) {
        let content = null;
        _executeHooks(hooks.beforeSanitizeElements, currentNode, null);
        if (_isClobbered(currentNode)) {
          _forceRemove(currentNode);
          return true;
        }
        const tagName = transformCaseFunc(currentNode.nodeName);
        _executeHooks(hooks.uponSanitizeElement, currentNode, {
          tagName,
          allowedTags: ALLOWED_TAGS
        });
        if (currentNode.hasChildNodes() && !_isNode(currentNode.firstElementChild) && regExpTest(/<[/\w]/g, currentNode.innerHTML) && regExpTest(/<[/\w]/g, currentNode.textContent)) {
          _forceRemove(currentNode);
          return true;
        }
        if (currentNode.nodeType === NODE_TYPE.progressingInstruction) {
          _forceRemove(currentNode);
          return true;
        }
        if (SAFE_FOR_XML && currentNode.nodeType === NODE_TYPE.comment && regExpTest(/<[/\w]/g, currentNode.data)) {
          _forceRemove(currentNode);
          return true;
        }
        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {
          if (!FORBID_TAGS[tagName] && _isBasicCustomElement(tagName)) {
            if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)) {
              return false;
            }
            if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)) {
              return false;
            }
          }
          if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {
            const parentNode = getParentNode(currentNode) || currentNode.parentNode;
            const childNodes = getChildNodes(currentNode) || currentNode.childNodes;
            if (childNodes && parentNode) {
              const childCount = childNodes.length;
              for (let i = childCount - 1; i >= 0; --i) {
                const childClone = cloneNode(childNodes[i], true);
                childClone.__removalCount = (currentNode.__removalCount || 0) + 1;
                parentNode.insertBefore(childClone, getNextSibling(currentNode));
              }
            }
          }
          _forceRemove(currentNode);
          return true;
        }
        if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {
          _forceRemove(currentNode);
          return true;
        }
        if ((tagName === "noscript" || tagName === "noembed" || tagName === "noframes") && regExpTest(/<\/no(script|embed|frames)/i, currentNode.innerHTML)) {
          _forceRemove(currentNode);
          return true;
        }
        if (SAFE_FOR_TEMPLATES && currentNode.nodeType === NODE_TYPE.text) {
          content = currentNode.textContent;
          arrayForEach([MUSTACHE_EXPR2, ERB_EXPR2, TMPLIT_EXPR2], (expr) => {
            content = stringReplace(content, expr, " ");
          });
          if (currentNode.textContent !== content) {
            arrayPush(DOMPurify2.removed, {
              element: currentNode.cloneNode()
            });
            currentNode.textContent = content;
          }
        }
        _executeHooks(hooks.afterSanitizeElements, currentNode, null);
        return false;
      };
      const _isValidAttribute = function _isValidAttribute2(lcTag, lcName, value) {
        if (SANITIZE_DOM && (lcName === "id" || lcName === "name") && (value in document || value in formElement)) {
          return false;
        }
        if (ALLOW_DATA_ATTR && !FORBID_ATTR[lcName] && regExpTest(DATA_ATTR2, lcName))
          ;
        else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR2, lcName))
          ;
        else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {
          if (
            // First condition does a very basic check if a) it's basically a valid custom element tagname AND
            // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck
            // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck
            _isBasicCustomElement(lcTag) && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag)) && (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName) || CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)) || // Alternative, second condition checks if it's an `is`-attribute, AND
            // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck
            lcName === "is" && CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))
          )
            ;
          else {
            return false;
          }
        } else if (URI_SAFE_ATTRIBUTES[lcName])
          ;
        else if (regExpTest(IS_ALLOWED_URI$1, stringReplace(value, ATTR_WHITESPACE2, "")))
          ;
        else if ((lcName === "src" || lcName === "xlink:href" || lcName === "href") && lcTag !== "script" && stringIndexOf(value, "data:") === 0 && DATA_URI_TAGS[lcTag])
          ;
        else if (ALLOW_UNKNOWN_PROTOCOLS && !regExpTest(IS_SCRIPT_OR_DATA2, stringReplace(value, ATTR_WHITESPACE2, "")))
          ;
        else if (value) {
          return false;
        } else
          ;
        return true;
      };
      const _isBasicCustomElement = function _isBasicCustomElement2(tagName) {
        return tagName !== "annotation-xml" && stringMatch(tagName, CUSTOM_ELEMENT2);
      };
      const _sanitizeAttributes = function _sanitizeAttributes2(currentNode) {
        _executeHooks(hooks.beforeSanitizeAttributes, currentNode, null);
        const {
          attributes
        } = currentNode;
        if (!attributes || _isClobbered(currentNode)) {
          return;
        }
        const hookEvent = {
          attrName: "",
          attrValue: "",
          keepAttr: true,
          allowedAttributes: ALLOWED_ATTR,
          forceKeepAttr: void 0
        };
        let l = attributes.length;
        while (l--) {
          const attr = attributes[l];
          const {
            name,
            namespaceURI,
            value: attrValue
          } = attr;
          const lcName = transformCaseFunc(name);
          let value = name === "value" ? attrValue : stringTrim(attrValue);
          hookEvent.attrName = lcName;
          hookEvent.attrValue = value;
          hookEvent.keepAttr = true;
          hookEvent.forceKeepAttr = void 0;
          _executeHooks(hooks.uponSanitizeAttribute, currentNode, hookEvent);
          value = hookEvent.attrValue;
          if (SANITIZE_NAMED_PROPS && (lcName === "id" || lcName === "name")) {
            _removeAttribute(name, currentNode);
            value = SANITIZE_NAMED_PROPS_PREFIX + value;
          }
          if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\/(style|title)/i, value)) {
            _removeAttribute(name, currentNode);
            continue;
          }
          if (hookEvent.forceKeepAttr) {
            continue;
          }
          _removeAttribute(name, currentNode);
          if (!hookEvent.keepAttr) {
            continue;
          }
          if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\/>/i, value)) {
            _removeAttribute(name, currentNode);
            continue;
          }
          if (SAFE_FOR_TEMPLATES) {
            arrayForEach([MUSTACHE_EXPR2, ERB_EXPR2, TMPLIT_EXPR2], (expr) => {
              value = stringReplace(value, expr, " ");
            });
          }
          const lcTag = transformCaseFunc(currentNode.nodeName);
          if (!_isValidAttribute(lcTag, lcName, value)) {
            continue;
          }
          if (trustedTypesPolicy && typeof trustedTypes === "object" && typeof trustedTypes.getAttributeType === "function") {
            if (namespaceURI)
              ;
            else {
              switch (trustedTypes.getAttributeType(lcTag, lcName)) {
                case "TrustedHTML": {
                  value = trustedTypesPolicy.createHTML(value);
                  break;
                }
                case "TrustedScriptURL": {
                  value = trustedTypesPolicy.createScriptURL(value);
                  break;
                }
              }
            }
          }
          try {
            if (namespaceURI) {
              currentNode.setAttributeNS(namespaceURI, name, value);
            } else {
              currentNode.setAttribute(name, value);
            }
            if (_isClobbered(currentNode)) {
              _forceRemove(currentNode);
            } else {
              arrayPop(DOMPurify2.removed);
            }
          } catch (_) {
          }
        }
        _executeHooks(hooks.afterSanitizeAttributes, currentNode, null);
      };
      const _sanitizeShadowDOM = function _sanitizeShadowDOM2(fragment) {
        let shadowNode = null;
        const shadowIterator = _createNodeIterator(fragment);
        _executeHooks(hooks.beforeSanitizeShadowDOM, fragment, null);
        while (shadowNode = shadowIterator.nextNode()) {
          _executeHooks(hooks.uponSanitizeShadowNode, shadowNode, null);
          _sanitizeElements(shadowNode);
          _sanitizeAttributes(shadowNode);
          if (shadowNode.content instanceof DocumentFragment) {
            _sanitizeShadowDOM2(shadowNode.content);
          }
        }
        _executeHooks(hooks.afterSanitizeShadowDOM, fragment, null);
      };
      DOMPurify2.sanitize = function(dirty) {
        let cfg = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
        let body = null;
        let importedNode = null;
        let currentNode = null;
        let returnNode = null;
        IS_EMPTY_INPUT = !dirty;
        if (IS_EMPTY_INPUT) {
          dirty = "<!-->";
        }
        if (typeof dirty !== "string" && !_isNode(dirty)) {
          if (typeof dirty.toString === "function") {
            dirty = dirty.toString();
            if (typeof dirty !== "string") {
              throw typeErrorCreate("dirty is not a string, aborting");
            }
          } else {
            throw typeErrorCreate("toString is not a function");
          }
        }
        if (!DOMPurify2.isSupported) {
          return dirty;
        }
        if (!SET_CONFIG) {
          _parseConfig(cfg);
        }
        DOMPurify2.removed = [];
        if (typeof dirty === "string") {
          IN_PLACE = false;
        }
        if (IN_PLACE) {
          if (dirty.nodeName) {
            const tagName = transformCaseFunc(dirty.nodeName);
            if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {
              throw typeErrorCreate("root node is forbidden and cannot be sanitized in-place");
            }
          }
        } else if (dirty instanceof Node2) {
          body = _initDocument("<!---->");
          importedNode = body.ownerDocument.importNode(dirty, true);
          if (importedNode.nodeType === NODE_TYPE.element && importedNode.nodeName === "BODY") {
            body = importedNode;
          } else if (importedNode.nodeName === "HTML") {
            body = importedNode;
          } else {
            body.appendChild(importedNode);
          }
        } else {
          if (!RETURN_DOM && !SAFE_FOR_TEMPLATES && !WHOLE_DOCUMENT && // eslint-disable-next-line unicorn/prefer-includes
          dirty.indexOf("<") === -1) {
            return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(dirty) : dirty;
          }
          body = _initDocument(dirty);
          if (!body) {
            return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : "";
          }
        }
        if (body && FORCE_BODY) {
          _forceRemove(body.firstChild);
        }
        const nodeIterator = _createNodeIterator(IN_PLACE ? dirty : body);
        while (currentNode = nodeIterator.nextNode()) {
          _sanitizeElements(currentNode);
          _sanitizeAttributes(currentNode);
          if (currentNode.content instanceof DocumentFragment) {
            _sanitizeShadowDOM(currentNode.content);
          }
        }
        if (IN_PLACE) {
          return dirty;
        }
        if (RETURN_DOM) {
          if (RETURN_DOM_FRAGMENT) {
            returnNode = createDocumentFragment.call(body.ownerDocument);
            while (body.firstChild) {
              returnNode.appendChild(body.firstChild);
            }
          } else {
            returnNode = body;
          }
          if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmode) {
            returnNode = importNode.call(originalDocument, returnNode, true);
          }
          return returnNode;
        }
        let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;
        if (WHOLE_DOCUMENT && ALLOWED_TAGS["!doctype"] && body.ownerDocument && body.ownerDocument.doctype && body.ownerDocument.doctype.name && regExpTest(DOCTYPE_NAME, body.ownerDocument.doctype.name)) {
          serializedHTML = "<!DOCTYPE " + body.ownerDocument.doctype.name + ">\n" + serializedHTML;
        }
        if (SAFE_FOR_TEMPLATES) {
          arrayForEach([MUSTACHE_EXPR2, ERB_EXPR2, TMPLIT_EXPR2], (expr) => {
            serializedHTML = stringReplace(serializedHTML, expr, " ");
          });
        }
        return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(serializedHTML) : serializedHTML;
      };
      DOMPurify2.setConfig = function() {
        let cfg = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
        _parseConfig(cfg);
        SET_CONFIG = true;
      };
      DOMPurify2.clearConfig = function() {
        CONFIG = null;
        SET_CONFIG = false;
      };
      DOMPurify2.isValidAttribute = function(tag, attr, value) {
        if (!CONFIG) {
          _parseConfig({});
        }
        const lcTag = transformCaseFunc(tag);
        const lcName = transformCaseFunc(attr);
        return _isValidAttribute(lcTag, lcName, value);
      };
      DOMPurify2.addHook = function(entryPoint, hookFunction) {
        if (typeof hookFunction !== "function") {
          return;
        }
        arrayPush(hooks[entryPoint], hookFunction);
      };
      DOMPurify2.removeHook = function(entryPoint, hookFunction) {
        if (hookFunction !== void 0) {
          const index = arrayLastIndexOf(hooks[entryPoint], hookFunction);
          return index === -1 ? void 0 : arraySplice(hooks[entryPoint], index, 1)[0];
        }
        return arrayPop(hooks[entryPoint]);
      };
      DOMPurify2.removeHooks = function(entryPoint) {
        hooks[entryPoint] = [];
      };
      DOMPurify2.removeAllHooks = function() {
        hooks = _createHooksMap();
      };
      return DOMPurify2;
    }
    var purify = createDOMPurify();
    module.exports = purify;
  }
});

// node_modules/isomorphic-dompurify/browser.js
var require_browser = __commonJS({
  "node_modules/isomorphic-dompurify/browser.js"(exports, module) {
    module.exports = window.DOMPurify || (window.DOMPurify = require_purify_cjs().default || require_purify_cjs());
  }
});

// app/components/common/TinyMCE.tsx
var import_isomorphic_dompurify = __toESM(require_browser(), 1);
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/components/common/TinyMCE.tsx"
  );
  import.meta.hot.lastModified = "1748453408015.2866";
}
var tinyConfig = {
  inline: true,
  image_title: true,
  menubar: false,
  plugins: [
    "preview",
    "autolink",
    "code",
    "visualblocks",
    "visualchars",
    "image",
    "link",
    "media",
    "charmap",
    "pagebreak",
    "nonbreaking",
    "anchor",
    "lists",
    "wordcount",
    "help"
  ],
  toolbar: "undo redo | aidialog aishortcuts | blocks fontsizeinput | bold italic lineheight align numlist bullist | link image | strikethrough forecolor backcolor | code | charmap preview",
  images_upload_handler
};
function htmlFrom(htmlString) {
  const cleanHtmlString = import_isomorphic_dompurify.default.sanitize(htmlString, { USE_PROFILES: { html: true } });
  return esm_default(cleanHtmlString);
}
async function images_upload_handler(blobInfo, progress) {
  const formData = new FormData();
  formData.append("file", blobInfo.blob(), blobInfo.filename());
  const uploadedFile = await fetch("/admin/uploadfile", {
    method: "POST",
    body: formData
  });
  const file = await uploadedFile.json();
  return file.uri;
}

// node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/components/Editor.js
var React = __toESM(require_react());

// node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/components/EditorPropTypes.js
var PropTypes = __toESM(require_prop_types());
var __assign = function() {
  __assign = Object.assign || function(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s)
        if (Object.prototype.hasOwnProperty.call(s, p))
          t[p] = s[p];
    }
    return t;
  };
  return __assign.apply(this, arguments);
};
var eventPropTypes = {
  onActivate: PropTypes.func,
  onAddUndo: PropTypes.func,
  onBeforeAddUndo: PropTypes.func,
  onBeforeExecCommand: PropTypes.func,
  onBeforeGetContent: PropTypes.func,
  onBeforeRenderUI: PropTypes.func,
  onBeforeSetContent: PropTypes.func,
  onBeforePaste: PropTypes.func,
  onBlur: PropTypes.func,
  onChange: PropTypes.func,
  onClearUndos: PropTypes.func,
  onClick: PropTypes.func,
  onContextMenu: PropTypes.func,
  onCommentChange: PropTypes.func,
  onCompositionEnd: PropTypes.func,
  onCompositionStart: PropTypes.func,
  onCompositionUpdate: PropTypes.func,
  onCopy: PropTypes.func,
  onCut: PropTypes.func,
  onDblclick: PropTypes.func,
  onDeactivate: PropTypes.func,
  onDirty: PropTypes.func,
  onDrag: PropTypes.func,
  onDragDrop: PropTypes.func,
  onDragEnd: PropTypes.func,
  onDragGesture: PropTypes.func,
  onDragOver: PropTypes.func,
  onDrop: PropTypes.func,
  onExecCommand: PropTypes.func,
  onFocus: PropTypes.func,
  onFocusIn: PropTypes.func,
  onFocusOut: PropTypes.func,
  onGetContent: PropTypes.func,
  onHide: PropTypes.func,
  onInit: PropTypes.func,
  onInput: PropTypes.func,
  onKeyDown: PropTypes.func,
  onKeyPress: PropTypes.func,
  onKeyUp: PropTypes.func,
  onLoadContent: PropTypes.func,
  onMouseDown: PropTypes.func,
  onMouseEnter: PropTypes.func,
  onMouseLeave: PropTypes.func,
  onMouseMove: PropTypes.func,
  onMouseOut: PropTypes.func,
  onMouseOver: PropTypes.func,
  onMouseUp: PropTypes.func,
  onNodeChange: PropTypes.func,
  onObjectResizeStart: PropTypes.func,
  onObjectResized: PropTypes.func,
  onObjectSelected: PropTypes.func,
  onPaste: PropTypes.func,
  onPostProcess: PropTypes.func,
  onPostRender: PropTypes.func,
  onPreProcess: PropTypes.func,
  onProgressState: PropTypes.func,
  onRedo: PropTypes.func,
  onRemove: PropTypes.func,
  onReset: PropTypes.func,
  onSaveContent: PropTypes.func,
  onSelectionChange: PropTypes.func,
  onSetAttrib: PropTypes.func,
  onSetContent: PropTypes.func,
  onShow: PropTypes.func,
  onSubmit: PropTypes.func,
  onUndo: PropTypes.func,
  onVisualAid: PropTypes.func,
  onSkinLoadError: PropTypes.func,
  onThemeLoadError: PropTypes.func,
  onModelLoadError: PropTypes.func,
  onPluginLoadError: PropTypes.func,
  onIconsLoadError: PropTypes.func,
  onLanguageLoadError: PropTypes.func,
  onScriptsLoad: PropTypes.func,
  onScriptsLoadError: PropTypes.func
};
var EditorPropTypes = __assign({ apiKey: PropTypes.string, licenseKey: PropTypes.string, id: PropTypes.string, inline: PropTypes.bool, init: PropTypes.object, initialValue: PropTypes.string, onEditorChange: PropTypes.func, value: PropTypes.string, tagName: PropTypes.string, tabIndex: PropTypes.number, cloudChannel: PropTypes.string, plugins: PropTypes.oneOfType([PropTypes.string, PropTypes.array]), toolbar: PropTypes.oneOfType([PropTypes.string, PropTypes.array]), disabled: PropTypes.bool, textareaName: PropTypes.string, tinymceScriptSrc: PropTypes.oneOfType([
  PropTypes.string,
  PropTypes.arrayOf(PropTypes.string),
  PropTypes.arrayOf(PropTypes.shape({
    src: PropTypes.string,
    async: PropTypes.bool,
    defer: PropTypes.bool
  }))
]), rollback: PropTypes.oneOfType([PropTypes.number, PropTypes.oneOf([false])]), scriptLoading: PropTypes.shape({
  async: PropTypes.bool,
  defer: PropTypes.bool,
  delay: PropTypes.number
}) }, eventPropTypes);

// node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/Utils.js
var isFunction = function(x) {
  return typeof x === "function";
};
var isEventProp = function(name) {
  return name in eventPropTypes;
};
var eventAttrToEventName = function(attrName) {
  return attrName.substr(2);
};
var configHandlers2 = function(handlerLookup, on, off, adapter, prevProps, props, boundHandlers) {
  var prevEventKeys = Object.keys(prevProps).filter(isEventProp);
  var currEventKeys = Object.keys(props).filter(isEventProp);
  var removedKeys = prevEventKeys.filter(function(key) {
    return props[key] === void 0;
  });
  var addedKeys = currEventKeys.filter(function(key) {
    return prevProps[key] === void 0;
  });
  removedKeys.forEach(function(key) {
    var eventName = eventAttrToEventName(key);
    var wrappedHandler = boundHandlers[eventName];
    off(eventName, wrappedHandler);
    delete boundHandlers[eventName];
  });
  addedKeys.forEach(function(key) {
    var wrappedHandler = adapter(handlerLookup, key);
    var eventName = eventAttrToEventName(key);
    boundHandlers[eventName] = wrappedHandler;
    on(eventName, wrappedHandler);
  });
};
var configHandlers = function(editor, prevProps, props, boundHandlers, lookup) {
  return configHandlers2(
    lookup,
    editor.on.bind(editor),
    editor.off.bind(editor),
    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
    function(handlerLookup, key) {
      return function(e) {
        var _a;
        return (_a = handlerLookup(key)) === null || _a === void 0 ? void 0 : _a(e, editor);
      };
    },
    prevProps,
    props,
    boundHandlers
  );
};
var unique = 0;
var uuid = function(prefix) {
  var time = Date.now();
  var random = Math.floor(Math.random() * 1e9);
  unique++;
  return prefix + "_" + random + unique + String(time);
};
var isTextareaOrInput = function(element) {
  return element !== null && (element.tagName.toLowerCase() === "textarea" || element.tagName.toLowerCase() === "input");
};
var normalizePluginArray = function(plugins) {
  if (typeof plugins === "undefined" || plugins === "") {
    return [];
  }
  return Array.isArray(plugins) ? plugins : plugins.split(" ");
};
var mergePlugins = function(initPlugins, inputPlugins) {
  return normalizePluginArray(initPlugins).concat(normalizePluginArray(inputPlugins));
};
var isBeforeInputEventAvailable = function() {
  return window.InputEvent && typeof InputEvent.prototype.getTargetRanges === "function";
};
var isInDoc = function(elem) {
  if (!("isConnected" in Node.prototype)) {
    var current = elem;
    var parent_1 = elem.parentNode;
    while (parent_1 != null) {
      current = parent_1;
      parent_1 = current.parentNode;
    }
    return current === elem.ownerDocument;
  }
  return elem.isConnected;
};
var setMode = function(editor, mode) {
  if (editor !== void 0) {
    if (editor.mode != null && typeof editor.mode === "object" && typeof editor.mode.set === "function") {
      editor.mode.set(mode);
    } else {
      editor.setMode(mode);
    }
  }
};

// node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/ScriptLoader2.js
var __assign2 = function() {
  __assign2 = Object.assign || function(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s)
        if (Object.prototype.hasOwnProperty.call(s, p))
          t[p] = s[p];
    }
    return t;
  };
  return __assign2.apply(this, arguments);
};
var injectScriptTag = function(doc, item, handler) {
  var _a, _b;
  var scriptTag = doc.createElement("script");
  scriptTag.referrerPolicy = "origin";
  scriptTag.type = "application/javascript";
  scriptTag.id = item.id;
  scriptTag.src = item.src;
  scriptTag.async = (_a = item.async) !== null && _a !== void 0 ? _a : false;
  scriptTag.defer = (_b = item.defer) !== null && _b !== void 0 ? _b : false;
  var loadHandler = function() {
    scriptTag.removeEventListener("load", loadHandler);
    scriptTag.removeEventListener("error", errorHandler);
    handler(item.src);
  };
  var errorHandler = function(err) {
    scriptTag.removeEventListener("load", loadHandler);
    scriptTag.removeEventListener("error", errorHandler);
    handler(item.src, err);
  };
  scriptTag.addEventListener("load", loadHandler);
  scriptTag.addEventListener("error", errorHandler);
  if (doc.head) {
    doc.head.appendChild(scriptTag);
  }
};
var createDocumentScriptLoader = function(doc) {
  var lookup = {};
  var scriptLoadOrErrorHandler = function(src, err) {
    var item = lookup[src];
    item.done = true;
    item.error = err;
    for (var _i = 0, _a = item.handlers; _i < _a.length; _i++) {
      var h = _a[_i];
      h(src, err);
    }
    item.handlers = [];
  };
  var loadScripts = function(items, success, failure) {
    var failureOrLog = function(err) {
      return failure !== void 0 ? failure(err) : console.error(err);
    };
    if (items.length === 0) {
      failureOrLog(new Error("At least one script must be provided"));
      return;
    }
    var successCount = 0;
    var failed = false;
    var loaded = function(_src, err) {
      if (failed) {
        return;
      }
      if (err) {
        failed = true;
        failureOrLog(err);
      } else if (++successCount === items.length) {
        success();
      }
    };
    for (var _i = 0, items_1 = items; _i < items_1.length; _i++) {
      var item = items_1[_i];
      var existing = lookup[item.src];
      if (existing) {
        if (existing.done) {
          loaded(item.src, existing.error);
        } else {
          existing.handlers.push(loaded);
        }
      } else {
        var id = uuid("tiny-");
        lookup[item.src] = {
          id,
          src: item.src,
          done: false,
          error: null,
          handlers: [loaded]
        };
        injectScriptTag(doc, __assign2({ id }, item), scriptLoadOrErrorHandler);
      }
    }
  };
  var deleteScripts = function() {
    var _a;
    for (var _i = 0, _b = Object.values(lookup); _i < _b.length; _i++) {
      var item = _b[_i];
      var scriptTag = doc.getElementById(item.id);
      if (scriptTag != null && scriptTag.tagName === "SCRIPT") {
        (_a = scriptTag.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(scriptTag);
      }
    }
    lookup = {};
  };
  var getDocument = function() {
    return doc;
  };
  return {
    loadScripts,
    deleteScripts,
    getDocument
  };
};
var createScriptLoader = function() {
  var cache = [];
  var getDocumentScriptLoader = function(doc) {
    var loader = cache.find(function(l) {
      return l.getDocument() === doc;
    });
    if (loader === void 0) {
      loader = createDocumentScriptLoader(doc);
      cache.push(loader);
    }
    return loader;
  };
  var loadList = function(doc, items, delay, success, failure) {
    var doLoad = function() {
      return getDocumentScriptLoader(doc).loadScripts(items, success, failure);
    };
    if (delay > 0) {
      setTimeout(doLoad, delay);
    } else {
      doLoad();
    }
  };
  var reinitialize = function() {
    for (var loader = cache.pop(); loader != null; loader = cache.pop()) {
      loader.deleteScripts();
    }
  };
  return {
    loadList,
    reinitialize
  };
};
var ScriptLoader = createScriptLoader();

// node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/TinyMCE.js
var getTinymce = function(view) {
  var global = view;
  return global && global.tinymce ? global.tinymce : null;
};

// node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/components/Editor.js
var __extends = function() {
  var extendStatics = function(d, b) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
      d2.__proto__ = b2;
    } || function(d2, b2) {
      for (var p in b2)
        if (Object.prototype.hasOwnProperty.call(b2, p))
          d2[p] = b2[p];
    };
    return extendStatics(d, b);
  };
  return function(d, b) {
    if (typeof b !== "function" && b !== null)
      throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
    extendStatics(d, b);
    function __() {
      this.constructor = d;
    }
    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
  };
}();
var __assign3 = function() {
  __assign3 = Object.assign || function(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s)
        if (Object.prototype.hasOwnProperty.call(s, p))
          t[p] = s[p];
    }
    return t;
  };
  return __assign3.apply(this, arguments);
};
var Editor = (
  /** @class */
  function(_super) {
    __extends(Editor2, _super);
    function Editor2(props) {
      var _a, _b, _c;
      var _this = _super.call(this, props) || this;
      _this.rollbackTimer = void 0;
      _this.valueCursor = void 0;
      _this.rollbackChange = function() {
        var editor = _this.editor;
        var value = _this.props.value;
        if (editor && value && value !== _this.currentContent) {
          editor.undoManager.ignore(function() {
            editor.setContent(value);
            if (_this.valueCursor && (!_this.inline || editor.hasFocus())) {
              try {
                editor.selection.moveToBookmark(_this.valueCursor);
              } catch (e) {
              }
            }
          });
        }
        _this.rollbackTimer = void 0;
      };
      _this.handleBeforeInput = function(_evt) {
        if (_this.props.value !== void 0 && _this.props.value === _this.currentContent && _this.editor) {
          if (!_this.inline || _this.editor.hasFocus()) {
            try {
              _this.valueCursor = _this.editor.selection.getBookmark(3);
            } catch (e) {
            }
          }
        }
      };
      _this.handleBeforeInputSpecial = function(evt) {
        if (evt.key === "Enter" || evt.key === "Backspace" || evt.key === "Delete") {
          _this.handleBeforeInput(evt);
        }
      };
      _this.handleEditorChange = function(_evt) {
        var editor = _this.editor;
        if (editor && editor.initialized) {
          var newContent = editor.getContent();
          if (_this.props.value !== void 0 && _this.props.value !== newContent && _this.props.rollback !== false) {
            if (!_this.rollbackTimer) {
              _this.rollbackTimer = window.setTimeout(_this.rollbackChange, typeof _this.props.rollback === "number" ? _this.props.rollback : 200);
            }
          }
          if (newContent !== _this.currentContent) {
            _this.currentContent = newContent;
            if (isFunction(_this.props.onEditorChange)) {
              _this.props.onEditorChange(newContent, editor);
            }
          }
        }
      };
      _this.handleEditorChangeSpecial = function(evt) {
        if (evt.key === "Backspace" || evt.key === "Delete") {
          _this.handleEditorChange(evt);
        }
      };
      _this.initialise = function(attempts) {
        var _a2, _b2, _c2;
        if (attempts === void 0) {
          attempts = 0;
        }
        var target = _this.elementRef.current;
        if (!target) {
          return;
        }
        if (!isInDoc(target)) {
          if (attempts === 0) {
            setTimeout(function() {
              return _this.initialise(1);
            }, 1);
          } else if (attempts < 100) {
            setTimeout(function() {
              return _this.initialise(attempts + 1);
            }, 100);
          } else {
            throw new Error("tinymce can only be initialised when in a document");
          }
          return;
        }
        var tinymce = getTinymce(_this.view);
        if (!tinymce) {
          throw new Error("tinymce should have been loaded into global scope");
        }
        var finalInit = __assign3(__assign3(__assign3(__assign3({}, _this.props.init), { selector: void 0, target, readonly: _this.props.disabled, inline: _this.inline, plugins: mergePlugins((_a2 = _this.props.init) === null || _a2 === void 0 ? void 0 : _a2.plugins, _this.props.plugins), toolbar: (_b2 = _this.props.toolbar) !== null && _b2 !== void 0 ? _b2 : (_c2 = _this.props.init) === null || _c2 === void 0 ? void 0 : _c2.toolbar }), _this.props.licenseKey ? { license_key: _this.props.licenseKey } : {}), { setup: function(editor) {
          _this.editor = editor;
          _this.bindHandlers({});
          if (_this.inline && !isTextareaOrInput(target)) {
            editor.once("PostRender", function(_evt) {
              editor.setContent(_this.getInitialValue(), { no_events: true });
            });
          }
          if (_this.props.init && isFunction(_this.props.init.setup)) {
            _this.props.init.setup(editor);
          }
        }, init_instance_callback: function(editor) {
          var _a3, _b3;
          var initialValue = _this.getInitialValue();
          _this.currentContent = (_a3 = _this.currentContent) !== null && _a3 !== void 0 ? _a3 : editor.getContent();
          if (_this.currentContent !== initialValue) {
            _this.currentContent = initialValue;
            editor.setContent(initialValue);
            editor.undoManager.clear();
            editor.undoManager.add();
            editor.setDirty(false);
          }
          var disabled = (_b3 = _this.props.disabled) !== null && _b3 !== void 0 ? _b3 : false;
          setMode(_this.editor, disabled ? "readonly" : "design");
          if (_this.props.init && isFunction(_this.props.init.init_instance_callback)) {
            _this.props.init.init_instance_callback(editor);
          }
        } });
        if (!_this.inline) {
          target.style.visibility = "";
        }
        if (isTextareaOrInput(target)) {
          target.value = _this.getInitialValue();
        }
        tinymce.init(finalInit);
      };
      _this.id = _this.props.id || uuid("tiny-react");
      _this.elementRef = React.createRef();
      _this.inline = (_c = (_a = _this.props.inline) !== null && _a !== void 0 ? _a : (_b = _this.props.init) === null || _b === void 0 ? void 0 : _b.inline) !== null && _c !== void 0 ? _c : false;
      _this.boundHandlers = {};
      return _this;
    }
    Object.defineProperty(Editor2.prototype, "view", {
      get: function() {
        var _a, _b;
        return (_b = (_a = this.elementRef.current) === null || _a === void 0 ? void 0 : _a.ownerDocument.defaultView) !== null && _b !== void 0 ? _b : window;
      },
      enumerable: false,
      configurable: true
    });
    Editor2.prototype.componentDidUpdate = function(prevProps) {
      var _this = this;
      var _a, _b;
      if (this.rollbackTimer) {
        clearTimeout(this.rollbackTimer);
        this.rollbackTimer = void 0;
      }
      if (this.editor) {
        this.bindHandlers(prevProps);
        if (this.editor.initialized) {
          this.currentContent = (_a = this.currentContent) !== null && _a !== void 0 ? _a : this.editor.getContent();
          if (typeof this.props.initialValue === "string" && this.props.initialValue !== prevProps.initialValue) {
            this.editor.setContent(this.props.initialValue);
            this.editor.undoManager.clear();
            this.editor.undoManager.add();
            this.editor.setDirty(false);
          } else if (typeof this.props.value === "string" && this.props.value !== this.currentContent) {
            var localEditor_1 = this.editor;
            localEditor_1.undoManager.transact(function() {
              var cursor;
              if (!_this.inline || localEditor_1.hasFocus()) {
                try {
                  cursor = localEditor_1.selection.getBookmark(3);
                } catch (e) {
                }
              }
              var valueCursor = _this.valueCursor;
              localEditor_1.setContent(_this.props.value);
              if (!_this.inline || localEditor_1.hasFocus()) {
                for (var _i = 0, _a2 = [cursor, valueCursor]; _i < _a2.length; _i++) {
                  var bookmark = _a2[_i];
                  if (bookmark) {
                    try {
                      localEditor_1.selection.moveToBookmark(bookmark);
                      _this.valueCursor = bookmark;
                      break;
                    } catch (e) {
                    }
                  }
                }
              }
            });
          }
          if (this.props.disabled !== prevProps.disabled) {
            var disabled = (_b = this.props.disabled) !== null && _b !== void 0 ? _b : false;
            setMode(this.editor, disabled ? "readonly" : "design");
          }
        }
      }
    };
    Editor2.prototype.componentDidMount = function() {
      var _this = this;
      var _a, _b, _c, _d, _e;
      if (getTinymce(this.view) !== null) {
        this.initialise();
      } else if (Array.isArray(this.props.tinymceScriptSrc) && this.props.tinymceScriptSrc.length === 0) {
        (_b = (_a = this.props).onScriptsLoadError) === null || _b === void 0 ? void 0 : _b.call(_a, new Error("No `tinymce` global is present but the `tinymceScriptSrc` prop was an empty array."));
      } else if ((_c = this.elementRef.current) === null || _c === void 0 ? void 0 : _c.ownerDocument) {
        var successHandler = function() {
          var _a2, _b2;
          (_b2 = (_a2 = _this.props).onScriptsLoad) === null || _b2 === void 0 ? void 0 : _b2.call(_a2);
          _this.initialise();
        };
        var errorHandler = function(err) {
          var _a2, _b2;
          (_b2 = (_a2 = _this.props).onScriptsLoadError) === null || _b2 === void 0 ? void 0 : _b2.call(_a2, err);
        };
        ScriptLoader.loadList(this.elementRef.current.ownerDocument, this.getScriptSources(), (_e = (_d = this.props.scriptLoading) === null || _d === void 0 ? void 0 : _d.delay) !== null && _e !== void 0 ? _e : 0, successHandler, errorHandler);
      }
    };
    Editor2.prototype.componentWillUnmount = function() {
      var _this = this;
      var editor = this.editor;
      if (editor) {
        editor.off(this.changeEvents(), this.handleEditorChange);
        editor.off(this.beforeInputEvent(), this.handleBeforeInput);
        editor.off("keypress", this.handleEditorChangeSpecial);
        editor.off("keydown", this.handleBeforeInputSpecial);
        editor.off("NewBlock", this.handleEditorChange);
        Object.keys(this.boundHandlers).forEach(function(eventName) {
          editor.off(eventName, _this.boundHandlers[eventName]);
        });
        this.boundHandlers = {};
        editor.remove();
        this.editor = void 0;
      }
    };
    Editor2.prototype.render = function() {
      return this.inline ? this.renderInline() : this.renderIframe();
    };
    Editor2.prototype.changeEvents = function() {
      var _a, _b, _c;
      var isIE = (_c = (_b = (_a = getTinymce(this.view)) === null || _a === void 0 ? void 0 : _a.Env) === null || _b === void 0 ? void 0 : _b.browser) === null || _c === void 0 ? void 0 : _c.isIE();
      return isIE ? "change keyup compositionend setcontent CommentChange" : "change input compositionend setcontent CommentChange";
    };
    Editor2.prototype.beforeInputEvent = function() {
      return isBeforeInputEventAvailable() ? "beforeinput SelectionChange" : "SelectionChange";
    };
    Editor2.prototype.renderInline = function() {
      var _a = this.props.tagName, tagName = _a === void 0 ? "div" : _a;
      return React.createElement(tagName, {
        ref: this.elementRef,
        id: this.id,
        tabIndex: this.props.tabIndex
      });
    };
    Editor2.prototype.renderIframe = function() {
      return React.createElement("textarea", {
        ref: this.elementRef,
        style: { visibility: "hidden" },
        name: this.props.textareaName,
        id: this.id,
        tabIndex: this.props.tabIndex
      });
    };
    Editor2.prototype.getScriptSources = function() {
      var _a, _b;
      var async = (_a = this.props.scriptLoading) === null || _a === void 0 ? void 0 : _a.async;
      var defer = (_b = this.props.scriptLoading) === null || _b === void 0 ? void 0 : _b.defer;
      if (this.props.tinymceScriptSrc !== void 0) {
        if (typeof this.props.tinymceScriptSrc === "string") {
          return [{ src: this.props.tinymceScriptSrc, async, defer }];
        }
        return this.props.tinymceScriptSrc.map(function(item) {
          if (typeof item === "string") {
            return { src: item, async, defer };
          } else {
            return item;
          }
        });
      }
      var channel = this.props.cloudChannel;
      var apiKey = this.props.apiKey ? this.props.apiKey : "no-api-key";
      var cloudTinyJs = "https://cdn.tiny.cloud/1/".concat(apiKey, "/tinymce/").concat(channel, "/tinymce.min.js");
      return [{ src: cloudTinyJs, async, defer }];
    };
    Editor2.prototype.getInitialValue = function() {
      if (typeof this.props.initialValue === "string") {
        return this.props.initialValue;
      } else if (typeof this.props.value === "string") {
        return this.props.value;
      } else {
        return "";
      }
    };
    Editor2.prototype.bindHandlers = function(prevProps) {
      var _this = this;
      if (this.editor !== void 0) {
        configHandlers(this.editor, prevProps, this.props, this.boundHandlers, function(key) {
          return _this.props[key];
        });
        var isValueControlled = function(p) {
          return p.onEditorChange !== void 0 || p.value !== void 0;
        };
        var wasControlled = isValueControlled(prevProps);
        var nowControlled = isValueControlled(this.props);
        if (!wasControlled && nowControlled) {
          this.editor.on(this.changeEvents(), this.handleEditorChange);
          this.editor.on(this.beforeInputEvent(), this.handleBeforeInput);
          this.editor.on("keydown", this.handleBeforeInputSpecial);
          this.editor.on("keyup", this.handleEditorChangeSpecial);
          this.editor.on("NewBlock", this.handleEditorChange);
        } else if (wasControlled && !nowControlled) {
          this.editor.off(this.changeEvents(), this.handleEditorChange);
          this.editor.off(this.beforeInputEvent(), this.handleBeforeInput);
          this.editor.off("keydown", this.handleBeforeInputSpecial);
          this.editor.off("keyup", this.handleEditorChangeSpecial);
          this.editor.off("NewBlock", this.handleEditorChange);
        }
      }
    };
    Editor2.propTypes = EditorPropTypes;
    Editor2.defaultProps = {
      cloudChannel: "7"
    };
    return Editor2;
  }(React.Component)
);

export {
  require_browser,
  tinyConfig,
  htmlFrom,
  Editor
};
/*! Bundled license information:

dompurify/dist/purify.cjs.js:
  (*! @license DOMPurify 3.2.4 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.4/LICENSE *)
*/
//# sourceMappingURL=/build/_shared/chunk-SIGMETZO.js.map
