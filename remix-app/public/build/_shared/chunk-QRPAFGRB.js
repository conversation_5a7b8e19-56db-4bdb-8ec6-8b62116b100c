import {
  Icon_Admin_Delete_default
} from "/build/_shared/chunk-RUOV274T.js";
import {
  Icon_Admin_Add_default
} from "/build/_shared/chunk-OHIZ2QAR.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/context/SearchContext.tsx
var import_react = __toESM(require_react(), 1);
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/context/SearchContext.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/context/SearchContext.tsx"
  );
  import.meta.hot.lastModified = "1747752412820.846";
}
var SearchContext = (0, import_react.createContext)(void 0);
function SearchProvider({
  children,
  filters
}) {
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(SearchContext.Provider, { value: {
    filters
  }, children }, void 0, false, {
    fileName: "app/context/SearchContext.tsx",
    lineNumber: 28,
    columnNumber: 10
  }, this);
}
_c = SearchProvider;
function useSearchContext() {
  _s();
  const context = (0, import_react.useContext)(SearchContext);
  if (context === void 0) {
    throw new Error("useSearchContext must be used within a SearchProvider");
  }
  return context;
}
_s(useSearchContext, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
$RefreshReg$(_c, "SearchProvider");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;

// app/components/search/AddAnotherFilter.tsx
var import_jsx_dev_runtime2 = __toESM(require_jsx_dev_runtime(), 1);
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/components/search/AddAnotherFilter.tsx"
  );
  import.meta.hot.lastModified = "1750252761862.7222";
}
var AddAnotherFilter_default = ({ onChange, className = "" }) => {
  const { filters } = useSearchContext();
  const handleFieldChange = (fieldId) => {
    const newFilterDef = filters.get(fieldId);
    if (!newFilterDef)
      return;
    const operators = newFilterDef.getOperators();
    if (operators.length === 0)
      return;
    const defaultOperator = operators[0].value;
    if (!isValidOperator(defaultOperator))
      return;
    const defaultValue = newFilterDef.getDefaultValue(defaultOperator);
    onChange(fieldId, defaultOperator, defaultValue);
  };
  return /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: `filter ${className}`, children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "title", children: "Option: Add Another Filter" }, void 0, false, {
      fileName: "app/components/search/AddAnotherFilter.tsx",
      lineNumber: 34,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("select", { className: "w-full md:w-1/2", onChange: (e) => handleFieldChange(e.target.value), value: "", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("option", { value: "", children: "Type of Filter" }, void 0, false, {
        fileName: "app/components/search/AddAnotherFilter.tsx",
        lineNumber: 36,
        columnNumber: 5
      }, this),
      Array.from(filters.entries()).map(([key, f]) => /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("option", { value: key, children: f.name }, key, false, {
        fileName: "app/components/search/AddAnotherFilter.tsx",
        lineNumber: 38,
        columnNumber: 6
      }, this))
    ] }, void 0, true, {
      fileName: "app/components/search/AddAnotherFilter.tsx",
      lineNumber: 35,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/components/search/AddAnotherFilter.tsx",
    lineNumber: 33,
    columnNumber: 3
  }, this);
};
function isValidOperator(operator) {
  return true;
  if (!operator)
    return false;
  const validOperators = [
    "equals",
    "not",
    "in",
    "notIn",
    "lt",
    "lte",
    "gt",
    "gte",
    "contains",
    "search",
    "startsWith",
    "endsWith",
    "between"
  ];
  return validOperators.includes(operator);
}

// app/components/search/FilterComponent2.tsx
var import_jsx_dev_runtime3 = __toESM(require_jsx_dev_runtime(), 1);
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/components/search/FilterComponent2.tsx"
  );
  import.meta.hot.lastModified = "1750252761863.1316";
}
var FilterComponent2_default = ({ filter, onUpdate, onDelete, className = "", addFilter }) => {
  const { filters } = useSearchContext();
  const filterDef = filters.get(filter.field);
  const operators = filterDef?.getOperators();
  const InputComponent = filterDef?.getInputComponent(filter.operator);
  const handleOperatorChange = (operator) => {
    if (isValidOperator2(operator)) {
      onUpdate({
        ...filter,
        operator,
        value: filterDef.getDefaultValue(operator)
      });
    }
  };
  const handleFieldChange = (fieldId) => {
    const newFilterDef = filters.get(fieldId);
    if (!newFilterDef)
      return;
    const operators2 = newFilterDef.getOperators();
    if (operators2.length === 0)
      return;
    const defaultOperator = operators2[0].value;
    if (!isValidOperator2(defaultOperator))
      return;
    onUpdate({
      ...filter,
      field: fieldId,
      operator: defaultOperator,
      value: newFilterDef.getDefaultValue(defaultOperator)
    });
  };
  if (filterDef && "getFormComponent" in filterDef && typeof filterDef.getFormComponent === "function") {
    const FormComponent = filterDef.getFormComponent();
    const handleSubmit = (value) => {
      const processedValue = "handleSubmit" in filterDef && typeof filterDef.handleSubmit === "function" ? filterDef.handleSubmit(value) : value;
      onUpdate({ ...filter, value: processedValue });
    };
    return /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: `filter ${className}`, children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: "title flex justify-between", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { children: filterDef.name }, void 0, false, {
          fileName: "app/components/search/FilterComponent2.tsx",
          lineNumber: 79,
          columnNumber: 6
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("img", { src: Icon_Admin_Delete_default, alt: "Delete", width: 16, className: "cursor-pointer", onClick: onDelete }, void 0, false, {
          fileName: "app/components/search/FilterComponent2.tsx",
          lineNumber: 80,
          columnNumber: 6
        }, this)
      ] }, void 0, true, {
        fileName: "app/components/search/FilterComponent2.tsx",
        lineNumber: 78,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)(
        FormComponent,
        {
          value: filter.value,
          onChange: (value) => onUpdate({ ...filter, value }),
          onSubmit: handleSubmit,
          filter: filterDef,
          className: "flex items-center gap-5"
        },
        void 0,
        false,
        {
          fileName: "app/components/search/FilterComponent2.tsx",
          lineNumber: 83,
          columnNumber: 5
        },
        this
      ),
      addFilter && /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: "addfilter", children: /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("button", { type: "button", className: "flex items-center", onClick: addFilter, children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("img", { className: "mr-2.5", src: Icon_Admin_Add_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/components/search/FilterComponent2.tsx",
          lineNumber: 94,
          columnNumber: 8
        }, this),
        "Add Filter"
      ] }, void 0, true, {
        fileName: "app/components/search/FilterComponent2.tsx",
        lineNumber: 93,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/components/search/FilterComponent2.tsx",
        lineNumber: 92,
        columnNumber: 6
      }, this)
    ] }, void 0, true, {
      fileName: "app/components/search/FilterComponent2.tsx",
      lineNumber: 77,
      columnNumber: 4
    }, this);
  }
  return /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: `filter ${"filter-" + filter.field} ${className}`, children: [
    filterDef && /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: "title flex justify-between", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { children: filterDef?.name }, void 0, false, {
        fileName: "app/components/search/FilterComponent2.tsx",
        lineNumber: 107,
        columnNumber: 6
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("img", { src: Icon_Admin_Delete_default, alt: "Delete", width: 16, className: "cursor-pointer", onClick: onDelete }, void 0, false, {
        fileName: "app/components/search/FilterComponent2.tsx",
        lineNumber: 108,
        columnNumber: 6
      }, this)
    ] }, void 0, true, {
      fileName: "app/components/search/FilterComponent2.tsx",
      lineNumber: 106,
      columnNumber: 5
    }, this),
    !filterDef && /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("select", { value: filter.field, onChange: (e) => handleFieldChange(e.target.value), className: "border", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("option", { value: "", children: "Select Field" }, void 0, false, {
        fileName: "app/components/search/FilterComponent2.tsx",
        lineNumber: 114,
        columnNumber: 6
      }, this),
      Array.from(filters.entries()).map(([key, f]) => /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("option", { value: key, children: f.name }, key, false, {
        fileName: "app/components/search/FilterComponent2.tsx",
        lineNumber: 116,
        columnNumber: 7
      }, this))
    ] }, void 0, true, {
      fileName: "app/components/search/FilterComponent2.tsx",
      lineNumber: 113,
      columnNumber: 5
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: "flex items-center gap-5", children: [
      operators && /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("select", { value: filter.operator, onChange: (e) => handleOperatorChange(e.target.value), className: "border", children: operators.map((op) => /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("option", { value: op.value, children: op.label }, op.value, false, {
        fileName: "app/components/search/FilterComponent2.tsx",
        lineNumber: 125,
        columnNumber: 8
      }, this)) }, void 0, false, {
        fileName: "app/components/search/FilterComponent2.tsx",
        lineNumber: 123,
        columnNumber: 6
      }, this),
      filterDef && /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)(
        InputComponent,
        {
          value: filter.value,
          onChange: (value) => onUpdate({ ...filter, value }),
          operator: filter.operator,
          filter: filterDef,
          className: "flex-1"
        },
        void 0,
        false,
        {
          fileName: "app/components/search/FilterComponent2.tsx",
          lineNumber: 133,
          columnNumber: 6
        },
        this
      )
    ] }, void 0, true, {
      fileName: "app/components/search/FilterComponent2.tsx",
      lineNumber: 121,
      columnNumber: 4
    }, this),
    addFilter && filterDef && /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: "addfilter", children: /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("button", { type: "button", className: "flex items-center", onClick: addFilter, children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("img", { className: "mr-2.5", src: Icon_Admin_Add_default, alt: "", width: 16 }, void 0, false, {
        fileName: "app/components/search/FilterComponent2.tsx",
        lineNumber: 146,
        columnNumber: 7
      }, this),
      "Add Filter"
    ] }, void 0, true, {
      fileName: "app/components/search/FilterComponent2.tsx",
      lineNumber: 145,
      columnNumber: 6
    }, this) }, void 0, false, {
      fileName: "app/components/search/FilterComponent2.tsx",
      lineNumber: 144,
      columnNumber: 5
    }, this)
  ] }, void 0, true, {
    fileName: "app/components/search/FilterComponent2.tsx",
    lineNumber: 104,
    columnNumber: 3
  }, this);
};
function isValidOperator2(operator) {
  return true;
  if (!operator)
    return false;
  const validOperators = [
    "equals",
    "not",
    "in",
    "notIn",
    "lt",
    "lte",
    "gt",
    "gte",
    "contains",
    "search",
    "startsWith",
    "endsWith",
    "between"
  ];
  return validOperators.includes(operator);
}

export {
  SearchProvider,
  AddAnotherFilter_default,
  FilterComponent2_default
};
//# sourceMappingURL=/build/_shared/chunk-QRPAFGRB.js.map
