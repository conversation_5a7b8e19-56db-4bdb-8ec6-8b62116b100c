import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import {
  __commonJS,
  __publicField,
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// empty-module:./SearchBuilder.server
var require_SearchBuilder = __commonJS({
  "empty-module:./SearchBuilder.server"(exports, module) {
    module.exports = {};
  }
});

// empty-module:~/services/search/SearchBuilder.server
var require_SearchBuilder2 = __commonJS({
  "empty-module:~/services/search/SearchBuilder.server"(exports, module) {
    module.exports = {};
  }
});

// app/components/search/inputs/DefaultFilterInput.tsx
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/components/search/inputs/DefaultFilterInput.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/components/search/inputs/DefaultFilterInput.tsx"
  );
  import.meta.hot.lastModified = "1750252761863.876";
}
function DefaultFilterInput({
  value,
  onChange,
  operator,
  filter,
  className = ""
}) {
  if (!filter)
    return null;
  const operators = filter.getOperators();
  const currentOperator = operators.find((op) => op.value === operator);
  if (!currentOperator)
    return null;
  const isMultiSelect = currentOperator.value === "in";
  switch (currentOperator.inputType) {
    case "select":
      return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("select", { value, multiple: isMultiSelect, size: isMultiSelect ? currentOperator.options?.length : void 0, onChange: (e) => onChange(isMultiSelect ? Array.from(e.target.selectedOptions, (option) => option.value) : e.target.value), className: `border ${className} ${isMultiSelect ? "h-auto py-2" : ""}`, children: [
        !isMultiSelect && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("option", { value: "", children: "Select..." }, void 0, false, {
          fileName: "app/components/search/inputs/DefaultFilterInput.tsx",
          lineNumber: 36,
          columnNumber: 25
        }, this),
        currentOperator.options?.map((option) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("option", { value: option.value, children: option.label }, option.value, false, {
          fileName: "app/components/search/inputs/DefaultFilterInput.tsx",
          lineNumber: 37,
          columnNumber: 46
        }, this))
      ] }, void 0, true, {
        fileName: "app/components/search/inputs/DefaultFilterInput.tsx",
        lineNumber: 35,
        columnNumber: 14
      }, this);
    case "none":
      return null;
    case "text":
    default:
      return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("input", { type: "text", value, onChange: (e) => onChange(e.target.value), className: `border ${className}`, placeholder: "Enter value..." }, void 0, false, {
        fileName: "app/components/search/inputs/DefaultFilterInput.tsx",
        lineNumber: 45,
        columnNumber: 14
      }, this);
  }
}
_c = DefaultFilterInput;
var _c;
$RefreshReg$(_c, "DefaultFilterInput");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;

// app/services/search/BaseFilter.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/BaseFilter.ts"
  );
  import.meta.hot.lastModified = "1751311374860.7346";
}
var BaseFilter = class {
  description;
  constructor() {
  }
  /**
   * Apply the filter to the query builder
   * @deprecated Filters should not directly modify the QueryBuilder. Use getConditions instead.
   */
  apply(builder, value) {
    const conditions = this.getConditions(value);
    if (conditions) {
      conditions.forEach((condition) => {
        if (condition.type === "raw") {
          builder.whereRaw(condition.sql, condition.params);
        } else {
          builder.where(condition.field, condition.operator, condition.value);
        }
      });
    }
  }
  getFieldName() {
    return this.field;
  }
  // Default operators - override in specific filters
  getOperators() {
    return [
      { value: "equals", label: "Equals", inputType: "text" },
      { value: "contains", label: "Contains", inputType: "text" },
      { value: "not", label: "Does not equal", inputType: "text" }
    ];
  }
  // Default input component - override for custom inputs
  getInputComponent(operator) {
    const op = this.getOperators().find((o) => o.value === operator);
    return op?.component || DefaultFilterInput;
  }
  // Default value for operator
  getDefaultValue(operator) {
    return "";
  }
  // Format value for display
  formatValue(value, operator) {
    return String(value);
  }
  hasPermission(userRoles, userPermissions = []) {
    return this.permissions.some((permission) => {
      const hasRole = permission.roles.some((role) => userRoles.includes(role));
      if (!permission.permissions || permission.permissions.length === 0) {
        return hasRole;
      }
      const hasPermission = permission.permissions.some((perm) => userPermissions.includes(perm));
      return hasRole && hasPermission;
    });
  }
};

// node_modules/date-fns/constants.js
var daysInYear = 365.2425;
var maxTime = Math.pow(10, 8) * 24 * 60 * 60 * 1e3;
var minTime = -maxTime;
var millisecondsInWeek = 6048e5;
var millisecondsInDay = 864e5;
var secondsInHour = 3600;
var secondsInDay = secondsInHour * 24;
var secondsInWeek = secondsInDay * 7;
var secondsInYear = secondsInDay * daysInYear;
var secondsInMonth = secondsInYear / 12;
var secondsInQuarter = secondsInMonth * 3;
var constructFromSymbol = Symbol.for("constructDateFrom");

// node_modules/date-fns/constructFrom.js
function constructFrom(date, value) {
  if (typeof date === "function")
    return date(value);
  if (date && typeof date === "object" && constructFromSymbol in date)
    return date[constructFromSymbol](value);
  if (date instanceof Date)
    return new date.constructor(value);
  return new Date(value);
}

// node_modules/date-fns/toDate.js
function toDate(argument, context) {
  return constructFrom(context || argument, argument);
}

// node_modules/date-fns/_lib/defaultOptions.js
var defaultOptions = {};
function getDefaultOptions() {
  return defaultOptions;
}

// node_modules/date-fns/startOfWeek.js
function startOfWeek(date, options) {
  const defaultOptions2 = getDefaultOptions();
  const weekStartsOn = options?.weekStartsOn ?? options?.locale?.options?.weekStartsOn ?? defaultOptions2.weekStartsOn ?? defaultOptions2.locale?.options?.weekStartsOn ?? 0;
  const _date = toDate(date, options?.in);
  const day = _date.getDay();
  const diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;
  _date.setDate(_date.getDate() - diff);
  _date.setHours(0, 0, 0, 0);
  return _date;
}

// node_modules/date-fns/startOfISOWeek.js
function startOfISOWeek(date, options) {
  return startOfWeek(date, { ...options, weekStartsOn: 1 });
}

// node_modules/date-fns/getISOWeekYear.js
function getISOWeekYear(date, options) {
  const _date = toDate(date, options?.in);
  const year = _date.getFullYear();
  const fourthOfJanuaryOfNextYear = constructFrom(_date, 0);
  fourthOfJanuaryOfNextYear.setFullYear(year + 1, 0, 4);
  fourthOfJanuaryOfNextYear.setHours(0, 0, 0, 0);
  const startOfNextYear = startOfISOWeek(fourthOfJanuaryOfNextYear);
  const fourthOfJanuaryOfThisYear = constructFrom(_date, 0);
  fourthOfJanuaryOfThisYear.setFullYear(year, 0, 4);
  fourthOfJanuaryOfThisYear.setHours(0, 0, 0, 0);
  const startOfThisYear = startOfISOWeek(fourthOfJanuaryOfThisYear);
  if (_date.getTime() >= startOfNextYear.getTime()) {
    return year + 1;
  } else if (_date.getTime() >= startOfThisYear.getTime()) {
    return year;
  } else {
    return year - 1;
  }
}

// node_modules/date-fns/_lib/getTimezoneOffsetInMilliseconds.js
function getTimezoneOffsetInMilliseconds(date) {
  const _date = toDate(date);
  const utcDate = new Date(
    Date.UTC(
      _date.getFullYear(),
      _date.getMonth(),
      _date.getDate(),
      _date.getHours(),
      _date.getMinutes(),
      _date.getSeconds(),
      _date.getMilliseconds()
    )
  );
  utcDate.setUTCFullYear(_date.getFullYear());
  return +date - +utcDate;
}

// node_modules/date-fns/_lib/normalizeDates.js
function normalizeDates(context, ...dates) {
  const normalize = constructFrom.bind(
    null,
    context || dates.find((date) => typeof date === "object")
  );
  return dates.map(normalize);
}

// node_modules/date-fns/startOfDay.js
function startOfDay(date, options) {
  const _date = toDate(date, options?.in);
  _date.setHours(0, 0, 0, 0);
  return _date;
}

// node_modules/date-fns/differenceInCalendarDays.js
function differenceInCalendarDays(laterDate, earlierDate, options) {
  const [laterDate_, earlierDate_] = normalizeDates(
    options?.in,
    laterDate,
    earlierDate
  );
  const laterStartOfDay = startOfDay(laterDate_);
  const earlierStartOfDay = startOfDay(earlierDate_);
  const laterTimestamp = +laterStartOfDay - getTimezoneOffsetInMilliseconds(laterStartOfDay);
  const earlierTimestamp = +earlierStartOfDay - getTimezoneOffsetInMilliseconds(earlierStartOfDay);
  return Math.round((laterTimestamp - earlierTimestamp) / millisecondsInDay);
}

// node_modules/date-fns/startOfISOWeekYear.js
function startOfISOWeekYear(date, options) {
  const year = getISOWeekYear(date, options);
  const fourthOfJanuary = constructFrom(options?.in || date, 0);
  fourthOfJanuary.setFullYear(year, 0, 4);
  fourthOfJanuary.setHours(0, 0, 0, 0);
  return startOfISOWeek(fourthOfJanuary);
}

// node_modules/date-fns/isDate.js
function isDate(value) {
  return value instanceof Date || typeof value === "object" && Object.prototype.toString.call(value) === "[object Date]";
}

// node_modules/date-fns/isValid.js
function isValid(date) {
  return !(!isDate(date) && typeof date !== "number" || isNaN(+toDate(date)));
}

// node_modules/date-fns/startOfYear.js
function startOfYear(date, options) {
  const date_ = toDate(date, options?.in);
  date_.setFullYear(date_.getFullYear(), 0, 1);
  date_.setHours(0, 0, 0, 0);
  return date_;
}

// node_modules/date-fns/locale/en-US/_lib/formatDistance.js
var formatDistanceLocale = {
  lessThanXSeconds: {
    one: "less than a second",
    other: "less than {{count}} seconds"
  },
  xSeconds: {
    one: "1 second",
    other: "{{count}} seconds"
  },
  halfAMinute: "half a minute",
  lessThanXMinutes: {
    one: "less than a minute",
    other: "less than {{count}} minutes"
  },
  xMinutes: {
    one: "1 minute",
    other: "{{count}} minutes"
  },
  aboutXHours: {
    one: "about 1 hour",
    other: "about {{count}} hours"
  },
  xHours: {
    one: "1 hour",
    other: "{{count}} hours"
  },
  xDays: {
    one: "1 day",
    other: "{{count}} days"
  },
  aboutXWeeks: {
    one: "about 1 week",
    other: "about {{count}} weeks"
  },
  xWeeks: {
    one: "1 week",
    other: "{{count}} weeks"
  },
  aboutXMonths: {
    one: "about 1 month",
    other: "about {{count}} months"
  },
  xMonths: {
    one: "1 month",
    other: "{{count}} months"
  },
  aboutXYears: {
    one: "about 1 year",
    other: "about {{count}} years"
  },
  xYears: {
    one: "1 year",
    other: "{{count}} years"
  },
  overXYears: {
    one: "over 1 year",
    other: "over {{count}} years"
  },
  almostXYears: {
    one: "almost 1 year",
    other: "almost {{count}} years"
  }
};
var formatDistance = (token, count, options) => {
  let result;
  const tokenValue = formatDistanceLocale[token];
  if (typeof tokenValue === "string") {
    result = tokenValue;
  } else if (count === 1) {
    result = tokenValue.one;
  } else {
    result = tokenValue.other.replace("{{count}}", count.toString());
  }
  if (options?.addSuffix) {
    if (options.comparison && options.comparison > 0) {
      return "in " + result;
    } else {
      return result + " ago";
    }
  }
  return result;
};

// node_modules/date-fns/locale/_lib/buildFormatLongFn.js
function buildFormatLongFn(args) {
  return (options = {}) => {
    const width = options.width ? String(options.width) : args.defaultWidth;
    const format2 = args.formats[width] || args.formats[args.defaultWidth];
    return format2;
  };
}

// node_modules/date-fns/locale/en-US/_lib/formatLong.js
var dateFormats = {
  full: "EEEE, MMMM do, y",
  long: "MMMM do, y",
  medium: "MMM d, y",
  short: "MM/dd/yyyy"
};
var timeFormats = {
  full: "h:mm:ss a zzzz",
  long: "h:mm:ss a z",
  medium: "h:mm:ss a",
  short: "h:mm a"
};
var dateTimeFormats = {
  full: "{{date}} 'at' {{time}}",
  long: "{{date}} 'at' {{time}}",
  medium: "{{date}}, {{time}}",
  short: "{{date}}, {{time}}"
};
var formatLong = {
  date: buildFormatLongFn({
    formats: dateFormats,
    defaultWidth: "full"
  }),
  time: buildFormatLongFn({
    formats: timeFormats,
    defaultWidth: "full"
  }),
  dateTime: buildFormatLongFn({
    formats: dateTimeFormats,
    defaultWidth: "full"
  })
};

// node_modules/date-fns/locale/en-US/_lib/formatRelative.js
var formatRelativeLocale = {
  lastWeek: "'last' eeee 'at' p",
  yesterday: "'yesterday at' p",
  today: "'today at' p",
  tomorrow: "'tomorrow at' p",
  nextWeek: "eeee 'at' p",
  other: "P"
};
var formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];

// node_modules/date-fns/locale/_lib/buildLocalizeFn.js
function buildLocalizeFn(args) {
  return (value, options) => {
    const context = options?.context ? String(options.context) : "standalone";
    let valuesArray;
    if (context === "formatting" && args.formattingValues) {
      const defaultWidth = args.defaultFormattingWidth || args.defaultWidth;
      const width = options?.width ? String(options.width) : defaultWidth;
      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];
    } else {
      const defaultWidth = args.defaultWidth;
      const width = options?.width ? String(options.width) : args.defaultWidth;
      valuesArray = args.values[width] || args.values[defaultWidth];
    }
    const index = args.argumentCallback ? args.argumentCallback(value) : value;
    return valuesArray[index];
  };
}

// node_modules/date-fns/locale/en-US/_lib/localize.js
var eraValues = {
  narrow: ["B", "A"],
  abbreviated: ["BC", "AD"],
  wide: ["Before Christ", "Anno Domini"]
};
var quarterValues = {
  narrow: ["1", "2", "3", "4"],
  abbreviated: ["Q1", "Q2", "Q3", "Q4"],
  wide: ["1st quarter", "2nd quarter", "3rd quarter", "4th quarter"]
};
var monthValues = {
  narrow: ["J", "F", "M", "A", "M", "J", "J", "A", "S", "O", "N", "D"],
  abbreviated: [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec"
  ],
  wide: [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December"
  ]
};
var dayValues = {
  narrow: ["S", "M", "T", "W", "T", "F", "S"],
  short: ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"],
  abbreviated: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
  wide: [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday"
  ]
};
var dayPeriodValues = {
  narrow: {
    am: "a",
    pm: "p",
    midnight: "mi",
    noon: "n",
    morning: "morning",
    afternoon: "afternoon",
    evening: "evening",
    night: "night"
  },
  abbreviated: {
    am: "AM",
    pm: "PM",
    midnight: "midnight",
    noon: "noon",
    morning: "morning",
    afternoon: "afternoon",
    evening: "evening",
    night: "night"
  },
  wide: {
    am: "a.m.",
    pm: "p.m.",
    midnight: "midnight",
    noon: "noon",
    morning: "morning",
    afternoon: "afternoon",
    evening: "evening",
    night: "night"
  }
};
var formattingDayPeriodValues = {
  narrow: {
    am: "a",
    pm: "p",
    midnight: "mi",
    noon: "n",
    morning: "in the morning",
    afternoon: "in the afternoon",
    evening: "in the evening",
    night: "at night"
  },
  abbreviated: {
    am: "AM",
    pm: "PM",
    midnight: "midnight",
    noon: "noon",
    morning: "in the morning",
    afternoon: "in the afternoon",
    evening: "in the evening",
    night: "at night"
  },
  wide: {
    am: "a.m.",
    pm: "p.m.",
    midnight: "midnight",
    noon: "noon",
    morning: "in the morning",
    afternoon: "in the afternoon",
    evening: "in the evening",
    night: "at night"
  }
};
var ordinalNumber = (dirtyNumber, _options) => {
  const number = Number(dirtyNumber);
  const rem100 = number % 100;
  if (rem100 > 20 || rem100 < 10) {
    switch (rem100 % 10) {
      case 1:
        return number + "st";
      case 2:
        return number + "nd";
      case 3:
        return number + "rd";
    }
  }
  return number + "th";
};
var localize = {
  ordinalNumber,
  era: buildLocalizeFn({
    values: eraValues,
    defaultWidth: "wide"
  }),
  quarter: buildLocalizeFn({
    values: quarterValues,
    defaultWidth: "wide",
    argumentCallback: (quarter) => quarter - 1
  }),
  month: buildLocalizeFn({
    values: monthValues,
    defaultWidth: "wide"
  }),
  day: buildLocalizeFn({
    values: dayValues,
    defaultWidth: "wide"
  }),
  dayPeriod: buildLocalizeFn({
    values: dayPeriodValues,
    defaultWidth: "wide",
    formattingValues: formattingDayPeriodValues,
    defaultFormattingWidth: "wide"
  })
};

// node_modules/date-fns/locale/_lib/buildMatchFn.js
function buildMatchFn(args) {
  return (string, options = {}) => {
    const width = options.width;
    const matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];
    const matchResult = string.match(matchPattern);
    if (!matchResult) {
      return null;
    }
    const matchedString = matchResult[0];
    const parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];
    const key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, (pattern) => pattern.test(matchedString)) : (
      // [TODO] -- I challenge you to fix the type
      findKey(parsePatterns, (pattern) => pattern.test(matchedString))
    );
    let value;
    value = args.valueCallback ? args.valueCallback(key) : key;
    value = options.valueCallback ? (
      // [TODO] -- I challenge you to fix the type
      options.valueCallback(value)
    ) : value;
    const rest = string.slice(matchedString.length);
    return { value, rest };
  };
}
function findKey(object, predicate) {
  for (const key in object) {
    if (Object.prototype.hasOwnProperty.call(object, key) && predicate(object[key])) {
      return key;
    }
  }
  return void 0;
}
function findIndex(array, predicate) {
  for (let key = 0; key < array.length; key++) {
    if (predicate(array[key])) {
      return key;
    }
  }
  return void 0;
}

// node_modules/date-fns/locale/_lib/buildMatchPatternFn.js
function buildMatchPatternFn(args) {
  return (string, options = {}) => {
    const matchResult = string.match(args.matchPattern);
    if (!matchResult)
      return null;
    const matchedString = matchResult[0];
    const parseResult = string.match(args.parsePattern);
    if (!parseResult)
      return null;
    let value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];
    value = options.valueCallback ? options.valueCallback(value) : value;
    const rest = string.slice(matchedString.length);
    return { value, rest };
  };
}

// node_modules/date-fns/locale/en-US/_lib/match.js
var matchOrdinalNumberPattern = /^(\d+)(th|st|nd|rd)?/i;
var parseOrdinalNumberPattern = /\d+/i;
var matchEraPatterns = {
  narrow: /^(b|a)/i,
  abbreviated: /^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,
  wide: /^(before christ|before common era|anno domini|common era)/i
};
var parseEraPatterns = {
  any: [/^b/i, /^(a|c)/i]
};
var matchQuarterPatterns = {
  narrow: /^[1234]/i,
  abbreviated: /^q[1234]/i,
  wide: /^[1234](th|st|nd|rd)? quarter/i
};
var parseQuarterPatterns = {
  any: [/1/i, /2/i, /3/i, /4/i]
};
var matchMonthPatterns = {
  narrow: /^[jfmasond]/i,
  abbreviated: /^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,
  wide: /^(january|february|march|april|may|june|july|august|september|october|november|december)/i
};
var parseMonthPatterns = {
  narrow: [
    /^j/i,
    /^f/i,
    /^m/i,
    /^a/i,
    /^m/i,
    /^j/i,
    /^j/i,
    /^a/i,
    /^s/i,
    /^o/i,
    /^n/i,
    /^d/i
  ],
  any: [
    /^ja/i,
    /^f/i,
    /^mar/i,
    /^ap/i,
    /^may/i,
    /^jun/i,
    /^jul/i,
    /^au/i,
    /^s/i,
    /^o/i,
    /^n/i,
    /^d/i
  ]
};
var matchDayPatterns = {
  narrow: /^[smtwf]/i,
  short: /^(su|mo|tu|we|th|fr|sa)/i,
  abbreviated: /^(sun|mon|tue|wed|thu|fri|sat)/i,
  wide: /^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i
};
var parseDayPatterns = {
  narrow: [/^s/i, /^m/i, /^t/i, /^w/i, /^t/i, /^f/i, /^s/i],
  any: [/^su/i, /^m/i, /^tu/i, /^w/i, /^th/i, /^f/i, /^sa/i]
};
var matchDayPeriodPatterns = {
  narrow: /^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,
  any: /^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i
};
var parseDayPeriodPatterns = {
  any: {
    am: /^a/i,
    pm: /^p/i,
    midnight: /^mi/i,
    noon: /^no/i,
    morning: /morning/i,
    afternoon: /afternoon/i,
    evening: /evening/i,
    night: /night/i
  }
};
var match = {
  ordinalNumber: buildMatchPatternFn({
    matchPattern: matchOrdinalNumberPattern,
    parsePattern: parseOrdinalNumberPattern,
    valueCallback: (value) => parseInt(value, 10)
  }),
  era: buildMatchFn({
    matchPatterns: matchEraPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseEraPatterns,
    defaultParseWidth: "any"
  }),
  quarter: buildMatchFn({
    matchPatterns: matchQuarterPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseQuarterPatterns,
    defaultParseWidth: "any",
    valueCallback: (index) => index + 1
  }),
  month: buildMatchFn({
    matchPatterns: matchMonthPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseMonthPatterns,
    defaultParseWidth: "any"
  }),
  day: buildMatchFn({
    matchPatterns: matchDayPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseDayPatterns,
    defaultParseWidth: "any"
  }),
  dayPeriod: buildMatchFn({
    matchPatterns: matchDayPeriodPatterns,
    defaultMatchWidth: "any",
    parsePatterns: parseDayPeriodPatterns,
    defaultParseWidth: "any"
  })
};

// node_modules/date-fns/locale/en-US.js
var enUS = {
  code: "en-US",
  formatDistance,
  formatLong,
  formatRelative,
  localize,
  match,
  options: {
    weekStartsOn: 0,
    firstWeekContainsDate: 1
  }
};

// node_modules/date-fns/getDayOfYear.js
function getDayOfYear(date, options) {
  const _date = toDate(date, options?.in);
  const diff = differenceInCalendarDays(_date, startOfYear(_date));
  const dayOfYear = diff + 1;
  return dayOfYear;
}

// node_modules/date-fns/getISOWeek.js
function getISOWeek(date, options) {
  const _date = toDate(date, options?.in);
  const diff = +startOfISOWeek(_date) - +startOfISOWeekYear(_date);
  return Math.round(diff / millisecondsInWeek) + 1;
}

// node_modules/date-fns/getWeekYear.js
function getWeekYear(date, options) {
  const _date = toDate(date, options?.in);
  const year = _date.getFullYear();
  const defaultOptions2 = getDefaultOptions();
  const firstWeekContainsDate = options?.firstWeekContainsDate ?? options?.locale?.options?.firstWeekContainsDate ?? defaultOptions2.firstWeekContainsDate ?? defaultOptions2.locale?.options?.firstWeekContainsDate ?? 1;
  const firstWeekOfNextYear = constructFrom(options?.in || date, 0);
  firstWeekOfNextYear.setFullYear(year + 1, 0, firstWeekContainsDate);
  firstWeekOfNextYear.setHours(0, 0, 0, 0);
  const startOfNextYear = startOfWeek(firstWeekOfNextYear, options);
  const firstWeekOfThisYear = constructFrom(options?.in || date, 0);
  firstWeekOfThisYear.setFullYear(year, 0, firstWeekContainsDate);
  firstWeekOfThisYear.setHours(0, 0, 0, 0);
  const startOfThisYear = startOfWeek(firstWeekOfThisYear, options);
  if (+_date >= +startOfNextYear) {
    return year + 1;
  } else if (+_date >= +startOfThisYear) {
    return year;
  } else {
    return year - 1;
  }
}

// node_modules/date-fns/startOfWeekYear.js
function startOfWeekYear(date, options) {
  const defaultOptions2 = getDefaultOptions();
  const firstWeekContainsDate = options?.firstWeekContainsDate ?? options?.locale?.options?.firstWeekContainsDate ?? defaultOptions2.firstWeekContainsDate ?? defaultOptions2.locale?.options?.firstWeekContainsDate ?? 1;
  const year = getWeekYear(date, options);
  const firstWeek = constructFrom(options?.in || date, 0);
  firstWeek.setFullYear(year, 0, firstWeekContainsDate);
  firstWeek.setHours(0, 0, 0, 0);
  const _date = startOfWeek(firstWeek, options);
  return _date;
}

// node_modules/date-fns/getWeek.js
function getWeek(date, options) {
  const _date = toDate(date, options?.in);
  const diff = +startOfWeek(_date, options) - +startOfWeekYear(_date, options);
  return Math.round(diff / millisecondsInWeek) + 1;
}

// node_modules/date-fns/_lib/addLeadingZeros.js
function addLeadingZeros(number, targetLength) {
  const sign = number < 0 ? "-" : "";
  const output = Math.abs(number).toString().padStart(targetLength, "0");
  return sign + output;
}

// node_modules/date-fns/_lib/format/lightFormatters.js
var lightFormatters = {
  // Year
  y(date, token) {
    const signedYear = date.getFullYear();
    const year = signedYear > 0 ? signedYear : 1 - signedYear;
    return addLeadingZeros(token === "yy" ? year % 100 : year, token.length);
  },
  // Month
  M(date, token) {
    const month = date.getMonth();
    return token === "M" ? String(month + 1) : addLeadingZeros(month + 1, 2);
  },
  // Day of the month
  d(date, token) {
    return addLeadingZeros(date.getDate(), token.length);
  },
  // AM or PM
  a(date, token) {
    const dayPeriodEnumValue = date.getHours() / 12 >= 1 ? "pm" : "am";
    switch (token) {
      case "a":
      case "aa":
        return dayPeriodEnumValue.toUpperCase();
      case "aaa":
        return dayPeriodEnumValue;
      case "aaaaa":
        return dayPeriodEnumValue[0];
      case "aaaa":
      default:
        return dayPeriodEnumValue === "am" ? "a.m." : "p.m.";
    }
  },
  // Hour [1-12]
  h(date, token) {
    return addLeadingZeros(date.getHours() % 12 || 12, token.length);
  },
  // Hour [0-23]
  H(date, token) {
    return addLeadingZeros(date.getHours(), token.length);
  },
  // Minute
  m(date, token) {
    return addLeadingZeros(date.getMinutes(), token.length);
  },
  // Second
  s(date, token) {
    return addLeadingZeros(date.getSeconds(), token.length);
  },
  // Fraction of second
  S(date, token) {
    const numberOfDigits = token.length;
    const milliseconds = date.getMilliseconds();
    const fractionalSeconds = Math.trunc(
      milliseconds * Math.pow(10, numberOfDigits - 3)
    );
    return addLeadingZeros(fractionalSeconds, token.length);
  }
};

// node_modules/date-fns/_lib/format/formatters.js
var dayPeriodEnum = {
  am: "am",
  pm: "pm",
  midnight: "midnight",
  noon: "noon",
  morning: "morning",
  afternoon: "afternoon",
  evening: "evening",
  night: "night"
};
var formatters = {
  // Era
  G: function(date, token, localize2) {
    const era = date.getFullYear() > 0 ? 1 : 0;
    switch (token) {
      case "G":
      case "GG":
      case "GGG":
        return localize2.era(era, { width: "abbreviated" });
      case "GGGGG":
        return localize2.era(era, { width: "narrow" });
      case "GGGG":
      default:
        return localize2.era(era, { width: "wide" });
    }
  },
  // Year
  y: function(date, token, localize2) {
    if (token === "yo") {
      const signedYear = date.getFullYear();
      const year = signedYear > 0 ? signedYear : 1 - signedYear;
      return localize2.ordinalNumber(year, { unit: "year" });
    }
    return lightFormatters.y(date, token);
  },
  // Local week-numbering year
  Y: function(date, token, localize2, options) {
    const signedWeekYear = getWeekYear(date, options);
    const weekYear = signedWeekYear > 0 ? signedWeekYear : 1 - signedWeekYear;
    if (token === "YY") {
      const twoDigitYear = weekYear % 100;
      return addLeadingZeros(twoDigitYear, 2);
    }
    if (token === "Yo") {
      return localize2.ordinalNumber(weekYear, { unit: "year" });
    }
    return addLeadingZeros(weekYear, token.length);
  },
  // ISO week-numbering year
  R: function(date, token) {
    const isoWeekYear = getISOWeekYear(date);
    return addLeadingZeros(isoWeekYear, token.length);
  },
  // Extended year. This is a single number designating the year of this calendar system.
  // The main difference between `y` and `u` localizers are B.C. years:
  // | Year | `y` | `u` |
  // |------|-----|-----|
  // | AC 1 |   1 |   1 |
  // | BC 1 |   1 |   0 |
  // | BC 2 |   2 |  -1 |
  // Also `yy` always returns the last two digits of a year,
  // while `uu` pads single digit years to 2 characters and returns other years unchanged.
  u: function(date, token) {
    const year = date.getFullYear();
    return addLeadingZeros(year, token.length);
  },
  // Quarter
  Q: function(date, token, localize2) {
    const quarter = Math.ceil((date.getMonth() + 1) / 3);
    switch (token) {
      case "Q":
        return String(quarter);
      case "QQ":
        return addLeadingZeros(quarter, 2);
      case "Qo":
        return localize2.ordinalNumber(quarter, { unit: "quarter" });
      case "QQQ":
        return localize2.quarter(quarter, {
          width: "abbreviated",
          context: "formatting"
        });
      case "QQQQQ":
        return localize2.quarter(quarter, {
          width: "narrow",
          context: "formatting"
        });
      case "QQQQ":
      default:
        return localize2.quarter(quarter, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  // Stand-alone quarter
  q: function(date, token, localize2) {
    const quarter = Math.ceil((date.getMonth() + 1) / 3);
    switch (token) {
      case "q":
        return String(quarter);
      case "qq":
        return addLeadingZeros(quarter, 2);
      case "qo":
        return localize2.ordinalNumber(quarter, { unit: "quarter" });
      case "qqq":
        return localize2.quarter(quarter, {
          width: "abbreviated",
          context: "standalone"
        });
      case "qqqqq":
        return localize2.quarter(quarter, {
          width: "narrow",
          context: "standalone"
        });
      case "qqqq":
      default:
        return localize2.quarter(quarter, {
          width: "wide",
          context: "standalone"
        });
    }
  },
  // Month
  M: function(date, token, localize2) {
    const month = date.getMonth();
    switch (token) {
      case "M":
      case "MM":
        return lightFormatters.M(date, token);
      case "Mo":
        return localize2.ordinalNumber(month + 1, { unit: "month" });
      case "MMM":
        return localize2.month(month, {
          width: "abbreviated",
          context: "formatting"
        });
      case "MMMMM":
        return localize2.month(month, {
          width: "narrow",
          context: "formatting"
        });
      case "MMMM":
      default:
        return localize2.month(month, { width: "wide", context: "formatting" });
    }
  },
  // Stand-alone month
  L: function(date, token, localize2) {
    const month = date.getMonth();
    switch (token) {
      case "L":
        return String(month + 1);
      case "LL":
        return addLeadingZeros(month + 1, 2);
      case "Lo":
        return localize2.ordinalNumber(month + 1, { unit: "month" });
      case "LLL":
        return localize2.month(month, {
          width: "abbreviated",
          context: "standalone"
        });
      case "LLLLL":
        return localize2.month(month, {
          width: "narrow",
          context: "standalone"
        });
      case "LLLL":
      default:
        return localize2.month(month, { width: "wide", context: "standalone" });
    }
  },
  // Local week of year
  w: function(date, token, localize2, options) {
    const week = getWeek(date, options);
    if (token === "wo") {
      return localize2.ordinalNumber(week, { unit: "week" });
    }
    return addLeadingZeros(week, token.length);
  },
  // ISO week of year
  I: function(date, token, localize2) {
    const isoWeek = getISOWeek(date);
    if (token === "Io") {
      return localize2.ordinalNumber(isoWeek, { unit: "week" });
    }
    return addLeadingZeros(isoWeek, token.length);
  },
  // Day of the month
  d: function(date, token, localize2) {
    if (token === "do") {
      return localize2.ordinalNumber(date.getDate(), { unit: "date" });
    }
    return lightFormatters.d(date, token);
  },
  // Day of year
  D: function(date, token, localize2) {
    const dayOfYear = getDayOfYear(date);
    if (token === "Do") {
      return localize2.ordinalNumber(dayOfYear, { unit: "dayOfYear" });
    }
    return addLeadingZeros(dayOfYear, token.length);
  },
  // Day of week
  E: function(date, token, localize2) {
    const dayOfWeek = date.getDay();
    switch (token) {
      case "E":
      case "EE":
      case "EEE":
        return localize2.day(dayOfWeek, {
          width: "abbreviated",
          context: "formatting"
        });
      case "EEEEE":
        return localize2.day(dayOfWeek, {
          width: "narrow",
          context: "formatting"
        });
      case "EEEEEE":
        return localize2.day(dayOfWeek, {
          width: "short",
          context: "formatting"
        });
      case "EEEE":
      default:
        return localize2.day(dayOfWeek, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  // Local day of week
  e: function(date, token, localize2, options) {
    const dayOfWeek = date.getDay();
    const localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;
    switch (token) {
      case "e":
        return String(localDayOfWeek);
      case "ee":
        return addLeadingZeros(localDayOfWeek, 2);
      case "eo":
        return localize2.ordinalNumber(localDayOfWeek, { unit: "day" });
      case "eee":
        return localize2.day(dayOfWeek, {
          width: "abbreviated",
          context: "formatting"
        });
      case "eeeee":
        return localize2.day(dayOfWeek, {
          width: "narrow",
          context: "formatting"
        });
      case "eeeeee":
        return localize2.day(dayOfWeek, {
          width: "short",
          context: "formatting"
        });
      case "eeee":
      default:
        return localize2.day(dayOfWeek, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  // Stand-alone local day of week
  c: function(date, token, localize2, options) {
    const dayOfWeek = date.getDay();
    const localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;
    switch (token) {
      case "c":
        return String(localDayOfWeek);
      case "cc":
        return addLeadingZeros(localDayOfWeek, token.length);
      case "co":
        return localize2.ordinalNumber(localDayOfWeek, { unit: "day" });
      case "ccc":
        return localize2.day(dayOfWeek, {
          width: "abbreviated",
          context: "standalone"
        });
      case "ccccc":
        return localize2.day(dayOfWeek, {
          width: "narrow",
          context: "standalone"
        });
      case "cccccc":
        return localize2.day(dayOfWeek, {
          width: "short",
          context: "standalone"
        });
      case "cccc":
      default:
        return localize2.day(dayOfWeek, {
          width: "wide",
          context: "standalone"
        });
    }
  },
  // ISO day of week
  i: function(date, token, localize2) {
    const dayOfWeek = date.getDay();
    const isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;
    switch (token) {
      case "i":
        return String(isoDayOfWeek);
      case "ii":
        return addLeadingZeros(isoDayOfWeek, token.length);
      case "io":
        return localize2.ordinalNumber(isoDayOfWeek, { unit: "day" });
      case "iii":
        return localize2.day(dayOfWeek, {
          width: "abbreviated",
          context: "formatting"
        });
      case "iiiii":
        return localize2.day(dayOfWeek, {
          width: "narrow",
          context: "formatting"
        });
      case "iiiiii":
        return localize2.day(dayOfWeek, {
          width: "short",
          context: "formatting"
        });
      case "iiii":
      default:
        return localize2.day(dayOfWeek, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  // AM or PM
  a: function(date, token, localize2) {
    const hours = date.getHours();
    const dayPeriodEnumValue = hours / 12 >= 1 ? "pm" : "am";
    switch (token) {
      case "a":
      case "aa":
        return localize2.dayPeriod(dayPeriodEnumValue, {
          width: "abbreviated",
          context: "formatting"
        });
      case "aaa":
        return localize2.dayPeriod(dayPeriodEnumValue, {
          width: "abbreviated",
          context: "formatting"
        }).toLowerCase();
      case "aaaaa":
        return localize2.dayPeriod(dayPeriodEnumValue, {
          width: "narrow",
          context: "formatting"
        });
      case "aaaa":
      default:
        return localize2.dayPeriod(dayPeriodEnumValue, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  // AM, PM, midnight, noon
  b: function(date, token, localize2) {
    const hours = date.getHours();
    let dayPeriodEnumValue;
    if (hours === 12) {
      dayPeriodEnumValue = dayPeriodEnum.noon;
    } else if (hours === 0) {
      dayPeriodEnumValue = dayPeriodEnum.midnight;
    } else {
      dayPeriodEnumValue = hours / 12 >= 1 ? "pm" : "am";
    }
    switch (token) {
      case "b":
      case "bb":
        return localize2.dayPeriod(dayPeriodEnumValue, {
          width: "abbreviated",
          context: "formatting"
        });
      case "bbb":
        return localize2.dayPeriod(dayPeriodEnumValue, {
          width: "abbreviated",
          context: "formatting"
        }).toLowerCase();
      case "bbbbb":
        return localize2.dayPeriod(dayPeriodEnumValue, {
          width: "narrow",
          context: "formatting"
        });
      case "bbbb":
      default:
        return localize2.dayPeriod(dayPeriodEnumValue, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  // in the morning, in the afternoon, in the evening, at night
  B: function(date, token, localize2) {
    const hours = date.getHours();
    let dayPeriodEnumValue;
    if (hours >= 17) {
      dayPeriodEnumValue = dayPeriodEnum.evening;
    } else if (hours >= 12) {
      dayPeriodEnumValue = dayPeriodEnum.afternoon;
    } else if (hours >= 4) {
      dayPeriodEnumValue = dayPeriodEnum.morning;
    } else {
      dayPeriodEnumValue = dayPeriodEnum.night;
    }
    switch (token) {
      case "B":
      case "BB":
      case "BBB":
        return localize2.dayPeriod(dayPeriodEnumValue, {
          width: "abbreviated",
          context: "formatting"
        });
      case "BBBBB":
        return localize2.dayPeriod(dayPeriodEnumValue, {
          width: "narrow",
          context: "formatting"
        });
      case "BBBB":
      default:
        return localize2.dayPeriod(dayPeriodEnumValue, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  // Hour [1-12]
  h: function(date, token, localize2) {
    if (token === "ho") {
      let hours = date.getHours() % 12;
      if (hours === 0)
        hours = 12;
      return localize2.ordinalNumber(hours, { unit: "hour" });
    }
    return lightFormatters.h(date, token);
  },
  // Hour [0-23]
  H: function(date, token, localize2) {
    if (token === "Ho") {
      return localize2.ordinalNumber(date.getHours(), { unit: "hour" });
    }
    return lightFormatters.H(date, token);
  },
  // Hour [0-11]
  K: function(date, token, localize2) {
    const hours = date.getHours() % 12;
    if (token === "Ko") {
      return localize2.ordinalNumber(hours, { unit: "hour" });
    }
    return addLeadingZeros(hours, token.length);
  },
  // Hour [1-24]
  k: function(date, token, localize2) {
    let hours = date.getHours();
    if (hours === 0)
      hours = 24;
    if (token === "ko") {
      return localize2.ordinalNumber(hours, { unit: "hour" });
    }
    return addLeadingZeros(hours, token.length);
  },
  // Minute
  m: function(date, token, localize2) {
    if (token === "mo") {
      return localize2.ordinalNumber(date.getMinutes(), { unit: "minute" });
    }
    return lightFormatters.m(date, token);
  },
  // Second
  s: function(date, token, localize2) {
    if (token === "so") {
      return localize2.ordinalNumber(date.getSeconds(), { unit: "second" });
    }
    return lightFormatters.s(date, token);
  },
  // Fraction of second
  S: function(date, token) {
    return lightFormatters.S(date, token);
  },
  // Timezone (ISO-8601. If offset is 0, output is always `'Z'`)
  X: function(date, token, _localize) {
    const timezoneOffset = date.getTimezoneOffset();
    if (timezoneOffset === 0) {
      return "Z";
    }
    switch (token) {
      case "X":
        return formatTimezoneWithOptionalMinutes(timezoneOffset);
      case "XXXX":
      case "XX":
        return formatTimezone(timezoneOffset);
      case "XXXXX":
      case "XXX":
      default:
        return formatTimezone(timezoneOffset, ":");
    }
  },
  // Timezone (ISO-8601. If offset is 0, output is `'+00:00'` or equivalent)
  x: function(date, token, _localize) {
    const timezoneOffset = date.getTimezoneOffset();
    switch (token) {
      case "x":
        return formatTimezoneWithOptionalMinutes(timezoneOffset);
      case "xxxx":
      case "xx":
        return formatTimezone(timezoneOffset);
      case "xxxxx":
      case "xxx":
      default:
        return formatTimezone(timezoneOffset, ":");
    }
  },
  // Timezone (GMT)
  O: function(date, token, _localize) {
    const timezoneOffset = date.getTimezoneOffset();
    switch (token) {
      case "O":
      case "OO":
      case "OOO":
        return "GMT" + formatTimezoneShort(timezoneOffset, ":");
      case "OOOO":
      default:
        return "GMT" + formatTimezone(timezoneOffset, ":");
    }
  },
  // Timezone (specific non-location)
  z: function(date, token, _localize) {
    const timezoneOffset = date.getTimezoneOffset();
    switch (token) {
      case "z":
      case "zz":
      case "zzz":
        return "GMT" + formatTimezoneShort(timezoneOffset, ":");
      case "zzzz":
      default:
        return "GMT" + formatTimezone(timezoneOffset, ":");
    }
  },
  // Seconds timestamp
  t: function(date, token, _localize) {
    const timestamp = Math.trunc(+date / 1e3);
    return addLeadingZeros(timestamp, token.length);
  },
  // Milliseconds timestamp
  T: function(date, token, _localize) {
    return addLeadingZeros(+date, token.length);
  }
};
function formatTimezoneShort(offset, delimiter = "") {
  const sign = offset > 0 ? "-" : "+";
  const absOffset = Math.abs(offset);
  const hours = Math.trunc(absOffset / 60);
  const minutes = absOffset % 60;
  if (minutes === 0) {
    return sign + String(hours);
  }
  return sign + String(hours) + delimiter + addLeadingZeros(minutes, 2);
}
function formatTimezoneWithOptionalMinutes(offset, delimiter) {
  if (offset % 60 === 0) {
    const sign = offset > 0 ? "-" : "+";
    return sign + addLeadingZeros(Math.abs(offset) / 60, 2);
  }
  return formatTimezone(offset, delimiter);
}
function formatTimezone(offset, delimiter = "") {
  const sign = offset > 0 ? "-" : "+";
  const absOffset = Math.abs(offset);
  const hours = addLeadingZeros(Math.trunc(absOffset / 60), 2);
  const minutes = addLeadingZeros(absOffset % 60, 2);
  return sign + hours + delimiter + minutes;
}

// node_modules/date-fns/_lib/format/longFormatters.js
var dateLongFormatter = (pattern, formatLong2) => {
  switch (pattern) {
    case "P":
      return formatLong2.date({ width: "short" });
    case "PP":
      return formatLong2.date({ width: "medium" });
    case "PPP":
      return formatLong2.date({ width: "long" });
    case "PPPP":
    default:
      return formatLong2.date({ width: "full" });
  }
};
var timeLongFormatter = (pattern, formatLong2) => {
  switch (pattern) {
    case "p":
      return formatLong2.time({ width: "short" });
    case "pp":
      return formatLong2.time({ width: "medium" });
    case "ppp":
      return formatLong2.time({ width: "long" });
    case "pppp":
    default:
      return formatLong2.time({ width: "full" });
  }
};
var dateTimeLongFormatter = (pattern, formatLong2) => {
  const matchResult = pattern.match(/(P+)(p+)?/) || [];
  const datePattern = matchResult[1];
  const timePattern = matchResult[2];
  if (!timePattern) {
    return dateLongFormatter(pattern, formatLong2);
  }
  let dateTimeFormat;
  switch (datePattern) {
    case "P":
      dateTimeFormat = formatLong2.dateTime({ width: "short" });
      break;
    case "PP":
      dateTimeFormat = formatLong2.dateTime({ width: "medium" });
      break;
    case "PPP":
      dateTimeFormat = formatLong2.dateTime({ width: "long" });
      break;
    case "PPPP":
    default:
      dateTimeFormat = formatLong2.dateTime({ width: "full" });
      break;
  }
  return dateTimeFormat.replace("{{date}}", dateLongFormatter(datePattern, formatLong2)).replace("{{time}}", timeLongFormatter(timePattern, formatLong2));
};
var longFormatters = {
  p: timeLongFormatter,
  P: dateTimeLongFormatter
};

// node_modules/date-fns/_lib/protectedTokens.js
var dayOfYearTokenRE = /^D+$/;
var weekYearTokenRE = /^Y+$/;
var throwTokens = ["D", "DD", "YY", "YYYY"];
function isProtectedDayOfYearToken(token) {
  return dayOfYearTokenRE.test(token);
}
function isProtectedWeekYearToken(token) {
  return weekYearTokenRE.test(token);
}
function warnOrThrowProtectedError(token, format2, input) {
  const _message = message(token, format2, input);
  console.warn(_message);
  if (throwTokens.includes(token))
    throw new RangeError(_message);
}
function message(token, format2, input) {
  const subject = token[0] === "Y" ? "years" : "days of the month";
  return `Use \`${token.toLowerCase()}\` instead of \`${token}\` (in \`${format2}\`) for formatting ${subject} to the input \`${input}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`;
}

// node_modules/date-fns/format.js
var formattingTokensRegExp = /[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g;
var longFormattingTokensRegExp = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;
var escapedStringRegExp = /^'([^]*?)'?$/;
var doubleQuoteRegExp = /''/g;
var unescapedLatinCharacterRegExp = /[a-zA-Z]/;
function format(date, formatStr, options) {
  const defaultOptions2 = getDefaultOptions();
  const locale = options?.locale ?? defaultOptions2.locale ?? enUS;
  const firstWeekContainsDate = options?.firstWeekContainsDate ?? options?.locale?.options?.firstWeekContainsDate ?? defaultOptions2.firstWeekContainsDate ?? defaultOptions2.locale?.options?.firstWeekContainsDate ?? 1;
  const weekStartsOn = options?.weekStartsOn ?? options?.locale?.options?.weekStartsOn ?? defaultOptions2.weekStartsOn ?? defaultOptions2.locale?.options?.weekStartsOn ?? 0;
  const originalDate = toDate(date, options?.in);
  if (!isValid(originalDate)) {
    throw new RangeError("Invalid time value");
  }
  let parts = formatStr.match(longFormattingTokensRegExp).map((substring) => {
    const firstCharacter = substring[0];
    if (firstCharacter === "p" || firstCharacter === "P") {
      const longFormatter = longFormatters[firstCharacter];
      return longFormatter(substring, locale.formatLong);
    }
    return substring;
  }).join("").match(formattingTokensRegExp).map((substring) => {
    if (substring === "''") {
      return { isToken: false, value: "'" };
    }
    const firstCharacter = substring[0];
    if (firstCharacter === "'") {
      return { isToken: false, value: cleanEscapedString(substring) };
    }
    if (formatters[firstCharacter]) {
      return { isToken: true, value: substring };
    }
    if (firstCharacter.match(unescapedLatinCharacterRegExp)) {
      throw new RangeError(
        "Format string contains an unescaped latin alphabet character `" + firstCharacter + "`"
      );
    }
    return { isToken: false, value: substring };
  });
  if (locale.localize.preprocessor) {
    parts = locale.localize.preprocessor(originalDate, parts);
  }
  const formatterOptions = {
    firstWeekContainsDate,
    weekStartsOn,
    locale
  };
  return parts.map((part) => {
    if (!part.isToken)
      return part.value;
    const token = part.value;
    if (!options?.useAdditionalWeekYearTokens && isProtectedWeekYearToken(token) || !options?.useAdditionalDayOfYearTokens && isProtectedDayOfYearToken(token)) {
      warnOrThrowProtectedError(token, formatStr, String(date));
    }
    const formatter = formatters[token[0]];
    return formatter(originalDate, token, locale.localize, formatterOptions);
  }).join("");
}
function cleanEscapedString(input) {
  const matched = input.match(escapedStringRegExp);
  if (!matched) {
    return input;
  }
  return matched[1].replace(doubleQuoteRegExp, "'");
}

// app/components/search/inputs/DateRangeInput.tsx
var import_jsx_dev_runtime2 = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/components/search/inputs/DateRangeInput.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/components/search/inputs/DateRangeInput.tsx"
  );
  import.meta.hot.lastModified = "1751032940168.562";
}
function DateRangeInput({
  value,
  onChange
}) {
  return /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "flex items-center gap-2", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)(
      "input",
      {
        type: "date",
        className: "border",
        placeholder: "Start Date",
        value: value.start ? format(value.start, "yyyy-MM-dd") : "",
        onChange: (e) => {
          const [year, month, day] = e.target.value.split("-").map(Number);
          const date = new Date(year, month - 1, day);
          if (!isNaN(date.getTime())) {
            onChange({
              ...value,
              start: date
            });
          } else {
            onChange({
              ...value,
              start: void 0
            });
          }
        },
        max: value.end ? format(value.end, "yyyy-MM-dd") : void 0
      },
      void 0,
      false,
      {
        fileName: "app/components/search/inputs/DateRangeInput.tsx",
        lineNumber: 27,
        columnNumber: 4
      },
      this
    ),
    /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("span", { children: "to" }, void 0, false, {
      fileName: "app/components/search/inputs/DateRangeInput.tsx",
      lineNumber: 43,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)(
      "input",
      {
        type: "date",
        className: "border",
        placeholder: "End Date",
        value: value.end ? format(value.end, "yyyy-MM-dd") : "",
        onChange: (e) => {
          const [year, month, day] = e.target.value.split("-").map(Number);
          const date = new Date(year, month - 1, day);
          if (!isNaN(date.getTime())) {
            onChange({
              ...value,
              end: date
            });
          } else {
            onChange({
              ...value,
              end: void 0
            });
          }
        },
        min: value.start ? format(value.start, "yyyy-MM-dd") : void 0
      },
      void 0,
      false,
      {
        fileName: "app/components/search/inputs/DateRangeInput.tsx",
        lineNumber: 44,
        columnNumber: 4
      },
      this
    )
  ] }, void 0, true, {
    fileName: "app/components/search/inputs/DateRangeInput.tsx",
    lineNumber: 26,
    columnNumber: 10
  }, this);
}
_c2 = DateRangeInput;
var _c2;
$RefreshReg$(_c2, "DateRangeInput");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;

// app/services/search/filters/DateRangeFilter.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/filters/DateRangeFilter.ts"
  );
  import.meta.hot.lastModified = "1751311132337.1782";
}
var DateRangeFilter = class extends BaseFilter {
  id = "date_range";
  name = "Date Range";
  field = "incident_date";
  permissions = [{ roles: ["user", "admin"] }];
  constructor() {
    super();
  }
  getOperators() {
    return [
      {
        value: "between",
        label: "Between",
        inputType: "daterange",
        component: DateRangeInput
      },
      {
        value: "this_year",
        label: "This Year",
        inputType: "none"
      },
      {
        value: "last_year",
        label: "Last Year",
        inputType: "none"
      },
      {
        value: "next_year",
        label: "Next Year",
        inputType: "none"
      }
    ];
  }
  getDefaultValue(operator) {
    switch (operator) {
      case "between":
        return { start: void 0, end: void 0 };
      case "this_year":
      case "last_year":
      case "next_year":
        return void 0;
      default:
        return void 0;
    }
  }
  formatValue(value, operator) {
    switch (operator) {
      case "between":
        if (!value?.start || !value?.end)
          return "";
        return `${format(value.start, "MMM d, yyyy")} - ${format(value.end, "MMM d, yyyy")}`;
      case "this_year":
        return "This Year";
      case "last_year":
        return "Last Year";
      case "next_year":
        return "Next Year";
      default:
        return "";
    }
  }
  /**
   * Get the conditions that should be applied to the query builder
   * @param value The filter value
   * @returns An array of conditions to apply
   */
  getConditions(value) {
    console.log("DateRangeFilter getConditions called with value:", JSON.stringify(value, null, 2));
    const conditions = [];
    switch (value.operator) {
      case "equals":
      case "between":
        const dateValue = value.value || value;
        if (dateValue.start) {
          const startDate = dateValue.start instanceof Date ? dateValue.start : new Date(dateValue.start);
          conditions.push({
            type: "standard",
            field: this.field,
            operator: "gte",
            value: startDate
          });
        }
        if (dateValue.end) {
          const endDate = dateValue.end instanceof Date ? dateValue.end : new Date(dateValue.end);
          conditions.push({
            type: "standard",
            field: this.field,
            operator: "lte",
            value: endDate
          });
        }
        break;
      case "this_year":
        conditions.push({
          type: "raw",
          sql: `EXTRACT(YEAR FROM ${this.field}) = EXTRACT(YEAR FROM CURRENT_DATE)`
        });
        break;
      case "last_year":
        conditions.push({
          type: "raw",
          sql: `EXTRACT(YEAR FROM ${this.field}) = EXTRACT(YEAR FROM CURRENT_DATE) - 1`
        });
        break;
      case "next_year":
        conditions.push({
          type: "raw",
          sql: `EXTRACT(YEAR FROM ${this.field}) = EXTRACT(YEAR FROM CURRENT_DATE) + 1`
        });
        break;
      default:
        console.warn(`DateRangeFilter: Unhandled operator "${value.operator}"`);
        break;
    }
    return conditions;
  }
  validate(value) {
    if (value.operator === "between" || value.operator === "equals") {
      const dateValue = value.value || value;
      return dateValue && (dateValue.start || dateValue.end);
    }
    return ["this_year", "last_year", "next_year"].includes(value.operator);
  }
};

// app/components/search/inputs/StatusInput.tsx
var import_jsx_dev_runtime3 = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/components/search/inputs/StatusInput.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/components/search/inputs/StatusInput.tsx"
  );
  import.meta.hot.lastModified = "1750252761865.0645";
}
function StatusInput({
  value,
  onChange,
  operator,
  filter
}) {
  if (!filter)
    return null;
  const operators = filter.getOperators();
  const currentOperator = operators.find((op) => op.value === operator);
  if (!currentOperator || !currentOperator.options)
    return null;
  return /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: "flex flex-col gap-2", children: currentOperator.options.map((option) => /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("label", { className: "flex items-center gap-2", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("input", { type: "radio", value: option.value, checked: value === option.value, onChange: () => onChange(option.value) }, void 0, false, {
      fileName: "app/components/search/inputs/StatusInput.tsx",
      lineNumber: 33,
      columnNumber: 6
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("div", { className: "flex items-center gap-2", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("span", { className: `size-4 rounded-full ${getStatusColor(option.value)}` }, void 0, false, {
        fileName: "app/components/search/inputs/StatusInput.tsx",
        lineNumber: 35,
        columnNumber: 7
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime3.jsxDEV)("span", { children: option.label }, void 0, false, {
        fileName: "app/components/search/inputs/StatusInput.tsx",
        lineNumber: 36,
        columnNumber: 7
      }, this)
    ] }, void 0, true, {
      fileName: "app/components/search/inputs/StatusInput.tsx",
      lineNumber: 34,
      columnNumber: 6
    }, this)
  ] }, option.value, true, {
    fileName: "app/components/search/inputs/StatusInput.tsx",
    lineNumber: 32,
    columnNumber: 43
  }, this)) }, void 0, false, {
    fileName: "app/components/search/inputs/StatusInput.tsx",
    lineNumber: 31,
    columnNumber: 10
  }, this);
}
_c3 = StatusInput;
function getStatusColor(status) {
  switch (status) {
    case "active":
      return "bg-green-500";
    case "pending":
      return "bg-yellow-500";
    case "closed":
      return "bg-blue-500";
    case "archived":
      return "bg-gray-600";
    default:
      return "bg-gray-500";
  }
}
var _c3;
$RefreshReg$(_c3, "StatusInput");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;

// app/services/search/filters/StatusFilter.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/filters/StatusFilter.ts"
  );
  import.meta.hot.lastModified = "1751311374839.876";
}
var StatusFilter = class extends BaseFilter {
  id = "status";
  name = "Status";
  field = "status";
  permissions = [{ roles: ["user", "admin"] }];
  constructor() {
    super();
  }
  getOperators() {
    return [
      {
        value: "equals",
        label: "Is",
        inputType: "radio",
        component: StatusInput,
        options: [
          { value: "active", label: "Active" },
          { value: "pending", label: "Pending" },
          { value: "closed", label: "Closed" },
          { value: "archived", label: "Archived" }
        ]
      },
      {
        value: "not",
        label: "Is not",
        inputType: "radio",
        component: StatusInput,
        options: [
          { value: "active", label: "Active" },
          { value: "pending", label: "Pending" },
          { value: "closed", label: "Closed" },
          { value: "archived", label: "Archived" }
        ]
      }
    ];
  }
  getDefaultValue(operator) {
    return "";
  }
  formatValue(value, operator) {
    return value.charAt(0).toUpperCase() + value.slice(1);
  }
  apply(builder, value) {
    const operator = value.operator || "equals";
    switch (operator) {
      case "equals":
        builder.where(this.field, "equals", value.value);
        break;
      case "not":
        builder.where(this.field, "not", value.value);
        break;
      default:
        builder.where(this.field, "equals", value.value);
        break;
    }
  }
  validate(value) {
    if (!value.value)
      return false;
    const operator = value.operator || "equals";
    const validOperators = ["equals", "not"];
    const validStatuses = ["active", "pending", "closed", "archived"];
    return validOperators.includes(operator) && validStatuses.includes(value.value);
  }
};

// app/components/search/inputs/LocationInput.tsx
var import_react = __toESM(require_react(), 1);
var import_jsx_dev_runtime4 = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/components/search/inputs/LocationInput.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/components/search/inputs/LocationInput.tsx"
  );
  import.meta.hot.lastModified = "1750252761864.5889";
}
function LocationInput({
  value,
  onChange,
  operator,
  filter
}) {
  _s();
  const [inputValue, setInputValue] = (0, import_react.useState)(value || "");
  const [suggestions, setSuggestions] = (0, import_react.useState)([]);
  const [showSuggestions, setShowSuggestions] = (0, import_react.useState)(false);
  const [highlightedIndex, setHighlightedIndex] = (0, import_react.useState)(-1);
  const sampleLocations = ["New York, NY", "Los Angeles, CA", "Chicago, IL", "Houston, TX", "Phoenix, AZ", "Philadelphia, PA", "San Antonio, TX", "San Diego, CA", "Dallas, TX", "San Jose, CA", "Austin, TX", "Jacksonville, FL", "Fort Worth, TX", "Columbus, OH", "Charlotte, NC", "San Francisco, CA", "Indianapolis, IN", "Seattle, WA", "Denver, CO", "Washington, DC"];
  (0, import_react.useEffect)(() => {
    if (inputValue.length > 1) {
      const filtered = sampleLocations.filter((location) => location.toLowerCase().includes(inputValue.toLowerCase()));
      setSuggestions(filtered.slice(0, 5));
      setShowSuggestions(true);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  }, [inputValue]);
  (0, import_react.useEffect)(() => {
    setInputValue(value || "");
  }, [operator]);
  const handleInputChange = (e) => {
    setInputValue(e.target.value);
  };
  const handleSuggestionClick = (suggestion) => {
    setInputValue(suggestion);
    onChange(suggestion);
    setShowSuggestions(false);
  };
  const handleBlur = () => {
    setTimeout(() => {
      setShowSuggestions(false);
      onChange(inputValue);
    }, 200);
  };
  const handleKeyDown = (e) => {
    if (e.key === "ArrowDown") {
      setHighlightedIndex((prev) => Math.min(prev + 1, suggestions.length - 1));
    } else if (e.key === "ArrowUp") {
      setHighlightedIndex((prev) => Math.max(prev - 1, 0));
    } else if (e.key === "Enter" && highlightedIndex >= 0) {
      handleSuggestionClick(suggestions[highlightedIndex]);
    } else if (e.key === "Escape") {
      setShowSuggestions(false);
    }
  };
  return /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("div", { className: "relative w-full", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("input", { type: "text", value: inputValue, onChange: handleInputChange, onBlur: handleBlur, onKeyDown: handleKeyDown, onFocus: () => inputValue.length > 1 && suggestions.length > 0 && setShowSuggestions(true), className: "w-full border", placeholder: "Enter location..." }, void 0, false, {
      fileName: "app/components/search/inputs/LocationInput.tsx",
      lineNumber: 91,
      columnNumber: 4
    }, this),
    showSuggestions && suggestions.length > 0 && /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("ul", { className: "absolute mt-1 max-h-60 w-full overflow-y-auto rounded border bg-white shadow-md", children: suggestions.map((suggestion, index) => /* @__PURE__ */ (0, import_jsx_dev_runtime4.jsxDEV)("li", { className: `p-2 sm:px-3 lg:px-5 hover:bg-gray-100 cursor-pointer ${highlightedIndex === index ? "bg-gray-100" : ""}`, onClick: () => handleSuggestionClick(suggestion), onMouseDown: () => handleSuggestionClick(suggestion), children: suggestion }, `suggestion-${index}`, false, {
      fileName: "app/components/search/inputs/LocationInput.tsx",
      lineNumber: 94,
      columnNumber: 46
    }, this)) }, void 0, false, {
      fileName: "app/components/search/inputs/LocationInput.tsx",
      lineNumber: 93,
      columnNumber: 50
    }, this)
  ] }, void 0, true, {
    fileName: "app/components/search/inputs/LocationInput.tsx",
    lineNumber: 90,
    columnNumber: 10
  }, this);
}
_s(LocationInput, "ypa3OC54oOKVWObS4FUROSK1LYQ=");
_c4 = LocationInput;
var _c4;
$RefreshReg$(_c4, "LocationInput");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;

// app/services/search/filters/LocationFilter.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/filters/LocationFilter.ts"
  );
  import.meta.hot.lastModified = "1751311114009.8";
}
var LocationFilter = class extends BaseFilter {
  id = "location";
  name = "Location";
  field = "city_or_county";
  permissions = [{ roles: ["user", "admin"] }];
  constructor() {
    super();
  }
  getOperators() {
    return [
      {
        value: "equals",
        label: "Is",
        inputType: "autocomplete",
        component: LocationInput
      },
      {
        value: "contains",
        label: "Contains",
        inputType: "autocomplete",
        component: LocationInput
      },
      {
        value: "not",
        label: "Is not",
        inputType: "autocomplete",
        component: LocationInput
      },
      {
        value: "dwithin",
        label: "Within distance",
        inputType: "object",
        component: LocationInput
      }
    ];
  }
  getDefaultValue(operator) {
    if (operator === "dwithin") {
      return { point: "", distance: 1e3 };
    }
    return "";
  }
  formatValue(value, operator) {
    if (operator === "dwithin") {
      if (typeof value === "object" && value !== null) {
        return value;
      }
      return { point: value, distance: 1e3 };
    }
    return value;
  }
  apply(builder, value) {
    console.log("LocationFilter apply called with value:", JSON.stringify(value, null, 2));
    switch (value.operator) {
      case "equals":
        if (typeof value.value === "object" && value.value !== null) {
          if ("state" in value.value) {
            builder.where("state", "equals", value.value.state);
            if (value.value.city) {
              builder.where(this.field, "equals", value.value.city);
            }
          } else {
            builder.where(this.field, "equals", value.value);
          }
        } else {
          builder.where(this.field, "equals", value.value);
        }
        break;
      case "contains":
        if (typeof value.value === "object" && value.value !== null && "state" in value.value) {
          builder.where("state", "contains", value.value.state);
          if (value.value.city) {
            builder.where(this.field, "contains", value.value.city);
          }
        } else {
          builder.where(this.field, "contains", value.value);
        }
        break;
      case "not":
        if (typeof value.value === "object" && value.value !== null && "state" in value.value) {
          builder.where("state", "not", value.value.state);
          if (value.value.city) {
            builder.where(this.field, "not", value.value.city);
          }
        } else {
          builder.where(this.field, "not", value.value);
        }
        break;
      case "dwithin":
        if (typeof value.value === "object" && value.value !== null && "point" in value.value && "distance" in value.value) {
          builder.where(this.field, "dwithin", value.value);
        } else if (typeof value.value === "string") {
          builder.where(this.field, "dwithin", {
            point: { type: "geography", value: value.value },
            distance: 1e3
            // Default 1000 meters
          });
        }
        break;
    }
  }
  validate(value) {
    if (!value.operator || value.value === void 0)
      return false;
    const validOperators = ["equals", "contains", "not", "dwithin"];
    if (!validOperators.includes(value.operator))
      return false;
    if (value.operator === "dwithin") {
      if (typeof value.value === "object" && value.value !== null) {
        return "point" in value.value && "distance" in value.value;
      }
      return typeof value.value === "string";
    }
    if (typeof value.value === "object" && value.value !== null) {
      return "state" in value.value && value.value.state !== null;
    }
    return typeof value.value === "string";
  }
};

// app/components/search/inputs/VictimsInput.tsx
var import_react2 = __toESM(require_react(), 1);
var import_jsx_dev_runtime5 = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/components/search/inputs/VictimsInput.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s2 = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/components/search/inputs/VictimsInput.tsx"
  );
  import.meta.hot.lastModified = "1750252761865.3713";
}
function VictimsInput({
  value,
  onChange,
  operator,
  filter
}) {
  if (operator === "between") {
    return /* @__PURE__ */ (0, import_jsx_dev_runtime5.jsxDEV)(VictimsRangeInput, { value, onChange, operator, filter }, void 0, false, {
      fileName: "app/components/search/inputs/VictimsInput.tsx",
      lineNumber: 30,
      columnNumber: 12
    }, this);
  }
  const handleChange = (e) => {
    onChange(e.target.value);
  };
  return /* @__PURE__ */ (0, import_jsx_dev_runtime5.jsxDEV)("input", { type: "number", min: "0", value, onChange: handleChange, className: "w-full border" }, void 0, false, {
    fileName: "app/components/search/inputs/VictimsInput.tsx",
    lineNumber: 35,
    columnNumber: 10
  }, this);
}
_c5 = VictimsInput;
var VictimsRangeInput = ({
  value,
  onChange,
  operator,
  filter
}) => {
  _s2();
  const [minValue, setMinValue] = (0, import_react2.useState)(1);
  const [maxValue, setMaxValue] = (0, import_react2.useState)(10);
  (0, import_react2.useEffect)(() => {
    if (value && typeof value === "string" && value.includes(",")) {
      const [min, max] = value.split(",").map((v) => parseInt(v.trim(), 10));
      if (!isNaN(min))
        setMinValue(min);
      if (!isNaN(max))
        setMaxValue(max);
    }
  }, []);
  (0, import_react2.useEffect)(() => {
    onChange(`${minValue},${maxValue}`);
  }, [minValue, maxValue]);
  return /* @__PURE__ */ (0, import_jsx_dev_runtime5.jsxDEV)("div", { className: "flex flex-col", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime5.jsxDEV)("div", { className: "flex items-center justify-between", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime5.jsxDEV)("span", { children: [
        "Min: ",
        minValue
      ] }, void 0, true, {
        fileName: "app/components/search/inputs/VictimsInput.tsx",
        lineNumber: 64,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime5.jsxDEV)("span", { children: [
        "Max: ",
        maxValue
      ] }, void 0, true, {
        fileName: "app/components/search/inputs/VictimsInput.tsx",
        lineNumber: 65,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/components/search/inputs/VictimsInput.tsx",
      lineNumber: 63,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime5.jsxDEV)("div", { className: "flex items-center gap-5", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime5.jsxDEV)("input", { type: "range", min: "0", max: "50", value: minValue, onChange: (e) => {
        const newMin = parseInt(e.target.value, 10);
        setMinValue(newMin > maxValue ? maxValue : newMin);
      }, className: "p-0" }, void 0, false, {
        fileName: "app/components/search/inputs/VictimsInput.tsx",
        lineNumber: 69,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime5.jsxDEV)("input", { type: "range", min: "0", max: "50", value: maxValue, onChange: (e) => {
        const newMax = parseInt(e.target.value, 10);
        setMaxValue(newMax < minValue ? minValue : newMax);
      }, className: "p-0" }, void 0, false, {
        fileName: "app/components/search/inputs/VictimsInput.tsx",
        lineNumber: 73,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/components/search/inputs/VictimsInput.tsx",
      lineNumber: 68,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime5.jsxDEV)("div", { className: "flex items-center justify-center gap-2", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime5.jsxDEV)("input", { type: "number", min: "0", max: maxValue, value: minValue, onChange: (e) => {
        const newMin = parseInt(e.target.value, 10);
        if (!isNaN(newMin)) {
          setMinValue(newMin > maxValue ? maxValue : newMin);
        }
      }, className: "w-20 border text-center" }, void 0, false, {
        fileName: "app/components/search/inputs/VictimsInput.tsx",
        lineNumber: 80,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime5.jsxDEV)("span", { children: "to" }, void 0, false, {
        fileName: "app/components/search/inputs/VictimsInput.tsx",
        lineNumber: 86,
        columnNumber: 5
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime5.jsxDEV)("input", { type: "number", min: minValue, value: maxValue, onChange: (e) => {
        const newMax = parseInt(e.target.value, 10);
        if (!isNaN(newMax)) {
          setMaxValue(newMax < minValue ? minValue : newMax);
        }
      }, className: "w-20 border text-center" }, void 0, false, {
        fileName: "app/components/search/inputs/VictimsInput.tsx",
        lineNumber: 87,
        columnNumber: 5
      }, this)
    ] }, void 0, true, {
      fileName: "app/components/search/inputs/VictimsInput.tsx",
      lineNumber: 79,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/components/search/inputs/VictimsInput.tsx",
    lineNumber: 62,
    columnNumber: 10
  }, this);
};
_s2(VictimsRangeInput, "ndoeto0wP2lGp6rrl11z4oSO5Yk=");
_c22 = VictimsRangeInput;
var _c5;
var _c22;
$RefreshReg$(_c5, "VictimsInput");
$RefreshReg$(_c22, "VictimsRangeInput");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;

// app/services/search/filters/VictimsFilter.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/filters/VictimsFilter.ts"
  );
  import.meta.hot.lastModified = "1751311374836.718";
}
var VictimsFilter = class extends BaseFilter {
  id = "victims";
  name = "Victims";
  field = "victims_total";
  permissions = [{ roles: ["user", "admin"] }];
  constructor() {
    super();
  }
  getOperators() {
    return [
      {
        value: "equals",
        label: "Equals",
        inputType: "text",
        component: VictimsInput
      },
      {
        value: "gt",
        label: "Greater than",
        inputType: "text",
        component: VictimsInput
      },
      {
        value: "lt",
        label: "Less than",
        inputType: "text",
        component: VictimsInput
      },
      {
        value: "between",
        label: "Between",
        inputType: "daterange",
        component: VictimsInput
      }
    ];
  }
  getDefaultValue(operator) {
    return operator === "between" ? "1,10" : "1";
  }
  formatValue(value, operator) {
    if (operator === "between" && value.includes(",")) {
      const [min, max] = value.split(",");
      return `${min} - ${max}`;
    }
    return value;
  }
  apply(builder, value) {
    switch (value.operator) {
      case "equals":
        builder.where(this.field, "equals", parseInt(value.value, 10));
        break;
      case "gt":
        builder.where(this.field, "gt", parseInt(value.value, 10));
        break;
      case "lt":
        builder.where(this.field, "lt", parseInt(value.value, 10));
        break;
      case "between":
        if (value.value && value.value.includes(",")) {
          const [min, max] = value.value.split(",").map((v) => parseInt(v.trim(), 10));
          builder.where(this.field, "gte", min);
          builder.where(this.field, "lte", max);
        }
        break;
    }
  }
  validate(value) {
    if (!value.operator || !value.value)
      return false;
    const validOperators = ["equals", "gt", "lt", "between"];
    if (!validOperators.includes(value.operator))
      return false;
    if (value.operator === "between") {
      if (!value.value.includes(","))
        return false;
      const [min, max] = value.value.split(",").map((v) => parseInt(v.trim(), 10));
      return !isNaN(min) && !isNaN(max) && min <= max;
    }
    return !isNaN(parseInt(value.value, 10));
  }
};

// app/components/search/inputs/IncidentTypeInput.tsx
var import_jsx_dev_runtime6 = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/components/search/inputs/IncidentTypeInput.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/components/search/inputs/IncidentTypeInput.tsx"
  );
  import.meta.hot.lastModified = "1750252761864.3442";
}
function IncidentTypeInput({
  value,
  onChange,
  operator,
  filter
}) {
  if (!filter)
    return null;
  const operators = filter.getOperators();
  const currentOperator = operators.find((op) => op.value === operator);
  if (!currentOperator || !currentOperator.options)
    return null;
  if (operator === "in") {
    const selectedValues = Array.isArray(value) ? value : [value];
    return /* @__PURE__ */ (0, import_jsx_dev_runtime6.jsxDEV)("div", { className: "flex max-h-60 flex-col gap-2 overflow-y-auto rounded border", children: currentOperator.options.map((option) => /* @__PURE__ */ (0, import_jsx_dev_runtime6.jsxDEV)("label", { className: "flex items-center gap-2", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime6.jsxDEV)("input", { type: "checkbox", value: option.value, checked: selectedValues.includes(option.value), onChange: (e) => {
        if (e.target.checked) {
          onChange([...selectedValues, option.value]);
        } else {
          onChange(selectedValues.filter((v) => v !== option.value));
        }
      } }, void 0, false, {
        fileName: "app/components/search/inputs/IncidentTypeInput.tsx",
        lineNumber: 37,
        columnNumber: 7
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime6.jsxDEV)("span", { children: option.label }, void 0, false, {
        fileName: "app/components/search/inputs/IncidentTypeInput.tsx",
        lineNumber: 44,
        columnNumber: 7
      }, this)
    ] }, option.value, true, {
      fileName: "app/components/search/inputs/IncidentTypeInput.tsx",
      lineNumber: 36,
      columnNumber: 44
    }, this)) }, void 0, false, {
      fileName: "app/components/search/inputs/IncidentTypeInput.tsx",
      lineNumber: 35,
      columnNumber: 12
    }, this);
  }
  return /* @__PURE__ */ (0, import_jsx_dev_runtime6.jsxDEV)("div", { className: "flex max-h-60 flex-col gap-2 overflow-y-auto rounded border", children: currentOperator.options.map((option) => /* @__PURE__ */ (0, import_jsx_dev_runtime6.jsxDEV)("label", { className: "flex items-center gap-2", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime6.jsxDEV)("input", { type: "radio", value: option.value, checked: value === option.value, onChange: () => onChange(option.value) }, void 0, false, {
      fileName: "app/components/search/inputs/IncidentTypeInput.tsx",
      lineNumber: 52,
      columnNumber: 6
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime6.jsxDEV)("span", { children: option.label }, void 0, false, {
      fileName: "app/components/search/inputs/IncidentTypeInput.tsx",
      lineNumber: 53,
      columnNumber: 6
    }, this)
  ] }, option.value, true, {
    fileName: "app/components/search/inputs/IncidentTypeInput.tsx",
    lineNumber: 51,
    columnNumber: 43
  }, this)) }, void 0, false, {
    fileName: "app/components/search/inputs/IncidentTypeInput.tsx",
    lineNumber: 50,
    columnNumber: 10
  }, this);
}
_c6 = IncidentTypeInput;
var _c6;
$RefreshReg$(_c6, "IncidentTypeInput");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;

// app/services/search/filters/IncidentTypeFilter.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/filters/IncidentTypeFilter.ts"
  );
  import.meta.hot.lastModified = "1751311374829.5132";
}
var IncidentTypeFilter = class extends BaseFilter {
  id = "incident_type";
  name = "Incident Type";
  field = "incident_type";
  permissions = [{ roles: ["user", "admin"] }];
  constructor() {
    super();
  }
  getOperators() {
    const incidentTypeOptions = [
      { value: "mass_shooting", label: "Mass Shooting" },
      { value: "domestic_violence", label: "Domestic Violence" },
      { value: "officer_involved", label: "Officer Involved" },
      { value: "defensive_use", label: "Defensive Use" },
      { value: "unintentional", label: "Unintentional" },
      { value: "suicide", label: "Suicide" },
      { value: "other", label: "Other" }
    ];
    return [
      {
        value: "equals",
        label: "Is",
        inputType: "radio",
        component: IncidentTypeInput,
        options: incidentTypeOptions
      },
      {
        value: "not",
        label: "Is not",
        inputType: "radio",
        component: IncidentTypeInput,
        options: incidentTypeOptions
      },
      {
        value: "in",
        label: "Is one of",
        inputType: "select",
        component: IncidentTypeInput,
        options: incidentTypeOptions
      }
    ];
  }
  getDefaultValue(operator) {
    return operator === "in" ? ["mass_shooting"] : "mass_shooting";
  }
  formatValue(value, operator) {
    if (operator === "in" && Array.isArray(value)) {
      return value.map((v) => this.getOptionLabel(v)).join(", ");
    }
    return this.getOptionLabel(value);
  }
  getOptionLabel(value) {
    const options = this.getOperators()[0].options;
    if (!options)
      return value;
    const option = options.find((opt) => opt.value === value);
    return option ? option.label : value;
  }
  apply(builder, value) {
    switch (value.operator) {
      case "equals":
        builder.where(this.field, "equals", value.value);
        break;
      case "not":
        builder.where(this.field, "not", value.value);
        break;
      case "in":
        if (Array.isArray(value.value)) {
          builder.where(this.field, "in", value.value);
        } else {
          builder.where(this.field, "equals", value.value);
        }
        break;
    }
  }
  validate(value) {
    if (!value.operator)
      return false;
    const validOperators = ["equals", "not", "in"];
    const validTypes = [
      "mass_shooting",
      "domestic_violence",
      "officer_involved",
      "defensive_use",
      "unintentional",
      "suicide",
      "other"
    ];
    if (!validOperators.includes(value.operator))
      return false;
    if (value.operator === "in") {
      if (!Array.isArray(value.value) || value.value.length === 0)
        return false;
      return value.value.every((v) => validTypes.includes(v));
    }
    return validTypes.includes(value.value);
  }
};

// app/services/search/filters/AddressFilter.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/filters/AddressFilter.ts"
  );
  import.meta.hot.lastModified = "1751311375055.5647";
}
var AddressFilter = class extends BaseFilter {
  id = "address";
  name = "Address";
  field = "address";
  permissions = [{ roles: ["user", "admin"] }];
  constructor() {
    super();
  }
  /** Use default input component
  getFormComponent(): ComponentType<FilterFormProps> {
  	return AddressFilterForm;
  }
  */
  handleSubmit(value) {
    return {
      operator: value.operator,
      value: value.address
    };
  }
  getOperators() {
    return [
      {
        value: "equals",
        label: "Is",
        inputType: "text",
        component: DefaultFilterInput
      },
      {
        value: "contains",
        label: "Contains",
        inputType: "text",
        component: DefaultFilterInput
      },
      {
        value: "not",
        label: "Is not",
        inputType: "text",
        component: DefaultFilterInput
      },
      {
        value: "starts_with",
        label: "Starts with",
        inputType: "text",
        component: DefaultFilterInput
      },
      {
        value: "ends_with",
        label: "Ends with",
        inputType: "text",
        component: DefaultFilterInput
      }
    ];
  }
  getDefaultValue(operator) {
    return "";
  }
  formatValue(value, operator) {
    return value;
  }
  apply(builder, value) {
    const filterValue = value.value !== null && value.value !== void 0 ? value.value : "";
    switch (value.operator) {
      case "equals":
        builder.where(this.field, "equals", filterValue);
        break;
      case "contains":
        builder.where(this.field, "contains", filterValue);
        break;
      case "not":
        builder.where(this.field, "not", filterValue);
        break;
      case "starts_with":
        builder.whereRaw(`${this.field} LIKE '${filterValue}%'`);
        break;
      case "ends_with":
        builder.whereRaw(`${this.field} LIKE '%${filterValue}'`);
        break;
    }
  }
  validate(value) {
    if (!value.operator || !value.value)
      return false;
    const validOperators = ["equals", "contains", "not", "starts_with", "ends_with"];
    return validOperators.includes(value.operator) && typeof value.value === "string";
  }
};

// app/services/search/filters/NotesFilter.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/filters/NotesFilter.ts"
  );
  import.meta.hot.lastModified = "1751311114016.3044";
}
var NotesFilter = class extends BaseFilter {
  id = "notes";
  name = "Notes";
  field = "notes";
  permissions = [{ roles: ["user", "admin"] }];
  constructor() {
    super();
  }
  getOperators() {
    return [
      {
        value: "contains",
        label: "Contains",
        inputType: "text",
        component: DefaultFilterInput
      },
      {
        value: "not_contains",
        label: "Does not contain",
        inputType: "text",
        component: DefaultFilterInput
      },
      {
        value: "is_empty",
        label: "Is empty",
        inputType: "none"
      },
      {
        value: "is_not_empty",
        label: "Is not empty",
        inputType: "none"
      }
    ];
  }
  getDefaultValue(operator) {
    if (operator === "is_empty" || operator === "is_not_empty") {
      return void 0;
    }
    return "";
  }
  formatValue(value, operator) {
    if (operator === "is_empty") {
      return "Is empty";
    }
    if (operator === "is_not_empty") {
      return "Is not empty";
    }
    return value || "";
  }
  apply(builder, value) {
    const filterValue = value.value !== null && value.value !== void 0 ? value.value : "";
    switch (value.operator) {
      case "contains":
        builder.where(this.field, "contains", filterValue);
        break;
      case "not_contains":
        builder.whereRaw(`${this.field} NOT ILIKE '%${filterValue}%'`);
        break;
      case "is_empty":
        builder.whereRaw(`(${this.field} IS NULL OR ${this.field} = '')`);
        break;
      case "is_not_empty":
        builder.whereRaw(`(${this.field} IS NOT NULL AND ${this.field} != '')`);
        break;
    }
  }
  validate(value) {
    if (!value.operator)
      return false;
    const validOperators = ["contains", "not_contains", "is_empty", "is_not_empty"];
    if (!validOperators.includes(value.operator))
      return false;
    if (["contains", "not_contains"].includes(value.operator)) {
      return typeof value.value === "string" && value.value.length > 0;
    }
    return true;
  }
};

// app/services/search/filters/BusinessFilter.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/filters/BusinessFilter.ts"
  );
  import.meta.hot.lastModified = "1751311114019.5974";
}
var BusinessFilter = class extends BaseFilter {
  id = "business";
  name = "Business";
  field = "business";
  permissions = [{ roles: ["user", "admin"] }];
  constructor() {
    super();
  }
  getOperators() {
    return [
      {
        value: "equals",
        label: "Is",
        inputType: "text",
        component: DefaultFilterInput
      },
      {
        value: "contains",
        label: "Contains",
        inputType: "text",
        component: DefaultFilterInput
      },
      {
        value: "not",
        label: "Is not",
        inputType: "text",
        component: DefaultFilterInput
      },
      {
        value: "is_empty",
        label: "Is empty",
        inputType: "none"
      },
      {
        value: "is_not_empty",
        label: "Is not empty",
        inputType: "none"
      }
    ];
  }
  getDefaultValue(operator) {
    if (operator === "is_empty" || operator === "is_not_empty") {
      return void 0;
    }
    return "";
  }
  formatValue(value, operator) {
    if (operator === "is_empty") {
      return "Is empty";
    }
    if (operator === "is_not_empty") {
      return "Is not empty";
    }
    return value || "";
  }
  apply(builder, value) {
    switch (value.operator) {
      case "equals":
        builder.where(this.field, "equals", value.value);
        break;
      case "contains":
        builder.where(this.field, "contains", value.value);
        break;
      case "not":
        builder.where(this.field, "not", value.value);
        break;
      case "is_empty":
        builder.whereRaw(`(${this.field} IS NULL OR ${this.field} = '')`);
        break;
      case "is_not_empty":
        builder.whereRaw(`(${this.field} IS NOT NULL AND ${this.field} != '')`);
        break;
    }
  }
  validate(value) {
    if (!value.operator)
      return false;
    const validOperators = ["equals", "contains", "not", "is_empty", "is_not_empty"];
    if (!validOperators.includes(value.operator))
      return false;
    if (["equals", "contains", "not"].includes(value.operator)) {
      return typeof value.value === "string" && value.value.length > 0;
    }
    return true;
  }
};

// app/services/search/filters/ZipCodeFilter.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/filters/ZipCodeFilter.ts"
  );
  import.meta.hot.lastModified = "1751311374980.6733";
}
var ZipCodeFilter = class extends BaseFilter {
  id = "zipcode";
  name = "ZIP Code";
  field = "zipcode";
  permissions = [{ roles: ["user", "admin"] }];
  constructor() {
    super();
  }
  getOperators() {
    return [
      {
        value: "equals",
        label: "Is",
        inputType: "text",
        component: DefaultFilterInput
      },
      {
        value: "not",
        label: "Is not",
        inputType: "text",
        component: DefaultFilterInput
      },
      {
        value: "starts_with",
        label: "Starts with",
        inputType: "text",
        component: DefaultFilterInput
      },
      {
        value: "in",
        label: "Is one of",
        inputType: "text",
        component: DefaultFilterInput
      }
    ];
  }
  getDefaultValue(operator) {
    if (operator === "in") {
      return "";
    }
    return "";
  }
  formatValue(value, operator) {
    if (operator === "in" && value.includes(",")) {
      return value.split(",").join(", ");
    }
    return value;
  }
  apply(builder, value) {
    switch (value.operator) {
      case "equals":
        builder.where(this.field, "equals", value.value);
        break;
      case "not":
        builder.where(this.field, "not", value.value);
        break;
      case "starts_with":
        builder.whereRaw(`${this.field} LIKE '${value.value}%'`);
        break;
      case "in":
        if (value.value && value.value.includes(",")) {
          const zipCodes = value.value.split(",").map((zip) => zip.trim());
          builder.where(this.field, "in", zipCodes);
        } else {
          builder.where(this.field, "equals", value.value);
        }
        break;
    }
  }
  validate(value) {
    if (!value.operator || !value.value)
      return false;
    const validOperators = ["equals", "not", "starts_with", "in"];
    if (!validOperators.includes(value.operator))
      return false;
    if (value.operator === "in" && value.value.includes(",")) {
      const zipCodes = value.value.split(",").map((zip) => zip.trim());
      return zipCodes.every((zip) => this.isValidZipCode(zip));
    }
    return this.isValidZipCode(value.value);
  }
  isValidZipCode(zip) {
    return /^\d{5}(-\d{4})?$/.test(zip);
  }
};

// app/services/search/filters/GunTypeFilter.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/filters/GunTypeFilter.ts"
  );
  import.meta.hot.lastModified = "1751311374848.1445";
}
var GunTypeFilter = class extends BaseFilter {
  id = "gun_type";
  name = "Gun Type";
  field = "gun_type";
  permissions = [{ roles: ["user", "admin"] }];
  constructor() {
    super();
  }
  getOperators() {
    return [
      {
        value: "equals",
        label: "Is",
        inputType: "select",
        component: DefaultFilterInput,
        options: [
          { value: "handgun", label: "Handgun" },
          { value: "rifle", label: "Rifle" },
          { value: "shotgun", label: "Shotgun" },
          { value: "assault_rifle", label: "Assault Rifle" },
          { value: "unknown", label: "Unknown" }
        ]
      },
      {
        value: "not",
        label: "Is not",
        inputType: "select",
        component: DefaultFilterInput,
        options: [
          { value: "handgun", label: "Handgun" },
          { value: "rifle", label: "Rifle" },
          { value: "shotgun", label: "Shotgun" },
          { value: "assault_rifle", label: "Assault Rifle" },
          { value: "unknown", label: "Unknown" }
        ]
      },
      {
        value: "in",
        label: "Is one of",
        inputType: "select",
        component: DefaultFilterInput,
        options: [
          { value: "handgun", label: "Handgun" },
          { value: "rifle", label: "Rifle" },
          { value: "shotgun", label: "Shotgun" },
          { value: "assault_rifle", label: "Assault Rifle" },
          { value: "unknown", label: "Unknown" }
        ]
      },
      {
        value: "has_any",
        label: "Has any gun",
        inputType: "none"
      },
      {
        value: "has_none",
        label: "Has no guns",
        inputType: "none"
      }
    ];
  }
  getDefaultValue(operator) {
    if (operator === "in") {
      return ["handgun"];
    }
    if (operator === "has_any" || operator === "has_none") {
      return void 0;
    }
    return "handgun";
  }
  formatValue(value, operator) {
    if (operator === "has_any") {
      return "Has any gun";
    }
    if (operator === "has_none") {
      return "Has no guns";
    }
    if (operator === "in" && Array.isArray(value)) {
      return value.map((v) => this.getOptionLabel(v)).join(", ");
    }
    return this.getOptionLabel(value);
  }
  getOptionLabel(value) {
    const options = this.getOperators()[0].options;
    if (!options)
      return value;
    const option = options.find((opt) => opt.value === value);
    return option ? option.label : value;
  }
  apply(builder, value) {
    builder.join("incident_guns", "incident_guns.incident_id = i.incident_id", "left", "ig");
    builder.join("taxonomy", "taxonomy.tid = ig.gun_type_tid", "left", "t_gun");
    switch (value.operator) {
      case "equals":
        builder.whereRaw(`t_gun.value = '${value.value}'`);
        break;
      case "not":
        builder.whereRaw(`i.incident_id NOT IN (
          SELECT incident_id FROM incident_guns 
          JOIN taxonomy ON taxonomy.tid = incident_guns.gun_type_tid 
          WHERE taxonomy.value = '${value.value}'
        )`);
        break;
      case "in":
        if (Array.isArray(value.value)) {
          const values = value.value.map((v) => `'${v}'`).join(", ");
          builder.whereRaw(`t_gun.value IN (${values})`);
        } else {
          builder.whereRaw(`t_gun.value = '${value.value}'`);
        }
        break;
      case "has_any":
        builder.whereRaw(`i.guns_involved_counter > 0`);
        break;
      case "has_none":
        builder.whereRaw(`i.guns_involved_counter = 0 OR i.guns_involved_counter IS NULL`);
        break;
    }
  }
  validate(value) {
    if (!value.operator)
      return false;
    const validOperators = ["equals", "not", "in", "has_any", "has_none"];
    if (!validOperators.includes(value.operator))
      return false;
    if (["equals", "not"].includes(value.operator)) {
      return typeof value.value === "string" && value.value.length > 0;
    }
    if (value.operator === "in") {
      return Array.isArray(value.value) && value.value.length > 0;
    }
    return true;
  }
};

// app/services/search/filters/ParticipantFilter.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/filters/ParticipantFilter.ts"
  );
  import.meta.hot.lastModified = "1751311114005.8503";
}
var ParticipantFilter = class extends BaseFilter {
  id = "participant";
  name = "Participant";
  field = "participant";
  permissions = [{ roles: ["user", "admin"] }];
  constructor() {
    super();
  }
  getOperators() {
    return [
      {
        value: "name_contains",
        label: "Name contains",
        inputType: "text",
        component: DefaultFilterInput
      },
      {
        value: "type_equals",
        label: "Type is",
        inputType: "select",
        component: DefaultFilterInput,
        options: [
          { value: "Victim", label: "Victim" },
          { value: "Subject-Suspect", label: "Subject/Suspect" },
          { value: "Officer", label: "Officer" }
        ]
      },
      {
        value: "age_equals",
        label: "Age equals",
        inputType: "text",
        component: DefaultFilterInput
      },
      {
        value: "age_range",
        label: "Age between",
        inputType: "text",
        component: DefaultFilterInput
      },
      {
        value: "gender_equals",
        label: "Gender is",
        inputType: "select",
        component: DefaultFilterInput,
        options: [
          { value: "Male", label: "Male" },
          { value: "Female", label: "Female" },
          { value: "Unknown", label: "Unknown" }
        ]
      },
      {
        value: "min_victims",
        label: "Minimum victims",
        inputType: "text",
        component: DefaultFilterInput
      }
    ];
  }
  getDefaultValue(operator) {
    switch (operator) {
      case "name_contains":
        return "";
      case "type_equals":
        return "Victim";
      case "age_equals":
        return "";
      case "age_range":
        return "18,30";
      case "gender_equals":
        return "Male";
      case "min_victims":
        return "1";
      default:
        return "";
    }
  }
  formatValue(value, operator) {
    switch (operator) {
      case "name_contains":
        return `Name contains "${value}"`;
      case "type_equals":
        return `Type is ${value}`;
      case "age_equals":
        return `Age is ${value}`;
      case "age_range":
        if (typeof value === "string" && value.includes(",")) {
          const [min, max] = value.split(",");
          return `Age between ${min} and ${max}`;
        }
        return `Age range ${value}`;
      case "gender_equals":
        return `Gender is ${value}`;
      case "min_victims":
        return `At least ${value} victim${parseInt(value, 10) !== 1 ? "s" : ""}`;
      default:
        return String(value);
    }
  }
  apply(builder, value) {
    builder.join("incident_participants", "incident_participants.incident_id = i.incident_id", "left", "ip");
    switch (value.operator) {
      case "name_contains":
        builder.whereRaw(`ip.name ILIKE '%${value.value}%'`);
        break;
      case "type_equals":
        builder.whereRaw(`ip.participant_type = '${value.value}'`);
        break;
      case "age_equals":
        const age = parseInt(value.value, 10);
        if (!isNaN(age)) {
          builder.whereRaw(`ip.age = ${age}`);
        }
        break;
      case "age_range":
        if (typeof value.value === "string" && value.value.includes(",")) {
          const [minStr, maxStr] = value.value.split(",");
          const min = parseInt(minStr.trim(), 10);
          const max = parseInt(maxStr.trim(), 10);
          if (!isNaN(min) && !isNaN(max)) {
            builder.whereRaw(`ip.age >= ${min} AND ip.age <= ${max}`);
          }
        }
        break;
      case "gender_equals":
        builder.whereRaw(`ip.gender = '${value.value}'`);
        break;
      case "min_victims":
        const minVictims = parseInt(value.value, 10);
        if (!isNaN(minVictims)) {
          builder.having(
            `COUNT(DISTINCT CASE WHEN ip.participant_type = 'Victim' THEN ip.participant_id END) >= ${minVictims}`
          );
        }
        break;
    }
  }
  validate(value) {
    if (!value.operator)
      return false;
    const validOperators = [
      "name_contains",
      "type_equals",
      "age_equals",
      "age_range",
      "gender_equals",
      "min_victims"
    ];
    if (!validOperators.includes(value.operator))
      return false;
    switch (value.operator) {
      case "name_contains":
        return typeof value.value === "string" && value.value.length > 0;
      case "type_equals":
        return ["Victim", "Subject-Suspect", "Officer"].includes(value.value);
      case "age_equals":
        const age = parseInt(value.value, 10);
        return !isNaN(age) && age >= 0 && age <= 120;
      case "age_range":
        if (typeof value.value !== "string" || !value.value.includes(","))
          return false;
        const [minStr, maxStr] = value.value.split(",");
        const min = parseInt(minStr.trim(), 10);
        const max = parseInt(maxStr.trim(), 10);
        return !isNaN(min) && !isNaN(max) && min >= 0 && max <= 120 && min <= max;
      case "gender_equals":
        return ["Male", "Female", "Unknown"].includes(value.value);
      case "min_victims":
        const minVictims = parseInt(value.value, 10);
        return !isNaN(minVictims) && minVictims >= 0;
      default:
        return false;
    }
  }
};

// app/services/search/filters/SourcesFilter.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/filters/SourcesFilter.ts"
  );
  import.meta.hot.lastModified = "1751311375057.3784";
}
var SourcesFilter = class extends BaseFilter {
  id = "sources";
  name = "Sources";
  field = "sources";
  permissions = [{ roles: ["user", "admin"] }];
  constructor() {
    super();
  }
  getOperators() {
    return [
      {
        value: "name_contains",
        label: "Source name contains",
        inputType: "text",
        component: DefaultFilterInput
      },
      {
        value: "url_contains",
        label: "URL contains",
        inputType: "text",
        component: DefaultFilterInput
      },
      {
        value: "has_image",
        label: "Has image",
        inputType: "none"
      },
      {
        value: "min_sources",
        label: "Minimum sources",
        inputType: "text",
        component: DefaultFilterInput
      },
      {
        value: "has_any",
        label: "Has any source",
        inputType: "none"
      },
      {
        value: "has_none",
        label: "Has no sources",
        inputType: "none"
      }
    ];
  }
  getDefaultValue(operator) {
    switch (operator) {
      case "name_contains":
      case "url_contains":
        return "";
      case "min_sources":
        return "1";
      case "has_image":
      case "has_any":
      case "has_none":
        return void 0;
      default:
        return "";
    }
  }
  formatValue(value, operator) {
    switch (operator) {
      case "name_contains":
        return `Source name contains "${value}"`;
      case "url_contains":
        return `URL contains "${value}"`;
      case "has_image":
        return "Has image";
      case "min_sources":
        return `At least ${value} source${parseInt(value, 10) !== 1 ? "s" : ""}`;
      case "has_any":
        return "Has any source";
      case "has_none":
        return "Has no sources";
      default:
        return String(value);
    }
  }
  apply(builder, value) {
    builder.join("incident_sources", "incident_sources.incident_id = i.incident_id", "left", "is");
    switch (value.operator) {
      case "name_contains":
        builder.whereRaw(`is.source_name ILIKE '%${value.value}%'`);
        break;
      case "url_contains":
        builder.whereRaw(`is.source_url ILIKE '%${value.value}%'`);
        break;
      case "has_image":
        builder.whereRaw(`is.image_fid IS NOT NULL AND is.image_fid > 0`);
        break;
      case "min_sources":
        const minSources = parseInt(value.value, 10);
        if (!isNaN(minSources)) {
          builder.having(`COUNT(DISTINCT is.source_entity_id) >= ${minSources}`);
        }
        break;
      case "has_any":
        builder.whereRaw(`is.source_entity_id IS NOT NULL`);
        break;
      case "has_none":
        builder.whereRaw(`is.source_entity_id IS NULL`);
        break;
    }
  }
  validate(value) {
    if (!value.operator)
      return false;
    const validOperators = ["name_contains", "url_contains", "has_image", "min_sources", "has_any", "has_none"];
    if (!validOperators.includes(value.operator))
      return false;
    switch (value.operator) {
      case "name_contains":
      case "url_contains":
        return typeof value.value === "string" && value.value.length > 0;
      case "min_sources":
        const minSources = parseInt(value.value, 10);
        return !isNaN(minSources) && minSources >= 0;
      case "has_image":
      case "has_any":
      case "has_none":
        return true;
      default:
        return false;
    }
  }
};

// app/services/search/filters/index.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/filters/index.ts"
  );
  import.meta.hot.lastModified = "1747752412843.454";
}

// app/services/search/FilterRegistry.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/FilterRegistry.ts"
  );
  import.meta.hot.lastModified = "1751311375036.8076";
}
var _FilterRegistry = class {
  filters = /* @__PURE__ */ new Map();
  constructor() {
    this.registerDefaultFilters();
  }
  /**
   * Register default filters
   * @private
   */
  registerDefaultFilters() {
    this.register(new DateRangeFilter());
    this.register(new StatusFilter());
    this.register(new LocationFilter());
    this.register(new VictimsFilter());
    this.register(new IncidentTypeFilter());
    this.register(new AddressFilter());
    this.register(new NotesFilter());
    this.register(new BusinessFilter());
    this.register(new ZipCodeFilter());
    this.register(new GunTypeFilter());
    this.register(new ParticipantFilter());
    this.register(new SourcesFilter());
  }
  /**
   * Get the singleton instance of the registry
   */
  static getInstance() {
    if (!_FilterRegistry.instance) {
      _FilterRegistry.instance = new _FilterRegistry();
    }
    return _FilterRegistry.instance;
  }
  /**
   * Register a filter with the registry
   * @param filter The filter to register
   */
  register(filter) {
    this.filters.set(filter.id, filter);
  }
  /**
   * Register a filter class with the registry
   * @param filterClass The filter class to register
   */
  registerClass(filterClass) {
    const filter = new filterClass();
    this.register(filter);
  }
  /**
   * Get a filter by ID
   * @param id The ID of the filter to retrieve
   * @returns The filter, or undefined if not found
   */
  get(id) {
    return this.filters.get(id);
  }
  /**
   * Get all registered filters
   * @returns An array of all registered filters
   */
  getAll() {
    return Array.from(this.filters.values());
  }
  /**
   * Get all registered filters as a map
   * @returns A map of all registered filters, keyed by ID
   */
  getAllAsMap() {
    return new Map(this.filters);
  }
  /**
   * Find a filter by field name
   * @param field The field name to search for
   * @returns The filter, or undefined if not found
   */
  findByField(field) {
    return Array.from(this.filters.values()).find((filter) => {
      return filter.getFieldName().toLowerCase() === field.toLowerCase();
    });
  }
  /**
   * Clear all registered filters
   */
  clear() {
    this.filters.clear();
  }
};
var FilterRegistry = _FilterRegistry;
__publicField(FilterRegistry, "instance");

// app/services/search/BaseColumn.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/BaseColumn.ts"
  );
  import.meta.hot.lastModified = "1751311375029.559";
}
var BaseColumn = class {
  sortable = false;
  permissions = [{ roles: ["user", "admin"] }];
  // Default permissions
  beforeQuery(builder) {
  }
  afterQuery(builder) {
  }
  beforeRender(data) {
    return data;
  }
  render(value, row) {
    return value;
  }
  afterRender(output) {
    return output;
  }
  getCssClasses() {
    return [];
  }
  hasPermission(userRoles, userPermissions = []) {
    return this.permissions.some((permission) => {
      const hasRole = permission.roles.some((role) => userRoles.includes(role));
      if (!permission.permissions || permission.permissions.length === 0) {
        return hasRole;
      }
      const hasPermission = permission.permissions.some((perm) => userPermissions.includes(perm));
      return hasRole && hasPermission;
    });
  }
};

// app/services/search/columns/IncidentIDColumn.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/columns/IncidentIDColumn.ts"
  );
  import.meta.hot.lastModified = "1750085655620.1157";
}
var IncidentIDColumn = class extends BaseColumn {
  key = "incident_id";
  label = "Incident ID";
  sortable = true;
  addToQuery(builder) {
    builder.select(["i.incident_id"]);
  }
  beforeRender(data) {
    return data.incident_id;
  }
  render(value, row) {
    return value;
  }
  getCssClasses() {
    return ["incident-id-column", "text-center"];
  }
};

// app/services/search/columns/IncidentDateColumn.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/columns/IncidentDateColumn.ts"
  );
  import.meta.hot.lastModified = "1750252761867.512";
}
var IncidentDateColumn = class extends BaseColumn {
  key = "incident_date";
  label = "Date";
  sortable = true;
  addToQuery(builder) {
    builder.select(["i.incident_date"]);
  }
  beforeRender(data) {
    if (!data.incident_date)
      return data;
    if (data.incident_date.toString().length < 13) {
      data.incident_date = new Date(Number(data.incident_date) * 1e3);
    } else {
      data.incident_date = new Date(Number(data.incident_date));
    }
    return data;
  }
  render(value, row) {
    return value ? format(value.incident_date, "MMM d, yyyy") : "";
  }
  getCssClasses() {
    return ["date-column", "text-center"];
  }
};

// app/services/search/columns/LocationColumn.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/columns/LocationColumn.ts"
  );
  import.meta.hot.lastModified = "1750085655620.6516";
}
var LocationColumn = class extends BaseColumn {
  key = "location";
  label = "Location";
  sortable = true;
  beforeQuery(builder) {
    builder.join("gva_data.taxonomy", "i.state_taxonomy_id = statename.tid", "left", "statename");
  }
  addToQuery(builder) {
    builder.select(["i.city_county"]);
    builder.selectRaw("string_agg(DISTINCT statename.value, ', ') AS state", "state");
    builder.groupBy("i.incident_id");
  }
  beforeRender(data) {
    return {
      location: (data.city_county || "") + ", " + (data.state || "")
    };
  }
  render(value, row) {
    return value.location;
  }
  getCssClasses() {
    return ["location-column"];
  }
};

// app/services/search/columns/VictimsColumn.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/columns/VictimsColumn.ts"
  );
  import.meta.hot.lastModified = "1751311374879.729";
}
var VictimsColumn = class extends BaseColumn {
  key = "victims_total";
  label = "Total Victims";
  sortable = false;
  // Sorting complex aggregated data can be problematic
  beforeQuery(builder) {
    builder.join("gva_data.incident_participants", "ip.incident_id = i.incident_id", "left", "ip");
  }
  addToQuery(builder) {
    builder.selectRaw(
      "COUNT(DISTINCT CASE WHEN (ip.participant_type = 'victim' AND ',' || ip.participant_status_tid || ',' LIKE '%,98,%') THEN ip.participant_id END) AS victims_killed",
      "victims_killed"
    );
    builder.selectRaw(
      "COUNT(DISTINCT CASE WHEN (ip.participant_type = 'victim' AND ',' || ip.participant_status_tid || ',' LIKE '%,99,%') THEN ip.participant_id END) AS victims_injured",
      "victims_injured"
    );
    builder.selectRaw(
      "COUNT(DISTINCT CASE WHEN ip.participant_type = 'victim' AND (',' || ip.participant_status_tid || ',' LIKE '%,98,%' OR ',' || ip.participant_status_tid || ',' LIKE '%,99,%') THEN ip.participant_id END) AS victims_total",
      "victims_total"
    );
    builder.groupBy("i.incident_id");
  }
  beforeRender(data) {
    return {
      victims_killed: parseInt(data.victims_killed || "0", 10),
      victims_injured: parseInt(data.victims_injured || "0", 10),
      victims_total: parseInt(data.victims_total || "0", 10)
    };
  }
  render(value, row) {
    return `${value.victims_total} (${value.victims_killed} killed, ${value.victims_injured} injured)`;
  }
  getCssClasses() {
    return ["victims-column", "text-center", "font-medium"];
  }
};

// app/services/search/columns/IncidentTypeColumn.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/columns/IncidentTypeColumn.ts"
  );
  import.meta.hot.lastModified = "1751311375058.8909";
}
var IncidentTypeColumn = class extends BaseColumn {
  key = "incident_type";
  label = "Incident Type";
  sortable = false;
  // Sorting complex aggregated data can be problematic
  beforeQuery(builder) {
    builder.join("gva_data.incident_types", "inc_t.incident_id = i.incident_id", "left", "inc_t");
    builder.join("gva_data.taxonomy", "t_type.tid = inc_t.type_tid", "left", "t_type");
  }
  addToQuery(builder) {
    builder.selectRaw("string_agg(DISTINCT t_type.value, ', ') AS incident_type", "incident_type");
    builder.groupBy("i.incident_id");
  }
  getCssClasses() {
    return ["incident-type-column"];
  }
  // Add a method to get a CSS class based on the incident type
  getTypeClass(value) {
    const classMap = {
      "Mass Shooting": "bg-red-100 text-red-800",
      "Domestic Violence": "bg-purple-100 text-purple-800",
      "Officer Involved": "bg-blue-100 text-blue-800",
      "Defensive Use": "bg-green-100 text-green-800",
      Unintentional: "bg-yellow-100 text-yellow-800",
      Suicide: "bg-gray-100 text-gray-800",
      Other: "bg-gray-100 text-gray-800"
    };
    return classMap[value] || "";
  }
  beforeRender(data) {
    data.incident_type = data.incident_type || "";
    return data;
  }
  // Render method to include a badge with the incident type
  render(value, row) {
    let html = "";
    value.incident_type.split(", ").forEach((type) => {
      const label = this.getLabel(type);
      const typeClass = this.getTypeClass(type);
      html += `<span class="px-2 py-1 inline-block rounded-full text-xs font-medium ${typeClass}">${label}</span>`;
    });
    return {
      __html: html
    };
  }
  // Helper method to get the label for an incident type
  getLabel(value) {
    const typeMap = {
      mass_shooting: "Mass Shooting",
      domestic_violence: "Domestic Violence",
      officer_involved: "Officer Involved",
      defensive_use: "Defensive Use",
      unintentional: "Unintentional",
      suicide: "Suicide",
      other: "Other"
    };
    return typeMap[value] || value;
  }
};

// app/services/search/columns/AddressColumn.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/columns/AddressColumn.ts"
  );
  import.meta.hot.lastModified = "1751311374825.0925";
}
var AddressColumn = class extends BaseColumn {
  key = "address";
  label = "Address";
  sortable = true;
  addToQuery(builder) {
    builder.select(["i.address"]);
  }
  beforeRender(data) {
    return {
      address: data.address || ""
    };
  }
  render(value, row) {
    return value.address;
  }
  getCssClasses() {
    return ["address-column"];
  }
};

// app/services/search/columns/NotesColumn.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/columns/NotesColumn.ts"
  );
  import.meta.hot.lastModified = "1751311374872.0505";
}
var NotesColumn = class extends BaseColumn {
  key = "notes";
  label = "Notes";
  sortable = true;
  addToQuery(builder) {
    builder.select(["i.notes"]);
  }
  beforeRender(data) {
    data.notes = data.notes || "";
    return data;
  }
  render(value, row) {
    if (value.notes.length > 100) {
      return value.notes.substring(0, 100) + "...";
    }
    return value.notes;
  }
  getCssClasses() {
    return ["notes-column"];
  }
};

// app/services/search/columns/BusinessColumn.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/columns/BusinessColumn.ts"
  );
  import.meta.hot.lastModified = "1751311375014.099";
}
var BusinessColumn = class extends BaseColumn {
  key = "business";
  label = "Business";
  sortable = true;
  addToQuery(builder) {
    builder.select(["i.business"]);
  }
  beforeRender(data) {
    return {
      business: data.business || ""
    };
  }
  render(value, row) {
    return value.business;
  }
  getCssClasses() {
    return ["business-column"];
  }
};

// app/services/search/columns/ZipCodeColumn.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/columns/ZipCodeColumn.ts"
  );
  import.meta.hot.lastModified = "1751311374827.8982";
}
var ZipCodeColumn = class extends BaseColumn {
  key = "zipcode";
  label = "ZIP Code";
  sortable = true;
  addToQuery(builder) {
    builder.select(["i.zipcode"]);
  }
  beforeRender(data) {
    data.zipcode = data.zipcode || "";
    return data;
  }
  render(value, row) {
    return value.zipcode;
  }
  getCssClasses() {
    return ["zipcode-column", "text-center"];
  }
};

// app/services/search/columns/GunTypeColumn.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/columns/GunTypeColumn.ts"
  );
  import.meta.hot.lastModified = "1751311374864.1072";
}
var GunTypeColumn = class extends BaseColumn {
  key = "gun_type";
  label = "Gun Type";
  sortable = true;
  beforeQuery(builder) {
    builder.join("gva_data.incident_guns", "ig.incident_id = i.incident_id", "left", "ig");
    builder.join("gva_data.taxonomy", "t_gun.tid = ig.gun_type_tid", "left", "t_gun");
  }
  addToQuery(builder) {
    builder.selectRaw("string_agg(DISTINCT t_gun.value, ', ') AS gun_type", "gun_type");
    builder.groupBy("i.incident_id");
  }
  beforeRender(data) {
    data.gun_type = data.gun_type || "";
    return data;
  }
  render(value, row) {
    return value.gun_type;
  }
  getCssClasses() {
    return ["gun-type-column"];
  }
};

// app/services/search/columns/ParticipantColumn.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/columns/ParticipantColumn.ts"
  );
  import.meta.hot.lastModified = "1751311374964.1746";
}
var ParticipantColumn = class extends BaseColumn {
  key = "participants";
  label = "Participants";
  sortable = false;
  // Sorting complex aggregated data can be problematic
  beforeQuery(builder) {
    builder.join("gva_data.incident_participants", "ip.incident_id = i.incident_id", "left", "ip");
  }
  addToQuery(builder) {
    builder.selectRaw(
      "COUNT(DISTINCT CASE WHEN ip.participant_type = 'victim' THEN ip.participant_id END) AS participants_victim_count",
      "participants_victim_count"
    );
    builder.selectRaw(
      "COUNT(DISTINCT CASE WHEN ip.participant_type = 'perpetrator' THEN ip.participant_id END) AS participants_suspect_count",
      "participants_suspect_count"
    );
    builder.selectRaw("string_agg(DISTINCT ip.name, ', ') AS participants_names", "participants_names");
    builder.groupBy("i.incident_id");
  }
  beforeRender(data) {
    data.participants_victim_count = parseInt(data.participants_victim_count || "0", 10);
    data.participants_suspect_count = parseInt(data.participants_suspect_count || "0", 10);
    data.participants_names = data.participants_names || "";
    return data;
  }
  render(value, row) {
    const counts = [];
    if (value.participants_victim_count > 0) {
      counts.push(`${value.participants_victim_count} victim${value.participants_victim_count !== 1 ? "s" : ""}`);
    }
    if (value.participants_suspect_count > 0) {
      counts.push(
        `${value.participants_suspect_count} suspect${value.participants_suspect_count !== 1 ? "s" : ""}`
      );
    }
    let result = counts.join(", ");
    if (value.participants_names) {
      const names = value.participants_names.length > 50 ? value.participants_names.substring(0, 50) + "..." : value.participants_names;
      result += result ? ` (${names})` : names;
    }
    return result || "No participants";
  }
  getCssClasses() {
    return ["participants-column"];
  }
};

// app/services/search/columns/SourcesColumn.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/columns/SourcesColumn.ts"
  );
  import.meta.hot.lastModified = "1750085655621.4485";
}
var SourcesColumn = class extends BaseColumn {
  key = "sources";
  label = "Sources";
  sortable = false;
  // Sorting complex aggregated data can be problematic
  beforeQuery(builder) {
    builder.join("gva_data.incident_sources", "inc_s.incident_id = i.incident_id", "left", "inc_s");
  }
  addToQuery(builder) {
    builder.selectRaw("COUNT(DISTINCT inc_s.source_entity_id) AS sources_count", "sources_count");
    builder.selectRaw("string_agg(DISTINCT inc_s.source_name, ', ') AS sources_names", "sources_names");
    builder.selectRaw("string_agg(DISTINCT inc_s.source_url, ', ') AS sources_urls", "sources_urls");
    builder.groupBy("i.incident_id");
  }
  beforeRender(data) {
    data.sources_count = parseInt(data.sources_count || "0", 10);
    data.sources_names = data.sources_names || "";
    data.sources_urls = data.sources_urls || "";
    return data;
  }
  render(value, row) {
    if (value.sources_count === 0) {
      return "No sources";
    }
    const sourceLinks = [];
    if (value.sources_names && value.sources_urls) {
      const names = value.sources_names.split(", ");
      const urls = value.sources_urls.split(", ");
      for (let i = 0; i < Math.min(names.length, urls.length); i++) {
        if (names[i] && urls[i]) {
          sourceLinks.push(`<a href="${urls[i]}" target="_blank" rel="noopener noreferrer">${names[i]}</a>`);
        }
      }
    }
    if (sourceLinks.length > 0) {
      return {
        __html: sourceLinks.join(", ")
      };
    }
    return `${value.sources_count} source${value.sources_count !== 1 ? "s" : ""}`;
  }
  getCssClasses() {
    return ["sources-column"];
  }
};

// app/services/search/columns/index.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/columns/index.ts"
  );
  import.meta.hot.lastModified = "1750085655622.0334";
}

// app/services/search/ColumnRegistry.ts
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/ColumnRegistry.ts"
  );
  import.meta.hot.lastModified = "1751311374862.0388";
}
var _ColumnRegistry = class {
  columns = /* @__PURE__ */ new Map();
  constructor() {
    this.registerDefaultColumns();
  }
  /**
   * Register default columns
   * @private
   */
  registerDefaultColumns() {
    this.register(new IncidentIDColumn());
    this.register(new IncidentDateColumn());
    this.register(new LocationColumn());
    this.register(new VictimsColumn());
    this.register(new IncidentTypeColumn());
    this.register(new AddressColumn());
    this.register(new NotesColumn());
    this.register(new BusinessColumn());
    this.register(new ZipCodeColumn());
    this.register(new GunTypeColumn());
    this.register(new ParticipantColumn());
    this.register(new SourcesColumn());
  }
  /**
   * Get the singleton instance of the registry
   */
  static getInstance() {
    if (!_ColumnRegistry.instance) {
      _ColumnRegistry.instance = new _ColumnRegistry();
    }
    return _ColumnRegistry.instance;
  }
  /**
   * Register a column with the registry
   * @param column The column to register
   */
  register(column) {
    this.columns.set(column.key, column);
  }
  /**
   * Register a column class with the registry
   * @param columnClass The column class to register
   */
  registerClass(columnClass) {
    const column = new columnClass();
    this.register(column);
  }
  /**
   * Get a column by key
   * @param key The key of the column to retrieve
   * @returns The column, or undefined if not found
   */
  get(key) {
    return this.columns.get(key);
  }
  /**
   * Get all registered columns
   * @returns An array of all registered columns
   */
  getAll() {
    return Array.from(this.columns.values());
  }
  /**
   * Get all registered columns as a map
   * @returns A map of all registered columns, keyed by key
   */
  getAllAsMap() {
    return new Map(this.columns);
  }
  /**
   * Clear all registered columns
   */
  clear() {
    this.columns.clear();
  }
};
var ColumnRegistry = _ColumnRegistry;
__publicField(ColumnRegistry, "instance");

// app/services/search/index.ts
var import_SearchBuilder = __toESM(require_SearchBuilder(), 1);
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/services/search/index.ts"
  );
  import.meta.hot.lastModified = "1751311374908.7507";
}

export {
  FilterRegistry,
  ColumnRegistry,
  require_SearchBuilder2 as require_SearchBuilder
};
//# sourceMappingURL=/build/_shared/chunk-E7PJAPPF.js.map
