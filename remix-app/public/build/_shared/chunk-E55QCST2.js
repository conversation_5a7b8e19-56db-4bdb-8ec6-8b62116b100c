import {
  esm_exports,
  init_esm
} from "/build/_shared/chunk-R4KMRLXS.js";
import {
  __commonJS,
  __toCommonJS
} from "/build/_shared/chunk-73CLBT4D.js";

// node_modules/remix-auth/build/authenticator.js
var require_authenticator = __commonJS({
  "node_modules/remix-auth/build/authenticator.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Authenticator = void 0;
    var server_runtime_1 = (init_esm(), __toCommonJS(esm_exports));
    var Authenticator = class {
      /**
       * Create a new instance of the Authenticator.
       *
       * It receives a instance of the SessionStorage. This session storage could
       * be created using any method exported by Remix, this includes:
       * - `createSessionStorage`
       * - `createFileSystemSessionStorage`
       * - `createCookieSessionStorage`
       * - `createMemorySessionStorage`
       *
       * It optionally receives an object with extra options. The supported options
       * are:
       * - `sessionKey`: The key used to store and read the user in the session storage.
       * @example
       * import { sessionStorage } from "./session.server";
       * let authenticator = new Authenticator(sessionStorage);
       * @example
       * import { sessionStorage } from "./session.server";
       * let authenticator = new Authenticator(sessionStorage, {
       *   sessionKey: "token",
       * });
       */
      constructor(sessionStorage, options = {}) {
        var _a;
        this.sessionStorage = sessionStorage;
        this.strategies = /* @__PURE__ */ new Map();
        this.sessionKey = options.sessionKey || "user";
        this.sessionErrorKey = options.sessionErrorKey || "auth:error";
        this.sessionStrategyKey = options.sessionStrategyKey || "strategy";
        this.throwOnError = (_a = options.throwOnError) !== null && _a !== void 0 ? _a : false;
      }
      /**
       * Call this method with the Strategy, the optional name allows you to setup
       * the same strategy multiple times with different names.
       * It returns the Authenticator instance for concatenation.
       * @example
       * authenticator
       *  .use(new SomeStrategy({}, (user) => Promise.resolve(user)))
       *  .use(new SomeStrategy({}, (user) => Promise.resolve(user)), "another");
       */
      use(strategy, name) {
        this.strategies.set(name !== null && name !== void 0 ? name : strategy.name, strategy);
        return this;
      }
      /**
       * Call this method with the name of the strategy you want to remove.
       * It returns the Authenticator instance for concatenation.
       * @example
       * authenticator.unuse("another").unuse("some");
       */
      unuse(name) {
        this.strategies.delete(name);
        return this;
      }
      authenticate(strategy, request, options = {}) {
        const strategyObj = this.strategies.get(strategy);
        if (!strategyObj)
          throw new Error(`Strategy ${strategy} not found.`);
        return strategyObj.authenticate(new Request(request.url, request), this.sessionStorage, {
          throwOnError: this.throwOnError,
          ...options,
          name: strategy,
          sessionKey: this.sessionKey,
          sessionErrorKey: this.sessionErrorKey,
          sessionStrategyKey: this.sessionStrategyKey
        });
      }
      async isAuthenticated(request, options = {}) {
        var _a;
        let session = (0, server_runtime_1.isSession)(request) ? request : await this.sessionStorage.getSession(request.headers.get("Cookie"));
        let user = (_a = session.get(this.sessionKey)) !== null && _a !== void 0 ? _a : null;
        if (user) {
          if (options.successRedirect) {
            throw (0, server_runtime_1.redirect)(options.successRedirect, { headers: options.headers });
          } else
            return user;
        }
        if (options.failureRedirect) {
          throw (0, server_runtime_1.redirect)(options.failureRedirect, { headers: options.headers });
        } else
          return null;
      }
      /**
       * Destroy the user session throw a redirect to another URL.
       * @example
       * async function action({ request }: ActionFunctionArgs) {
       *   await authenticator.logout(request, { redirectTo: "/login" });
       * }
       */
      async logout(request, options) {
        let session = (0, server_runtime_1.isSession)(request) ? request : await this.sessionStorage.getSession(request.headers.get("Cookie"));
        let headers = new Headers(options.headers);
        headers.append("Set-Cookie", await this.sessionStorage.destroySession(session));
        throw (0, server_runtime_1.redirect)(options.redirectTo, { headers });
      }
    };
    exports.Authenticator = Authenticator;
  }
});

// node_modules/remix-auth/build/authorizer.js
var require_authorizer = __commonJS({
  "node_modules/remix-auth/build/authorizer.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Authorizer = void 0;
    var server_runtime_1 = (init_esm(), __toCommonJS(esm_exports));
    var Authorizer = class {
      constructor(authenticator, rules = []) {
        this.authenticator = authenticator;
        this.rules = rules;
      }
      async authorize(args, { failureRedirect, raise = "response", rules = [] } = {}) {
        let user = await this.authenticator.isAuthenticated(args.request);
        if (!user) {
          if (raise === "response") {
            throw (0, server_runtime_1.json)({ message: "Not authenticated." }, { status: 401 });
          }
          if (raise === "redirect") {
            throw (0, server_runtime_1.redirect)(failureRedirect);
          }
          throw new Error("Not authenticated.");
        }
        for (let rule of [...this.rules, ...rules]) {
          if (await rule({ user, ...args }))
            continue;
          if (raise === "redirect")
            throw (0, server_runtime_1.redirect)(failureRedirect);
          if (raise === "response") {
            if (!rule.name)
              throw (0, server_runtime_1.json)({ message: "Forbidden" }, { status: 403 });
            throw (0, server_runtime_1.json)({ message: `Forbidden by policy ${rule.name}` }, { status: 403 });
          }
          if (!rule.name)
            throw new Error("Forbidden.");
          throw new Error(`Forbidden by policy ${rule.name}`);
        }
        return user;
      }
    };
    exports.Authorizer = Authorizer;
  }
});

// node_modules/remix-auth/build/error.js
var require_error = __commonJS({
  "node_modules/remix-auth/build/error.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.AuthorizationError = void 0;
    var AuthorizationError = class extends Error {
      constructor(message, cause) {
        super(message);
        this.cause = cause;
      }
    };
    exports.AuthorizationError = AuthorizationError;
  }
});

// node_modules/remix-auth/build/strategy.js
var require_strategy = __commonJS({
  "node_modules/remix-auth/build/strategy.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Strategy = void 0;
    var server_runtime_1 = (init_esm(), __toCommonJS(esm_exports));
    var error_1 = require_error();
    var Strategy = class {
      constructor(verify) {
        this.verify = verify;
      }
      /**
       * Throw an AuthorizationError or a redirect to the failureRedirect.
       * @param message The error message to set in the session.
       * @param request The request to get the cookie out of.
       * @param sessionStorage The session storage to retrieve the session from.
       * @param options The strategy options.
       * @throws {AuthorizationError} If the throwOnError is set to true.
       * @throws {Response} If the failureRedirect is set or throwOnError is false.
       * @returns {Promise<never>}
       */
      async failure(message, request, sessionStorage, options, cause) {
        if (!options.failureRedirect) {
          if (options.throwOnError)
            throw new error_1.AuthorizationError(message, cause);
          throw (0, server_runtime_1.json)({ message }, 401);
        }
        let session = await sessionStorage.getSession(request.headers.get("Cookie"));
        session.flash(options.sessionErrorKey, { message });
        throw (0, server_runtime_1.redirect)(options.failureRedirect, {
          headers: { "Set-Cookie": await sessionStorage.commitSession(session) }
        });
      }
      /**
       * Returns the user data or throw a redirect to the successRedirect.
       * @param user The user data to set in the session.
       * @param request The request to get the cookie out of.
       * @param sessionStorage The session storage to retrieve the session from.
       * @param options The strategy options.
       * @returns {Promise<User>} The user data.
       * @throws {Response} If the successRedirect is set, it will redirect to it.
       */
      async success(user, request, sessionStorage, options) {
        var _a;
        if (!options.successRedirect)
          return user;
        let session = await sessionStorage.getSession(request.headers.get("Cookie"));
        session.set(options.sessionKey, user);
        session.set(options.sessionStrategyKey, (_a = options.name) !== null && _a !== void 0 ? _a : this.name);
        throw (0, server_runtime_1.redirect)(options.successRedirect, {
          headers: { "Set-Cookie": await sessionStorage.commitSession(session) }
        });
      }
    };
    exports.Strategy = Strategy;
  }
});

// node_modules/remix-auth/build/index.js
var require_build = __commonJS({
  "node_modules/remix-auth/build/index.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0)
        k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0)
        k2 = k;
      o[k2] = m[k];
    });
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m)
        if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p))
          __createBinding(exports2, m, p);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    __exportStar(require_authenticator(), exports);
    __exportStar(require_authorizer(), exports);
    __exportStar(require_error(), exports);
    __exportStar(require_strategy(), exports);
  }
});

export {
  require_build
};
//# sourceMappingURL=/build/_shared/chunk-E55QCST2.js.map
