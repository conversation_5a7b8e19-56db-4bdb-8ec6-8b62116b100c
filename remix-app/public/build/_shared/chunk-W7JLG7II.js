import {
  Icon_Admin_Add_default
} from "/build/_shared/chunk-OHIZ2QAR.js";
import {
  useUser
} from "/build/_shared/chunk-DFTVPORL.js";
import {
  Link,
  NavLink,
  useSubmit
} from "/build/_shared/chunk-R4KMRLXS.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js
var import_react = __toESM(require_react(), 1);

// node_modules/@headlessui/react/dist/utils/env.js
var i = Object.defineProperty;
var d = (t11, e, n4) => e in t11 ? i(t11, e, { enumerable: true, configurable: true, writable: true, value: n4 }) : t11[e] = n4;
var r = (t11, e, n4) => (d(t11, typeof e != "symbol" ? e + "" : e, n4), n4);
var o = class {
  constructor() {
    r(this, "current", this.detect());
    r(this, "handoffState", "pending");
    r(this, "currentId", 0);
  }
  set(e) {
    this.current !== e && (this.handoffState = "pending", this.currentId = 0, this.current = e);
  }
  reset() {
    this.set(this.detect());
  }
  nextId() {
    return ++this.currentId;
  }
  get isServer() {
    return this.current === "server";
  }
  get isClient() {
    return this.current === "client";
  }
  detect() {
    return typeof window == "undefined" || typeof document == "undefined" ? "server" : "client";
  }
  handoff() {
    this.handoffState === "pending" && (this.handoffState = "complete");
  }
  get isHandoffComplete() {
    return this.handoffState === "complete";
  }
};
var s = new o();

// node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js
var l = (e, f4) => {
  s.isServer ? (0, import_react.useEffect)(e, f4) : (0, import_react.useLayoutEffect)(e, f4);
};

// node_modules/@headlessui/react/dist/hooks/use-latest-value.js
var import_react2 = __toESM(require_react(), 1);
function s2(e) {
  let r3 = (0, import_react2.useRef)(e);
  return l(() => {
    r3.current = e;
  }, [e]), r3;
}

// node_modules/@headlessui/react/dist/hooks/use-event.js
var import_react3 = __toESM(require_react(), 1);
var o2 = function(t11) {
  let e = s2(t11);
  return import_react3.default.useCallback((...r3) => e.current(...r3), [e]);
};

// node_modules/@headlessui/react/dist/hooks/use-disposables.js
var import_react4 = __toESM(require_react(), 1);

// node_modules/@headlessui/react/dist/utils/micro-task.js
function t3(e) {
  typeof queueMicrotask == "function" ? queueMicrotask(e) : Promise.resolve().then(e).catch((o11) => setTimeout(() => {
    throw o11;
  }));
}

// node_modules/@headlessui/react/dist/utils/disposables.js
function o3() {
  let n4 = [], r3 = { addEventListener(e, t11, s8, a3) {
    return e.addEventListener(t11, s8, a3), r3.add(() => e.removeEventListener(t11, s8, a3));
  }, requestAnimationFrame(...e) {
    let t11 = requestAnimationFrame(...e);
    return r3.add(() => cancelAnimationFrame(t11));
  }, nextFrame(...e) {
    return r3.requestAnimationFrame(() => r3.requestAnimationFrame(...e));
  }, setTimeout(...e) {
    let t11 = setTimeout(...e);
    return r3.add(() => clearTimeout(t11));
  }, microTask(...e) {
    let t11 = { current: true };
    return t3(() => {
      t11.current && e[0]();
    }), r3.add(() => {
      t11.current = false;
    });
  }, style(e, t11, s8) {
    let a3 = e.style.getPropertyValue(t11);
    return Object.assign(e.style, { [t11]: s8 }), this.add(() => {
      Object.assign(e.style, { [t11]: a3 });
    });
  }, group(e) {
    let t11 = o3();
    return e(t11), this.add(() => t11.dispose());
  }, add(e) {
    return n4.push(e), () => {
      let t11 = n4.indexOf(e);
      if (t11 >= 0)
        for (let s8 of n4.splice(t11, 1))
          s8();
    };
  }, dispose() {
    for (let e of n4.splice(0))
      e();
  } };
  return r3;
}

// node_modules/@headlessui/react/dist/hooks/use-disposables.js
function p() {
  let [e] = (0, import_react4.useState)(o3);
  return (0, import_react4.useEffect)(() => () => e.dispose(), [e]), e;
}

// node_modules/@headlessui/react/dist/hooks/use-id.js
var import_react5 = __toESM(require_react(), 1);

// node_modules/@headlessui/react/dist/hooks/use-server-handoff-complete.js
var t4 = __toESM(require_react(), 1);
function s4() {
  let r3 = typeof document == "undefined";
  return "useSyncExternalStore" in t4 ? ((o11) => o11.useSyncExternalStore)(t4)(() => () => {
  }, () => false, () => !r3) : false;
}
function l2() {
  let r3 = s4(), [e, n4] = t4.useState(s.isHandoffComplete);
  return e && s.isHandoffComplete === false && n4(false), t4.useEffect(() => {
    e !== true && n4(true);
  }, [e]), t4.useEffect(() => s.handoff(), []), r3 ? false : e;
}

// node_modules/@headlessui/react/dist/hooks/use-id.js
var o5;
var I = (o5 = import_react5.default.useId) != null ? o5 : function() {
  let n4 = l2(), [e, u6] = import_react5.default.useState(n4 ? () => s.nextId() : null);
  return l(() => {
    e === null && u6(s.nextId());
  }, [e]), e != null ? "" + e : void 0;
};

// node_modules/@headlessui/react/dist/hooks/use-outside-click.js
var import_react8 = __toESM(require_react(), 1);

// node_modules/@headlessui/react/dist/utils/match.js
function u(r3, n4, ...a3) {
  if (r3 in n4) {
    let e = n4[r3];
    return typeof e == "function" ? e(...a3) : e;
  }
  let t11 = new Error(`Tried to handle "${r3}" but there is no handler defined. Only defined handlers are: ${Object.keys(n4).map((e) => `"${e}"`).join(", ")}.`);
  throw Error.captureStackTrace && Error.captureStackTrace(t11, u), t11;
}

// node_modules/@headlessui/react/dist/utils/owner.js
function o6(r3) {
  return s.isServer ? null : r3 instanceof Node ? r3.ownerDocument : r3 != null && r3.hasOwnProperty("current") && r3.current instanceof Node ? r3.current.ownerDocument : document;
}

// node_modules/@headlessui/react/dist/utils/focus-management.js
var c2 = ["[contentEditable=true]", "[tabindex]", "a[href]", "area[href]", "button:not([disabled])", "iframe", "input:not([disabled])", "select:not([disabled])", "textarea:not([disabled])"].map((e) => `${e}:not([tabindex='-1'])`).join(",");
var M = ((n4) => (n4[n4.First = 1] = "First", n4[n4.Previous = 2] = "Previous", n4[n4.Next = 4] = "Next", n4[n4.Last = 8] = "Last", n4[n4.WrapAround = 16] = "WrapAround", n4[n4.NoScroll = 32] = "NoScroll", n4))(M || {});
var N = ((o11) => (o11[o11.Error = 0] = "Error", o11[o11.Overflow = 1] = "Overflow", o11[o11.Success = 2] = "Success", o11[o11.Underflow = 3] = "Underflow", o11))(N || {});
var F = ((t11) => (t11[t11.Previous = -1] = "Previous", t11[t11.Next = 1] = "Next", t11))(F || {});
function f(e = document.body) {
  return e == null ? [] : Array.from(e.querySelectorAll(c2)).sort((r3, t11) => Math.sign((r3.tabIndex || Number.MAX_SAFE_INTEGER) - (t11.tabIndex || Number.MAX_SAFE_INTEGER)));
}
var T = ((t11) => (t11[t11.Strict = 0] = "Strict", t11[t11.Loose = 1] = "Loose", t11))(T || {});
function h(e, r3 = 0) {
  var t11;
  return e === ((t11 = o6(e)) == null ? void 0 : t11.body) ? false : u(r3, { [0]() {
    return e.matches(c2);
  }, [1]() {
    let l6 = e;
    for (; l6 !== null; ) {
      if (l6.matches(c2))
        return true;
      l6 = l6.parentElement;
    }
    return false;
  } });
}
function D(e) {
  let r3 = o6(e);
  o3().nextFrame(() => {
    r3 && !h(r3.activeElement, 0) && y(e);
  });
}
var w = ((t11) => (t11[t11.Keyboard = 0] = "Keyboard", t11[t11.Mouse = 1] = "Mouse", t11))(w || {});
typeof window != "undefined" && typeof document != "undefined" && (document.addEventListener("keydown", (e) => {
  e.metaKey || e.altKey || e.ctrlKey || (document.documentElement.dataset.headlessuiFocusVisible = "");
}, true), document.addEventListener("click", (e) => {
  e.detail === 1 ? delete document.documentElement.dataset.headlessuiFocusVisible : e.detail === 0 && (document.documentElement.dataset.headlessuiFocusVisible = "");
}, true));
function y(e) {
  e == null || e.focus({ preventScroll: true });
}
var S = ["textarea", "input"].join(",");
function H(e) {
  var r3, t11;
  return (t11 = (r3 = e == null ? void 0 : e.matches) == null ? void 0 : r3.call(e, S)) != null ? t11 : false;
}
function I2(e, r3 = (t11) => t11) {
  return e.slice().sort((t11, l6) => {
    let o11 = r3(t11), i6 = r3(l6);
    if (o11 === null || i6 === null)
      return 0;
    let n4 = o11.compareDocumentPosition(i6);
    return n4 & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : n4 & Node.DOCUMENT_POSITION_PRECEDING ? 1 : 0;
  });
}
function _(e, r3) {
  return O(f(), r3, { relativeTo: e });
}
function O(e, r3, { sorted: t11 = true, relativeTo: l6 = null, skipElements: o11 = [] } = {}) {
  let i6 = Array.isArray(e) ? e.length > 0 ? e[0].ownerDocument : document : e.ownerDocument, n4 = Array.isArray(e) ? t11 ? I2(e) : e : f(e);
  o11.length > 0 && n4.length > 1 && (n4 = n4.filter((s8) => !o11.includes(s8))), l6 = l6 != null ? l6 : i6.activeElement;
  let E3 = (() => {
    if (r3 & 5)
      return 1;
    if (r3 & 10)
      return -1;
    throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last");
  })(), x2 = (() => {
    if (r3 & 1)
      return 0;
    if (r3 & 2)
      return Math.max(0, n4.indexOf(l6)) - 1;
    if (r3 & 4)
      return Math.max(0, n4.indexOf(l6)) + 1;
    if (r3 & 8)
      return n4.length - 1;
    throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last");
  })(), p3 = r3 & 32 ? { preventScroll: true } : {}, d6 = 0, a3 = n4.length, u6;
  do {
    if (d6 >= a3 || d6 + a3 <= 0)
      return 0;
    let s8 = x2 + d6;
    if (r3 & 16)
      s8 = (s8 + a3) % a3;
    else {
      if (s8 < 0)
        return 3;
      if (s8 >= a3)
        return 1;
    }
    u6 = n4[s8], u6 == null || u6.focus(p3), d6 += E3;
  } while (u6 !== i6.activeElement);
  return r3 & 6 && H(u6) && u6.select(), 2;
}

// node_modules/@headlessui/react/dist/utils/platform.js
function t6() {
  return /iPhone/gi.test(window.navigator.platform) || /Mac/gi.test(window.navigator.platform) && window.navigator.maxTouchPoints > 0;
}
function i2() {
  return /Android/gi.test(window.navigator.userAgent);
}
function n() {
  return t6() || i2();
}

// node_modules/@headlessui/react/dist/hooks/use-document-event.js
var import_react6 = __toESM(require_react(), 1);
function d2(e, r3, n4) {
  let o11 = s2(r3);
  (0, import_react6.useEffect)(() => {
    function t11(u6) {
      o11.current(u6);
    }
    return document.addEventListener(e, t11, n4), () => document.removeEventListener(e, t11, n4);
  }, [e, n4]);
}

// node_modules/@headlessui/react/dist/hooks/use-window-event.js
var import_react7 = __toESM(require_react(), 1);
function s5(e, r3, n4) {
  let o11 = s2(r3);
  (0, import_react7.useEffect)(() => {
    function t11(i6) {
      o11.current(i6);
    }
    return window.addEventListener(e, t11, n4), () => window.removeEventListener(e, t11, n4);
  }, [e, n4]);
}

// node_modules/@headlessui/react/dist/hooks/use-outside-click.js
function y2(s8, m4, a3 = true) {
  let i6 = (0, import_react8.useRef)(false);
  (0, import_react8.useEffect)(() => {
    requestAnimationFrame(() => {
      i6.current = a3;
    });
  }, [a3]);
  function c4(e, r3) {
    if (!i6.current || e.defaultPrevented)
      return;
    let t11 = r3(e);
    if (t11 === null || !t11.getRootNode().contains(t11) || !t11.isConnected)
      return;
    let E3 = function u6(n4) {
      return typeof n4 == "function" ? u6(n4()) : Array.isArray(n4) || n4 instanceof Set ? n4 : [n4];
    }(s8);
    for (let u6 of E3) {
      if (u6 === null)
        continue;
      let n4 = u6 instanceof HTMLElement ? u6 : u6.current;
      if (n4 != null && n4.contains(t11) || e.composed && e.composedPath().includes(n4))
        return;
    }
    return !h(t11, T.Loose) && t11.tabIndex !== -1 && e.preventDefault(), m4(e, t11);
  }
  let o11 = (0, import_react8.useRef)(null);
  d2("pointerdown", (e) => {
    var r3, t11;
    i6.current && (o11.current = ((t11 = (r3 = e.composedPath) == null ? void 0 : r3.call(e)) == null ? void 0 : t11[0]) || e.target);
  }, true), d2("mousedown", (e) => {
    var r3, t11;
    i6.current && (o11.current = ((t11 = (r3 = e.composedPath) == null ? void 0 : r3.call(e)) == null ? void 0 : t11[0]) || e.target);
  }, true), d2("click", (e) => {
    n() || o11.current && (c4(e, () => o11.current), o11.current = null);
  }, true), d2("touchend", (e) => c4(e, () => e.target instanceof HTMLElement ? e.target : null), true), s5("blur", (e) => c4(e, () => window.document.activeElement instanceof HTMLIFrameElement ? window.document.activeElement : null), true);
}

// node_modules/@headlessui/react/dist/hooks/use-owner.js
var import_react9 = __toESM(require_react(), 1);
function n2(...e) {
  return (0, import_react9.useMemo)(() => o6(...e), [...e]);
}

// node_modules/@headlessui/react/dist/hooks/use-resolve-button-type.js
var import_react10 = __toESM(require_react(), 1);
function i3(t11) {
  var n4;
  if (t11.type)
    return t11.type;
  let e = (n4 = t11.as) != null ? n4 : "button";
  if (typeof e == "string" && e.toLowerCase() === "button")
    return "button";
}
function T2(t11, e) {
  let [n4, u6] = (0, import_react10.useState)(() => i3(t11));
  return l(() => {
    u6(i3(t11));
  }, [t11.type, t11.as]), l(() => {
    n4 || e.current && e.current instanceof HTMLButtonElement && !e.current.hasAttribute("type") && u6("button");
  }, [n4, e]), n4;
}

// node_modules/@headlessui/react/dist/hooks/use-sync-refs.js
var import_react11 = __toESM(require_react(), 1);
var u2 = Symbol();
function y3(...t11) {
  let n4 = (0, import_react11.useRef)(t11);
  (0, import_react11.useEffect)(() => {
    n4.current = t11;
  }, [t11]);
  let c4 = o2((e) => {
    for (let o11 of n4.current)
      o11 != null && (typeof o11 == "function" ? o11(e) : o11.current = e);
  });
  return t11.every((e) => e == null || (e == null ? void 0 : e[u2])) ? void 0 : c4;
}

// node_modules/@headlessui/react/dist/hooks/use-tracked-pointer.js
var import_react12 = __toESM(require_react(), 1);
function t8(e) {
  return [e.screenX, e.screenY];
}
function u3() {
  let e = (0, import_react12.useRef)([-1, -1]);
  return { wasMoved(r3) {
    let n4 = t8(r3);
    return e.current[0] === n4[0] && e.current[1] === n4[1] ? false : (e.current = n4, true);
  }, update(r3) {
    e.current = t8(r3);
  } };
}

// node_modules/@headlessui/react/dist/hooks/use-tree-walker.js
var import_react13 = __toESM(require_react(), 1);
function F2({ container: e, accept: t11, walk: r3, enabled: c4 = true }) {
  let o11 = (0, import_react13.useRef)(t11), l6 = (0, import_react13.useRef)(r3);
  (0, import_react13.useEffect)(() => {
    o11.current = t11, l6.current = r3;
  }, [t11, r3]), l(() => {
    if (!e || !c4)
      return;
    let n4 = o6(e);
    if (!n4)
      return;
    let f4 = o11.current, p3 = l6.current, d6 = Object.assign((i6) => f4(i6), { acceptNode: f4 }), u6 = n4.createTreeWalker(e, NodeFilter.SHOW_ELEMENT, d6, false);
    for (; u6.nextNode(); )
      p3(u6.currentNode);
  }, [e, c4, o11, l6]);
}

// node_modules/@headlessui/react/dist/utils/render.js
var import_react14 = __toESM(require_react(), 1);

// node_modules/@headlessui/react/dist/utils/class-names.js
function t9(...r3) {
  return Array.from(new Set(r3.flatMap((n4) => typeof n4 == "string" ? n4.split(" ") : []))).filter(Boolean).join(" ");
}

// node_modules/@headlessui/react/dist/utils/render.js
var O2 = ((n4) => (n4[n4.None = 0] = "None", n4[n4.RenderStrategy = 1] = "RenderStrategy", n4[n4.Static = 2] = "Static", n4))(O2 || {});
var v = ((e) => (e[e.Unmount = 0] = "Unmount", e[e.Hidden = 1] = "Hidden", e))(v || {});
function C({ ourProps: r3, theirProps: t11, slot: e, defaultTag: n4, features: o11, visible: a3 = true, name: f4, mergeRefs: l6 }) {
  l6 = l6 != null ? l6 : k;
  let s8 = R(t11, r3);
  if (a3)
    return m3(s8, e, n4, f4, l6);
  let y4 = o11 != null ? o11 : 0;
  if (y4 & 2) {
    let { static: u6 = false, ...d6 } = s8;
    if (u6)
      return m3(d6, e, n4, f4, l6);
  }
  if (y4 & 1) {
    let { unmount: u6 = true, ...d6 } = s8;
    return u(u6 ? 0 : 1, { [0]() {
      return null;
    }, [1]() {
      return m3({ ...d6, hidden: true, style: { display: "none" } }, e, n4, f4, l6);
    } });
  }
  return m3(s8, e, n4, f4, l6);
}
function m3(r3, t11 = {}, e, n4, o11) {
  let { as: a3 = e, children: f4, refName: l6 = "ref", ...s8 } = F3(r3, ["unmount", "static"]), y4 = r3.ref !== void 0 ? { [l6]: r3.ref } : {}, u6 = typeof f4 == "function" ? f4(t11) : f4;
  "className" in s8 && s8.className && typeof s8.className == "function" && (s8.className = s8.className(t11));
  let d6 = {};
  if (t11) {
    let i6 = false, c4 = [];
    for (let [T3, p3] of Object.entries(t11))
      typeof p3 == "boolean" && (i6 = true), p3 === true && c4.push(T3);
    i6 && (d6["data-headlessui-state"] = c4.join(" "));
  }
  if (a3 === import_react14.Fragment && Object.keys(x(s8)).length > 0) {
    if (!(0, import_react14.isValidElement)(u6) || Array.isArray(u6) && u6.length > 1)
      throw new Error(['Passing props on "Fragment"!', "", `The current component <${n4} /> is rendering a "Fragment".`, "However we need to passthrough the following props:", Object.keys(s8).map((p3) => `  - ${p3}`).join(`
`), "", "You can apply a few solutions:", ['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".', "Render a single element as the child so that we can forward the props onto that element."].map((p3) => `  - ${p3}`).join(`
`)].join(`
`));
    let i6 = u6.props, c4 = typeof (i6 == null ? void 0 : i6.className) == "function" ? (...p3) => t9(i6 == null ? void 0 : i6.className(...p3), s8.className) : t9(i6 == null ? void 0 : i6.className, s8.className), T3 = c4 ? { className: c4 } : {};
    return (0, import_react14.cloneElement)(u6, Object.assign({}, R(u6.props, x(F3(s8, ["ref"]))), d6, y4, { ref: o11(u6.ref, y4.ref) }, T3));
  }
  return (0, import_react14.createElement)(a3, Object.assign({}, F3(s8, ["ref"]), a3 !== import_react14.Fragment && y4, a3 !== import_react14.Fragment && d6), u6);
}
function k(...r3) {
  return r3.every((t11) => t11 == null) ? void 0 : (t11) => {
    for (let e of r3)
      e != null && (typeof e == "function" ? e(t11) : e.current = t11);
  };
}
function R(...r3) {
  var n4;
  if (r3.length === 0)
    return {};
  if (r3.length === 1)
    return r3[0];
  let t11 = {}, e = {};
  for (let o11 of r3)
    for (let a3 in o11)
      a3.startsWith("on") && typeof o11[a3] == "function" ? ((n4 = e[a3]) != null || (e[a3] = []), e[a3].push(o11[a3])) : t11[a3] = o11[a3];
  if (t11.disabled || t11["aria-disabled"])
    return Object.assign(t11, Object.fromEntries(Object.keys(e).map((o11) => [o11, void 0])));
  for (let o11 in e)
    Object.assign(t11, { [o11](a3, ...f4) {
      let l6 = e[o11];
      for (let s8 of l6) {
        if ((a3 instanceof Event || (a3 == null ? void 0 : a3.nativeEvent) instanceof Event) && a3.defaultPrevented)
          return;
        s8(a3, ...f4);
      }
    } });
  return t11;
}
function U(r3) {
  var t11;
  return Object.assign((0, import_react14.forwardRef)(r3), { displayName: (t11 = r3.displayName) != null ? t11 : r3.name });
}
function x(r3) {
  let t11 = Object.assign({}, r3);
  for (let e in t11)
    t11[e] === void 0 && delete t11[e];
  return t11;
}
function F3(r3, t11 = []) {
  let e = Object.assign({}, r3);
  for (let n4 of t11)
    n4 in e && delete e[n4];
  return e;
}

// node_modules/@headlessui/react/dist/internal/open-closed.js
var import_react15 = __toESM(require_react(), 1);
var n3 = (0, import_react15.createContext)(null);
n3.displayName = "OpenClosedContext";
var d5 = ((e) => (e[e.Open = 1] = "Open", e[e.Closed = 2] = "Closed", e[e.Closing = 4] = "Closing", e[e.Opening = 8] = "Opening", e))(d5 || {});
function u4() {
  return (0, import_react15.useContext)(n3);
}
function s6({ value: o11, children: r3 }) {
  return import_react15.default.createElement(n3.Provider, { value: o11 }, r3);
}

// node_modules/@headlessui/react/dist/utils/bugs.js
function r2(n4) {
  let e = n4.parentElement, l6 = null;
  for (; e && !(e instanceof HTMLFieldSetElement); )
    e instanceof HTMLLegendElement && (l6 = e), e = e.parentElement;
  let t11 = (e == null ? void 0 : e.getAttribute("disabled")) === "";
  return t11 && i5(l6) ? false : t11;
}
function i5(n4) {
  if (!n4)
    return false;
  let e = n4.previousElementSibling;
  for (; e !== null; ) {
    if (e instanceof HTMLLegendElement)
      return false;
    e = e.previousElementSibling;
  }
  return true;
}

// node_modules/@headlessui/react/dist/utils/calculate-active-index.js
function u5(l6) {
  throw new Error("Unexpected object: " + l6);
}
var c3 = ((i6) => (i6[i6.First = 0] = "First", i6[i6.Previous = 1] = "Previous", i6[i6.Next = 2] = "Next", i6[i6.Last = 3] = "Last", i6[i6.Specific = 4] = "Specific", i6[i6.Nothing = 5] = "Nothing", i6))(c3 || {});
function f3(l6, n4) {
  let t11 = n4.resolveItems();
  if (t11.length <= 0)
    return null;
  let r3 = n4.resolveActiveIndex(), s8 = r3 != null ? r3 : -1;
  switch (l6.focus) {
    case 0: {
      for (let e = 0; e < t11.length; ++e)
        if (!n4.resolveDisabled(t11[e], e, t11))
          return e;
      return r3;
    }
    case 1: {
      for (let e = s8 - 1; e >= 0; --e)
        if (!n4.resolveDisabled(t11[e], e, t11))
          return e;
      return r3;
    }
    case 2: {
      for (let e = s8 + 1; e < t11.length; ++e)
        if (!n4.resolveDisabled(t11[e], e, t11))
          return e;
      return r3;
    }
    case 3: {
      for (let e = t11.length - 1; e >= 0; --e)
        if (!n4.resolveDisabled(t11[e], e, t11))
          return e;
      return r3;
    }
    case 4: {
      for (let e = 0; e < t11.length; ++e)
        if (n4.resolveId(t11[e], e, t11) === l6.id)
          return e;
      return r3;
    }
    case 5:
      return null;
    default:
      u5(l6);
  }
}

// node_modules/@headlessui/react/dist/components/keyboard.js
var o9 = ((r3) => (r3.Space = " ", r3.Enter = "Enter", r3.Escape = "Escape", r3.Backspace = "Backspace", r3.Delete = "Delete", r3.ArrowLeft = "ArrowLeft", r3.ArrowUp = "ArrowUp", r3.ArrowRight = "ArrowRight", r3.ArrowDown = "ArrowDown", r3.Home = "Home", r3.End = "End", r3.PageUp = "PageUp", r3.PageDown = "PageDown", r3.Tab = "Tab", r3))(o9 || {});

// node_modules/@headlessui/react/dist/hooks/use-text-value.js
var import_react16 = __toESM(require_react(), 1);

// node_modules/@headlessui/react/dist/utils/get-text-value.js
var a2 = /([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;
function o10(e) {
  var r3, i6;
  let n4 = (r3 = e.innerText) != null ? r3 : "", t11 = e.cloneNode(true);
  if (!(t11 instanceof HTMLElement))
    return n4;
  let u6 = false;
  for (let f4 of t11.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))
    f4.remove(), u6 = true;
  let l6 = u6 ? (i6 = t11.innerText) != null ? i6 : "" : n4;
  return a2.test(l6) && (l6 = l6.replace(a2, "")), l6;
}
function g2(e) {
  let n4 = e.getAttribute("aria-label");
  if (typeof n4 == "string")
    return n4.trim();
  let t11 = e.getAttribute("aria-labelledby");
  if (t11) {
    let u6 = t11.split(" ").map((l6) => {
      let r3 = document.getElementById(l6);
      if (r3) {
        let i6 = r3.getAttribute("aria-label");
        return typeof i6 == "string" ? i6.trim() : o10(r3).trim();
      }
      return null;
    }).filter(Boolean);
    if (u6.length > 0)
      return u6.join(", ");
  }
  return o10(e).trim();
}

// node_modules/@headlessui/react/dist/hooks/use-text-value.js
function s7(c4) {
  let t11 = (0, import_react16.useRef)(""), r3 = (0, import_react16.useRef)("");
  return o2(() => {
    let e = c4.current;
    if (!e)
      return "";
    let u6 = e.innerText;
    if (t11.current === u6)
      return r3.current;
    let n4 = g2(e).trim().toLowerCase();
    return t11.current = u6, r3.current = n4, n4;
  });
}

// node_modules/@headlessui/react/dist/components/menu/menu.js
var import_react17 = __toESM(require_react(), 1);
var me = ((r3) => (r3[r3.Open = 0] = "Open", r3[r3.Closed = 1] = "Closed", r3))(me || {});
var de = ((r3) => (r3[r3.Pointer = 0] = "Pointer", r3[r3.Other = 1] = "Other", r3))(de || {});
var fe = ((a3) => (a3[a3.OpenMenu = 0] = "OpenMenu", a3[a3.CloseMenu = 1] = "CloseMenu", a3[a3.GoToItem = 2] = "GoToItem", a3[a3.Search = 3] = "Search", a3[a3.ClearSearch = 4] = "ClearSearch", a3[a3.RegisterItem = 5] = "RegisterItem", a3[a3.UnregisterItem = 6] = "UnregisterItem", a3))(fe || {});
function w2(e, u6 = (r3) => r3) {
  let r3 = e.activeItemIndex !== null ? e.items[e.activeItemIndex] : null, s8 = I2(u6(e.items.slice()), (t11) => t11.dataRef.current.domRef.current), i6 = r3 ? s8.indexOf(r3) : null;
  return i6 === -1 && (i6 = null), { items: s8, activeItemIndex: i6 };
}
var Te = { [1](e) {
  return e.menuState === 1 ? e : { ...e, activeItemIndex: null, menuState: 1 };
}, [0](e) {
  return e.menuState === 0 ? e : { ...e, __demoMode: false, menuState: 0 };
}, [2]: (e, u6) => {
  var i6;
  let r3 = w2(e), s8 = f3(u6, { resolveItems: () => r3.items, resolveActiveIndex: () => r3.activeItemIndex, resolveId: (t11) => t11.id, resolveDisabled: (t11) => t11.dataRef.current.disabled });
  return { ...e, ...r3, searchQuery: "", activeItemIndex: s8, activationTrigger: (i6 = u6.trigger) != null ? i6 : 1 };
}, [3]: (e, u6) => {
  let s8 = e.searchQuery !== "" ? 0 : 1, i6 = e.searchQuery + u6.value.toLowerCase(), o11 = (e.activeItemIndex !== null ? e.items.slice(e.activeItemIndex + s8).concat(e.items.slice(0, e.activeItemIndex + s8)) : e.items).find((l6) => {
    var m4;
    return ((m4 = l6.dataRef.current.textValue) == null ? void 0 : m4.startsWith(i6)) && !l6.dataRef.current.disabled;
  }), a3 = o11 ? e.items.indexOf(o11) : -1;
  return a3 === -1 || a3 === e.activeItemIndex ? { ...e, searchQuery: i6 } : { ...e, searchQuery: i6, activeItemIndex: a3, activationTrigger: 1 };
}, [4](e) {
  return e.searchQuery === "" ? e : { ...e, searchQuery: "", searchActiveItemIndex: null };
}, [5]: (e, u6) => {
  let r3 = w2(e, (s8) => [...s8, { id: u6.id, dataRef: u6.dataRef }]);
  return { ...e, ...r3 };
}, [6]: (e, u6) => {
  let r3 = w2(e, (s8) => {
    let i6 = s8.findIndex((t11) => t11.id === u6.id);
    return i6 !== -1 && s8.splice(i6, 1), s8;
  });
  return { ...e, ...r3, activationTrigger: 1 };
} };
var U2 = (0, import_react17.createContext)(null);
U2.displayName = "MenuContext";
function C2(e) {
  let u6 = (0, import_react17.useContext)(U2);
  if (u6 === null) {
    let r3 = new Error(`<${e} /> is missing a parent <Menu /> component.`);
    throw Error.captureStackTrace && Error.captureStackTrace(r3, C2), r3;
  }
  return u6;
}
function ye(e, u6) {
  return u(u6.type, Te, e, u6);
}
var Ie = import_react17.Fragment;
function Me(e, u6) {
  let { __demoMode: r3 = false, ...s8 } = e, i6 = (0, import_react17.useReducer)(ye, { __demoMode: r3, menuState: r3 ? 0 : 1, buttonRef: (0, import_react17.createRef)(), itemsRef: (0, import_react17.createRef)(), items: [], searchQuery: "", activeItemIndex: null, activationTrigger: 1 }), [{ menuState: t11, itemsRef: o11, buttonRef: a3 }, l6] = i6, m4 = y3(u6);
  y2([a3, o11], (g3, R2) => {
    var p3;
    l6({ type: 1 }), h(R2, T.Loose) || (g3.preventDefault(), (p3 = a3.current) == null || p3.focus());
  }, t11 === 0);
  let I3 = o2(() => {
    l6({ type: 1 });
  }), A = (0, import_react17.useMemo)(() => ({ open: t11 === 0, close: I3 }), [t11, I3]), f4 = { ref: m4 };
  return import_react17.default.createElement(U2.Provider, { value: i6 }, import_react17.default.createElement(s6, { value: u(t11, { [0]: d5.Open, [1]: d5.Closed }) }, C({ ourProps: f4, theirProps: s8, slot: A, defaultTag: Ie, name: "Menu" })));
}
var ge = "button";
function Re(e, u6) {
  var R2;
  let r3 = I(), { id: s8 = `headlessui-menu-button-${r3}`, ...i6 } = e, [t11, o11] = C2("Menu.Button"), a3 = y3(t11.buttonRef, u6), l6 = p(), m4 = o2((p3) => {
    switch (p3.key) {
      case o9.Space:
      case o9.Enter:
      case o9.ArrowDown:
        p3.preventDefault(), p3.stopPropagation(), o11({ type: 0 }), l6.nextFrame(() => o11({ type: 2, focus: c3.First }));
        break;
      case o9.ArrowUp:
        p3.preventDefault(), p3.stopPropagation(), o11({ type: 0 }), l6.nextFrame(() => o11({ type: 2, focus: c3.Last }));
        break;
    }
  }), I3 = o2((p3) => {
    switch (p3.key) {
      case o9.Space:
        p3.preventDefault();
        break;
    }
  }), A = o2((p3) => {
    if (r2(p3.currentTarget))
      return p3.preventDefault();
    e.disabled || (t11.menuState === 0 ? (o11({ type: 1 }), l6.nextFrame(() => {
      var M2;
      return (M2 = t11.buttonRef.current) == null ? void 0 : M2.focus({ preventScroll: true });
    })) : (p3.preventDefault(), o11({ type: 0 })));
  }), f4 = (0, import_react17.useMemo)(() => ({ open: t11.menuState === 0 }), [t11]), g3 = { ref: a3, id: s8, type: T2(e, t11.buttonRef), "aria-haspopup": "menu", "aria-controls": (R2 = t11.itemsRef.current) == null ? void 0 : R2.id, "aria-expanded": t11.menuState === 0, onKeyDown: m4, onKeyUp: I3, onClick: A };
  return C({ ourProps: g3, theirProps: i6, slot: f4, defaultTag: ge, name: "Menu.Button" });
}
var Ae = "div";
var be = O2.RenderStrategy | O2.Static;
function Ee(e, u6) {
  var M2, b;
  let r3 = I(), { id: s8 = `headlessui-menu-items-${r3}`, ...i6 } = e, [t11, o11] = C2("Menu.Items"), a3 = y3(t11.itemsRef, u6), l6 = n2(t11.itemsRef), m4 = p(), I3 = u4(), A = (() => I3 !== null ? (I3 & d5.Open) === d5.Open : t11.menuState === 0)();
  (0, import_react17.useEffect)(() => {
    let n4 = t11.itemsRef.current;
    n4 && t11.menuState === 0 && n4 !== (l6 == null ? void 0 : l6.activeElement) && n4.focus({ preventScroll: true });
  }, [t11.menuState, t11.itemsRef, l6]), F2({ container: t11.itemsRef.current, enabled: t11.menuState === 0, accept(n4) {
    return n4.getAttribute("role") === "menuitem" ? NodeFilter.FILTER_REJECT : n4.hasAttribute("role") ? NodeFilter.FILTER_SKIP : NodeFilter.FILTER_ACCEPT;
  }, walk(n4) {
    n4.setAttribute("role", "none");
  } });
  let f4 = o2((n4) => {
    var E3, x2;
    switch (m4.dispose(), n4.key) {
      case o9.Space:
        if (t11.searchQuery !== "")
          return n4.preventDefault(), n4.stopPropagation(), o11({ type: 3, value: n4.key });
      case o9.Enter:
        if (n4.preventDefault(), n4.stopPropagation(), o11({ type: 1 }), t11.activeItemIndex !== null) {
          let { dataRef: S3 } = t11.items[t11.activeItemIndex];
          (x2 = (E3 = S3.current) == null ? void 0 : E3.domRef.current) == null || x2.click();
        }
        D(t11.buttonRef.current);
        break;
      case o9.ArrowDown:
        return n4.preventDefault(), n4.stopPropagation(), o11({ type: 2, focus: c3.Next });
      case o9.ArrowUp:
        return n4.preventDefault(), n4.stopPropagation(), o11({ type: 2, focus: c3.Previous });
      case o9.Home:
      case o9.PageUp:
        return n4.preventDefault(), n4.stopPropagation(), o11({ type: 2, focus: c3.First });
      case o9.End:
      case o9.PageDown:
        return n4.preventDefault(), n4.stopPropagation(), o11({ type: 2, focus: c3.Last });
      case o9.Escape:
        n4.preventDefault(), n4.stopPropagation(), o11({ type: 1 }), o3().nextFrame(() => {
          var S3;
          return (S3 = t11.buttonRef.current) == null ? void 0 : S3.focus({ preventScroll: true });
        });
        break;
      case o9.Tab:
        n4.preventDefault(), n4.stopPropagation(), o11({ type: 1 }), o3().nextFrame(() => {
          _(t11.buttonRef.current, n4.shiftKey ? M.Previous : M.Next);
        });
        break;
      default:
        n4.key.length === 1 && (o11({ type: 3, value: n4.key }), m4.setTimeout(() => o11({ type: 4 }), 350));
        break;
    }
  }), g3 = o2((n4) => {
    switch (n4.key) {
      case o9.Space:
        n4.preventDefault();
        break;
    }
  }), R2 = (0, import_react17.useMemo)(() => ({ open: t11.menuState === 0 }), [t11]), p3 = { "aria-activedescendant": t11.activeItemIndex === null || (M2 = t11.items[t11.activeItemIndex]) == null ? void 0 : M2.id, "aria-labelledby": (b = t11.buttonRef.current) == null ? void 0 : b.id, id: s8, onKeyDown: f4, onKeyUp: g3, role: "menu", tabIndex: 0, ref: a3 };
  return C({ ourProps: p3, theirProps: i6, slot: R2, defaultTag: Ae, features: be, visible: A, name: "Menu.Items" });
}
var Se = import_react17.Fragment;
function xe(e, u6) {
  let r3 = I(), { id: s8 = `headlessui-menu-item-${r3}`, disabled: i6 = false, ...t11 } = e, [o11, a3] = C2("Menu.Item"), l6 = o11.activeItemIndex !== null ? o11.items[o11.activeItemIndex].id === s8 : false, m4 = (0, import_react17.useRef)(null), I3 = y3(u6, m4);
  l(() => {
    if (o11.__demoMode || o11.menuState !== 0 || !l6 || o11.activationTrigger === 0)
      return;
    let T3 = o3();
    return T3.requestAnimationFrame(() => {
      var P2, B;
      (B = (P2 = m4.current) == null ? void 0 : P2.scrollIntoView) == null || B.call(P2, { block: "nearest" });
    }), T3.dispose;
  }, [o11.__demoMode, m4, l6, o11.menuState, o11.activationTrigger, o11.activeItemIndex]);
  let A = s7(m4), f4 = (0, import_react17.useRef)({ disabled: i6, domRef: m4, get textValue() {
    return A();
  } });
  l(() => {
    f4.current.disabled = i6;
  }, [f4, i6]), l(() => (a3({ type: 5, id: s8, dataRef: f4 }), () => a3({ type: 6, id: s8 })), [f4, s8]);
  let g3 = o2(() => {
    a3({ type: 1 });
  }), R2 = o2((T3) => {
    if (i6)
      return T3.preventDefault();
    a3({ type: 1 }), D(o11.buttonRef.current);
  }), p3 = o2(() => {
    if (i6)
      return a3({ type: 2, focus: c3.Nothing });
    a3({ type: 2, focus: c3.Specific, id: s8 });
  }), M2 = u3(), b = o2((T3) => M2.update(T3)), n4 = o2((T3) => {
    M2.wasMoved(T3) && (i6 || l6 || a3({ type: 2, focus: c3.Specific, id: s8, trigger: 0 }));
  }), E3 = o2((T3) => {
    M2.wasMoved(T3) && (i6 || l6 && a3({ type: 2, focus: c3.Nothing }));
  }), x2 = (0, import_react17.useMemo)(() => ({ active: l6, disabled: i6, close: g3 }), [l6, i6, g3]);
  return C({ ourProps: { id: s8, ref: I3, role: "menuitem", tabIndex: i6 === true ? void 0 : -1, "aria-disabled": i6 === true ? true : void 0, disabled: void 0, onClick: R2, onFocus: p3, onPointerEnter: b, onMouseEnter: b, onPointerMove: n4, onMouseMove: n4, onPointerLeave: E3, onMouseLeave: E3 }, theirProps: t11, slot: x2, defaultTag: Se, name: "Menu.Item" });
}
var Pe = U(Me);
var ve = U(Re);
var he = U(Ee);
var De = U(xe);
var qe = Object.assign(Pe, { Button: ve, Items: he, Item: De });

// app/images/Gun-Violence-Archive-Logo-Icon.svg
var Gun_Violence_Archive_Logo_Icon_default = "/build/_assets/Gun-Violence-Archive-Logo-Icon-ZND5RXOQ.svg";

// app/images/Icon-Arrow-Down.svg
var Icon_Arrow_Down_default = "/build/_assets/Icon-Arrow-Down-MQGEAPEO.svg";

// app/images/Icon-Arrow-Up.svg
var Icon_Arrow_Up_default = "/build/_assets/Icon-Arrow-Up-HPCVLA4K.svg";

// app/components/admin/Header.tsx
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/components/admin/Header.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/components/admin/Header.tsx"
  );
  import.meta.hot.lastModified = "1748977380601.4482";
}
var AdminHeader = () => {
  _s();
  const user = useUser();
  const submit = useSubmit();
  const onSignOut = () => {
    submit({}, {
      action: "/user/logout",
      method: "POST"
    });
  };
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("header", { className: "sticky top-0 z-50", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "xl:flex xl:flex-row-reverse justify-between space-x-3 bg-gray-800 pb-3 xl:pb-0", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex justify-end items-center space-x-10", children: [
      (user.role == "Admin" || user.role == "Editor") && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-sm text-white", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { href: "/admin/incident/new", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2.5", src: Icon_Admin_Add_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/components/admin/Header.tsx",
          lineNumber: 45,
          columnNumber: 10
        }, this),
        "Add Incident"
      ] }, void 0, true, {
        fileName: "app/components/admin/Header.tsx",
        lineNumber: 44,
        columnNumber: 9
      }, this) }, void 0, false, {
        fileName: "app/components/admin/Header.tsx",
        lineNumber: 43,
        columnNumber: 8
      }, this) }, void 0, false, {
        fileName: "app/components/admin/Header.tsx",
        lineNumber: 42,
        columnNumber: 58
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "h-full text-lg text-white", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe, { as: "div", className: "relative inline-block h-full text-left z-10", children: ({
        open
      }) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(import_jsx_dev_runtime.Fragment, { children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe.Button, { className: "inline-flex h-full min-w-40 xl:min-w-52 items-center bg-orange-500 px-5", children: [
          user.name,
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "ml-2.5", src: open ? Icon_Arrow_Up_default : Icon_Arrow_Down_default, alt: "", width: 8 }, void 0, false, {
            fileName: "app/components/admin/Header.tsx",
            lineNumber: 57,
            columnNumber: 11
          }, this)
        ] }, void 0, true, {
          fileName: "app/components/admin/Header.tsx",
          lineNumber: 55,
          columnNumber: 10
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe.Items, { className: "absolute right-0 w-full divide-y divide-orange-500 bg-orange-800", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe.Item, { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "block h-[60px] w-full pl-5 leading-[60px]", to: "/user/profile", children: "My Account" }, void 0, false, {
            fileName: "app/components/admin/Header.tsx",
            lineNumber: 61,
            columnNumber: 12
          }, this) }, void 0, false, {
            fileName: "app/components/admin/Header.tsx",
            lineNumber: 60,
            columnNumber: 11
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe.Item, { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { type: "button", className: "block h-[60px] w-full pl-5 text-left", onClick: onSignOut, children: "Sign Out" }, void 0, false, {
            fileName: "app/components/admin/Header.tsx",
            lineNumber: 66,
            columnNumber: 12
          }, this) }, void 0, false, {
            fileName: "app/components/admin/Header.tsx",
            lineNumber: 65,
            columnNumber: 11
          }, this)
        ] }, void 0, true, {
          fileName: "app/components/admin/Header.tsx",
          lineNumber: 59,
          columnNumber: 10
        }, this)
      ] }, void 0, true, {
        fileName: "app/components/admin/Header.tsx",
        lineNumber: 54,
        columnNumber: 19
      }, this) }, void 0, false, {
        fileName: "app/components/admin/Header.tsx",
        lineNumber: 51,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/components/admin/Header.tsx",
        lineNumber: 50,
        columnNumber: 6
      }, this)
    ] }, void 0, true, {
      fileName: "app/components/admin/Header.tsx",
      lineNumber: 41,
      columnNumber: 5
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex items-center space-x-3 xl:space-x-10", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: `/`, children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Gun_Violence_Archive_Logo_Icon_default, alt: "Gun Violence Archive", width: 60 }, void 0, false, {
        fileName: "app/components/admin/Header.tsx",
        lineNumber: 78,
        columnNumber: 8
      }, this) }, void 0, false, {
        fileName: "app/components/admin/Header.tsx",
        lineNumber: 77,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/components/admin/Header.tsx",
        lineNumber: 76,
        columnNumber: 6
      }, this),
      (user.role == "Admin" || user.role == "Editor") && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("ul", { className: "flex flex-wrap space-x-3 xl:space-x-8 xl:text-xl font-bold text-white", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(NavLink, { to: "/admin/workspace", className: ({
          isActive
        }) => isActive ? "underline underline-offset-10 decoration-3 decoration-orange-500" : "", children: "Workspace" }, void 0, false, {
          fileName: "app/components/admin/Header.tsx",
          lineNumber: 83,
          columnNumber: 9
        }, this) }, void 0, false, {
          fileName: "app/components/admin/Header.tsx",
          lineNumber: 82,
          columnNumber: 8
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(NavLink, { to: "/admin/incident", className: ({
          isActive
        }) => isActive ? "underline underline-offset-10 decoration-3 decoration-orange-500" : "", children: "Incidents" }, void 0, false, {
          fileName: "app/components/admin/Header.tsx",
          lineNumber: 90,
          columnNumber: 9
        }, this) }, void 0, false, {
          fileName: "app/components/admin/Header.tsx",
          lineNumber: 89,
          columnNumber: 8
        }, this),
        user.role == "Admin" && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(import_jsx_dev_runtime.Fragment, { children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(NavLink, { to: "/admin/dashboard", className: ({
            isActive
          }) => isActive ? "underline underline-offset-10 decoration-3 decoration-orange-500" : "", children: "Dashboards" }, void 0, false, {
            fileName: "app/components/admin/Header.tsx",
            lineNumber: 98,
            columnNumber: 11
          }, this) }, void 0, false, {
            fileName: "app/components/admin/Header.tsx",
            lineNumber: 97,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(NavLink, { to: "/admin/user", reloadDocument: true, className: ({
            isActive
          }) => isActive ? "underline underline-offset-10 decoration-3 decoration-orange-500" : "", children: "User Management" }, void 0, false, {
            fileName: "app/components/admin/Header.tsx",
            lineNumber: 105,
            columnNumber: 11
          }, this) }, void 0, false, {
            fileName: "app/components/admin/Header.tsx",
            lineNumber: 104,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe, { as: "div", className: "relative inline-block h-full text-left", children: ({
            open
          }) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(import_jsx_dev_runtime.Fragment, { children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe.Button, { className: "inline-flex h-full items-center", children: [
              "Settings",
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "ml-2.5", src: open ? Icon_Arrow_Up_default : Icon_Arrow_Down_default, alt: "", width: 8 }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 118,
                columnNumber: 15
              }, this)
            ] }, void 0, true, {
              fileName: "app/components/admin/Header.tsx",
              lineNumber: 116,
              columnNumber: 14
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe.Items, { className: "absolute -left-5 top-10 min-w-80 divide-y divide-gray-500 bg-gray-800", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe.Item, { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "block h-[60px] w-full pl-5 leading-[60px]", to: "/admin/toll", children: "Manage Toll" }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 122,
                columnNumber: 16
              }, this) }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 121,
                columnNumber: 15
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe.Item, { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "block h-[60px] w-full pl-5 leading-[60px]", to: "/admin/column", children: "Manage Column Templates" }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 127,
                columnNumber: 16
              }, this) }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 126,
                columnNumber: 15
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe.Item, { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "block h-[60px] w-full pl-5 leading-[60px]", to: "/admin/configuration", children: "Manage Configurations" }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 132,
                columnNumber: 16
              }, this) }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 131,
                columnNumber: 15
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe.Item, { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "block h-[60px] w-full pl-5 leading-[60px]", to: "/admin/taxonomy", children: "Manage Taxonomy" }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 137,
                columnNumber: 16
              }, this) }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 136,
                columnNumber: 15
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe.Item, { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "block h-[60px] w-full pl-5 leading-[60px]", to: "/admin/token", children: "Generate Token" }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 142,
                columnNumber: 16
              }, this) }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 141,
                columnNumber: 15
              }, this)
            ] }, void 0, true, {
              fileName: "app/components/admin/Header.tsx",
              lineNumber: 120,
              columnNumber: 14
            }, this)
          ] }, void 0, true, {
            fileName: "app/components/admin/Header.tsx",
            lineNumber: 115,
            columnNumber: 23
          }, this) }, void 0, false, {
            fileName: "app/components/admin/Header.tsx",
            lineNumber: 112,
            columnNumber: 11
          }, this) }, void 0, false, {
            fileName: "app/components/admin/Header.tsx",
            lineNumber: 111,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe, { as: "div", className: "relative inline-block h-full text-left", children: ({
            open
          }) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(import_jsx_dev_runtime.Fragment, { children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe.Button, { className: "inline-flex h-full items-center", children: [
              "Reports",
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "ml-2.5", src: open ? Icon_Arrow_Up_default : Icon_Arrow_Down_default, alt: "", width: 8 }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 157,
                columnNumber: 15
              }, this)
            ] }, void 0, true, {
              fileName: "app/components/admin/Header.tsx",
              lineNumber: 155,
              columnNumber: 14
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe.Items, { className: "absolute -left-5 top-10 min-w-80 divide-y divide-gray-500 bg-gray-800", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe.Item, { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "block h-[60px] w-full pl-5 leading-[60px]", to: "/admin/reports/geocode", children: "Geocode Error Logs" }, void 0, false, {
              fileName: "app/components/admin/Header.tsx",
              lineNumber: 161,
              columnNumber: 16
            }, this) }, void 0, false, {
              fileName: "app/components/admin/Header.tsx",
              lineNumber: 160,
              columnNumber: 15
            }, this) }, void 0, false, {
              fileName: "app/components/admin/Header.tsx",
              lineNumber: 159,
              columnNumber: 14
            }, this)
          ] }, void 0, true, {
            fileName: "app/components/admin/Header.tsx",
            lineNumber: 154,
            columnNumber: 23
          }, this) }, void 0, false, {
            fileName: "app/components/admin/Header.tsx",
            lineNumber: 151,
            columnNumber: 11
          }, this) }, void 0, false, {
            fileName: "app/components/admin/Header.tsx",
            lineNumber: 150,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe, { as: "div", className: "relative inline-block h-full text-left", children: ({
            open
          }) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(import_jsx_dev_runtime.Fragment, { children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe.Button, { className: "inline-flex h-full items-center", children: [
              "System",
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "ml-2.5", src: open ? Icon_Arrow_Up_default : Icon_Arrow_Down_default, alt: "", width: 8 }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 176,
                columnNumber: 15
              }, this)
            ] }, void 0, true, {
              fileName: "app/components/admin/Header.tsx",
              lineNumber: 174,
              columnNumber: 14
            }, this),
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe.Items, { className: "absolute -left-5 top-10 min-w-80 divide-y divide-gray-500 bg-gray-800", children: [
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe.Item, { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "block h-[60px] w-full pl-5 leading-[60px]", to: "/admin/system/aws", children: "AWS Stats" }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 180,
                columnNumber: 16
              }, this) }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 179,
                columnNumber: 15
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe.Item, { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "block h-[60px] w-full pl-5 leading-[60px]", to: "/admin/system/health", children: "Site Health" }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 185,
                columnNumber: 16
              }, this) }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 184,
                columnNumber: 15
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe.Item, { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "block h-[60px] w-full pl-5 leading-[60px]", to: "/admin/system/migrations", children: "Database Migrations" }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 190,
                columnNumber: 16
              }, this) }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 189,
                columnNumber: 15
              }, this),
              /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(qe.Item, { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { className: "block h-[60px] w-full pl-5 leading-[60px]", to: "/admin/system/errors", children: "Error Logs" }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 195,
                columnNumber: 16
              }, this) }, void 0, false, {
                fileName: "app/components/admin/Header.tsx",
                lineNumber: 194,
                columnNumber: 15
              }, this)
            ] }, void 0, true, {
              fileName: "app/components/admin/Header.tsx",
              lineNumber: 178,
              columnNumber: 14
            }, this)
          ] }, void 0, true, {
            fileName: "app/components/admin/Header.tsx",
            lineNumber: 173,
            columnNumber: 23
          }, this) }, void 0, false, {
            fileName: "app/components/admin/Header.tsx",
            lineNumber: 170,
            columnNumber: 11
          }, this) }, void 0, false, {
            fileName: "app/components/admin/Header.tsx",
            lineNumber: 169,
            columnNumber: 10
          }, this)
        ] }, void 0, true, {
          fileName: "app/components/admin/Header.tsx",
          lineNumber: 96,
          columnNumber: 33
        }, this)
      ] }, void 0, true, {
        fileName: "app/components/admin/Header.tsx",
        lineNumber: 81,
        columnNumber: 58
      }, this)
    ] }, void 0, true, {
      fileName: "app/components/admin/Header.tsx",
      lineNumber: 75,
      columnNumber: 5
    }, this)
  ] }, void 0, true, {
    fileName: "app/components/admin/Header.tsx",
    lineNumber: 40,
    columnNumber: 4
  }, this) }, void 0, false, {
    fileName: "app/components/admin/Header.tsx",
    lineNumber: 39,
    columnNumber: 10
  }, this);
};
_s(AdminHeader, "+Ndybgbnp0wy2S0Wz1zwbUeBAnM=", false, function() {
  return [useUser, useSubmit];
});
_c = AdminHeader;
var _c;
$RefreshReg$(_c, "AdminHeader");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;

export {
  Gun_Violence_Archive_Logo_Icon_default,
  AdminHeader
};
//# sourceMappingURL=/build/_shared/chunk-W7JLG7II.js.map
