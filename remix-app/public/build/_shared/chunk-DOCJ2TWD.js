import {
  require_jsx_runtime
} from "/build/_shared/chunk-5NT3OUFA.js";
import {
  require_react_dom
} from "/build/_shared/chunk-74BWT7FI.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// node_modules/@dnd-kit/react/index.js
var import_react2 = __toESM(require_react(), 1);

// node_modules/@preact/signals-core/dist/signals-core.module.js
var i = Symbol.for("preact-signals");
function t() {
  if (!(s > 1)) {
    var i2, t2 = false;
    while (void 0 !== h) {
      var r2 = h;
      h = void 0;
      f++;
      while (void 0 !== r2) {
        var o2 = r2.o;
        r2.o = void 0;
        r2.f &= -3;
        if (!(8 & r2.f) && c(r2))
          try {
            r2.c();
          } catch (r3) {
            if (!t2) {
              i2 = r3;
              t2 = true;
            }
          }
        r2 = o2;
      }
    }
    f = 0;
    s--;
    if (t2)
      throw i2;
  } else
    s--;
}
function r(i2) {
  if (s > 0)
    return i2();
  s++;
  try {
    return i2();
  } finally {
    t();
  }
}
var o = void 0;
function n(i2) {
  var t2 = o;
  o = void 0;
  try {
    return i2();
  } finally {
    o = t2;
  }
}
var h = void 0;
var s = 0;
var f = 0;
var v = 0;
function e(i2) {
  if (void 0 !== o) {
    var t2 = i2.n;
    if (void 0 === t2 || t2.t !== o) {
      t2 = { i: 0, S: i2, p: o.s, n: void 0, t: o, e: void 0, x: void 0, r: t2 };
      if (void 0 !== o.s)
        o.s.n = t2;
      o.s = t2;
      i2.n = t2;
      if (32 & o.f)
        i2.S(t2);
      return t2;
    } else if (-1 === t2.i) {
      t2.i = 0;
      if (void 0 !== t2.n) {
        t2.n.p = t2.p;
        if (void 0 !== t2.p)
          t2.p.n = t2.n;
        t2.p = o.s;
        t2.n = void 0;
        o.s.n = t2;
        o.s = t2;
      }
      return t2;
    }
  }
}
function u(i2) {
  this.v = i2;
  this.i = 0;
  this.n = void 0;
  this.t = void 0;
}
u.prototype.brand = i;
u.prototype.h = function() {
  return true;
};
u.prototype.S = function(i2) {
  if (this.t !== i2 && void 0 === i2.e) {
    i2.x = this.t;
    if (void 0 !== this.t)
      this.t.e = i2;
    this.t = i2;
  }
};
u.prototype.U = function(i2) {
  if (void 0 !== this.t) {
    var t2 = i2.e, r2 = i2.x;
    if (void 0 !== t2) {
      t2.x = r2;
      i2.e = void 0;
    }
    if (void 0 !== r2) {
      r2.e = t2;
      i2.x = void 0;
    }
    if (i2 === this.t)
      this.t = r2;
  }
};
u.prototype.subscribe = function(i2) {
  var t2 = this;
  return E(function() {
    var r2 = t2.value, n2 = o;
    o = void 0;
    try {
      i2(r2);
    } finally {
      o = n2;
    }
  });
};
u.prototype.valueOf = function() {
  return this.value;
};
u.prototype.toString = function() {
  return this.value + "";
};
u.prototype.toJSON = function() {
  return this.value;
};
u.prototype.peek = function() {
  var i2 = o;
  o = void 0;
  try {
    return this.value;
  } finally {
    o = i2;
  }
};
Object.defineProperty(u.prototype, "value", { get: function() {
  var i2 = e(this);
  if (void 0 !== i2)
    i2.i = this.i;
  return this.v;
}, set: function(i2) {
  if (i2 !== this.v) {
    if (f > 100)
      throw new Error("Cycle detected");
    this.v = i2;
    this.i++;
    v++;
    s++;
    try {
      for (var r2 = this.t; void 0 !== r2; r2 = r2.x)
        r2.t.N();
    } finally {
      t();
    }
  }
} });
function d(i2) {
  return new u(i2);
}
function c(i2) {
  for (var t2 = i2.s; void 0 !== t2; t2 = t2.n)
    if (t2.S.i !== t2.i || !t2.S.h() || t2.S.i !== t2.i)
      return true;
  return false;
}
function a(i2) {
  for (var t2 = i2.s; void 0 !== t2; t2 = t2.n) {
    var r2 = t2.S.n;
    if (void 0 !== r2)
      t2.r = r2;
    t2.S.n = t2;
    t2.i = -1;
    if (void 0 === t2.n) {
      i2.s = t2;
      break;
    }
  }
}
function l(i2) {
  var t2 = i2.s, r2 = void 0;
  while (void 0 !== t2) {
    var o2 = t2.p;
    if (-1 === t2.i) {
      t2.S.U(t2);
      if (void 0 !== o2)
        o2.n = t2.n;
      if (void 0 !== t2.n)
        t2.n.p = o2;
    } else
      r2 = t2;
    t2.S.n = t2.r;
    if (void 0 !== t2.r)
      t2.r = void 0;
    t2 = o2;
  }
  i2.s = r2;
}
function y(i2) {
  u.call(this, void 0);
  this.x = i2;
  this.s = void 0;
  this.g = v - 1;
  this.f = 4;
}
(y.prototype = new u()).h = function() {
  this.f &= -3;
  if (1 & this.f)
    return false;
  if (32 == (36 & this.f))
    return true;
  this.f &= -5;
  if (this.g === v)
    return true;
  this.g = v;
  this.f |= 1;
  if (this.i > 0 && !c(this)) {
    this.f &= -2;
    return true;
  }
  var i2 = o;
  try {
    a(this);
    o = this;
    var t2 = this.x();
    if (16 & this.f || this.v !== t2 || 0 === this.i) {
      this.v = t2;
      this.f &= -17;
      this.i++;
    }
  } catch (i3) {
    this.v = i3;
    this.f |= 16;
    this.i++;
  }
  o = i2;
  l(this);
  this.f &= -2;
  return true;
};
y.prototype.S = function(i2) {
  if (void 0 === this.t) {
    this.f |= 36;
    for (var t2 = this.s; void 0 !== t2; t2 = t2.n)
      t2.S.S(t2);
  }
  u.prototype.S.call(this, i2);
};
y.prototype.U = function(i2) {
  if (void 0 !== this.t) {
    u.prototype.U.call(this, i2);
    if (void 0 === this.t) {
      this.f &= -33;
      for (var t2 = this.s; void 0 !== t2; t2 = t2.n)
        t2.S.U(t2);
    }
  }
};
y.prototype.N = function() {
  if (!(2 & this.f)) {
    this.f |= 6;
    for (var i2 = this.t; void 0 !== i2; i2 = i2.x)
      i2.t.N();
  }
};
Object.defineProperty(y.prototype, "value", { get: function() {
  if (1 & this.f)
    throw new Error("Cycle detected");
  var i2 = e(this);
  this.h();
  if (void 0 !== i2)
    i2.i = this.i;
  if (16 & this.f)
    throw this.v;
  return this.v;
} });
function w(i2) {
  return new y(i2);
}
function _(i2) {
  var r2 = i2.u;
  i2.u = void 0;
  if ("function" == typeof r2) {
    s++;
    var n2 = o;
    o = void 0;
    try {
      r2();
    } catch (t2) {
      i2.f &= -2;
      i2.f |= 8;
      g(i2);
      throw t2;
    } finally {
      o = n2;
      t();
    }
  }
}
function g(i2) {
  for (var t2 = i2.s; void 0 !== t2; t2 = t2.n)
    t2.S.U(t2);
  i2.x = void 0;
  i2.s = void 0;
  _(i2);
}
function p(i2) {
  if (o !== this)
    throw new Error("Out-of-order effect");
  l(this);
  o = i2;
  this.f &= -2;
  if (8 & this.f)
    g(this);
  t();
}
function b(i2) {
  this.x = i2;
  this.u = void 0;
  this.s = void 0;
  this.o = void 0;
  this.f = 32;
}
b.prototype.c = function() {
  var i2 = this.S();
  try {
    if (8 & this.f)
      return;
    if (void 0 === this.x)
      return;
    var t2 = this.x();
    if ("function" == typeof t2)
      this.u = t2;
  } finally {
    i2();
  }
};
b.prototype.S = function() {
  if (1 & this.f)
    throw new Error("Cycle detected");
  this.f |= 1;
  this.f &= -9;
  _(this);
  a(this);
  s++;
  var i2 = o;
  o = this;
  return p.bind(this, i2);
};
b.prototype.N = function() {
  if (!(2 & this.f)) {
    this.f |= 2;
    this.o = h;
    h = this;
  }
};
b.prototype.d = function() {
  this.f |= 8;
  if (!(1 & this.f))
    g(this);
};
function E(i2) {
  var t2 = new b(i2);
  try {
    t2.c();
  } catch (i3) {
    t2.d();
    throw i3;
  }
  return t2.d.bind(t2);
}

// node_modules/@dnd-kit/state/dist/index.mjs
function computed(compute, comparator) {
  if (comparator) {
    let previousValue;
    return w(() => {
      const value = compute();
      if (value && previousValue && comparator(previousValue, value)) {
        return previousValue;
      }
      previousValue = value;
      return value;
    });
  }
  return w(compute);
}
function deepEqual(a2, b2) {
  if (a2 === b2) {
    return true;
  }
  if (typeof a2 === "function" && typeof b2 === "function") {
    return a2 === b2;
  }
  if (a2 instanceof Set && b2 instanceof Set) {
    if (a2.size !== b2.size) {
      return false;
    }
    for (const value of a2) {
      if (!b2.has(value)) {
        return false;
      }
    }
    return true;
  }
  if (Array.isArray(a2)) {
    if (!Array.isArray(b2) || a2.length !== b2.length) {
      return false;
    }
    const hasDifferentValues = a2.some(
      (value, index) => !deepEqual(value, b2[index])
    );
    return !hasDifferentValues;
  }
  try {
    return JSON.stringify(a2) === JSON.stringify(b2);
  } catch (e2) {
    return false;
  }
}
function reactive({ get }, _2) {
  return {
    init(value) {
      return d(value);
    },
    get() {
      const current = get.call(this);
      return current.value;
    },
    set(newValue) {
      const current = get.call(this);
      if (current.peek() === newValue) {
        return;
      }
      current.value = newValue;
    }
  };
}
function derived(target, _2) {
  const map = /* @__PURE__ */ new WeakMap();
  return function() {
    let result = map.get(this);
    if (!result) {
      result = computed(target.bind(this));
      map.set(this, result);
    }
    return result.value;
  };
}
function effects(...entries) {
  const effects2 = entries.map(E);
  return () => effects2.forEach((cleanup) => cleanup());
}

// node_modules/@dnd-kit/geometry/dist/index.mjs
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __knownSymbol = (name, symbol) => (symbol = Symbol[name]) ? symbol : Symbol.for("Symbol." + name);
var __typeError = (msg) => {
  throw TypeError(msg);
};
var __pow = Math.pow;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a2, b2) => {
  for (var prop in b2 || (b2 = {}))
    if (__hasOwnProp.call(b2, prop))
      __defNormalProp(a2, prop, b2[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b2)) {
      if (__propIsEnum.call(b2, prop))
        __defNormalProp(a2, prop, b2[prop]);
    }
  return a2;
};
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __decoratorStart = (base) => {
  var _a4;
  return [, , , __create((_a4 = base == null ? void 0 : base[__knownSymbol("metadata")]) != null ? _a4 : null)];
};
var __decoratorStrings = ["class", "method", "getter", "setter", "accessor", "field", "value", "get", "set"];
var __expectFn = (fn) => fn !== void 0 && typeof fn !== "function" ? __typeError("Function expected") : fn;
var __decoratorContext = (kind, name, done, metadata, fns) => ({ kind: __decoratorStrings[kind], name, metadata, addInitializer: (fn) => done._ ? __typeError("Already initialized") : fns.push(__expectFn(fn || null)) });
var __decoratorMetadata = (array, target) => __defNormalProp(target, __knownSymbol("metadata"), array[3]);
var __runInitializers = (array, flags, self, value) => {
  for (var i2 = 0, fns = array[flags >> 1], n2 = fns && fns.length; i2 < n2; i2++)
    flags & 1 ? fns[i2].call(self) : value = fns[i2].call(self, value);
  return value;
};
var __decorateElement = (array, flags, name, decorators, target, extra) => {
  var fn, it, done, ctx, access, k = flags & 7, s2 = !!(flags & 8), p2 = !!(flags & 16);
  var j = k > 3 ? array.length + 1 : k ? s2 ? 1 : 2 : 0, key = __decoratorStrings[k + 5];
  var initializers = k > 3 && (array[j - 1] = []), extraInitializers = array[j] || (array[j] = []);
  var desc = k && (!p2 && !s2 && (target = target.prototype), k < 5 && (k > 3 || !p2) && __getOwnPropDesc(k < 4 ? target : { get [name]() {
    return __privateGet(this, extra);
  }, set [name](x) {
    return __privateSet(this, extra, x);
  } }, name));
  k ? p2 && k < 4 && __name(extra, (k > 2 ? "set " : k > 1 ? "get " : "") + name) : __name(target, name);
  for (var i2 = decorators.length - 1; i2 >= 0; i2--) {
    ctx = __decoratorContext(k, name, done = {}, array[3], extraInitializers);
    if (k) {
      ctx.static = s2, ctx.private = p2, access = ctx.access = { has: p2 ? (x) => __privateIn(target, x) : (x) => name in x };
      if (k ^ 3)
        access.get = p2 ? (x) => (k ^ 1 ? __privateGet : __privateMethod)(x, target, k ^ 4 ? extra : desc.get) : (x) => x[name];
      if (k > 2)
        access.set = p2 ? (x, y2) => __privateSet(x, target, y2, k ^ 4 ? extra : desc.set) : (x, y2) => x[name] = y2;
    }
    it = (0, decorators[i2])(k ? k < 4 ? p2 ? extra : desc[key] : k > 4 ? void 0 : { get: desc.get, set: desc.set } : target, ctx), done._ = 1;
    if (k ^ 4 || it === void 0)
      __expectFn(it) && (k > 4 ? initializers.unshift(it) : k ? p2 ? extra = it : desc[key] = it : target = it);
    else if (typeof it !== "object" || it === null)
      __typeError("Object expected");
    else
      __expectFn(fn = it.get) && (desc.get = fn), __expectFn(fn = it.set) && (desc.set = fn), __expectFn(fn = it.init) && initializers.unshift(fn);
  }
  return k || __decoratorMetadata(array, target), desc && __defProp(target, name, desc), p2 ? k ^ 4 ? extra : desc : target;
};
var __accessCheck = (obj, member, msg) => member.has(obj) || __typeError("Cannot " + msg);
var __privateIn = (member, obj) => Object(obj) !== obj ? __typeError('Cannot use the "in" operator on this value') : member.has(obj);
var __privateGet = (obj, member, getter) => (__accessCheck(obj, member, "read from private field"), getter ? getter.call(obj) : member.get(obj));
var __privateAdd = (obj, member, value) => member.has(obj) ? __typeError("Cannot add the same private member more than once") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);
var __privateSet = (obj, member, value, setter) => (__accessCheck(obj, member, "write to private field"), setter ? setter.call(obj, value) : member.set(obj, value), value);
var __privateMethod = (obj, member, method) => (__accessCheck(obj, member, "access private method"), method);
var Point = class _Point {
  /**
   * @param {number} Coordinate of the point on the horizontal axis
   * @param {number} Coordinate of the point on the vertical axis
   */
  constructor(x, y2) {
    this.x = x;
    this.y = y2;
  }
  /**
   * Returns the delta between this point and another point.
   *
   * @param {Point} a - A point
   * @param {Point} b - Another point
   */
  static delta(a2, b2) {
    return new _Point(a2.x - b2.x, a2.y - b2.y);
  }
  /**
   * Returns the distance (hypotenuse) between this point and another point.
   *
   * @param {Point} a - A point
   * @param {Point} b - Another point
   */
  static distance(a2, b2) {
    return Math.hypot(a2.x - b2.x, a2.y - b2.y);
  }
  /**
   * Returns true if both points are equal.
   *
   * @param {Point} a - A point
   * @param {Point} b - Another point
   */
  static equals(a2, b2) {
    return a2.x === b2.x && a2.y === b2.y;
  }
  static from({ x, y: y2 }) {
    return new _Point(x, y2);
  }
};
var Rectangle = class _Rectangle {
  constructor(left, top, width, height) {
    this.left = left;
    this.top = top;
    this.width = width;
    this.height = height;
    this.scale = {
      x: 1,
      y: 1
    };
  }
  get inverseScale() {
    return {
      x: 1 / this.scale.x,
      y: 1 / this.scale.y
    };
  }
  translate(x, y2) {
    const { top, left, width, height, scale } = this;
    const newShape = new _Rectangle(left + x, top + y2, width, height);
    newShape.scale = __spreadValues({}, scale);
    return newShape;
  }
  get boundingRectangle() {
    const { width, height, left, top, right, bottom } = this;
    return { width, height, left, top, right, bottom };
  }
  get center() {
    const { left, top, right, bottom } = this;
    return new Point((left + right) / 2, (top + bottom) / 2);
  }
  get area() {
    const { width, height } = this;
    return width * height;
  }
  equals(shape) {
    if (!(shape instanceof _Rectangle)) {
      return false;
    }
    const { left, top, width, height } = this;
    return left === shape.left && top === shape.top && width === shape.width && height === shape.height;
  }
  containsPoint(point) {
    const { top, left, bottom, right } = this;
    return top <= point.y && point.y <= bottom && left <= point.x && point.x <= right;
  }
  intersectionArea(shape) {
    if (shape instanceof _Rectangle) {
      return rectangleRectangleIntersection(this, shape);
    }
    return 0;
  }
  intersectionRatio(shape) {
    const { area } = this;
    const intersectionArea = this.intersectionArea(shape);
    const intersectionRatio = intersectionArea / (shape.area + area - intersectionArea);
    return intersectionRatio;
  }
  get bottom() {
    const { top, height } = this;
    return top + height;
  }
  get right() {
    const { left, width } = this;
    return left + width;
  }
  get aspectRatio() {
    const { width, height } = this;
    return width / height;
  }
  get corners() {
    return [
      { x: this.left, y: this.top },
      { x: this.right, y: this.top },
      { x: this.left, y: this.bottom },
      { x: this.right, y: this.bottom }
    ];
  }
  static from({ top, left, width, height }) {
    return new _Rectangle(left, top, width, height);
  }
  static delta(a2, b2, alignment = { x: "center", y: "center" }) {
    const getCoordinate = (rect, axis) => {
      const align = alignment[axis];
      const start = axis === "x" ? rect.left : rect.top;
      const size = axis === "x" ? rect.width : rect.height;
      if (align == "start")
        return start;
      if (align == "end")
        return start + size;
      return start + size / 2;
    };
    return Point.delta(
      { x: getCoordinate(a2, "x"), y: getCoordinate(a2, "y") },
      { x: getCoordinate(b2, "x"), y: getCoordinate(b2, "y") }
    );
  }
  static intersectionRatio(a2, b2) {
    return _Rectangle.from(a2).intersectionRatio(_Rectangle.from(b2));
  }
};
function rectangleRectangleIntersection(a2, b2) {
  const top = Math.max(b2.top, a2.top);
  const left = Math.max(b2.left, a2.left);
  const right = Math.min(b2.left + b2.width, a2.left + a2.width);
  const bottom = Math.min(b2.top + b2.height, a2.top + a2.height);
  const width = right - left;
  const height = bottom - top;
  if (left < right && top < bottom) {
    const intersectionArea = width * height;
    return intersectionArea;
  }
  return 0;
}
var SENSITIVITY = 10;
var _direction_dec;
var _delta_dec;
var _current_dec;
var _previous_dec;
var _initial_dec;
var _velocity_dec;
var _timestamp;
var _init;
var _velocity;
var _initial;
var _previous;
var _current;
_velocity_dec = [reactive], _initial_dec = [reactive], _previous_dec = [reactive], _current_dec = [reactive], _delta_dec = [derived], _direction_dec = [derived];
var Position = class {
  constructor(initialValue) {
    __runInitializers(_init, 5, this);
    __privateAdd(this, _timestamp, 0);
    __privateAdd(this, _velocity, __runInitializers(_init, 8, this, {
      x: 0,
      y: 0
    })), __runInitializers(_init, 11, this);
    __privateAdd(this, _initial, __runInitializers(_init, 12, this)), __runInitializers(_init, 15, this);
    __privateAdd(this, _previous, __runInitializers(_init, 16, this)), __runInitializers(_init, 19, this);
    __privateAdd(this, _current, __runInitializers(_init, 20, this)), __runInitializers(_init, 23, this);
    const point = Point.from(initialValue);
    this.initial = point;
    this.current = point;
    this.previous = point;
  }
  get delta() {
    return Point.delta(this.current, this.initial);
  }
  get direction() {
    const delta = {
      x: this.current.x - this.previous.x,
      y: this.current.y - this.previous.y
    };
    if (!delta.x && !delta.y) {
      return null;
    }
    if (Math.abs(delta.x) > Math.abs(delta.y)) {
      return delta.x > 0 ? "right" : "left";
    }
    return delta.y > 0 ? "down" : "up";
  }
  reset(coordinates) {
    const point = Point.from(coordinates);
    r(() => {
      __privateSet(this, _timestamp, 0);
      this.velocity = { x: 0, y: 0 };
      this.current = point;
      this.previous = point;
      this.initial = point;
    });
  }
  update(coordinates) {
    const { current } = this;
    const point = Point.from(coordinates);
    if (Point.equals(current, point)) {
      return;
    }
    const delta = {
      x: point.x - current.x,
      y: point.y - current.y
    };
    const timestamp = Date.now();
    const timeDelta = timestamp - __privateGet(this, _timestamp);
    const velocity = (delta2) => Math.round(delta2 / timeDelta * 100);
    if (Math.abs(delta.x) < SENSITIVITY || Math.abs(delta.y) < SENSITIVITY) {
      this.previous = current;
    }
    __privateSet(this, _timestamp, timestamp);
    this.velocity = {
      x: velocity(delta.x),
      y: velocity(delta.y)
    };
    this.current = point;
  }
};
_init = __decoratorStart(null);
_timestamp = /* @__PURE__ */ new WeakMap();
_velocity = /* @__PURE__ */ new WeakMap();
_initial = /* @__PURE__ */ new WeakMap();
_previous = /* @__PURE__ */ new WeakMap();
_current = /* @__PURE__ */ new WeakMap();
__decorateElement(_init, 4, "velocity", _velocity_dec, Position, _velocity);
__decorateElement(_init, 4, "initial", _initial_dec, Position, _initial);
__decorateElement(_init, 4, "previous", _previous_dec, Position, _previous);
__decorateElement(_init, 4, "current", _current_dec, Position, _current);
__decorateElement(_init, 2, "delta", _delta_dec, Position);
__decorateElement(_init, 2, "direction", _direction_dec, Position);
__decoratorMetadata(_init, Position);
function exceedsDistance({ x, y: y2 }, distance) {
  const dx = Math.abs(x);
  const dy = Math.abs(y2);
  if (typeof distance === "number") {
    return Math.sqrt(__pow(dx, 2) + __pow(dy, 2)) > distance;
  }
  if ("x" in distance && "y" in distance) {
    return dx > distance.x && dy > distance.y;
  }
  if ("x" in distance) {
    return dx > distance.x;
  }
  if ("y" in distance) {
    return dy > distance.y;
  }
  return false;
}
var Axis = /* @__PURE__ */ ((Axis2) => {
  Axis2["Horizontal"] = "x";
  Axis2["Vertical"] = "y";
  return Axis2;
})(Axis || {});
var Axes = Object.values(Axis);

// node_modules/@dnd-kit/abstract/index.js
var __create2 = Object.create;
var __defProp2 = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDesc2 = Object.getOwnPropertyDescriptor;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols2 = Object.getOwnPropertySymbols;
var __hasOwnProp2 = Object.prototype.hasOwnProperty;
var __propIsEnum2 = Object.prototype.propertyIsEnumerable;
var __knownSymbol2 = (name, symbol) => (symbol = Symbol[name]) ? symbol : Symbol.for("Symbol." + name);
var __typeError2 = (msg) => {
  throw TypeError(msg);
};
var __defNormalProp2 = (obj, key, value) => key in obj ? __defProp2(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues2 = (a2, b2) => {
  for (var prop in b2 || (b2 = {}))
    if (__hasOwnProp2.call(b2, prop))
      __defNormalProp2(a2, prop, b2[prop]);
  if (__getOwnPropSymbols2)
    for (var prop of __getOwnPropSymbols2(b2)) {
      if (__propIsEnum2.call(b2, prop))
        __defNormalProp2(a2, prop, b2[prop]);
    }
  return a2;
};
var __spreadProps = (a2, b2) => __defProps(a2, __getOwnPropDescs(b2));
var __name2 = (target, value) => __defProp2(target, "name", { value, configurable: true });
var __objRest = (source, exclude) => {
  var target = {};
  for (var prop in source)
    if (__hasOwnProp2.call(source, prop) && exclude.indexOf(prop) < 0)
      target[prop] = source[prop];
  if (source != null && __getOwnPropSymbols2)
    for (var prop of __getOwnPropSymbols2(source)) {
      if (exclude.indexOf(prop) < 0 && __propIsEnum2.call(source, prop))
        target[prop] = source[prop];
    }
  return target;
};
var __decoratorStart2 = (base) => {
  var _a4;
  return [, , , __create2((_a4 = base == null ? void 0 : base[__knownSymbol2("metadata")]) != null ? _a4 : null)];
};
var __decoratorStrings2 = ["class", "method", "getter", "setter", "accessor", "field", "value", "get", "set"];
var __expectFn2 = (fn) => fn !== void 0 && typeof fn !== "function" ? __typeError2("Function expected") : fn;
var __decoratorContext2 = (kind, name, done, metadata, fns) => ({ kind: __decoratorStrings2[kind], name, metadata, addInitializer: (fn) => done._ ? __typeError2("Already initialized") : fns.push(__expectFn2(fn || null)) });
var __decoratorMetadata2 = (array, target) => __defNormalProp2(target, __knownSymbol2("metadata"), array[3]);
var __runInitializers2 = (array, flags, self, value) => {
  for (var i2 = 0, fns = array[flags >> 1], n2 = fns && fns.length; i2 < n2; i2++)
    flags & 1 ? fns[i2].call(self) : value = fns[i2].call(self, value);
  return value;
};
var __decorateElement2 = (array, flags, name, decorators, target, extra) => {
  var fn, it, done, ctx, access, k = flags & 7, s2 = !!(flags & 8), p2 = !!(flags & 16);
  var j = k > 3 ? array.length + 1 : k ? s2 ? 1 : 2 : 0, key = __decoratorStrings2[k + 5];
  var initializers = k > 3 && (array[j - 1] = []), extraInitializers = array[j] || (array[j] = []);
  var desc = k && (!p2 && !s2 && (target = target.prototype), k < 5 && (k > 3 || !p2) && __getOwnPropDesc2(k < 4 ? target : { get [name]() {
    return __privateGet2(this, extra);
  }, set [name](x) {
    return __privateSet2(this, extra, x);
  } }, name));
  k ? p2 && k < 4 && __name2(extra, (k > 2 ? "set " : k > 1 ? "get " : "") + name) : __name2(target, name);
  for (var i2 = decorators.length - 1; i2 >= 0; i2--) {
    ctx = __decoratorContext2(k, name, done = {}, array[3], extraInitializers);
    if (k) {
      ctx.static = s2, ctx.private = p2, access = ctx.access = { has: p2 ? (x) => __privateIn2(target, x) : (x) => name in x };
      if (k ^ 3)
        access.get = p2 ? (x) => (k ^ 1 ? __privateGet2 : __privateMethod2)(x, target, k ^ 4 ? extra : desc.get) : (x) => x[name];
      if (k > 2)
        access.set = p2 ? (x, y2) => __privateSet2(x, target, y2, k ^ 4 ? extra : desc.set) : (x, y2) => x[name] = y2;
    }
    it = (0, decorators[i2])(k ? k < 4 ? p2 ? extra : desc[key] : k > 4 ? void 0 : { get: desc.get, set: desc.set } : target, ctx), done._ = 1;
    if (k ^ 4 || it === void 0)
      __expectFn2(it) && (k > 4 ? initializers.unshift(it) : k ? p2 ? extra = it : desc[key] = it : target = it);
    else if (typeof it !== "object" || it === null)
      __typeError2("Object expected");
    else
      __expectFn2(fn = it.get) && (desc.get = fn), __expectFn2(fn = it.set) && (desc.set = fn), __expectFn2(fn = it.init) && initializers.unshift(fn);
  }
  return k || __decoratorMetadata2(array, target), desc && __defProp2(target, name, desc), p2 ? k ^ 4 ? extra : desc : target;
};
var __accessCheck2 = (obj, member, msg) => member.has(obj) || __typeError2("Cannot " + msg);
var __privateIn2 = (member, obj) => Object(obj) !== obj ? __typeError2('Cannot use the "in" operator on this value') : member.has(obj);
var __privateGet2 = (obj, member, getter) => (__accessCheck2(obj, member, "read from private field"), getter ? getter.call(obj) : member.get(obj));
var __privateAdd2 = (obj, member, value) => member.has(obj) ? __typeError2("Cannot add the same private member more than once") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);
var __privateSet2 = (obj, member, value, setter) => (__accessCheck2(obj, member, "write to private field"), setter ? setter.call(obj, value) : member.set(obj, value), value);
var __privateMethod2 = (obj, member, method) => (__accessCheck2(obj, member, "access private method"), method);
function configure(plugin, options2) {
  return {
    plugin,
    options: options2
  };
}
function configurator(plugin) {
  return (options2) => {
    return configure(plugin, options2);
  };
}
function descriptor(plugin) {
  if (typeof plugin === "function") {
    return {
      plugin,
      options: void 0
    };
  }
  return plugin;
}
var _disabled_dec;
var _init2;
var _disabled;
var _cleanupFunctions;
_disabled_dec = [reactive];
var Plugin = class {
  constructor(manager, options2) {
    this.manager = manager;
    this.options = options2;
    __privateAdd2(this, _disabled, __runInitializers2(_init2, 8, this, false)), __runInitializers2(_init2, 11, this);
    __privateAdd2(this, _cleanupFunctions, /* @__PURE__ */ new Set());
  }
  /**
   * Enable a disabled plugin instance.
   * Triggers effects.
   */
  enable() {
    this.disabled = false;
  }
  /**
   * Disable an enabled plugin instance.
   * Triggers effects.
   */
  disable() {
    this.disabled = true;
  }
  /**
   * Whether the plugin instance is disabled.
   * Does not trigger effects when accessed.
   */
  isDisabled() {
    return n(() => {
      return this.disabled;
    });
  }
  /**
   * Configure a plugin instance with new options.
   */
  configure(options2) {
    this.options = options2;
  }
  /**
   * Register an effect that will be cleaned up when the plugin instance is destroyed.
   * Returns a cleanup function that can be used to dispose of the effect.
   */
  registerEffect(callback) {
    const dispose = E(callback.bind(this));
    __privateGet2(this, _cleanupFunctions).add(dispose);
    return dispose;
  }
  /**
   * Destroy a plugin instance.
   */
  destroy() {
    __privateGet2(this, _cleanupFunctions).forEach((cleanup) => cleanup());
  }
  /**
   * Configure a plugin constructor with options.
   * This method is used to configure the options that the
   * plugin constructor will use to create plugin instances.
   */
  static configure(options2) {
    return configure(this, options2);
  }
};
_init2 = __decoratorStart2(null);
_disabled = /* @__PURE__ */ new WeakMap();
_cleanupFunctions = /* @__PURE__ */ new WeakMap();
__decorateElement2(_init2, 4, "disabled", _disabled_dec, Plugin, _disabled);
__decoratorMetadata2(_init2, Plugin);
var CorePlugin = class extends Plugin {
};
var _previousValues;
var PluginRegistry = class {
  constructor(manager) {
    this.manager = manager;
    this.instances = /* @__PURE__ */ new Map();
    __privateAdd2(this, _previousValues, []);
  }
  get values() {
    return Array.from(this.instances.values());
  }
  set values(entries) {
    const descriptors = entries.map(descriptor).reduceRight((acc, descriptor2) => {
      if (acc.some(({ plugin }) => plugin === descriptor2.plugin)) {
        return acc;
      }
      return [descriptor2, ...acc];
    }, []);
    const constructors = descriptors.map(({ plugin }) => plugin);
    for (const plugin of __privateGet2(this, _previousValues)) {
      if (!constructors.includes(plugin)) {
        if (plugin.prototype instanceof CorePlugin) {
          continue;
        }
        this.unregister(plugin);
      }
    }
    for (const { plugin, options: options2 } of descriptors) {
      this.register(plugin, options2);
    }
    __privateSet2(this, _previousValues, constructors);
  }
  get(plugin) {
    const instance = this.instances.get(plugin);
    return instance;
  }
  register(plugin, options2) {
    const existingInstance = this.instances.get(plugin);
    if (existingInstance) {
      if (existingInstance.options !== options2) {
        existingInstance.options = options2;
      }
      return existingInstance;
    }
    const instance = new plugin(this.manager, options2);
    this.instances.set(plugin, instance);
    return instance;
  }
  unregister(plugin) {
    const instance = this.instances.get(plugin);
    if (instance) {
      instance.destroy();
      this.instances.delete(plugin);
    }
  }
  destroy() {
    for (const plugin of this.instances.values()) {
      plugin.destroy();
    }
    this.instances.clear();
  }
};
_previousValues = /* @__PURE__ */ new WeakMap();
function sortCollisions(a2, b2) {
  if (a2.priority === b2.priority) {
    if (a2.type === b2.type) {
      return b2.value - a2.value;
    }
    return b2.type - a2.type;
  }
  return b2.priority - a2.priority;
}
var DEFAULT_VALUE = [];
var _previousCoordinates;
var _collisions;
var CollisionObserver = class extends Plugin {
  constructor(manager) {
    super(manager);
    __privateAdd2(this, _previousCoordinates);
    __privateAdd2(this, _collisions);
    this.computeCollisions = this.computeCollisions.bind(this);
    __privateSet2(this, _collisions, d(DEFAULT_VALUE));
    this.destroy = effects(
      () => {
        const collisions = this.computeCollisions();
        const coordinates = n(
          () => this.manager.dragOperation.position.current
        );
        if (collisions !== DEFAULT_VALUE) {
          const previousCoordinates = __privateGet2(this, _previousCoordinates);
          __privateSet2(this, _previousCoordinates, coordinates);
          if (previousCoordinates && coordinates.x == previousCoordinates.x && coordinates.y == previousCoordinates.y) {
            return;
          }
        } else {
          __privateSet2(this, _previousCoordinates, void 0);
        }
        __privateGet2(this, _collisions).value = collisions;
      },
      () => {
        const { dragOperation } = this.manager;
        if (dragOperation.status.initialized) {
          this.forceUpdate();
        }
      }
    );
  }
  forceUpdate(immediate = true) {
    n(() => {
      if (immediate) {
        __privateGet2(this, _collisions).value = this.computeCollisions();
      } else {
        __privateSet2(this, _previousCoordinates, void 0);
      }
    });
  }
  computeCollisions(entries, collisionDetector) {
    const { registry, dragOperation } = this.manager;
    const { source, shape, status } = dragOperation;
    if (!status.initialized || !shape) {
      return DEFAULT_VALUE;
    }
    const collisions = [];
    const potentialTargets = [];
    for (const entry of entries != null ? entries : registry.droppables) {
      if (entry.disabled) {
        continue;
      }
      if (source && !entry.accepts(source)) {
        continue;
      }
      const detectCollision = collisionDetector != null ? collisionDetector : entry.collisionDetector;
      if (!detectCollision) {
        continue;
      }
      potentialTargets.push(entry);
      void entry.shape;
      const collision = n(
        () => detectCollision({
          droppable: entry,
          dragOperation
        })
      );
      if (collision) {
        if (entry.collisionPriority != null) {
          collision.priority = entry.collisionPriority;
        }
        collisions.push(collision);
      }
    }
    if (potentialTargets.length === 0) {
      return DEFAULT_VALUE;
    }
    collisions.sort(sortCollisions);
    return collisions;
  }
  get collisions() {
    return __privateGet2(this, _collisions).value;
  }
};
_previousCoordinates = /* @__PURE__ */ new WeakMap();
_collisions = /* @__PURE__ */ new WeakMap();
var Monitor = class {
  constructor() {
    this.registry = /* @__PURE__ */ new Map();
  }
  addEventListener(name, handler) {
    const { registry } = this;
    const listeners = new Set(registry.get(name));
    listeners.add(handler);
    registry.set(name, listeners);
    return () => this.removeEventListener(name, handler);
  }
  removeEventListener(name, handler) {
    const { registry } = this;
    const listeners = new Set(registry.get(name));
    listeners.delete(handler);
    registry.set(name, listeners);
  }
  dispatch(name, ...args) {
    const { registry } = this;
    const listeners = registry.get(name);
    if (!listeners) {
      return;
    }
    for (const listener of listeners) {
      listener(...args);
    }
  }
};
var DragDropMonitor = class extends Monitor {
  constructor(manager) {
    super();
    this.manager = manager;
  }
  dispatch(type, event) {
    const args = [event, this.manager];
    super.dispatch(type, ...args);
  }
};
function defaultPreventable(event, cancelable = true) {
  let defaultPrevented = false;
  return __spreadProps(__spreadValues2({}, event), {
    cancelable,
    get defaultPrevented() {
      return defaultPrevented;
    },
    preventDefault() {
      if (!cancelable) {
        return;
      }
      defaultPrevented = true;
    }
  });
}
var CollisionNotifier = class extends CorePlugin {
  constructor(manager) {
    super(manager);
    const isEqual = (a2, b2) => a2.map(({ id }) => id).join("") === b2.map(({ id }) => id).join("");
    let previousCollisions = [];
    this.destroy = effects(
      () => {
        const { dragOperation, collisionObserver } = manager;
        if (dragOperation.status.initializing) {
          previousCollisions = [];
          collisionObserver.enable();
        }
      },
      () => {
        const { collisionObserver, monitor } = manager;
        const { collisions } = collisionObserver;
        if (collisionObserver.isDisabled()) {
          return;
        }
        const event = defaultPreventable({
          collisions
        });
        monitor.dispatch("collision", event);
        if (event.defaultPrevented) {
          return;
        }
        if (isEqual(collisions, previousCollisions)) {
          return;
        } else {
          previousCollisions = collisions;
        }
        const [firstCollision] = collisions;
        n(() => {
          var _a4;
          if ((firstCollision == null ? void 0 : firstCollision.id) !== ((_a4 = manager.dragOperation.target) == null ? void 0 : _a4.id)) {
            collisionObserver.disable();
            manager.actions.setDropTarget(firstCollision == null ? void 0 : firstCollision.id).then(() => {
              collisionObserver.enable();
            });
          }
        });
      }
    );
  }
};
var CollisionPriority = /* @__PURE__ */ ((CollisionPriority2) => {
  CollisionPriority2[CollisionPriority2["Lowest"] = 0] = "Lowest";
  CollisionPriority2[CollisionPriority2["Low"] = 1] = "Low";
  CollisionPriority2[CollisionPriority2["Normal"] = 2] = "Normal";
  CollisionPriority2[CollisionPriority2["High"] = 3] = "High";
  CollisionPriority2[CollisionPriority2["Highest"] = 4] = "Highest";
  return CollisionPriority2;
})(CollisionPriority || {});
var CollisionType = /* @__PURE__ */ ((CollisionType2) => {
  CollisionType2[CollisionType2["Collision"] = 0] = "Collision";
  CollisionType2[CollisionType2["ShapeIntersection"] = 1] = "ShapeIntersection";
  CollisionType2[CollisionType2["PointerIntersection"] = 2] = "PointerIntersection";
  return CollisionType2;
})(CollisionType || {});
var _disabled_dec2;
var _data_dec;
var _id_dec;
var _manager_dec;
var _init22;
var _manager;
var _id;
var _data;
var _disabled2;
_manager_dec = [reactive], _id_dec = [reactive], _data_dec = [reactive], _disabled_dec2 = [reactive];
var Entity = class {
  /**
   * Creates a new instance of the `Entity` class.
   *
   * @param input - An object containing the initial properties of the entity.
   * @param manager - The manager that controls the drag and drop operations.
   */
  constructor(input, manager) {
    __privateAdd2(this, _manager, __runInitializers2(_init22, 8, this)), __runInitializers2(_init22, 11, this);
    __privateAdd2(this, _id, __runInitializers2(_init22, 12, this)), __runInitializers2(_init22, 15, this);
    __privateAdd2(this, _data, __runInitializers2(_init22, 16, this)), __runInitializers2(_init22, 19, this);
    __privateAdd2(this, _disabled2, __runInitializers2(_init22, 20, this)), __runInitializers2(_init22, 23, this);
    const { effects: effects5, id, data = {}, disabled = false, register = true } = input;
    let previousId = id;
    this.manager = manager;
    this.id = id;
    this.data = data;
    this.disabled = disabled;
    this.effects = () => {
      var _a4;
      return [
        () => {
          const { id: id2, manager: manager2 } = this;
          if (id2 === previousId) {
            return;
          }
          manager2 == null ? void 0 : manager2.registry.register(this);
          return () => manager2 == null ? void 0 : manager2.registry.unregister(this);
        },
        ...(_a4 = effects5 == null ? void 0 : effects5()) != null ? _a4 : []
      ];
    };
    this.register = this.register.bind(this);
    this.unregister = this.unregister.bind(this);
    this.destroy = this.destroy.bind(this);
    if (manager && register) {
      queueMicrotask(this.register);
    }
  }
  /**
   * A method that registers the entity with the manager.
   * @returns CleanupFunction | void
   */
  register() {
    var _a4;
    return (_a4 = this.manager) == null ? void 0 : _a4.registry.register(this);
  }
  /**
   * A method that unregisters the entity from the manager.
   * @returns void
   */
  unregister() {
    var _a4;
    (_a4 = this.manager) == null ? void 0 : _a4.registry.unregister(this);
  }
  /**
   * A method that cleans up the entity when it is no longer needed.
   * @returns void
   */
  destroy() {
    var _a4;
    (_a4 = this.manager) == null ? void 0 : _a4.registry.unregister(this);
  }
};
_init22 = __decoratorStart2(null);
_manager = /* @__PURE__ */ new WeakMap();
_id = /* @__PURE__ */ new WeakMap();
_data = /* @__PURE__ */ new WeakMap();
_disabled2 = /* @__PURE__ */ new WeakMap();
__decorateElement2(_init22, 4, "manager", _manager_dec, Entity, _manager);
__decorateElement2(_init22, 4, "id", _id_dec, Entity, _id);
__decorateElement2(_init22, 4, "data", _data_dec, Entity, _data);
__decorateElement2(_init22, 4, "disabled", _disabled_dec2, Entity, _disabled2);
__decoratorMetadata2(_init22, Entity);
var EntityRegistry = class {
  constructor() {
    this.map = d(/* @__PURE__ */ new Map());
    this.cleanupFunctions = /* @__PURE__ */ new WeakMap();
    this.register = (key, value) => {
      const current = this.map.peek();
      const currentValue2 = current.get(key);
      const unregister = () => this.unregister(key, value);
      if (currentValue2 === value)
        return unregister;
      if (currentValue2) {
        const cleanup2 = this.cleanupFunctions.get(currentValue2);
        cleanup2 == null ? void 0 : cleanup2();
        this.cleanupFunctions.delete(currentValue2);
      }
      const updatedMap = new Map(current);
      updatedMap.set(key, value);
      this.map.value = updatedMap;
      const cleanup = effects(...value.effects());
      this.cleanupFunctions.set(value, cleanup);
      return unregister;
    };
    this.unregister = (key, value) => {
      const current = this.map.peek();
      if (current.get(key) !== value) {
        return;
      }
      const cleanup = this.cleanupFunctions.get(value);
      cleanup == null ? void 0 : cleanup();
      this.cleanupFunctions.delete(value);
      const updatedMap = new Map(current);
      updatedMap.delete(key);
      this.map.value = updatedMap;
    };
  }
  /**
   * Iterator for the EntityRegistry class.
   * @returns An iterator for the values in the map.
   */
  [Symbol.iterator]() {
    return this.map.peek().values();
  }
  get value() {
    return this.map.value.values();
  }
  /**
   * Checks if a entity with the given identifier exists in the registry.
   * @param identifier - The unique identifier of the entity.
   * @returns True if the entity exists, false otherwise.
   */
  has(identifier) {
    return this.map.value.has(identifier);
  }
  /**
   * Retrieves a entity from the registry using its identifier.
   * @param identifier - The unique identifier of the entity.
   * @returns The entity if it exists, undefined otherwise.
   */
  get(identifier) {
    return this.map.value.get(identifier);
  }
  /**
   * Destroys all entries in the registry and clears the registry.
   */
  destroy() {
    for (const entry of this) {
      const cleanup = this.cleanupFunctions.get(entry);
      cleanup == null ? void 0 : cleanup();
      entry.destroy();
    }
    this.map.value = /* @__PURE__ */ new Map();
  }
};
var _isDragSource_dec;
var _isDragging_dec;
var _isDropping_dec;
var _status_dec;
var _modifiers_dec;
var _type_dec;
var _c;
var _init3;
var _type;
var _modifiers;
var _status;
var Draggable = class extends (_c = Entity, _type_dec = [reactive], _modifiers_dec = [reactive], _status_dec = [reactive], _isDropping_dec = [derived], _isDragging_dec = [derived], _isDragSource_dec = [derived], _c) {
  constructor(_a4, manager) {
    var _b2 = _a4, { modifiers, type, sensors } = _b2, input = __objRest(_b2, ["modifiers", "type", "sensors"]);
    super(input, manager);
    __runInitializers2(_init3, 5, this);
    __privateAdd2(this, _type, __runInitializers2(_init3, 8, this)), __runInitializers2(_init3, 11, this);
    __privateAdd2(this, _modifiers, __runInitializers2(_init3, 12, this)), __runInitializers2(_init3, 15, this);
    __privateAdd2(this, _status, __runInitializers2(_init3, 16, this, this.isDragSource ? "dragging" : "idle")), __runInitializers2(_init3, 19, this);
    this.type = type;
    this.sensors = sensors;
    this.modifiers = modifiers;
    this.alignment = input.alignment;
  }
  get isDropping() {
    return this.status === "dropping" && this.isDragSource;
  }
  get isDragging() {
    return this.status === "dragging" && this.isDragSource;
  }
  get isDragSource() {
    var _a4, _b2;
    return ((_b2 = (_a4 = this.manager) == null ? void 0 : _a4.dragOperation.source) == null ? void 0 : _b2.id) === this.id;
  }
};
_init3 = __decoratorStart2(_c);
_type = /* @__PURE__ */ new WeakMap();
_modifiers = /* @__PURE__ */ new WeakMap();
_status = /* @__PURE__ */ new WeakMap();
__decorateElement2(_init3, 4, "type", _type_dec, Draggable, _type);
__decorateElement2(_init3, 4, "modifiers", _modifiers_dec, Draggable, _modifiers);
__decorateElement2(_init3, 4, "status", _status_dec, Draggable, _status);
__decorateElement2(_init3, 2, "isDropping", _isDropping_dec, Draggable);
__decorateElement2(_init3, 2, "isDragging", _isDragging_dec, Draggable);
__decorateElement2(_init3, 2, "isDragSource", _isDragSource_dec, Draggable);
__decoratorMetadata2(_init3, Draggable);
var _isDropTarget_dec;
var _shape_dec;
var _collisionPriority_dec;
var _collisionDetector_dec;
var _type_dec2;
var _accept_dec;
var _c2;
var _init4;
var _accept;
var _type2;
var _collisionDetector;
var _collisionPriority;
var _shape;
var Droppable = class extends (_c2 = Entity, _accept_dec = [reactive], _type_dec2 = [reactive], _collisionDetector_dec = [reactive], _collisionPriority_dec = [reactive], _shape_dec = [reactive], _isDropTarget_dec = [derived], _c2) {
  constructor(_a4, manager) {
    var _b2 = _a4, { accept, collisionDetector, collisionPriority, type } = _b2, input = __objRest(_b2, ["accept", "collisionDetector", "collisionPriority", "type"]);
    super(input, manager);
    __runInitializers2(_init4, 5, this);
    __privateAdd2(this, _accept, __runInitializers2(_init4, 8, this)), __runInitializers2(_init4, 11, this);
    __privateAdd2(this, _type2, __runInitializers2(_init4, 12, this)), __runInitializers2(_init4, 15, this);
    __privateAdd2(this, _collisionDetector, __runInitializers2(_init4, 16, this)), __runInitializers2(_init4, 19, this);
    __privateAdd2(this, _collisionPriority, __runInitializers2(_init4, 20, this)), __runInitializers2(_init4, 23, this);
    __privateAdd2(this, _shape, __runInitializers2(_init4, 24, this)), __runInitializers2(_init4, 27, this);
    this.accept = accept;
    this.collisionDetector = collisionDetector;
    this.collisionPriority = collisionPriority;
    this.type = type;
  }
  /**
   * Checks whether or not the droppable accepts a given draggable.
   *
   * @param {Draggable} draggable
   * @returns {boolean}
   */
  accepts(draggable) {
    const { accept } = this;
    if (!accept) {
      return true;
    }
    if (typeof accept === "function") {
      return accept(draggable);
    }
    if (!draggable.type) {
      return false;
    }
    if (Array.isArray(accept)) {
      return accept.includes(draggable.type);
    }
    return draggable.type === accept;
  }
  get isDropTarget() {
    var _a4, _b2;
    return ((_b2 = (_a4 = this.manager) == null ? void 0 : _a4.dragOperation.target) == null ? void 0 : _b2.id) === this.id;
  }
};
_init4 = __decoratorStart2(_c2);
_accept = /* @__PURE__ */ new WeakMap();
_type2 = /* @__PURE__ */ new WeakMap();
_collisionDetector = /* @__PURE__ */ new WeakMap();
_collisionPriority = /* @__PURE__ */ new WeakMap();
_shape = /* @__PURE__ */ new WeakMap();
__decorateElement2(_init4, 4, "accept", _accept_dec, Droppable, _accept);
__decorateElement2(_init4, 4, "type", _type_dec2, Droppable, _type2);
__decorateElement2(_init4, 4, "collisionDetector", _collisionDetector_dec, Droppable, _collisionDetector);
__decorateElement2(_init4, 4, "collisionPriority", _collisionPriority_dec, Droppable, _collisionPriority);
__decorateElement2(_init4, 4, "shape", _shape_dec, Droppable, _shape);
__decorateElement2(_init4, 2, "isDropTarget", _isDropTarget_dec, Droppable);
__decoratorMetadata2(_init4, Droppable);
var Sensor = class extends Plugin {
  constructor(manager, options2) {
    super(manager, options2);
    this.manager = manager;
    this.options = options2;
  }
};
var Modifier = class extends Plugin {
  constructor(manager, options2) {
    super(manager, options2);
    this.manager = manager;
    this.options = options2;
  }
  apply(operation) {
    return operation.transform;
  }
};
var DragDropRegistry = class {
  constructor(manager) {
    this.draggables = new EntityRegistry();
    this.droppables = new EntityRegistry();
    this.plugins = new PluginRegistry(manager);
    this.sensors = new PluginRegistry(manager);
    this.modifiers = new PluginRegistry(manager);
  }
  register(input, options2) {
    if (input instanceof Draggable) {
      return this.draggables.register(input.id, input);
    }
    if (input instanceof Droppable) {
      return this.droppables.register(input.id, input);
    }
    if (input.prototype instanceof Modifier) {
      return this.modifiers.register(input, options2);
    }
    if (input.prototype instanceof Sensor) {
      return this.sensors.register(input, options2);
    }
    if (input.prototype instanceof Plugin) {
      return this.plugins.register(input, options2);
    }
    throw new Error("Invalid instance type");
  }
  unregister(input) {
    if (input instanceof Entity) {
      if (input instanceof Draggable) {
        return this.draggables.unregister(input.id, input);
      }
      if (input instanceof Droppable) {
        return this.droppables.unregister(input.id, input);
      }
      return () => {
      };
    }
    if (input.prototype instanceof Modifier) {
      return this.modifiers.unregister(input);
    }
    if (input.prototype instanceof Sensor) {
      return this.sensors.unregister(input);
    }
    if (input.prototype instanceof Plugin) {
      return this.plugins.unregister(input);
    }
    throw new Error("Invalid instance type");
  }
  destroy() {
    this.draggables.destroy();
    this.droppables.destroy();
    this.plugins.destroy();
    this.sensors.destroy();
    this.modifiers.destroy();
  }
};
function DragOperationManager(manager) {
  const {
    registry: { draggables, droppables },
    monitor
  } = manager;
  const status = d(
    "idle"
    /* Idle */
  );
  const shape = {
    initial: d(null),
    current: d(null)
  };
  const canceled = d(false);
  const position = new Position({ x: 0, y: 0 });
  const activatorEvent = d(null);
  const sourceIdentifier = d(null);
  const targetIdentifier = d(null);
  const dragging = computed(
    () => status.value === "dragging"
    /* Dragging */
  );
  const initialized = computed(
    () => status.value !== "idle"
    /* Idle */
  );
  const initializing = computed(
    () => status.value === "initializing"
    /* Initializing */
  );
  const idle = computed(
    () => status.value === "idle"
    /* Idle */
  );
  const dropped = computed(
    () => status.value === "dropped"
    /* Dropped */
  );
  const dragended = d(true);
  let previousSource;
  const source = computed(() => {
    var _a4;
    const identifier = sourceIdentifier.value;
    if (identifier == null)
      return null;
    const value = draggables.get(identifier);
    if (value) {
      previousSource = value;
    }
    return (_a4 = value != null ? value : previousSource) != null ? _a4 : null;
  });
  const target = computed(() => {
    var _a4;
    const identifier = targetIdentifier.value;
    return identifier != null ? (_a4 = droppables.get(identifier)) != null ? _a4 : null : null;
  });
  const modifiers = d([]);
  const dispose = E(() => {
    var _a4, _b2, _c32;
    const currentModifiers = modifiers.peek();
    if (!deepEqual(currentModifiers, manager.modifiers)) {
      currentModifiers.forEach((modifier) => modifier.destroy());
    }
    modifiers.value = (_c32 = (_b2 = (_a4 = source.value) == null ? void 0 : _a4.modifiers) == null ? void 0 : _b2.map((modifier) => {
      const { plugin, options: options2 } = descriptor(modifier);
      return new plugin(manager, options2);
    })) != null ? _c32 : manager.modifiers;
  });
  const transform = computed(() => {
    const { x, y: y2 } = position.delta;
    let transform2 = { x, y: y2 };
    const initialShape = shape.initial.value;
    const currentShape = shape.current.peek();
    const operation2 = {
      activatorEvent: activatorEvent.peek(),
      canceled: canceled.peek(),
      source: source.peek(),
      target: target.peek(),
      status: {
        current: status.peek(),
        idle: idle.peek(),
        initializing: initializing.peek(),
        initialized: initialized.peek(),
        dragging: dragging.peek(),
        dragended: dragended.peek(),
        dropped: dropped.peek()
      },
      shape: initialShape && currentShape ? { initial: initialShape, current: currentShape } : null,
      position
    };
    for (const modifier of modifiers.value) {
      transform2 = modifier.apply(__spreadProps(__spreadValues2({}, operation2), { transform: transform2 }));
    }
    return transform2;
  });
  const operation = {
    get activatorEvent() {
      return activatorEvent.value;
    },
    get canceled() {
      return canceled.value;
    },
    get source() {
      return source.value;
    },
    get target() {
      return target.value;
    },
    status: {
      get current() {
        return status.value;
      },
      get idle() {
        return idle.value;
      },
      get initializing() {
        return initializing.value;
      },
      get initialized() {
        return initialized.value;
      },
      get dragging() {
        return dragging.value;
      },
      get dragended() {
        return dragended.value;
      },
      get dropped() {
        return dropped.value;
      }
    },
    get shape() {
      const initial = shape.initial.value;
      const current = shape.current.value;
      return initial && current ? { initial, current } : null;
    },
    set shape(value) {
      var _a4;
      if (value && ((_a4 = shape.current.peek()) == null ? void 0 : _a4.equals(value))) {
        return;
      }
      const initial = shape.initial.peek();
      if (!initial) {
        shape.initial.value = value;
      }
      shape.current.value = value;
    },
    get transform() {
      return transform.value;
    },
    position
  };
  const reset = () => {
    r(() => {
      status.value = "idle";
      sourceIdentifier.value = null;
      targetIdentifier.value = null;
      shape.current.value = null;
      shape.initial.value = null;
      position.reset({ x: 0, y: 0 });
      modifiers.value = [];
    });
  };
  const actions = {
    setDragSource(identifier) {
      sourceIdentifier.value = identifier;
    },
    setDropTarget(identifier) {
      const id = identifier != null ? identifier : null;
      if (targetIdentifier.peek() === id) {
        return Promise.resolve(false);
      }
      targetIdentifier.value = id;
      const event = defaultPreventable({
        operation: snapshot(operation)
      });
      if (status.peek() === "dragging") {
        monitor.dispatch("dragover", event);
      }
      return manager.renderer.rendering.then(() => event.defaultPrevented);
    },
    start({
      event: nativeEvent,
      coordinates
    }) {
      const sourceInstance = source.peek();
      if (!sourceInstance) {
        throw new Error("Cannot start a drag operation without a drag source");
      }
      r(() => {
        shape.initial.value = null;
        shape.current.value = null;
        dragended.value = false;
        canceled.value = false;
        activatorEvent.value = nativeEvent != null ? nativeEvent : null;
        position.reset(coordinates);
      });
      const beforeStartEvent = defaultPreventable({
        operation: snapshot(operation)
      });
      monitor.dispatch("beforedragstart", beforeStartEvent);
      manager.renderer.rendering.then(() => {
        if (beforeStartEvent.defaultPrevented) {
          reset();
          return;
        }
        status.value = "initializing";
        requestAnimationFrame(() => {
          status.value = "dragging";
          monitor.dispatch("dragstart", {
            nativeEvent,
            operation: snapshot(operation),
            cancelable: false
          });
        });
      });
    },
    move({
      by,
      to,
      event: nativeEvent,
      cancelable = true,
      propagate = true
    }) {
      if (!dragging.peek()) {
        return;
      }
      const event = defaultPreventable(
        {
          nativeEvent,
          operation: snapshot(operation),
          by,
          to
        },
        cancelable
      );
      if (propagate) {
        monitor.dispatch("dragmove", event);
      }
      queueMicrotask(() => {
        if (event.defaultPrevented) {
          return;
        }
        const coordinates = to != null ? to : {
          x: position.current.x + by.x,
          y: position.current.y + by.y
        };
        position.update(coordinates);
      });
    },
    stop({
      canceled: eventCanceled = false,
      event: nativeEvent
    } = {}) {
      let promise;
      const suspend = () => {
        const output = {
          resume: () => {
          },
          abort: () => {
          }
        };
        promise = new Promise((resolve, reject) => {
          output.resume = resolve;
          output.abort = reject;
        });
        return output;
      };
      const end = () => {
        manager.renderer.rendering.then(() => {
          status.value = "dropped";
          const dropping = n(() => {
            var _a4;
            return ((_a4 = source.value) == null ? void 0 : _a4.status) === "dropping";
          });
          if (dropping) {
            const currentSource = source.value;
            const dispose2 = E(() => {
              if ((currentSource == null ? void 0 : currentSource.status) === "idle") {
                dispose2();
                if (source.value !== currentSource)
                  return;
                reset();
              }
            });
          } else {
            manager.renderer.rendering.then(reset);
          }
        });
      };
      r(() => {
        dragended.value = true;
        canceled.value = eventCanceled;
      });
      monitor.dispatch("dragend", {
        nativeEvent,
        operation: snapshot(operation),
        canceled: eventCanceled,
        suspend
      });
      if (promise) {
        promise.then(end).catch(reset);
      } else {
        end();
      }
    }
  };
  return {
    operation,
    actions,
    cleanup() {
      if (status.peek() !== "idle") {
        actions.stop({ canceled: true });
      }
      modifiers.value.forEach((modifier) => modifier.destroy());
      dispose();
    }
  };
}
function snapshot(obj) {
  return __spreadValues2({}, obj);
}
var defaultRenderer = {
  get rendering() {
    return Promise.resolve();
  }
};
var DragDropManager = class {
  constructor(config) {
    this.destroy = () => {
      this.registry.destroy();
      this.collisionObserver.destroy();
    };
    const {
      plugins = [],
      sensors = [],
      modifiers = [],
      renderer = defaultRenderer
    } = config != null ? config : {};
    const monitor = new DragDropMonitor(this);
    const registry = new DragDropRegistry(this);
    this.registry = registry;
    this.monitor = monitor;
    this.renderer = renderer;
    const { actions, operation, cleanup } = DragOperationManager(this);
    this.actions = actions;
    this.dragOperation = operation;
    this.collisionObserver = new CollisionObserver(this);
    this.plugins = [CollisionNotifier, ...plugins];
    this.modifiers = modifiers;
    this.sensors = sensors;
    const { destroy } = this;
    this.destroy = () => {
      cleanup();
      destroy();
    };
  }
  get plugins() {
    return this.registry.plugins.values;
  }
  set plugins(plugins) {
    this.registry.plugins.values = plugins;
  }
  get modifiers() {
    return this.registry.modifiers.values;
  }
  set modifiers(modifiers) {
    this.registry.modifiers.values = modifiers;
  }
  get sensors() {
    return this.registry.sensors.values;
  }
  set sensors(sensors) {
    this.registry.sensors.values = sensors;
  }
};

// node_modules/@dnd-kit/dom/utilities.js
var __typeError3 = (msg) => {
  throw TypeError(msg);
};
var __accessCheck3 = (obj, member, msg) => member.has(obj) || __typeError3("Cannot " + msg);
var __privateGet3 = (obj, member, getter) => (__accessCheck3(obj, member, "read from private field"), getter ? getter.call(obj) : member.get(obj));
var __privateAdd3 = (obj, member, value) => member.has(obj) ? __typeError3("Cannot add the same private member more than once") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);
var __privateSet3 = (obj, member, value, setter) => (__accessCheck3(obj, member, "write to private field"), member.set(obj, value), value);
var __privateMethod3 = (obj, member, method) => (__accessCheck3(obj, member, "access private method"), method);
function getBoundingRectangle(element) {
  const { width, height, top, left, bottom, right } = element.getBoundingClientRect();
  return { width, height, top, left, bottom, right };
}
var canUseDOM = typeof window !== "undefined" && typeof window.document !== "undefined" && typeof window.document.createElement !== "undefined";
function isWindow(element) {
  const elementString = Object.prototype.toString.call(element);
  return elementString === "[object Window]" || // In Electron context the Window object serializes to [object global]
  elementString === "[object global]";
}
function isNode(node) {
  return "nodeType" in node;
}
function getWindow(target) {
  var _a4, _b2, _c6;
  if (!target) {
    return window;
  }
  if (isWindow(target)) {
    return target;
  }
  if (!isNode(target)) {
    return window;
  }
  if ("defaultView" in target) {
    return (_a4 = target.defaultView) != null ? _a4 : window;
  }
  return (_c6 = (_b2 = target.ownerDocument) == null ? void 0 : _b2.defaultView) != null ? _c6 : window;
}
function isDocument(node) {
  const { Document } = getWindow(node);
  return node instanceof Document;
}
function isHTMLElement(node) {
  if (!node || isWindow(node))
    return false;
  return node instanceof getWindow(node).HTMLElement;
}
function getDocument(target) {
  if (!target) {
    return document;
  }
  if (isWindow(target)) {
    return target.document;
  }
  if (!isNode(target)) {
    return document;
  }
  if (isDocument(target)) {
    return target;
  }
  if (isHTMLElement(target)) {
    return target.ownerDocument;
  }
  return document;
}
function getViewportBoundingRectangle(element) {
  const { documentElement } = getDocument(element);
  const width = documentElement.clientWidth;
  const height = documentElement.clientHeight;
  return {
    top: 0,
    left: 0,
    right: width,
    bottom: height,
    width,
    height
  };
}
function timeout(callback, duration) {
  const id = setTimeout(callback, duration);
  return () => clearTimeout(id);
}
function throttle(func, limit) {
  const time = () => performance.now();
  let cancel;
  let lastRan;
  return function(...args) {
    const context = this;
    if (!lastRan) {
      func.apply(context, args);
      lastRan = time();
    } else {
      cancel == null ? void 0 : cancel();
      cancel = timeout(
        () => {
          func.apply(context, args);
          lastRan = time();
        },
        limit - (time() - lastRan)
      );
    }
  };
}
function isRectEqual(a2, b2) {
  if (a2 === b2)
    return true;
  if (!a2 || !b2)
    return false;
  return a2.top == b2.top && a2.left == b2.left && a2.right == b2.right && a2.bottom == b2.bottom;
}
function isOverflowVisible(element, style) {
  if (element instanceof getWindow(element).HTMLDetailsElement && element.open === false) {
    return false;
  }
  const { overflow, overflowX, overflowY } = getComputedStyle(element);
  return overflow === "visible" && overflowX === "visible" && overflowY === "visible";
}
function getVisibleBoundingRectangle(element, boundingClientRect = element.getBoundingClientRect(), margin = 0) {
  var _a4;
  let rect = boundingClientRect;
  const { ownerDocument } = element;
  const ownerWindow = (_a4 = ownerDocument.defaultView) != null ? _a4 : window;
  let ancestor = element.parentElement;
  while (ancestor && ancestor !== ownerDocument.documentElement) {
    if (!isOverflowVisible(ancestor)) {
      const ancestorRect = ancestor.getBoundingClientRect();
      const marginTop = margin * (ancestorRect.bottom - ancestorRect.top);
      const marginRight = margin * (ancestorRect.right - ancestorRect.left);
      const marginBottom = margin * (ancestorRect.bottom - ancestorRect.top);
      const marginLeft = margin * (ancestorRect.right - ancestorRect.left);
      rect = {
        top: Math.max(rect.top, ancestorRect.top - marginTop),
        right: Math.min(rect.right, ancestorRect.right + marginRight),
        bottom: Math.min(rect.bottom, ancestorRect.bottom + marginBottom),
        left: Math.max(rect.left, ancestorRect.left - marginLeft),
        width: 0,
        // Will be calculated next
        height: 0
        // Will be calculated next
      };
      rect.width = rect.right - rect.left;
      rect.height = rect.bottom - rect.top;
    }
    ancestor = ancestor.parentElement;
  }
  const viewportWidth = ownerWindow.innerWidth;
  const viewportHeight = ownerWindow.innerHeight;
  const viewportMarginY = margin * viewportHeight;
  const viewportMarginX = margin * viewportWidth;
  rect = {
    top: Math.max(rect.top, 0 - viewportMarginY),
    right: Math.min(rect.right, viewportWidth + viewportMarginX),
    bottom: Math.min(rect.bottom, viewportHeight + viewportMarginY),
    left: Math.max(rect.left, 0 - viewportMarginX),
    width: 0,
    // Will be calculated next
    height: 0
    // Will be calculated next
  };
  rect.width = rect.right - rect.left;
  rect.height = rect.bottom - rect.top;
  if (rect.width < 0) {
    rect.width = 0;
  }
  if (rect.height < 0) {
    rect.height = 0;
  }
  return rect;
}
function isVisible(element, boundingClientRect = element.getBoundingClientRect()) {
  const { width, height } = getVisibleBoundingRectangle(
    element,
    boundingClientRect
  );
  return width > 0 && height > 0;
}
var threshold = Array.from({ length: 100 }, (_2, index) => index / 100);
var THROTTLE_INTERVAL = 75;
var _callback;
var _visible;
var _previousBoundingClientRect;
var _resizeObserver;
var _positionObserver;
var _visibilityObserver;
var _debug;
var _disconnected;
var _observePosition;
var _PositionObserver_instances;
var notify_fn;
var updateDebug_fn;
var PositionObserver = class {
  constructor(element, callback, options2 = { debug: false }) {
    this.element = element;
    __privateAdd3(this, _PositionObserver_instances);
    __privateAdd3(this, _callback);
    __privateAdd3(this, _visible, true);
    __privateAdd3(this, _previousBoundingClientRect);
    __privateAdd3(this, _resizeObserver);
    __privateAdd3(this, _positionObserver);
    __privateAdd3(this, _visibilityObserver);
    __privateAdd3(this, _debug);
    __privateAdd3(this, _disconnected, false);
    __privateAdd3(this, _observePosition, throttle(() => {
      var _a4, _b2, _c6;
      const { element: element2 } = this;
      (_a4 = __privateGet3(this, _positionObserver)) == null ? void 0 : _a4.disconnect();
      if (__privateGet3(this, _disconnected) || !__privateGet3(this, _visible) || !element2.isConnected) {
        return;
      }
      const root2 = (_b2 = element2.ownerDocument) != null ? _b2 : document;
      const { innerHeight, innerWidth } = (_c6 = root2.defaultView) != null ? _c6 : window;
      const clientRect = element2.getBoundingClientRect();
      const visibleRect = getVisibleBoundingRectangle(element2, clientRect);
      const { top, left, bottom, right } = visibleRect;
      const insetTop = -Math.floor(top);
      const insetLeft = -Math.floor(left);
      const insetRight = -Math.floor(innerWidth - right);
      const insetBottom = -Math.floor(innerHeight - bottom);
      const rootMargin = `${insetTop}px ${insetRight}px ${insetBottom}px ${insetLeft}px`;
      this.boundingClientRect = clientRect;
      __privateSet3(this, _positionObserver, new IntersectionObserver(
        (entries) => {
          const [entry] = entries;
          const { intersectionRect } = entry;
          const intersectionRatio = Rectangle.intersectionRatio(
            intersectionRect,
            visibleRect
          );
          if (intersectionRatio !== 1) {
            __privateGet3(this, _observePosition).call(this);
          }
        },
        {
          threshold,
          rootMargin,
          root: root2
        }
      ));
      __privateGet3(this, _positionObserver).observe(element2);
      __privateMethod3(this, _PositionObserver_instances, notify_fn).call(this);
    }, THROTTLE_INTERVAL));
    __privateSet3(this, _callback, callback);
    this.boundingClientRect = element.getBoundingClientRect();
    __privateSet3(this, _visible, isVisible(element, this.boundingClientRect));
    const root = element.ownerDocument;
    if (options2 == null ? void 0 : options2.debug) {
      __privateSet3(this, _debug, document.createElement("div"));
      __privateGet3(this, _debug).style.background = "rgba(0,0,0,0.15)";
      __privateGet3(this, _debug).style.position = "fixed";
      __privateGet3(this, _debug).style.pointerEvents = "none";
      root.body.appendChild(__privateGet3(this, _debug));
    }
    __privateSet3(this, _visibilityObserver, new IntersectionObserver(
      (entries) => {
        var _a4, _b2;
        const entry = entries[entries.length - 1];
        const { boundingClientRect, isIntersecting: visible } = entry;
        const { width, height } = boundingClientRect;
        const previousVisible = __privateGet3(this, _visible);
        __privateSet3(this, _visible, visible);
        if (!width && !height)
          return;
        if (previousVisible && !visible) {
          (_a4 = __privateGet3(this, _positionObserver)) == null ? void 0 : _a4.disconnect();
          __privateGet3(this, _callback).call(this, null);
          (_b2 = __privateGet3(this, _resizeObserver)) == null ? void 0 : _b2.disconnect();
          __privateSet3(this, _resizeObserver, void 0);
          if (__privateGet3(this, _debug))
            __privateGet3(this, _debug).style.visibility = "hidden";
        } else {
          __privateGet3(this, _observePosition).call(this);
        }
        if (visible && !__privateGet3(this, _resizeObserver)) {
          __privateSet3(this, _resizeObserver, new ResizeObserver(__privateGet3(this, _observePosition)));
          __privateGet3(this, _resizeObserver).observe(element);
        }
      },
      {
        threshold,
        root
      }
    ));
    if (__privateGet3(this, _visible)) {
      __privateGet3(this, _callback).call(this, this.boundingClientRect);
    }
    __privateGet3(this, _visibilityObserver).observe(element);
  }
  disconnect() {
    var _a4, _b2, _c6;
    __privateSet3(this, _disconnected, true);
    (_a4 = __privateGet3(this, _resizeObserver)) == null ? void 0 : _a4.disconnect();
    (_b2 = __privateGet3(this, _positionObserver)) == null ? void 0 : _b2.disconnect();
    __privateGet3(this, _visibilityObserver).disconnect();
    (_c6 = __privateGet3(this, _debug)) == null ? void 0 : _c6.remove();
  }
};
_callback = /* @__PURE__ */ new WeakMap();
_visible = /* @__PURE__ */ new WeakMap();
_previousBoundingClientRect = /* @__PURE__ */ new WeakMap();
_resizeObserver = /* @__PURE__ */ new WeakMap();
_positionObserver = /* @__PURE__ */ new WeakMap();
_visibilityObserver = /* @__PURE__ */ new WeakMap();
_debug = /* @__PURE__ */ new WeakMap();
_disconnected = /* @__PURE__ */ new WeakMap();
_observePosition = /* @__PURE__ */ new WeakMap();
_PositionObserver_instances = /* @__PURE__ */ new WeakSet();
notify_fn = function() {
  if (__privateGet3(this, _disconnected))
    return;
  __privateMethod3(this, _PositionObserver_instances, updateDebug_fn).call(this);
  if (isRectEqual(this.boundingClientRect, __privateGet3(this, _previousBoundingClientRect)))
    return;
  __privateGet3(this, _callback).call(this, this.boundingClientRect);
  __privateSet3(this, _previousBoundingClientRect, this.boundingClientRect);
};
updateDebug_fn = function() {
  if (__privateGet3(this, _debug)) {
    const { top, left, width, height } = getVisibleBoundingRectangle(
      this.element
    );
    __privateGet3(this, _debug).style.overflow = "hidden";
    __privateGet3(this, _debug).style.visibility = "visible";
    __privateGet3(this, _debug).style.top = `${Math.floor(top)}px`;
    __privateGet3(this, _debug).style.left = `${Math.floor(left)}px`;
    __privateGet3(this, _debug).style.width = `${Math.floor(width)}px`;
    __privateGet3(this, _debug).style.height = `${Math.floor(height)}px`;
  }
};
function isSafari() {
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
}
function cloneElement(element) {
  const window2 = getWindow(element);
  const selector = "input, textarea, select, canvas, [contenteditable]";
  const clonedElement = element.cloneNode(true);
  const fields = Array.from(element.querySelectorAll(selector));
  const clonedFields = Array.from(clonedElement.querySelectorAll(selector));
  clonedFields.forEach((field, index) => {
    const originalField = fields[index];
    if (isField(field) && isField(originalField)) {
      if (field.type !== "file") {
        field.value = originalField.value;
      }
      if (field.type === "radio" && field.name) {
        field.name = `Cloned__${field.name}`;
      }
    }
    if (field instanceof window2.HTMLCanvasElement && originalField instanceof window2.HTMLCanvasElement && originalField.width > 0 && originalField.height > 0) {
      const destCtx = field.getContext("2d");
      destCtx == null ? void 0 : destCtx.drawImage(originalField, 0, 0);
    }
  });
  return clonedElement;
}
function isField(element) {
  return "value" in element;
}
function getElementFromPoint(document2, { x, y: y2 }) {
  const element = document2.elementFromPoint(x, y2);
  if (element instanceof HTMLIFrameElement) {
    const { contentDocument } = element;
    if (contentDocument) {
      const { left, top } = element.getBoundingClientRect();
      return getElementFromPoint(contentDocument, {
        x: x - left,
        y: y2 - top
      });
    }
  }
  return element;
}
var ProxiedElements = /* @__PURE__ */ new WeakMap();
var Listeners = class {
  constructor() {
    this.entries = /* @__PURE__ */ new Set();
    this.clear = () => {
      for (const entry of this.entries) {
        const [target, { type, listener, options: options2 }] = entry;
        target.removeEventListener(type, listener, options2);
      }
      this.entries.clear();
    };
  }
  bind(target, input) {
    const listeners = Array.isArray(input) ? input : [input];
    const entries = [];
    for (const descriptor2 of listeners) {
      const { type, listener, options: options2 } = descriptor2;
      const entry = [target, descriptor2];
      target.addEventListener(type, listener, options2);
      this.entries.add(entry);
      entries.push(entry);
    }
    return function cleanup() {
      for (const [target2, { type, listener, options: options2 }] of entries) {
        target2.removeEventListener(type, listener, options2);
      }
    };
  }
};
function supportsPopover(element) {
  return "showPopover" in element && "hidePopover" in element && typeof element.showPopover === "function" && typeof element.hidePopover === "function";
}
function showPopover(element) {
  if (supportsPopover(element) && element.isConnected && element.hasAttribute("popover")) {
    element.showPopover();
  }
}
function isDocumentScrollingElement(element) {
  if (!canUseDOM || !element) {
    return false;
  }
  return element === getDocument(element).scrollingElement;
}
function getScrollPosition(scrollableElement) {
  const window2 = getWindow(scrollableElement);
  const rect = isDocumentScrollingElement(scrollableElement) ? getViewportBoundingRectangle(scrollableElement) : getBoundingRectangle(scrollableElement);
  const dimensions = isDocumentScrollingElement(scrollableElement) ? {
    height: window2.innerHeight,
    width: window2.innerWidth
  } : {
    height: scrollableElement.clientHeight,
    width: scrollableElement.clientWidth
  };
  const position = {
    current: {
      x: scrollableElement.scrollLeft,
      y: scrollableElement.scrollTop
    },
    max: {
      x: scrollableElement.scrollWidth - dimensions.width,
      y: scrollableElement.scrollHeight - dimensions.height
    }
  };
  const isTop = position.current.y <= 0;
  const isLeft = position.current.x <= 0;
  const isBottom = position.current.y >= position.max.y;
  const isRight = position.current.x >= position.max.x;
  return {
    rect,
    position,
    isTop,
    isLeft,
    isBottom,
    isRight
  };
}
function canScroll(scrollableElement, by) {
  const { isTop, isBottom, isLeft, isRight, position } = getScrollPosition(scrollableElement);
  const { x, y: y2 } = by != null ? by : { x: 0, y: 0 };
  const top = !isTop && position.current.y + y2 > 0;
  const bottom = !isBottom && position.current.y + y2 < position.max.y;
  const left = !isLeft && position.current.x + x > 0;
  const right = !isRight && position.current.x + x < position.max.x;
  return {
    top,
    bottom,
    left,
    right,
    x: left || right,
    y: top || bottom
  };
}
function isSVGElement(node) {
  return node instanceof getWindow(node).SVGElement;
}
function getComputedStyles(element) {
  return getWindow(element).getComputedStyle(element);
}
function isFixed(node, computedStyle = getComputedStyles(node)) {
  return computedStyle.position === "fixed" || computedStyle.position === "sticky";
}
function isScrollable(element, computedStyle = getComputedStyles(element)) {
  const overflowRegex = /(auto|scroll|overlay)/;
  const properties = ["overflow", "overflowX", "overflowY"];
  return properties.some((property) => {
    const value = computedStyle[property];
    return typeof value === "string" ? overflowRegex.test(value) : false;
  });
}
var defaultOptions = {
  excludeElement: true
};
function getScrollableAncestors(element, options2 = defaultOptions) {
  const { limit, excludeElement } = options2;
  const scrollParents = /* @__PURE__ */ new Set();
  function findScrollableAncestors(node) {
    if (limit != null && scrollParents.size >= limit) {
      return scrollParents;
    }
    if (!node) {
      return scrollParents;
    }
    if (isDocument(node) && node.scrollingElement != null && !scrollParents.has(node.scrollingElement)) {
      scrollParents.add(node.scrollingElement);
      return scrollParents;
    }
    if (!isHTMLElement(node)) {
      if (isSVGElement(node)) {
        return findScrollableAncestors(node.parentElement);
      }
      return scrollParents;
    }
    if (scrollParents.has(node)) {
      return scrollParents;
    }
    const computedStyle = getComputedStyles(node);
    if (excludeElement && node === element)
      ;
    else if (isScrollable(node, computedStyle)) {
      scrollParents.add(node);
    }
    if (isFixed(node, computedStyle)) {
      const { scrollingElement } = node.ownerDocument;
      if (scrollingElement)
        scrollParents.add(scrollingElement);
      return scrollParents;
    }
    return findScrollableAncestors(node.parentNode);
  }
  if (!element) {
    return scrollParents;
  }
  return findScrollableAncestors(element);
}
function getFirstScrollableAncestor(node) {
  const [firstScrollableAncestor] = getScrollableAncestors(node, { limit: 1 });
  return firstScrollableAncestor != null ? firstScrollableAncestor : null;
}
function getFrameElement(el) {
  const refWindow = el == null ? void 0 : el.ownerDocument.defaultView;
  if (refWindow && refWindow.self !== refWindow.parent) {
    return refWindow.frameElement;
  }
  return null;
}
function getFrameTransform(el, boundary = window.frameElement) {
  const transform = {
    x: 0,
    y: 0,
    scaleX: 1,
    scaleY: 1
  };
  if (!el)
    return transform;
  let frame = getFrameElement(el);
  while (frame) {
    if (frame === boundary) {
      return transform;
    }
    const rect = frame.getBoundingClientRect();
    const { x: scaleX, y: scaleY } = getScale(frame, rect);
    transform.x = transform.x + rect.left;
    transform.y = transform.y + rect.top;
    transform.scaleX = transform.scaleX * scaleX;
    transform.scaleY = transform.scaleY * scaleY;
    frame = getFrameElement(frame);
  }
  return transform;
}
function getScale(element, boundingRectangle = element.getBoundingClientRect()) {
  const width = Math.round(boundingRectangle.width);
  const height = Math.round(boundingRectangle.height);
  if (isHTMLElement(element)) {
    return {
      x: width / element.offsetWidth,
      y: height / element.offsetHeight
    };
  }
  const styles = getComputedStyles(element);
  return {
    x: (parseFloat(styles.width) || width) / width,
    y: (parseFloat(styles.height) || height) / height
  };
}
var ScrollDirection = /* @__PURE__ */ ((ScrollDirection2) => {
  ScrollDirection2[ScrollDirection2["Idle"] = 0] = "Idle";
  ScrollDirection2[ScrollDirection2["Forward"] = 1] = "Forward";
  ScrollDirection2[ScrollDirection2["Reverse"] = -1] = "Reverse";
  return ScrollDirection2;
})(ScrollDirection || {});
var defaultThreshold = {
  x: 0.2,
  y: 0.2
};
var defaultTolerance = {
  x: 10,
  y: 10
};
function detectScrollIntent(scrollableElement, coordinates, intent, acceleration = 25, thresholdPercentage = defaultThreshold, tolerance = defaultTolerance) {
  const { x, y: y2 } = coordinates;
  const { rect, isTop, isBottom, isLeft, isRight } = getScrollPosition(scrollableElement);
  const frameTransform = getFrameTransform(scrollableElement);
  const scrollContainerRect = new Rectangle(
    rect.left * frameTransform.scaleX + frameTransform.x,
    rect.top * frameTransform.scaleY + frameTransform.y,
    rect.width * frameTransform.scaleX,
    rect.height * frameTransform.scaleY
  );
  const direction = {
    x: 0,
    y: 0
    /* Idle */
  };
  const speed = {
    x: 0,
    y: 0
  };
  const threshold2 = {
    height: scrollContainerRect.height * thresholdPercentage.y,
    width: scrollContainerRect.width * thresholdPercentage.x
  };
  if (!isTop && y2 <= scrollContainerRect.top + threshold2.height && (intent == null ? void 0 : intent.y) !== 1 && x >= scrollContainerRect.left - tolerance.x && x <= scrollContainerRect.right + tolerance.x) {
    direction.y = -1;
    speed.y = acceleration * Math.abs(
      (scrollContainerRect.top + threshold2.height - y2) / threshold2.height
    );
  } else if (!isBottom && y2 >= scrollContainerRect.bottom - threshold2.height && (intent == null ? void 0 : intent.y) !== -1 && x >= scrollContainerRect.left - tolerance.x && x <= scrollContainerRect.right + tolerance.x) {
    direction.y = 1;
    speed.y = acceleration * Math.abs(
      (scrollContainerRect.bottom - threshold2.height - y2) / threshold2.height
    );
  }
  if (!isRight && x >= scrollContainerRect.right - threshold2.width && (intent == null ? void 0 : intent.x) !== -1 && y2 >= scrollContainerRect.top - tolerance.y && y2 <= scrollContainerRect.bottom + tolerance.y) {
    direction.x = 1;
    speed.x = acceleration * Math.abs(
      (scrollContainerRect.right - threshold2.width - x) / threshold2.width
    );
  } else if (!isLeft && x <= scrollContainerRect.left + threshold2.width && (intent == null ? void 0 : intent.x) !== 1 && y2 >= scrollContainerRect.top - tolerance.y && y2 <= scrollContainerRect.bottom + tolerance.y) {
    direction.x = -1;
    speed.x = acceleration * Math.abs(
      (scrollContainerRect.left + threshold2.width - x) / threshold2.width
    );
  }
  return {
    direction,
    speed
  };
}
function supportsScrollIntoViewIfNeeded(element) {
  return "scrollIntoViewIfNeeded" in element && typeof element.scrollIntoViewIfNeeded === "function";
}
function scrollIntoViewIfNeeded(el, centerIfNeeded = false) {
  if (supportsScrollIntoViewIfNeeded(el)) {
    el.scrollIntoViewIfNeeded(centerIfNeeded);
    return;
  }
  if (!isHTMLElement(el)) {
    return el.scrollIntoView();
  }
  var parent = getFirstScrollableAncestor(el);
  if (!isHTMLElement(parent)) {
    return;
  }
  const parentComputedStyle = getComputedStyles(parent), parentBorderTopWidth = parseInt(
    parentComputedStyle.getPropertyValue("border-top-width")
  ), parentBorderLeftWidth = parseInt(
    parentComputedStyle.getPropertyValue("border-left-width")
  ), overTop = el.offsetTop - parent.offsetTop < parent.scrollTop, overBottom = el.offsetTop - parent.offsetTop + el.clientHeight - parentBorderTopWidth > parent.scrollTop + parent.clientHeight, overLeft = el.offsetLeft - parent.offsetLeft < parent.scrollLeft, overRight = el.offsetLeft - parent.offsetLeft + el.clientWidth - parentBorderLeftWidth > parent.scrollLeft + parent.clientWidth, alignWithTop = overTop && !overBottom;
  if ((overTop || overBottom) && centerIfNeeded) {
    parent.scrollTop = el.offsetTop - parent.offsetTop - parent.clientHeight / 2 - parentBorderTopWidth + el.clientHeight / 2;
  }
  if ((overLeft || overRight) && centerIfNeeded) {
    parent.scrollLeft = el.offsetLeft - parent.offsetLeft - parent.clientWidth / 2 - parentBorderLeftWidth + el.clientWidth / 2;
  }
  if ((overTop || overBottom || overLeft || overRight) && !centerIfNeeded) {
    el.scrollIntoView(alignWithTop);
  }
}
var Scheduler = class {
  constructor() {
    this.tasks = /* @__PURE__ */ new Set();
    this.flush = () => {
      const tasks = this.tasks;
      this.animationFrame = void 0;
      this.tasks = /* @__PURE__ */ new Set();
      for (const task of tasks) {
        task();
      }
    };
  }
  schedule(task) {
    this.tasks.add(task);
    if (!this.animationFrame) {
      this.animationFrame = requestAnimationFrame(this.flush);
    }
  }
};
var scheduler = new Scheduler();
function inverseTransform(rect, parsedTransform, transformOrigin) {
  const { scaleX, scaleY, x: translateX, y: translateY } = parsedTransform;
  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);
  const y2 = rect.top - translateY - (1 - scaleY) * parseFloat(transformOrigin.slice(transformOrigin.indexOf(" ") + 1));
  const w2 = scaleX ? rect.width / scaleX : rect.width;
  const h2 = scaleY ? rect.height / scaleY : rect.height;
  return {
    width: w2,
    height: h2,
    top: y2,
    right: x + w2,
    bottom: y2 + h2,
    left: x
  };
}
function supportsStyle(element) {
  return "style" in element && element.style instanceof getWindow(element).CSSStyleDeclaration;
}
var Styles = class {
  constructor(element) {
    this.element = element;
    this.initial = /* @__PURE__ */ new Map();
  }
  set(properties, prefix = "") {
    const { element } = this;
    if (!supportsStyle(element)) {
      return;
    }
    for (const [key, value] of Object.entries(properties)) {
      const property = `${prefix}${key}`;
      if (!this.initial.has(property)) {
        this.initial.set(property, element.style.getPropertyValue(property));
      }
      element.style.setProperty(
        property,
        typeof value === "string" ? value : `${value}px`
      );
    }
  }
  remove(properties, prefix = "") {
    const { element } = this;
    if (!supportsStyle(element)) {
      return;
    }
    for (const key of properties) {
      const property = `${prefix}${key}`;
      element.style.removeProperty(property);
    }
  }
  reset() {
    const { element } = this;
    if (!supportsStyle(element)) {
      return;
    }
    for (const [key, value] of this.initial) {
      element.style.setProperty(key, value);
    }
    if (element.getAttribute("style") === "") {
      element.removeAttribute("style");
    }
  }
};
function animateTransform({
  element,
  keyframes,
  options: options2,
  onReady,
  onFinish
}) {
  const styles = new Styles(element);
  const { transitionProperty } = getComputedStyles(element);
  const properties = transitionProperty.split(", ");
  styles.set({
    "transition-property": properties.length ? properties.filter(
      (property) => !property.includes("transform") && !property.includes("translate")
    ).join(", ") : "none"
  });
  onReady == null ? void 0 : onReady();
  element.animate(keyframes, options2).finished.then(() => {
    onFinish == null ? void 0 : onFinish();
    styles.reset();
  });
}
function parseTranslate(translate) {
  if (translate === "none") {
    return null;
  }
  const [x, y2, z = "0"] = translate.split(" ");
  const output = { x: parseFloat(x), y: parseFloat(y2), z: parseInt(z, 10) };
  if (isNaN(output.x) && isNaN(output.y)) {
    return null;
  }
  return {
    x: isNaN(output.x) ? 0 : output.x,
    y: isNaN(output.y) ? 0 : output.y,
    z: isNaN(output.z) ? 0 : output.z
  };
}
function getFinalKeyframe(element, match) {
  const { KeyframeEffect: KeyframeEffect2 } = getWindow(element);
  const animations = element.getAnimations();
  if (animations.length > 0) {
    for (const animation of animations) {
      const { effect } = animation;
      const keyframes = effect instanceof KeyframeEffect2 ? effect.getKeyframes() : [];
      const matchedKeyframes = keyframes.filter(match);
      if (matchedKeyframes.length > 0) {
        return matchedKeyframes[matchedKeyframes.length - 1];
      }
    }
  }
  return null;
}
function computeTranslate(element, translate = getComputedStyles(element).translate, projected = true) {
  if (projected) {
    const keyframe = getFinalKeyframe(
      element,
      (keyframe2) => "translate" in keyframe2
    );
    if (keyframe) {
      const { translate: translate2 = "" } = keyframe;
      if (typeof translate2 === "string") {
        const finalTranslate = parseTranslate(translate2);
        if (finalTranslate) {
          return finalTranslate;
        }
      }
    }
  }
  if (translate) {
    const finalTranslate = parseTranslate(translate);
    if (finalTranslate) {
      return finalTranslate;
    }
  }
  return { x: 0, y: 0, z: 0 };
}
function parseScale(scale) {
  if (scale === "none") {
    return null;
  }
  const values = scale.split(" ");
  const x = parseFloat(values[0]);
  const y2 = parseFloat(values[1]);
  if (isNaN(x) && isNaN(y2)) {
    return null;
  }
  return {
    x: isNaN(x) ? y2 : x,
    y: isNaN(y2) ? x : y2
  };
}
function parseTransform(computedStyles) {
  var _a4, _b2, _c6, _d2, _e, _f, _g, _h, _i;
  const { scale, transform, translate } = computedStyles;
  const parsedScale = parseScale(scale);
  const parsedTranslate = parseTranslate(translate);
  const parsedMatrix = parseTransformMatrix(transform);
  if (!parsedMatrix && !parsedScale && !parsedTranslate) {
    return null;
  }
  const normalizedScale = {
    x: (_a4 = parsedScale == null ? void 0 : parsedScale.x) != null ? _a4 : 1,
    y: (_b2 = parsedScale == null ? void 0 : parsedScale.y) != null ? _b2 : 1
  };
  const normalizedTranslate = {
    x: (_c6 = parsedTranslate == null ? void 0 : parsedTranslate.x) != null ? _c6 : 0,
    y: (_d2 = parsedTranslate == null ? void 0 : parsedTranslate.y) != null ? _d2 : 0
  };
  const normalizedMatrix = {
    x: (_e = parsedMatrix == null ? void 0 : parsedMatrix.x) != null ? _e : 0,
    y: (_f = parsedMatrix == null ? void 0 : parsedMatrix.y) != null ? _f : 0,
    scaleX: (_g = parsedMatrix == null ? void 0 : parsedMatrix.scaleX) != null ? _g : 1,
    scaleY: (_h = parsedMatrix == null ? void 0 : parsedMatrix.scaleY) != null ? _h : 1
  };
  return {
    x: normalizedTranslate.x + normalizedMatrix.x,
    y: normalizedTranslate.y + normalizedMatrix.y,
    z: (_i = parsedTranslate == null ? void 0 : parsedTranslate.z) != null ? _i : 0,
    scaleX: normalizedScale.x * normalizedMatrix.scaleX,
    scaleY: normalizedScale.y * normalizedMatrix.scaleY
  };
}
function parseTransformMatrix(transform) {
  if (transform.startsWith("matrix3d(")) {
    const transformArray = transform.slice(9, -1).split(/, /);
    return {
      x: +transformArray[12],
      y: +transformArray[13],
      scaleX: +transformArray[0],
      scaleY: +transformArray[5]
    };
  } else if (transform.startsWith("matrix(")) {
    const transformArray = transform.slice(7, -1).split(/, /);
    return {
      x: +transformArray[4],
      y: +transformArray[5],
      scaleX: +transformArray[0],
      scaleY: +transformArray[3]
    };
  }
  return null;
}
var DOMRectangle = class extends Rectangle {
  constructor(element, options2 = {}) {
    var _a4, _b2, _c6, _d2;
    const {
      frameTransform = getFrameTransform(element),
      ignoreTransforms,
      getBoundingClientRect = getBoundingRectangle
    } = options2;
    const resetAnimations = forceFinishAnimations(element);
    const boundingRectangle = getBoundingClientRect(element);
    let { top, left, width, height } = boundingRectangle;
    let updated;
    const computedStyles = getComputedStyles(element);
    const parsedTransform = parseTransform(computedStyles);
    const scale = {
      x: (_a4 = parsedTransform == null ? void 0 : parsedTransform.scaleX) != null ? _a4 : 1,
      y: (_b2 = parsedTransform == null ? void 0 : parsedTransform.scaleY) != null ? _b2 : 1
    };
    resetAnimations == null ? void 0 : resetAnimations();
    const projectedTransform = getProjectedTransform(element);
    if (parsedTransform) {
      updated = inverseTransform(
        boundingRectangle,
        parsedTransform,
        computedStyles.transformOrigin
      );
      if (ignoreTransforms || projectedTransform) {
        top = updated.top;
        left = updated.left;
        width = updated.width;
        height = updated.height;
      }
    }
    const intrinsic = {
      width: (_c6 = updated == null ? void 0 : updated.width) != null ? _c6 : width,
      height: (_d2 = updated == null ? void 0 : updated.height) != null ? _d2 : height
    };
    if (projectedTransform && !ignoreTransforms) {
      top = top + projectedTransform.y;
      left = left + projectedTransform.x;
      width = width * projectedTransform.scaleX;
      height = height * projectedTransform.scaleY;
      scale.x = projectedTransform.scaleX;
      scale.y = projectedTransform.scaleY;
    }
    if (frameTransform) {
      if (!ignoreTransforms) {
        left *= frameTransform.scaleX;
        width *= frameTransform.scaleX;
        top *= frameTransform.scaleY;
        height *= frameTransform.scaleY;
      }
      left += frameTransform.x;
      top += frameTransform.y;
    }
    super(left, top, width, height);
    this.scale = scale;
    this.intrinsicWidth = intrinsic.width;
    this.intrinsicHeight = intrinsic.height;
  }
};
function getProjectedTransform(element) {
  var _a4;
  const { KeyframeEffect: KeyframeEffect2 } = getWindow(element);
  const animations = element.getAnimations();
  let projectedTransform = null;
  if (!animations.length)
    return null;
  for (const animation of animations) {
    const keyframes = animation.effect instanceof KeyframeEffect2 ? animation.effect.getKeyframes() : [];
    const keyframe = keyframes[keyframes.length - 1];
    if (!keyframe)
      continue;
    const { transform = "", translate = "", scale = "" } = keyframe;
    if (transform || translate || scale) {
      const parsedTransform = parseTransform({
        transform: typeof transform === "string" ? transform : "",
        translate: typeof translate === "string" ? translate : "",
        scale: typeof scale === "string" ? scale : ""
      });
      if (parsedTransform) {
        projectedTransform = projectedTransform ? {
          x: projectedTransform.x + parsedTransform.x,
          y: projectedTransform.y + parsedTransform.y,
          z: (_a4 = projectedTransform.z) != null ? _a4 : parsedTransform.z,
          scaleX: projectedTransform.scaleX * parsedTransform.scaleX,
          scaleY: projectedTransform.scaleY * parsedTransform.scaleY
        } : parsedTransform;
      }
    }
  }
  return projectedTransform;
}
function forceFinishAnimations(element) {
  const { KeyframeEffect: KeyframeEffect2 } = getWindow(element);
  const animations = element.ownerDocument.getAnimations().filter((animation) => {
    if (animation.effect instanceof KeyframeEffect2) {
      const { target } = animation.effect;
      if (target !== element && (target == null ? void 0 : target.contains(element))) {
        return animation.effect.getKeyframes().some((keyframe) => {
          const { transform, translate, scale, width, height } = keyframe;
          return transform || translate || scale || width || height;
        });
      }
    }
  }).map((animation) => {
    const { effect, currentTime } = animation;
    const duration = effect == null ? void 0 : effect.getComputedTiming().duration;
    if (animation.pending)
      return;
    if (typeof duration == "number" && typeof currentTime == "number" && currentTime < duration) {
      animation.currentTime = duration;
      return () => {
        animation.currentTime = currentTime;
      };
    }
  });
  if (animations.length > 0) {
    return () => animations.forEach((reset) => reset == null ? void 0 : reset());
  }
}
function isElement(target) {
  return target instanceof getWindow(target).Element;
}
function isKeyboardEvent(event) {
  if (!event)
    return false;
  const { KeyboardEvent } = getWindow(event.target);
  return event instanceof KeyboardEvent;
}
function isPointerEvent(event) {
  if (!event)
    return false;
  const { PointerEvent } = getWindow(event.target);
  return event instanceof PointerEvent;
}
var ids = {};
function generateUniqueId(prefix) {
  const id = ids[prefix] == null ? 0 : ids[prefix] + 1;
  ids[prefix] = id;
  return `${prefix}-${id}`;
}

// node_modules/@dnd-kit/collision/dist/index.js
var pointerIntersection = ({
  dragOperation,
  droppable
}) => {
  const pointerCoordinates = dragOperation.position.current;
  if (!pointerCoordinates) {
    return null;
  }
  const { id } = droppable;
  if (!droppable.shape) {
    return null;
  }
  if (droppable.shape.containsPoint(pointerCoordinates)) {
    const distance = Point.distance(droppable.shape.center, pointerCoordinates);
    return {
      id,
      value: 1 / distance,
      type: CollisionType.PointerIntersection,
      priority: CollisionPriority.High
    };
  }
  return null;
};
var shapeIntersection = ({
  dragOperation,
  droppable
}) => {
  const { shape } = dragOperation;
  if (!droppable.shape || !(shape == null ? void 0 : shape.current)) {
    return null;
  }
  const intersectionArea = shape.current.intersectionArea(droppable.shape);
  if (intersectionArea) {
    const { position } = dragOperation;
    const distance = Point.distance(droppable.shape.center, position.current);
    const intersectionRatio = intersectionArea / (shape.current.area + droppable.shape.area - intersectionArea);
    const value = intersectionRatio / distance;
    return {
      id: droppable.id,
      value,
      type: CollisionType.ShapeIntersection,
      priority: CollisionPriority.Normal
    };
  }
  return null;
};
var defaultCollisionDetection = (args) => {
  var _a4;
  return (_a4 = pointerIntersection(args)) != null ? _a4 : shapeIntersection(args);
};
var closestCorners = (input) => {
  const { dragOperation, droppable } = input;
  const { shape, position } = dragOperation;
  if (!droppable.shape) {
    return null;
  }
  const shapeCorners = shape ? Rectangle.from(shape.current.boundingRectangle).corners : void 0;
  const distance = Rectangle.from(
    droppable.shape.boundingRectangle
  ).corners.reduce(
    (acc, corner, index) => {
      var _a4;
      return acc + Point.distance(
        Point.from(corner),
        (_a4 = shapeCorners == null ? void 0 : shapeCorners[index]) != null ? _a4 : position.current
      );
    },
    0
  );
  const value = distance / 4;
  return {
    id: droppable.id,
    value: 1 / value,
    type: CollisionType.Collision,
    priority: CollisionPriority.Normal
  };
};

// node_modules/@dnd-kit/dom/index.js
var __create3 = Object.create;
var __defProp3 = Object.defineProperty;
var __defProps2 = Object.defineProperties;
var __getOwnPropDesc3 = Object.getOwnPropertyDescriptor;
var __getOwnPropDescs2 = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols3 = Object.getOwnPropertySymbols;
var __hasOwnProp3 = Object.prototype.hasOwnProperty;
var __propIsEnum3 = Object.prototype.propertyIsEnumerable;
var __knownSymbol3 = (name, symbol) => (symbol = Symbol[name]) ? symbol : Symbol.for("Symbol." + name);
var __typeError4 = (msg) => {
  throw TypeError(msg);
};
var __defNormalProp3 = (obj, key, value) => key in obj ? __defProp3(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues3 = (a2, b2) => {
  for (var prop in b2 || (b2 = {}))
    if (__hasOwnProp3.call(b2, prop))
      __defNormalProp3(a2, prop, b2[prop]);
  if (__getOwnPropSymbols3)
    for (var prop of __getOwnPropSymbols3(b2)) {
      if (__propIsEnum3.call(b2, prop))
        __defNormalProp3(a2, prop, b2[prop]);
    }
  return a2;
};
var __spreadProps2 = (a2, b2) => __defProps2(a2, __getOwnPropDescs2(b2));
var __name3 = (target, value) => __defProp3(target, "name", { value, configurable: true });
var __objRest2 = (source, exclude) => {
  var target = {};
  for (var prop in source)
    if (__hasOwnProp3.call(source, prop) && exclude.indexOf(prop) < 0)
      target[prop] = source[prop];
  if (source != null && __getOwnPropSymbols3)
    for (var prop of __getOwnPropSymbols3(source)) {
      if (exclude.indexOf(prop) < 0 && __propIsEnum3.call(source, prop))
        target[prop] = source[prop];
    }
  return target;
};
var __decoratorStart3 = (base) => {
  var _a4;
  return [, , , __create3((_a4 = base == null ? void 0 : base[__knownSymbol3("metadata")]) != null ? _a4 : null)];
};
var __decoratorStrings3 = ["class", "method", "getter", "setter", "accessor", "field", "value", "get", "set"];
var __expectFn3 = (fn) => fn !== void 0 && typeof fn !== "function" ? __typeError4("Function expected") : fn;
var __decoratorContext3 = (kind, name, done, metadata, fns) => ({ kind: __decoratorStrings3[kind], name, metadata, addInitializer: (fn) => done._ ? __typeError4("Already initialized") : fns.push(__expectFn3(fn || null)) });
var __decoratorMetadata3 = (array, target) => __defNormalProp3(target, __knownSymbol3("metadata"), array[3]);
var __runInitializers3 = (array, flags, self, value) => {
  for (var i2 = 0, fns = array[flags >> 1], n2 = fns && fns.length; i2 < n2; i2++)
    flags & 1 ? fns[i2].call(self) : value = fns[i2].call(self, value);
  return value;
};
var __decorateElement3 = (array, flags, name, decorators, target, extra) => {
  var fn, it, done, ctx, access, k = flags & 7, s2 = !!(flags & 8), p2 = !!(flags & 16);
  var j = k > 3 ? array.length + 1 : k ? s2 ? 1 : 2 : 0, key = __decoratorStrings3[k + 5];
  var initializers = k > 3 && (array[j - 1] = []), extraInitializers = array[j] || (array[j] = []);
  var desc = k && (!p2 && !s2 && (target = target.prototype), k < 5 && (k > 3 || !p2) && __getOwnPropDesc3(k < 4 ? target : { get [name]() {
    return __privateGet4(this, extra);
  }, set [name](x) {
    return __privateSet4(this, extra, x);
  } }, name));
  k ? p2 && k < 4 && __name3(extra, (k > 2 ? "set " : k > 1 ? "get " : "") + name) : __name3(target, name);
  for (var i2 = decorators.length - 1; i2 >= 0; i2--) {
    ctx = __decoratorContext3(k, name, done = {}, array[3], extraInitializers);
    if (k) {
      ctx.static = s2, ctx.private = p2, access = ctx.access = { has: p2 ? (x) => __privateIn3(target, x) : (x) => name in x };
      if (k ^ 3)
        access.get = p2 ? (x) => (k ^ 1 ? __privateGet4 : __privateMethod4)(x, target, k ^ 4 ? extra : desc.get) : (x) => x[name];
      if (k > 2)
        access.set = p2 ? (x, y2) => __privateSet4(x, target, y2, k ^ 4 ? extra : desc.set) : (x, y2) => x[name] = y2;
    }
    it = (0, decorators[i2])(k ? k < 4 ? p2 ? extra : desc[key] : k > 4 ? void 0 : { get: desc.get, set: desc.set } : target, ctx), done._ = 1;
    if (k ^ 4 || it === void 0)
      __expectFn3(it) && (k > 4 ? initializers.unshift(it) : k ? p2 ? extra = it : desc[key] = it : target = it);
    else if (typeof it !== "object" || it === null)
      __typeError4("Object expected");
    else
      __expectFn3(fn = it.get) && (desc.get = fn), __expectFn3(fn = it.set) && (desc.set = fn), __expectFn3(fn = it.init) && initializers.unshift(fn);
  }
  return k || __decoratorMetadata3(array, target), desc && __defProp3(target, name, desc), p2 ? k ^ 4 ? extra : desc : target;
};
var __accessCheck4 = (obj, member, msg) => member.has(obj) || __typeError4("Cannot " + msg);
var __privateIn3 = (member, obj) => Object(obj) !== obj ? __typeError4('Cannot use the "in" operator on this value') : member.has(obj);
var __privateGet4 = (obj, member, getter) => (__accessCheck4(obj, member, "read from private field"), getter ? getter.call(obj) : member.get(obj));
var __privateAdd4 = (obj, member, value) => member.has(obj) ? __typeError4("Cannot add the same private member more than once") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);
var __privateSet4 = (obj, member, value, setter) => (__accessCheck4(obj, member, "write to private field"), setter ? setter.call(obj, value) : member.set(obj, value), value);
var __privateMethod4 = (obj, member, method) => (__accessCheck4(obj, member, "access private method"), method);
var defaultAttributes = {
  role: "button",
  roleDescription: "draggable",
  tabIndex: 0
};
var defaultDescriptionIdPrefix = `dnd-kit-description`;
var defaultAnnouncementIdPrefix = `dnd-kit-announcement`;
var defaultScreenReaderInstructions = {
  draggable: `To pick up a draggable item, press the space bar. While dragging, use the arrow keys to move the item in a given direction. Press space again to drop the item in its new position, or press escape to cancel.`
};
var defaultAnnouncements = {
  dragstart({ operation: { source } }) {
    if (!source)
      return;
    return `Picked up draggable item ${source.id}.`;
  },
  dragover({ operation: { source, target } }) {
    if (!source)
      return;
    if (target) {
      return `Draggable item ${source.id} was moved over droppable target ${target.id}.`;
    }
    return `Draggable item ${source.id} is no longer over a droppable target.`;
  },
  dragend({ operation: { source, target }, canceled }) {
    if (!source)
      return;
    if (canceled) {
      return `Dragging was cancelled. Draggable item ${source.id} was dropped.`;
    }
    if (target) {
      return `Draggable item ${source.id} was dropped over droppable target ${target.id}`;
    }
    return `Draggable item ${source.id} was dropped.`;
  }
};
function isFocusable(element) {
  const window2 = getWindow(element);
  return element instanceof window2.HTMLInputElement || element instanceof window2.HTMLSelectElement || element instanceof window2.HTMLTextAreaElement || element instanceof window2.HTMLAnchorElement || element instanceof window2.HTMLButtonElement || element instanceof window2.HTMLAreaElement;
}
function createHiddenText(id, value) {
  const element = document.createElement("div");
  element.id = id;
  element.style.setProperty("display", "none");
  element.innerText = value;
  return element;
}
function createLiveRegion(id) {
  const element = document.createElement("div");
  element.id = id;
  element.setAttribute("role", "status");
  element.setAttribute("aria-live", "polite");
  element.setAttribute("aria-atomic", "true");
  element.style.setProperty("position", "fixed");
  element.style.setProperty("width", "1px");
  element.style.setProperty("height", "1px");
  element.style.setProperty("margin", "-1px");
  element.style.setProperty("border", "0");
  element.style.setProperty("padding", "0");
  element.style.setProperty("overflow", "hidden");
  element.style.setProperty("clip", "rect(0 0 0 0)");
  element.style.setProperty("clip-path", "inset(100%)");
  element.style.setProperty("white-space", "nowrap");
  return element;
}
var Accessibility = class extends Plugin {
  constructor(manager, options2) {
    super(manager);
    const {
      id,
      idPrefix: {
        description: descriptionPrefix = defaultDescriptionIdPrefix,
        announcement: announcementPrefix = defaultAnnouncementIdPrefix
      } = {},
      announcements = defaultAnnouncements,
      screenReaderInstructions = defaultScreenReaderInstructions
    } = options2 != null ? options2 : {};
    const descriptionId = id ? `${descriptionPrefix}-${id}` : generateUniqueId(descriptionPrefix);
    const announcementId = id ? `${announcementPrefix}-${id}` : generateUniqueId(announcementPrefix);
    let hiddenTextElement;
    let liveRegionElement;
    const eventListeners = Object.entries(announcements).map(
      ([eventName, getAnnouncement]) => {
        return this.manager.monitor.addEventListener(
          eventName,
          (event, manager2) => {
            const announcement = getAnnouncement == null ? void 0 : getAnnouncement(event, manager2);
            if (announcement && liveRegionElement) {
              liveRegionElement.innerText = announcement;
            }
          }
        );
      }
    );
    const initialize = () => {
      hiddenTextElement = createHiddenText(
        descriptionId,
        screenReaderInstructions.draggable
      );
      liveRegionElement = createLiveRegion(announcementId);
      document.body.append(hiddenTextElement, liveRegionElement);
    };
    const cleanupEffects = effects(() => {
      for (const draggable of manager.registry.draggables.value) {
        const { element, handle } = draggable;
        const activator = handle != null ? handle : element;
        const window2 = getWindow(activator);
        if (activator) {
          if (!hiddenTextElement || !liveRegionElement) {
            initialize();
          }
          if ((!isFocusable(activator) || isSafari()) && !activator.hasAttribute("tabindex")) {
            activator.setAttribute("tabindex", "0");
          }
          if (!activator.hasAttribute("role") && !(activator instanceof window2.HTMLButtonElement)) {
            activator.setAttribute("role", defaultAttributes.role);
          }
          if (!activator.hasAttribute("role-description")) {
            activator.setAttribute(
              "aria-roledescription",
              defaultAttributes.roleDescription
            );
          }
          if (!activator.hasAttribute("aria-describedby")) {
            activator.setAttribute("aria-describedby", descriptionId);
          }
          for (const key of ["aria-pressed", "aria-grabbed"]) {
            activator.setAttribute(key, String(draggable.isDragSource));
          }
          activator.setAttribute("aria-disabled", String(draggable.disabled));
        }
      }
      this.destroy = () => {
        hiddenTextElement == null ? void 0 : hiddenTextElement.remove();
        liveRegionElement == null ? void 0 : liveRegionElement.remove();
        eventListeners.forEach((unsubscribe) => unsubscribe());
        cleanupEffects();
      };
    });
  }
};
var Cursor = class extends Plugin {
  constructor(manager, options2) {
    super(manager, options2);
    this.manager = manager;
    this.destroy = E(() => {
      var _a4;
      const { dragOperation } = this.manager;
      const { cursor = "grabbing" } = (_a4 = this.options) != null ? _a4 : {};
      if (dragOperation.status.initialized) {
        const style = document.createElement("style");
        style.innerText = `* { cursor: ${cursor} !important; }`;
        document.head.appendChild(style);
        return () => {
          style.remove();
        };
      }
    });
  }
};
var ATTR_PREFIX = "data-dnd-";
var CSS_PREFIX = "--dnd-";
var ATTRIBUTE = `${ATTR_PREFIX}dragging`;
var PLACEHOLDER_ATTRIBUTE = `${ATTR_PREFIX}placeholder`;
var IGNORED_ATTRIBUTES = [
  ATTRIBUTE,
  PLACEHOLDER_ATTRIBUTE,
  "popover",
  "aria-pressed",
  "aria-grabbing"
];
var IGNORED_STYLES = ["view-transition-name"];
var CSS_RULES = `
  :root [${ATTRIBUTE}] {
    position: fixed !important;
    pointer-events: none !important;
    touch-action: none;
    z-index: calc(infinity);
    will-change: translate;
    top: var(${CSS_PREFIX}top, 0px) !important;
    left: var(${CSS_PREFIX}left, 0px) !important;
    right: unset !important;
    bottom: unset !important;
    width: var(${CSS_PREFIX}width, auto);
    max-width: var(${CSS_PREFIX}width, auto);
    height: var(${CSS_PREFIX}height, auto);
    max-height: var(${CSS_PREFIX}height, auto);
    box-sizing: border-box;
  }
  [${ATTRIBUTE}] * {
    pointer-events: none !important;
  }
  [${ATTRIBUTE}][style*='${CSS_PREFIX}translate'] {
    translate: var(${CSS_PREFIX}translate) !important;
  }
  [style*='${CSS_PREFIX}transition'] {
    transition: var(${CSS_PREFIX}transition) !important;
  }
  [style*='${CSS_PREFIX}scale'] {
    scale: var(${CSS_PREFIX}scale) !important;
    transform-origin: var(${CSS_PREFIX}transform-origin) !important;
  }
  @layer {
    :where([${ATTRIBUTE}][popover]) {
      overflow: visible;
      background: unset;
      border: unset;
      margin: unset;
      padding: unset;
      color: inherit;
    }
  }
  [${ATTRIBUTE}]::backdrop, [${ATTR_PREFIX}overlay]:not([${ATTRIBUTE}]) {
    display: none;
  }
`.replace(/\n+/g, " ").replace(/\s+/g, " ").trim();
function createPlaceholder(source) {
  return n(() => {
    const { element, manager } = source;
    if (!element || !manager)
      return;
    const containedDroppables = findContainedDroppables(
      element,
      manager.registry.droppables
    );
    const cleanup = [];
    const placeholder = cloneElement(element);
    const { remove } = placeholder;
    proxyDroppableElements(containedDroppables, placeholder, cleanup);
    configurePlaceholder(placeholder);
    placeholder.remove = () => {
      cleanup.forEach((fn) => fn());
      remove.call(placeholder);
    };
    return placeholder;
  });
}
function findContainedDroppables(element, droppables) {
  const containedDroppables = /* @__PURE__ */ new Map();
  for (const droppable of droppables) {
    if (!droppable.element)
      continue;
    if (element === droppable.element || element.contains(droppable.element)) {
      const identifierAttribute = `${ATTR_PREFIX}${generateUniqueId("dom-id")}`;
      droppable.element.setAttribute(identifierAttribute, "");
      containedDroppables.set(droppable, identifierAttribute);
    }
  }
  return containedDroppables;
}
function proxyDroppableElements(containedDroppables, placeholder, cleanup) {
  for (const [droppable, identifierAttribute] of containedDroppables) {
    if (!droppable.element)
      continue;
    const selector = `[${identifierAttribute}]`;
    const clonedElement = placeholder.matches(selector) ? placeholder : placeholder.querySelector(selector);
    droppable.element.removeAttribute(identifierAttribute);
    if (!clonedElement)
      continue;
    const originalElement = droppable.element;
    droppable.proxy = clonedElement;
    clonedElement.removeAttribute(identifierAttribute);
    ProxiedElements.set(originalElement, clonedElement);
    cleanup.push(() => {
      ProxiedElements.delete(originalElement);
      droppable.proxy = void 0;
    });
  }
}
function configurePlaceholder(placeholder) {
  placeholder.setAttribute("inert", "true");
  placeholder.setAttribute("tab-index", "-1");
  placeholder.setAttribute("aria-hidden", "true");
  placeholder.setAttribute(PLACEHOLDER_ATTRIBUTE, "");
}
function isSameFrame(element, target) {
  if (element === target)
    return true;
  return getFrameElement(element) === getFrameElement(target);
}
function preventPopoverClose(event) {
  const { target } = event;
  if (event instanceof ToggleEvent && event.newState === "closed" && target instanceof Element && target.hasAttribute("popover")) {
    requestAnimationFrame(() => showPopover(target));
  }
}
var _overlay_dec;
var _a;
var _init5;
var _overlay;
var _Feedback_instances;
var render_fn;
var injectStyles_fn;
var _Feedback = class _Feedback2 extends (_a = Plugin, _overlay_dec = [reactive], _a) {
  constructor(manager, options2) {
    super(manager);
    __privateAdd4(this, _Feedback_instances);
    __privateAdd4(this, _overlay, __runInitializers3(_init5, 8, this)), __runInitializers3(_init5, 11, this);
    this.state = {
      initial: {},
      current: {}
    };
    this.registerEffect(__privateMethod4(this, _Feedback_instances, injectStyles_fn));
    this.registerEffect(__privateMethod4(this, _Feedback_instances, render_fn));
  }
  destroy() {
    super.destroy();
    injectedStyleTags.forEach((style) => style.remove());
    injectedStyleTags.clear();
  }
};
_init5 = __decoratorStart3(_a);
_overlay = /* @__PURE__ */ new WeakMap();
_Feedback_instances = /* @__PURE__ */ new WeakSet();
render_fn = function() {
  var _a4, _b2, _c32;
  const { state, manager, options: options2 } = this;
  const { dragOperation } = manager;
  const { position, source, status } = dragOperation;
  if (status.idle) {
    state.current = {};
    state.initial = {};
    return;
  }
  if (!source)
    return;
  const { element, feedback } = source;
  if (!element || feedback === "none" || status.initializing) {
    return;
  }
  const { initial } = state;
  const feedbackElement = (_a4 = this.overlay) != null ? _a4 : element;
  const frameTransform = getFrameTransform(feedbackElement);
  const elementFrameTransform = getFrameTransform(element);
  const crossFrame = !isSameFrame(element, feedbackElement);
  const shape = new DOMRectangle(element, {
    frameTransform: crossFrame ? elementFrameTransform : null,
    ignoreTransforms: !crossFrame
  });
  const scaleDelta = {
    x: elementFrameTransform.scaleX / frameTransform.scaleX,
    y: elementFrameTransform.scaleY / frameTransform.scaleY
  };
  let cleanup;
  let { width, height, top, left } = shape;
  if (crossFrame) {
    width = width / scaleDelta.x;
    height = height / scaleDelta.y;
  }
  const styles = new Styles(feedbackElement);
  const { transition, translate } = getComputedStyles(element);
  const clone = feedback === "clone";
  const placeholder = feedback !== "move" && !this.overlay ? createPlaceholder(source) : null;
  const isKeyboardOperation = n(
    () => isKeyboardEvent(manager.dragOperation.activatorEvent)
  );
  if (translate !== "none") {
    const parsedTranslate = parseTranslate(translate);
    if (parsedTranslate && !initial.translate) {
      initial.translate = parsedTranslate;
    }
  }
  if (!initial.transformOrigin) {
    const current = n(() => position.current);
    initial.transformOrigin = {
      x: (current.x - left * frameTransform.scaleX - frameTransform.x) / (width * frameTransform.scaleX),
      y: (current.y - top * frameTransform.scaleY - frameTransform.y) / (height * frameTransform.scaleY)
    };
  }
  const { transformOrigin } = initial;
  const relativeTop = top * frameTransform.scaleY + frameTransform.y;
  const relativeLeft = left * frameTransform.scaleX + frameTransform.x;
  if (!initial.coordinates) {
    initial.coordinates = {
      x: relativeLeft,
      y: relativeTop
    };
    if (scaleDelta.x !== 1 || scaleDelta.y !== 1) {
      const { scaleX, scaleY } = elementFrameTransform;
      const { x: tX2, y: tY2 } = transformOrigin;
      initial.coordinates.x += (width * scaleX - width) * tX2;
      initial.coordinates.y += (height * scaleY - height) * tY2;
    }
  }
  if (!initial.dimensions) {
    initial.dimensions = { width, height };
  }
  if (!initial.frameTransform) {
    initial.frameTransform = frameTransform;
  }
  const coordinatesDelta = {
    x: initial.coordinates.x - relativeLeft,
    y: initial.coordinates.y - relativeTop
  };
  const sizeDelta = {
    width: (initial.dimensions.width * initial.frameTransform.scaleX - width * frameTransform.scaleX) * transformOrigin.x,
    height: (initial.dimensions.height * initial.frameTransform.scaleY - height * frameTransform.scaleY) * transformOrigin.y
  };
  const delta = {
    x: coordinatesDelta.x / frameTransform.scaleX + sizeDelta.width,
    y: coordinatesDelta.y / frameTransform.scaleY + sizeDelta.height
  };
  const projected = {
    left: left + delta.x,
    top: top + delta.y
  };
  feedbackElement.setAttribute(ATTRIBUTE, "true");
  const transform = n(() => dragOperation.transform);
  const initialTranslate = (_b2 = initial.translate) != null ? _b2 : { x: 0, y: 0 };
  const tX = transform.x * frameTransform.scaleX + initialTranslate.x;
  const tY = transform.y * frameTransform.scaleY + initialTranslate.y;
  const translateString = `${tX}px ${tY}px 0`;
  styles.set(
    {
      width,
      height,
      top: projected.top,
      left: projected.left,
      translate: translateString,
      scale: crossFrame ? `${scaleDelta.x} ${scaleDelta.y}` : "",
      "transform-origin": `${transformOrigin.x * 100}% ${transformOrigin.y * 100}%`
    },
    CSS_PREFIX
  );
  if (placeholder) {
    element.insertAdjacentElement("afterend", placeholder);
    if (options2 == null ? void 0 : options2.rootElement) {
      const root = typeof options2.rootElement === "function" ? options2.rootElement(source) : options2.rootElement;
      root.appendChild(element);
    }
  }
  if (supportsPopover(feedbackElement)) {
    if (!feedbackElement.hasAttribute("popover")) {
      feedbackElement.setAttribute("popover", "");
    }
    showPopover(feedbackElement);
    feedbackElement.addEventListener("beforetoggle", preventPopoverClose);
  }
  const resizeObserver = new ResizeObserver(() => {
    if (!placeholder)
      return;
    const placeholderShape = new DOMRectangle(placeholder, {
      frameTransform,
      ignoreTransforms: true
    });
    const origin = transformOrigin != null ? transformOrigin : { x: 1, y: 1 };
    const dX = (width - placeholderShape.width) * origin.x + delta.x;
    const dY = (height - placeholderShape.height) * origin.y + delta.y;
    styles.set(
      {
        width: placeholderShape.width,
        height: placeholderShape.height,
        top: top + dY,
        left: left + dX
      },
      CSS_PREFIX
    );
    const window2 = getWindow(element);
    if (element instanceof window2.HTMLTableRowElement && placeholder instanceof window2.HTMLTableRowElement) {
      const cells = Array.from(element.cells);
      const placeholderCells = Array.from(placeholder.cells);
      for (const [index, cell] of cells.entries()) {
        const placeholderCell = placeholderCells[index];
        cell.style.width = `${placeholderCell.offsetWidth}px`;
      }
    }
    dragOperation.shape = new DOMRectangle(element);
  });
  dragOperation.shape = new DOMRectangle(feedbackElement);
  if (n(() => source.status) === "idle") {
    requestAnimationFrame(() => source.status = "dragging");
  }
  let elementMutationObserver;
  let documentMutationObserver;
  if (placeholder) {
    resizeObserver.observe(placeholder);
    elementMutationObserver = new MutationObserver(() => {
      for (const attribute of Array.from(element.attributes)) {
        if (attribute.name.startsWith("aria-") || IGNORED_ATTRIBUTES.includes(attribute.name)) {
          continue;
        }
        if (attribute.name === "style") {
          if (supportsStyle(element) && supportsStyle(placeholder)) {
            placeholder.setAttribute("style", clone ? "" : "opacity: 0;");
            placeholder.style.setProperty("transition", "none");
            for (const key of Object.values(element.style)) {
              if (key.startsWith(CSS_PREFIX) || IGNORED_STYLES.includes(key)) {
                continue;
              }
              placeholder.style.setProperty(
                key,
                element.style.getPropertyValue(key)
              );
            }
          }
          continue;
        }
        placeholder.setAttribute(attribute.name, attribute.value);
      }
      if (clone) {
        placeholder.innerHTML = element.innerHTML;
      }
    });
    elementMutationObserver.observe(element, {
      attributes: true,
      subtree: true
    });
    documentMutationObserver = new MutationObserver((entries) => {
      for (const entry of entries) {
        if (entry.addedNodes.length === 0)
          continue;
        for (const node of Array.from(entry.addedNodes)) {
          if (node.contains(element) && element.nextElementSibling !== placeholder) {
            element.insertAdjacentElement("afterend", placeholder);
            showPopover(feedbackElement);
            return;
          }
          if (node.contains(placeholder) && placeholder.previousElementSibling !== element) {
            placeholder.insertAdjacentElement("beforebegin", element);
            showPopover(feedbackElement);
            return;
          }
        }
      }
    });
    documentMutationObserver.observe(element.ownerDocument.body, {
      childList: true,
      subtree: true
    });
  }
  const cleanupEffect = E(() => {
    var _a5;
    const { transform: transform2, status: status2 } = dragOperation;
    if (!transform2.x && !transform2.y && !state.current.translate) {
      return;
    }
    if (status2.dragging) {
      const translateTransition = isKeyboardOperation ? "250ms cubic-bezier(0.25, 1, 0.5, 1)" : "0ms linear";
      const initialTranslate2 = (_a5 = initial.translate) != null ? _a5 : { x: 0, y: 0 };
      const x = transform2.x / frameTransform.scaleX + initialTranslate2.x;
      const y2 = transform2.y / frameTransform.scaleY + initialTranslate2.y;
      styles.set(
        {
          transition: `${transition}, translate ${translateTransition}`,
          translate: `${x}px ${y2}px 0`
        },
        CSS_PREFIX
      );
      dragOperation.shape = new DOMRectangle(feedbackElement);
      state.current.translate = {
        x,
        y: y2
      };
    }
  });
  const id = (_c32 = manager.dragOperation.source) == null ? void 0 : _c32.id;
  const restoreFocus = () => {
    var _a5;
    if (!isKeyboardOperation || id == null) {
      return;
    }
    const draggable = manager.registry.draggables.get(id);
    const element2 = (_a5 = draggable == null ? void 0 : draggable.handle) != null ? _a5 : draggable == null ? void 0 : draggable.element;
    if (element2 instanceof HTMLElement) {
      element2.focus();
    }
  };
  let dropEffectCleanup;
  cleanup = () => {
    elementMutationObserver == null ? void 0 : elementMutationObserver.disconnect();
    documentMutationObserver == null ? void 0 : documentMutationObserver.disconnect();
    resizeObserver.disconnect();
    if (supportsPopover(feedbackElement)) {
      feedbackElement.removeEventListener(
        "beforetoggle",
        preventPopoverClose
      );
      feedbackElement.removeAttribute("popover");
    }
    feedbackElement.removeAttribute(ATTRIBUTE);
    styles.reset();
    source.status = "idle";
    const moved = state.current.translate != null;
    if (placeholder && (moved || placeholder.parentElement !== feedbackElement.parentElement) && feedbackElement.isConnected) {
      placeholder.replaceWith(feedbackElement);
    }
    placeholder == null ? void 0 : placeholder.remove();
    cleanupEffect();
    dropEffectCleanup == null ? void 0 : dropEffectCleanup();
  };
  dropEffectCleanup = E(() => {
    if (dragOperation.status.dropped) {
      const onComplete = cleanup;
      cleanup = void 0;
      source.status = "dropping";
      let translate2 = state.current.translate;
      const moved = translate2 != null;
      if (!translate2 && element !== feedbackElement) {
        translate2 = {
          x: 0,
          y: 0
        };
      }
      if (!translate2) {
        onComplete == null ? void 0 : onComplete();
        return;
      }
      manager.renderer.rendering.then(() => {
        showPopover(feedbackElement);
        const target = placeholder != null ? placeholder : element;
        const animations = feedbackElement.getAnimations();
        if (animations.length) {
          animations.forEach((animation) => {
            const { effect: effect9 } = animation;
            if (effect9 instanceof KeyframeEffect) {
              if (effect9.getKeyframes().some((keyframe) => keyframe.translate)) {
                animation.finish();
              }
            }
          });
        }
        const options22 = {
          frameTransform: isSameFrame(feedbackElement, target) ? null : void 0
        };
        const current = new DOMRectangle(feedbackElement, options22);
        const final = new DOMRectangle(target, options22);
        const delta2 = Rectangle.delta(current, final, source.alignment);
        const finalTranslate = {
          x: translate2.x - delta2.x,
          y: translate2.y - delta2.y
        };
        const heightKeyframes = Math.round(current.intrinsicHeight) !== Math.round(final.intrinsicHeight) ? {
          minHeight: [
            `${current.intrinsicHeight}px`,
            `${final.intrinsicHeight}px`
          ],
          maxHeight: [
            `${current.intrinsicHeight}px`,
            `${final.intrinsicHeight}px`
          ]
        } : {};
        const widthKeyframes = Math.round(current.intrinsicWidth) !== Math.round(final.intrinsicWidth) ? {
          minWidth: [
            `${current.intrinsicWidth}px`,
            `${final.intrinsicWidth}px`
          ],
          maxWidth: [
            `${current.intrinsicWidth}px`,
            `${final.intrinsicWidth}px`
          ]
        } : {};
        animateTransform({
          element: feedbackElement,
          keyframes: __spreadProps2(__spreadValues3(__spreadValues3({}, heightKeyframes), widthKeyframes), {
            translate: [
              `${translate2.x}px ${translate2.y}px 0`,
              `${finalTranslate.x}px ${finalTranslate.y}px 0`
            ]
          }),
          options: {
            duration: moved || feedbackElement !== element ? 250 : 0,
            easing: "ease"
          },
          onReady() {
            styles.remove(["translate"], CSS_PREFIX);
          },
          onFinish() {
            onComplete == null ? void 0 : onComplete();
            requestAnimationFrame(restoreFocus);
          }
        });
      });
    }
  });
  return () => cleanup == null ? void 0 : cleanup();
};
injectStyles_fn = function() {
  var _a4, _b2;
  const { status, source, target } = this.manager.dragOperation;
  if (status.initialized) {
    const sourceDocument = getDocument((_a4 = source == null ? void 0 : source.element) != null ? _a4 : null);
    const targetDocument = getDocument((_b2 = target == null ? void 0 : target.element) != null ? _b2 : null);
    const documents = /* @__PURE__ */ new Set([sourceDocument, targetDocument]);
    for (const doc of documents) {
      if (!injectedStyleTags.has(doc)) {
        const style = document.createElement("style");
        style.innerText = CSS_RULES;
        doc.head.prepend(style);
        injectedStyleTags.set(doc, style);
      }
    }
  }
};
__decorateElement3(_init5, 4, "overlay", _overlay_dec, _Feedback, _overlay);
__decoratorMetadata3(_init5, _Feedback);
_Feedback.configure = configurator(_Feedback);
var Feedback = _Feedback;
var injectedStyleTags = /* @__PURE__ */ new Map();
var LOCKED = true;
var UNLOCKED = false;
var _dec;
var _a2;
var _dec2;
var _b;
var _init23;
var __b;
var __a;
_b = (_dec2 = [reactive], ScrollDirection.Forward), _a2 = (_dec = [reactive], ScrollDirection.Reverse);
var ScrollLock = class {
  constructor() {
    __privateAdd4(this, __b, __runInitializers3(_init23, 8, this, LOCKED)), __runInitializers3(_init23, 11, this);
    __privateAdd4(this, __a, __runInitializers3(_init23, 12, this, LOCKED)), __runInitializers3(_init23, 15, this);
  }
  isLocked(direction) {
    if (direction === ScrollDirection.Idle) {
      return false;
    }
    if (direction == null) {
      return this[ScrollDirection.Forward] === LOCKED && this[ScrollDirection.Reverse] === LOCKED;
    }
    return this[direction] === LOCKED;
  }
  unlock(direction) {
    if (direction === ScrollDirection.Idle) {
      return;
    }
    this[direction] = UNLOCKED;
  }
};
_init23 = __decoratorStart3(null);
__b = /* @__PURE__ */ new WeakMap();
__a = /* @__PURE__ */ new WeakMap();
__decorateElement3(_init23, 4, _b, _dec2, ScrollLock, __b);
__decorateElement3(_init23, 4, _a2, _dec, ScrollLock, __a);
__decoratorMetadata3(_init23, ScrollLock);
var DIRECTIONS = [ScrollDirection.Forward, ScrollDirection.Reverse];
var ScrollIntent = class {
  constructor() {
    this.x = new ScrollLock();
    this.y = new ScrollLock();
  }
  isLocked() {
    return this.x.isLocked() && this.y.isLocked();
  }
};
var ScrollIntentTracker = class extends Plugin {
  constructor(manager) {
    super(manager);
    const scrollIntent = d(new ScrollIntent());
    let previousDelta = null;
    this.signal = scrollIntent;
    E(() => {
      const { status } = manager.dragOperation;
      if (!status.initialized) {
        previousDelta = null;
        scrollIntent.value = new ScrollIntent();
        return;
      }
      const { delta } = manager.dragOperation.position;
      if (previousDelta) {
        const directions = {
          x: getDirection(delta.x, previousDelta.x),
          y: getDirection(delta.y, previousDelta.y)
        };
        const intent = scrollIntent.peek();
        r(() => {
          for (const axis of Axes) {
            for (const direction of DIRECTIONS) {
              if (directions[axis] === direction) {
                intent[axis].unlock(direction);
              }
            }
          }
          scrollIntent.value = intent;
        });
      }
      previousDelta = delta;
    });
  }
  get current() {
    return this.signal.peek();
  }
};
function getDirection(a2, b2) {
  return Math.sign(a2 - b2);
}
var _autoScrolling_dec;
var _a3;
var _init32;
var _autoScrolling;
var _meta;
var _scroll;
var Scroller = class extends (_a3 = CorePlugin, _autoScrolling_dec = [reactive], _a3) {
  constructor(manager) {
    super(manager);
    __privateAdd4(this, _autoScrolling, __runInitializers3(_init32, 8, this, false)), __runInitializers3(_init32, 11, this);
    __privateAdd4(this, _meta);
    __privateAdd4(this, _scroll, () => {
      if (!__privateGet4(this, _meta)) {
        return;
      }
      const { element, by } = __privateGet4(this, _meta);
      if (by.y)
        element.scrollTop += by.y;
      if (by.x)
        element.scrollLeft += by.x;
    });
    this.scroll = (options2) => {
      var _a4;
      if (this.disabled) {
        return false;
      }
      const elements = this.getScrollableElements();
      if (!elements) {
        __privateSet4(this, _meta, void 0);
        return false;
      }
      const { position } = this.manager.dragOperation;
      const currentPosition = position == null ? void 0 : position.current;
      if (currentPosition) {
        const { by } = options2 != null ? options2 : {};
        const intent = by ? {
          x: getScrollIntent(by.x),
          y: getScrollIntent(by.y)
        } : void 0;
        const scrollIntent = intent ? void 0 : this.scrollIntentTracker.current;
        if (scrollIntent == null ? void 0 : scrollIntent.isLocked()) {
          return false;
        }
        for (const scrollableElement of elements) {
          const elementCanScroll = canScroll(scrollableElement, by);
          if (elementCanScroll.x || elementCanScroll.y) {
            const { speed, direction } = detectScrollIntent(
              scrollableElement,
              currentPosition,
              intent
            );
            if (scrollIntent) {
              for (const axis of Axes) {
                if (scrollIntent[axis].isLocked(direction[axis])) {
                  speed[axis] = 0;
                  direction[axis] = 0;
                }
              }
            }
            if (direction.x || direction.y) {
              const { x, y: y2 } = by != null ? by : direction;
              const scrollLeftBy = x * speed.x;
              const scrollTopBy = y2 * speed.y;
              if (scrollLeftBy || scrollTopBy) {
                const previousScrollBy = (_a4 = __privateGet4(this, _meta)) == null ? void 0 : _a4.by;
                if (this.autoScrolling && previousScrollBy) {
                  const scrollIntentMismatch = previousScrollBy.x && !scrollLeftBy || previousScrollBy.y && !scrollTopBy;
                  if (scrollIntentMismatch)
                    continue;
                }
                __privateSet4(this, _meta, {
                  element: scrollableElement,
                  by: {
                    x: scrollLeftBy,
                    y: scrollTopBy
                  }
                });
                scheduler.schedule(__privateGet4(this, _scroll));
                return true;
              }
            }
          }
        }
      }
      __privateSet4(this, _meta, void 0);
      return false;
    };
    let previousElementFromPoint = null;
    let previousScrollableElements = null;
    const elementFromPoint = computed(() => {
      const { position } = manager.dragOperation;
      if (!position) {
        return null;
      }
      const element = getElementFromPoint(document, position.current);
      if (element) {
        previousElementFromPoint = element;
      }
      return element != null ? element : previousElementFromPoint;
    });
    const scrollableElements = computed(() => {
      const element = elementFromPoint.value;
      const { documentElement } = getDocument(element);
      if (!element || element === documentElement) {
        const { target } = manager.dragOperation;
        const targetElement = target == null ? void 0 : target.element;
        if (targetElement) {
          const elements = getScrollableAncestors(targetElement, {
            excludeElement: false
          });
          previousScrollableElements = elements;
          return elements;
        }
      }
      if (element) {
        const elements = getScrollableAncestors(element, {
          excludeElement: false
        });
        if (this.autoScrolling && previousScrollableElements && elements.size < (previousScrollableElements == null ? void 0 : previousScrollableElements.size)) {
          return previousScrollableElements;
        }
        previousScrollableElements = elements;
        return elements;
      }
      previousScrollableElements = null;
      return null;
    }, deepEqual);
    this.getScrollableElements = () => {
      return scrollableElements.value;
    };
    this.scrollIntentTracker = new ScrollIntentTracker(manager);
    this.destroy = manager.monitor.addEventListener("dragmove", (event) => {
      if (this.disabled || event.defaultPrevented || !isKeyboardEvent(manager.dragOperation.activatorEvent) || !event.by) {
        return;
      }
      if (this.scroll({ by: event.by })) {
        event.preventDefault();
      }
    });
  }
};
_init32 = __decoratorStart3(_a3);
_autoScrolling = /* @__PURE__ */ new WeakMap();
_meta = /* @__PURE__ */ new WeakMap();
_scroll = /* @__PURE__ */ new WeakMap();
__decorateElement3(_init32, 4, "autoScrolling", _autoScrolling_dec, Scroller, _autoScrolling);
__decoratorMetadata3(_init32, Scroller);
function getScrollIntent(value) {
  if (value > 0) {
    return ScrollDirection.Forward;
  }
  if (value < 0) {
    return ScrollDirection.Reverse;
  }
  return ScrollDirection.Idle;
}
var AUTOSCROLL_INTERVAL = 10;
var AutoScroller = class extends Plugin {
  constructor(manager, _options) {
    super(manager);
    const scroller = manager.registry.plugins.get(Scroller);
    if (!scroller) {
      throw new Error("AutoScroller plugin depends on Scroller plugin");
    }
    this.destroy = E(() => {
      if (this.disabled) {
        return;
      }
      const { position: _2, status } = manager.dragOperation;
      if (status.dragging) {
        const canScroll2 = scroller.scroll();
        if (canScroll2) {
          scroller.autoScrolling = true;
          const interval = setInterval(scroller.scroll, AUTOSCROLL_INTERVAL);
          return () => {
            clearInterval(interval);
          };
        } else {
          scroller.autoScrolling = false;
        }
      }
    });
  }
};
var listenerOptions = {
  capture: true,
  passive: true
};
var _timeout;
var ScrollListener = class extends CorePlugin {
  constructor(manager) {
    super(manager);
    __privateAdd4(this, _timeout);
    this.handleScroll = () => {
      if (__privateGet4(this, _timeout) == null) {
        __privateSet4(this, _timeout, setTimeout(() => {
          this.manager.collisionObserver.forceUpdate(false);
          __privateSet4(this, _timeout, void 0);
        }, 50));
      }
    };
    const { dragOperation } = this.manager;
    this.destroy = E(() => {
      var _a4, _b2, _c32;
      const enabled = dragOperation.status.dragging;
      if (enabled) {
        const root = (_c32 = (_b2 = (_a4 = dragOperation.source) == null ? void 0 : _a4.element) == null ? void 0 : _b2.ownerDocument) != null ? _c32 : document;
        root.addEventListener("scroll", this.handleScroll, listenerOptions);
        return () => {
          root.removeEventListener(
            "scroll",
            this.handleScroll,
            listenerOptions
          );
        };
      }
    });
  }
};
_timeout = /* @__PURE__ */ new WeakMap();
var PreventSelection = class extends Plugin {
  constructor(manager) {
    super(manager);
    this.manager = manager;
    this.destroy = E(() => {
      const { dragOperation } = this.manager;
      if (dragOperation.status.initialized) {
        const style = document.createElement("style");
        style.innerText = `* { user-select: none !important; -webkit-user-select: none !important; }`;
        document.head.appendChild(style);
        removeSelection();
        document.addEventListener("selectionchange", removeSelection, {
          capture: true
        });
        return () => {
          document.removeEventListener("selectionchange", removeSelection, {
            capture: true
          });
          style.remove();
        };
      }
    });
  }
};
function removeSelection() {
  var _a4;
  (_a4 = document.getSelection()) == null ? void 0 : _a4.removeAllRanges();
}
var DEFAULT_KEYBOARD_CODES = {
  start: ["Space", "Enter"],
  cancel: ["Escape"],
  end: ["Space", "Enter", "Tab"],
  up: ["ArrowUp"],
  down: ["ArrowDown"],
  left: ["ArrowLeft"],
  right: ["ArrowRight"]
};
var DEFAULT_OFFSET = 10;
var _cleanupFunctions2;
var KeyboardSensor = class extends Sensor {
  constructor(manager, options2) {
    super(manager);
    this.manager = manager;
    this.options = options2;
    __privateAdd4(this, _cleanupFunctions2, []);
    this.listeners = new Listeners();
    this.handleSourceKeyDown = (event, source, options3) => {
      if (this.disabled || event.defaultPrevented) {
        return;
      }
      if (!isElement(event.target)) {
        return;
      }
      if (source.disabled) {
        return;
      }
      if (!source.handle && source.element && event.target === source.element || source.handle && event.target === source.handle) {
        const { keyboardCodes = DEFAULT_KEYBOARD_CODES } = options3 != null ? options3 : {};
        if (!keyboardCodes.start.includes(event.code)) {
          return;
        }
        if (!this.manager.dragOperation.status.idle) {
          return;
        }
        this.handleStart(event, source, options3);
      }
    };
  }
  bind(source, options2 = this.options) {
    const unbind = E(() => {
      var _a4;
      const target = (_a4 = source.handle) != null ? _a4 : source.element;
      const listener = (event) => {
        if (isKeyboardEvent(event)) {
          this.handleSourceKeyDown(event, source, options2);
        }
      };
      if (target) {
        target.addEventListener("keydown", listener);
        return () => {
          target.removeEventListener("keydown", listener);
        };
      }
    });
    return unbind;
  }
  handleStart(event, source, options2) {
    const { element } = source;
    if (!element) {
      throw new Error("Source draggable does not have an associated element");
    }
    event.preventDefault();
    event.stopImmediatePropagation();
    scrollIntoViewIfNeeded(element);
    const { center } = new DOMRectangle(element);
    r(() => {
      this.manager.actions.setDragSource(source.id);
      this.manager.actions.start({
        event,
        coordinates: {
          x: center.x,
          y: center.y
        }
      });
    });
    this.sideEffects();
    const sourceDocument = getDocument(element);
    const sourceWindow = getWindow(sourceDocument);
    const listeners = [
      this.listeners.bind(sourceDocument, [
        {
          type: "keydown",
          listener: (event2) => this.handleKeyDown(event2, source, options2),
          options: { capture: true }
        }
      ]),
      this.listeners.bind(sourceWindow, [
        { type: "resize", listener: () => this.handleEnd(event, true) }
      ])
    ];
    __privateGet4(this, _cleanupFunctions2).push(...listeners);
  }
  handleKeyDown(event, _source, options2) {
    const { keyboardCodes = DEFAULT_KEYBOARD_CODES } = options2 != null ? options2 : {};
    if (isKeycode(event, [...keyboardCodes.end, ...keyboardCodes.cancel])) {
      event.preventDefault();
      const canceled = isKeycode(event, keyboardCodes.cancel);
      this.handleEnd(event, canceled);
      return;
    }
    if (isKeycode(event, keyboardCodes.up)) {
      this.handleMove("up", event);
    } else if (isKeycode(event, keyboardCodes.down)) {
      this.handleMove("down", event);
    }
    if (isKeycode(event, keyboardCodes.left)) {
      this.handleMove("left", event);
    } else if (isKeycode(event, keyboardCodes.right)) {
      this.handleMove("right", event);
    }
  }
  handleEnd(event, canceled) {
    this.manager.actions.stop({
      event,
      canceled
    });
    this.cleanup();
  }
  handleMove(direction, event) {
    const { shape } = this.manager.dragOperation;
    const factor = event.shiftKey ? 5 : 1;
    let offset = {
      x: 0,
      y: 0
    };
    if (!shape) {
      return;
    }
    switch (direction) {
      case "up":
        offset = { x: 0, y: -DEFAULT_OFFSET * factor };
        break;
      case "down":
        offset = { x: 0, y: DEFAULT_OFFSET * factor };
        break;
      case "left":
        offset = { x: -DEFAULT_OFFSET * factor, y: 0 };
        break;
      case "right":
        offset = { x: DEFAULT_OFFSET * factor, y: 0 };
        break;
    }
    if ((offset == null ? void 0 : offset.x) || (offset == null ? void 0 : offset.y)) {
      event.preventDefault();
      this.manager.actions.move({
        event,
        by: offset
      });
    }
  }
  sideEffects() {
    const autoScroller = this.manager.registry.plugins.get(AutoScroller);
    if ((autoScroller == null ? void 0 : autoScroller.disabled) === false) {
      autoScroller.disable();
      __privateGet4(this, _cleanupFunctions2).push(() => {
        autoScroller.enable();
      });
    }
  }
  cleanup() {
    __privateGet4(this, _cleanupFunctions2).forEach((cleanup) => cleanup());
  }
  destroy() {
    this.cleanup();
    this.listeners.clear();
  }
};
_cleanupFunctions2 = /* @__PURE__ */ new WeakMap();
function isKeycode(event, codes) {
  return codes.includes(event.code);
}
var _clearTimeout;
var _PointerSensor = class _PointerSensor2 extends Sensor {
  constructor(manager, options2) {
    super(manager);
    this.manager = manager;
    this.options = options2;
    this.listeners = new Listeners();
    this.cleanup = /* @__PURE__ */ new Set();
    __privateAdd4(this, _clearTimeout);
    this.handleCancel = this.handleCancel.bind(this);
    this.handlePointerUp = this.handlePointerUp.bind(this);
    this.handleKeyDown = this.handleKeyDown.bind(this);
  }
  bind(source, options2 = this.options) {
    const unbind = E(() => {
      var _a4;
      const target = (_a4 = source.handle) != null ? _a4 : source.element;
      const listener = (event) => {
        if (isPointerEvent(event)) {
          this.handlePointerDown(event, source, options2);
        }
      };
      if (target) {
        patchWindow(target.ownerDocument.defaultView);
        target.addEventListener("pointerdown", listener);
        return () => {
          target.removeEventListener("pointerdown", listener);
        };
      }
    });
    return unbind;
  }
  handlePointerDown(event, source, options2 = {}) {
    if (this.disabled || !event.isPrimary || event.button !== 0 || !isElement(event.target) || source.disabled || isCapturedBySensor(event)) {
      return;
    }
    const { target } = event;
    const isNativeDraggable = isHTMLElement(target) && target.draggable && target.getAttribute("draggable") === "true";
    const offset = getFrameTransform(source.element);
    this.initialCoordinates = {
      x: event.clientX * offset.scaleX + offset.x,
      y: event.clientY * offset.scaleY + offset.y
    };
    const { activationConstraints } = options2;
    const constraints = typeof activationConstraints === "function" ? activationConstraints(event, source) : activationConstraints;
    event.sensor = this;
    if (!(constraints == null ? void 0 : constraints.delay) && !(constraints == null ? void 0 : constraints.distance)) {
      this.handleStart(source, event);
    } else {
      const { delay } = constraints;
      if (delay) {
        const timeout2 = setTimeout(
          () => this.handleStart(source, event),
          delay.value
        );
        __privateSet4(this, _clearTimeout, () => {
          clearTimeout(timeout2);
          __privateSet4(this, _clearTimeout, void 0);
        });
      }
    }
    const ownerDocument = getDocument(event.target);
    const unbindListeners = this.listeners.bind(ownerDocument, [
      {
        type: "pointermove",
        listener: (event2) => this.handlePointerMove(event2, source, options2)
      },
      {
        type: "pointerup",
        listener: this.handlePointerUp,
        options: {
          capture: true
        }
      },
      {
        // Cancel activation if there is a competing Drag and Drop interaction
        type: "dragstart",
        listener: isNativeDraggable ? this.handleCancel : preventDefault,
        options: {
          capture: true
        }
      }
    ]);
    const cleanup = () => {
      var _a4;
      unbindListeners();
      (_a4 = __privateGet4(this, _clearTimeout)) == null ? void 0 : _a4.call(this);
      this.initialCoordinates = void 0;
    };
    this.cleanup.add(cleanup);
  }
  handlePointerMove(event, source, options2) {
    const coordinates = {
      x: event.clientX,
      y: event.clientY
    };
    const offset = getFrameTransform(source.element);
    coordinates.x = coordinates.x * offset.scaleX + offset.x;
    coordinates.y = coordinates.y * offset.scaleY + offset.y;
    if (this.manager.dragOperation.status.dragging) {
      event.preventDefault();
      event.stopPropagation();
      this.manager.actions.move({ event, to: coordinates });
      return;
    }
    if (!this.initialCoordinates) {
      return;
    }
    const delta = {
      x: coordinates.x - this.initialCoordinates.x,
      y: coordinates.y - this.initialCoordinates.y
    };
    const { activationConstraints } = options2;
    const constraints = typeof activationConstraints === "function" ? activationConstraints(event, source) : activationConstraints;
    const { distance, delay } = constraints != null ? constraints : {};
    if (distance) {
      if (distance.tolerance != null && exceedsDistance(delta, distance.tolerance)) {
        return this.handleCancel(event);
      }
      if (exceedsDistance(delta, distance.value)) {
        return this.handleStart(source, event);
      }
    }
    if (delay) {
      if (exceedsDistance(delta, delay.tolerance)) {
        return this.handleCancel(event);
      }
    }
  }
  handlePointerUp(event) {
    const { status } = this.manager.dragOperation;
    if (status.dragging) {
      event.preventDefault();
      event.stopPropagation();
      const canceled = !status.initialized;
      this.manager.actions.stop({ event, canceled });
    }
    this.cleanup.forEach((cleanup) => cleanup());
    this.cleanup.clear();
  }
  handleKeyDown(event) {
    if (event.key === "Escape") {
      event.preventDefault();
      this.handleCancel(event);
    }
  }
  handleStart(source, event) {
    var _a4;
    const { manager, initialCoordinates } = this;
    (_a4 = __privateGet4(this, _clearTimeout)) == null ? void 0 : _a4.call(this);
    if (!initialCoordinates || manager.dragOperation.status.initialized) {
      return;
    }
    if (event.defaultPrevented) {
      return;
    }
    event.preventDefault();
    r(() => {
      manager.actions.setDragSource(source.id);
      manager.actions.start({ coordinates: initialCoordinates, event });
    });
    const ownerDocument = getDocument(event.target);
    const pointerCaptureTarget = ownerDocument.body;
    pointerCaptureTarget.setPointerCapture(event.pointerId);
    const unbind = this.listeners.bind(ownerDocument, [
      {
        // Prevent scrolling on touch devices
        type: "touchmove",
        listener: preventDefault,
        options: {
          passive: false
        }
      },
      {
        // Prevent click events
        type: "click",
        listener: preventDefault
      },
      {
        type: "contextmenu",
        listener: preventDefault
      },
      {
        type: "keydown",
        listener: this.handleKeyDown
      },
      {
        type: "lostpointercapture",
        listener: (event2) => {
          if (event2.target !== pointerCaptureTarget)
            return;
          this.handlePointerUp(event2);
        }
      }
    ]);
    this.cleanup.add(unbind);
  }
  handleCancel(event) {
    const { dragOperation } = this.manager;
    if (dragOperation.status.initialized) {
      this.manager.actions.stop({ event, canceled: true });
    }
    this.cleanup.forEach((cleanup) => cleanup());
    this.cleanup.clear();
  }
  destroy() {
    this.listeners.clear();
  }
};
_clearTimeout = /* @__PURE__ */ new WeakMap();
_PointerSensor.configure = configurator(_PointerSensor);
var PointerSensor = _PointerSensor;
function isCapturedBySensor(event) {
  return "sensor" in event;
}
function preventDefault(event) {
  event.preventDefault();
}
function noop() {
}
var windows = /* @__PURE__ */ new WeakSet();
function patchWindow(window2) {
  if (!window2 || windows.has(window2)) {
    return;
  }
  window2.addEventListener("touchmove", noop, {
    capture: false,
    passive: false
  });
  windows.add(window2);
}
var defaultPreset = {
  modifiers: [],
  plugins: [Accessibility, AutoScroller, Cursor, Feedback, PreventSelection],
  sensors: [
    PointerSensor.configure({
      activationConstraints(event, source) {
        var _a4;
        const { pointerType, target } = event;
        if (pointerType === "mouse" && isElement(target) && (source.handle === target || ((_a4 = source.handle) == null ? void 0 : _a4.contains(target)))) {
          return void 0;
        }
        if (pointerType === "touch") {
          return {
            delay: { value: 250, tolerance: 5 }
          };
        }
        return {
          delay: { value: 200, tolerance: 10 },
          distance: { value: 5 }
        };
      }
    }),
    KeyboardSensor
  ]
};
var DragDropManager2 = class extends DragDropManager {
  constructor(input = {}) {
    const {
      plugins = defaultPreset.plugins,
      sensors = defaultPreset.sensors,
      modifiers = []
    } = input;
    super(__spreadProps2(__spreadValues3({}, input), {
      plugins: [ScrollListener, Scroller, ...plugins],
      sensors,
      modifiers
    }));
  }
};
var _feedback_dec;
var _element_dec;
var _handle_dec;
var _c3;
var _init42;
var _handle;
var _element;
var _feedback;
var Draggable2 = class extends (_c3 = Draggable, _handle_dec = [reactive], _element_dec = [reactive], _feedback_dec = [reactive], _c3) {
  constructor(_a4, manager) {
    var _b2 = _a4, {
      element,
      effects: effects2 = () => [],
      handle,
      feedback = "default"
    } = _b2, input = __objRest2(_b2, [
      "element",
      "effects",
      "handle",
      "feedback"
    ]);
    super(
      __spreadValues3({
        effects: () => [
          ...effects2(),
          () => {
            var _a5, _b3;
            const { manager: manager2 } = this;
            if (!manager2)
              return;
            const sensors = (_b3 = (_a5 = this.sensors) == null ? void 0 : _a5.map(descriptor)) != null ? _b3 : [
              ...manager2.sensors
            ];
            const unbindFunctions = sensors.map((entry) => {
              const sensorInstance = entry instanceof Sensor ? entry : manager2.registry.register(entry.plugin);
              const options2 = entry instanceof Sensor ? void 0 : entry.options;
              const unbind = sensorInstance.bind(this, options2);
              return unbind;
            });
            return function cleanup() {
              unbindFunctions.forEach((unbind) => unbind());
            };
          }
        ]
      }, input),
      manager
    );
    __privateAdd4(this, _handle, __runInitializers3(_init42, 8, this)), __runInitializers3(_init42, 11, this);
    __privateAdd4(this, _element, __runInitializers3(_init42, 12, this)), __runInitializers3(_init42, 15, this);
    __privateAdd4(this, _feedback, __runInitializers3(_init42, 16, this)), __runInitializers3(_init42, 19, this);
    this.element = element;
    this.handle = handle;
    this.feedback = feedback;
  }
};
_init42 = __decoratorStart3(_c3);
_handle = /* @__PURE__ */ new WeakMap();
_element = /* @__PURE__ */ new WeakMap();
_feedback = /* @__PURE__ */ new WeakMap();
__decorateElement3(_init42, 4, "handle", _handle_dec, Draggable2, _handle);
__decorateElement3(_init42, 4, "element", _element_dec, Draggable2, _element);
__decorateElement3(_init42, 4, "feedback", _feedback_dec, Draggable2, _feedback);
__decoratorMetadata3(_init42, Draggable2);
var _proxy_dec;
var _element_dec2;
var _c22;
var _init52;
var _element2;
var _d;
var element_get;
var element_set;
var _Droppable_instances;
var _proxy;
var Droppable2 = class extends (_c22 = Droppable, _element_dec2 = [reactive], _proxy_dec = [reactive], _c22) {
  constructor(_a4, manager) {
    var _b2 = _a4, { element, effects: effects2 = () => [] } = _b2, input = __objRest2(_b2, ["element", "effects"]);
    const { collisionDetector = defaultCollisionDetection } = input;
    const updateShape = (boundingClientRect) => {
      const { manager: manager2, element: element2 } = this;
      if (!element2 || boundingClientRect === null) {
        this.shape = void 0;
        return void 0;
      }
      if (!manager2)
        return;
      const updatedShape = new DOMRectangle(element2);
      const shape = n(() => this.shape);
      if (updatedShape && (shape == null ? void 0 : shape.equals(updatedShape))) {
        return shape;
      }
      this.shape = updatedShape;
      return updatedShape;
    };
    const observePosition = d(false);
    super(
      __spreadProps2(__spreadValues3({}, input), {
        collisionDetector,
        effects: () => [
          ...effects2(),
          () => {
            const { element: element2, manager: manager2 } = this;
            if (!manager2)
              return;
            const { dragOperation } = manager2;
            const { source } = dragOperation;
            observePosition.value = Boolean(
              source && dragOperation.status.initialized && element2 && !this.disabled && this.accepts(source)
            );
          },
          () => {
            const { element: element2 } = this;
            if (observePosition.value && element2) {
              const positionObserver = new PositionObserver(
                element2,
                updateShape
              );
              return () => {
                positionObserver.disconnect();
                this.shape = void 0;
              };
            }
          },
          () => {
            var _a5;
            if ((_a5 = this.manager) == null ? void 0 : _a5.dragOperation.status.initialized) {
              return () => {
                this.shape = void 0;
              };
            }
          }
        ]
      }),
      manager
    );
    __privateAdd4(this, _Droppable_instances);
    __privateAdd4(this, _element2, __runInitializers3(_init52, 8, this)), __runInitializers3(_init52, 11, this);
    __privateAdd4(this, _proxy, __runInitializers3(_init52, 12, this)), __runInitializers3(_init52, 15, this);
    this.element = element;
    this.refreshShape = () => updateShape();
  }
  set element(element) {
    __privateSet4(this, _Droppable_instances, element, element_set);
  }
  get element() {
    var _a4;
    return (_a4 = this.proxy) != null ? _a4 : __privateGet4(this, _Droppable_instances, element_get);
  }
};
_init52 = __decoratorStart3(_c22);
_element2 = /* @__PURE__ */ new WeakMap();
_Droppable_instances = /* @__PURE__ */ new WeakSet();
_proxy = /* @__PURE__ */ new WeakMap();
_d = __decorateElement3(_init52, 20, "#element", _element_dec2, _Droppable_instances, _element2), element_get = _d.get, element_set = _d.set;
__decorateElement3(_init52, 4, "proxy", _proxy_dec, Droppable2, _proxy);
__decoratorMetadata3(_init52, Droppable2);

// node_modules/@dnd-kit/react/hooks.js
var import_react = __toESM(require_react(), 1);
var import_react_dom = __toESM(require_react_dom(), 1);

// node_modules/@dnd-kit/react/utilities.js
function isRef(value) {
  return value != null && typeof value === "object" && "current" in value;
}
function currentValue(value) {
  var _a4;
  if (value == null) {
    return void 0;
  }
  if (isRef(value)) {
    return (_a4 = value.current) != null ? _a4 : void 0;
  }
  return value;
}

// node_modules/@dnd-kit/react/hooks.js
function useConstant(initializer) {
  const ref = (0, import_react.useRef)(null);
  if (!ref.current) {
    ref.current = initializer();
  }
  return ref.current;
}
var canUseDOM2 = typeof window !== "undefined" && typeof window.document !== "undefined" && typeof window.document.createElement !== "undefined";
var useIsomorphicLayoutEffect = canUseDOM2 ? import_react.useLayoutEffect : import_react.useEffect;
function useForceUpdate() {
  const setState = (0, import_react.useState)(0)[1];
  return (0, import_react.useCallback)(() => {
    setState((value) => value + 1);
  }, [setState]);
}
function useDeepSignal(target, synchronous) {
  const tracked = (0, import_react.useRef)(/* @__PURE__ */ new Map());
  const forceUpdate = useForceUpdate();
  useIsomorphicLayoutEffect(() => {
    if (!target) {
      tracked.current.clear();
      return;
    }
    return E(() => {
      var _a4;
      let stale = false;
      let sync = false;
      for (const entry of tracked.current) {
        const [key] = entry;
        const value = n(() => entry[1]);
        const latestValue = target[key];
        if (value !== latestValue) {
          stale = true;
          tracked.current.set(key, latestValue);
          sync = (_a4 = synchronous == null ? void 0 : synchronous(key, value, latestValue)) != null ? _a4 : false;
        }
      }
      if (stale) {
        sync ? (0, import_react_dom.flushSync)(forceUpdate) : forceUpdate();
      }
    });
  }, [target]);
  return (0, import_react.useMemo)(
    () => target ? new Proxy(target, {
      get(target2, key) {
        const value = target2[key];
        tracked.current.set(key, value);
        return value;
      }
    }) : target,
    [target]
  );
}
function useImmediateEffect(callback, _2) {
  callback();
}
function useLatest(value) {
  const valueRef = (0, import_react.useRef)(value);
  useIsomorphicLayoutEffect(() => {
    valueRef.current = value;
  }, [value]);
  return valueRef;
}
function useOnValueChange(value, onChange, effect3 = import_react.useEffect, compare = Object.is) {
  const tracked = (0, import_react.useRef)(value);
  effect3(() => {
    const oldValue = tracked.current;
    if (!compare(value, oldValue)) {
      tracked.current = value;
      onChange(value, oldValue);
    }
  }, [onChange, value]);
}
function useOnElementChange(value, onChange) {
  const previous = (0, import_react.useRef)(currentValue(value));
  useIsomorphicLayoutEffect(() => {
    const current = currentValue(value);
    if (current !== previous.current) {
      previous.current = current;
      onChange(current);
    }
  });
}

// node_modules/@dnd-kit/react/index.js
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var __defProp4 = Object.defineProperty;
var __defProps3 = Object.defineProperties;
var __getOwnPropDescs3 = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols4 = Object.getOwnPropertySymbols;
var __hasOwnProp4 = Object.prototype.hasOwnProperty;
var __propIsEnum4 = Object.prototype.propertyIsEnumerable;
var __defNormalProp4 = (obj, key, value) => key in obj ? __defProp4(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues4 = (a2, b2) => {
  for (var prop in b2 || (b2 = {}))
    if (__hasOwnProp4.call(b2, prop))
      __defNormalProp4(a2, prop, b2[prop]);
  if (__getOwnPropSymbols4)
    for (var prop of __getOwnPropSymbols4(b2)) {
      if (__propIsEnum4.call(b2, prop))
        __defNormalProp4(a2, prop, b2[prop]);
    }
  return a2;
};
var __spreadProps3 = (a2, b2) => __defProps3(a2, __getOwnPropDescs3(b2));
var __objRest3 = (source, exclude) => {
  var target = {};
  for (var prop in source)
    if (__hasOwnProp4.call(source, prop) && exclude.indexOf(prop) < 0)
      target[prop] = source[prop];
  if (source != null && __getOwnPropSymbols4)
    for (var prop of __getOwnPropSymbols4(source)) {
      if (exclude.indexOf(prop) < 0 && __propIsEnum4.call(source, prop))
        target[prop] = source[prop];
    }
  return target;
};
var defaultManager = new DragDropManager2();
var DragDropContext = (0, import_react2.createContext)(
  defaultManager
);
function useRenderer() {
  const [_2, startTransition2] = (0, import_react2.useTransition)();
  const [transitionCount, setTransitionCount] = (0, import_react2.useState)(0);
  const rendering = (0, import_react2.useRef)(null);
  const resolver = (0, import_react2.useRef)(null);
  const renderer = useConstant(() => ({
    get rendering() {
      var _a4;
      return (_a4 = rendering.current) != null ? _a4 : Promise.resolve();
    }
  }));
  useOnValueChange(
    transitionCount,
    () => {
      var _a4;
      (_a4 = resolver.current) == null ? void 0 : _a4.call(resolver);
      rendering.current = null;
    },
    import_react2.useLayoutEffect
  );
  return {
    renderer,
    trackRendering(callback) {
      if (!rendering.current) {
        rendering.current = new Promise((resolve) => {
          resolver.current = resolve;
        });
      }
      startTransition2(() => {
        callback();
        setTransitionCount((count) => count + 1);
      });
    }
  };
}
var options = [void 0, deepEqual];
function DragDropProvider(_a4) {
  var _b2 = _a4, {
    children,
    onCollision,
    onBeforeDragStart,
    onDragStart,
    onDragMove,
    onDragOver,
    onDragEnd
  } = _b2, input = __objRest3(_b2, [
    "children",
    "onCollision",
    "onBeforeDragStart",
    "onDragStart",
    "onDragMove",
    "onDragOver",
    "onDragEnd"
  ]);
  var _a22;
  const { renderer, trackRendering } = useRenderer();
  const [manager, setManager] = (0, import_react2.useState)(
    (_a22 = input.manager) != null ? _a22 : null
  );
  const { plugins, modifiers, sensors } = input;
  const handleBeforeDragStart = useLatest(onBeforeDragStart);
  const handleDragStart = useLatest(onDragStart);
  const handleDragOver = useLatest(onDragOver);
  const handleDragMove = useLatest(onDragMove);
  const handleDragEnd = useLatest(onDragEnd);
  const handleCollision = useLatest(onCollision);
  (0, import_react2.useEffect)(() => {
    var _a32;
    const manager2 = (_a32 = input.manager) != null ? _a32 : new DragDropManager2(input);
    manager2.renderer = renderer;
    manager2.monitor.addEventListener("beforedragstart", (event, manager3) => {
      const callback = handleBeforeDragStart.current;
      if (callback) {
        trackRendering(() => callback(event, manager3));
      }
    });
    manager2.monitor.addEventListener(
      "dragstart",
      (event, manager3) => {
        var _a42;
        return (_a42 = handleDragStart.current) == null ? void 0 : _a42.call(handleDragStart, event, manager3);
      }
    );
    manager2.monitor.addEventListener("dragover", (event, manager3) => {
      const callback = handleDragOver.current;
      if (callback) {
        trackRendering(() => callback(event, manager3));
      }
    });
    manager2.monitor.addEventListener("dragmove", (event, manager3) => {
      const callback = handleDragMove.current;
      if (callback) {
        trackRendering(() => callback(event, manager3));
      }
    });
    manager2.monitor.addEventListener("dragend", (event, manager3) => {
      const callback = handleDragEnd.current;
      if (callback) {
        trackRendering(() => callback(event, manager3));
      }
    });
    manager2.monitor.addEventListener(
      "collision",
      (event, manager3) => {
        var _a42;
        return (_a42 = handleCollision.current) == null ? void 0 : _a42.call(handleCollision, event, manager3);
      }
    );
    (0, import_react2.startTransition)(() => setManager(manager2));
    return manager2.destroy;
  }, [renderer, input.manager]);
  useOnValueChange(
    plugins,
    () => manager && (manager.plugins = plugins != null ? plugins : defaultPreset.plugins),
    ...options
  );
  useOnValueChange(
    sensors,
    () => manager && (manager.sensors = sensors != null ? sensors : defaultPreset.sensors),
    ...options
  );
  useOnValueChange(
    modifiers,
    () => manager && (manager.modifiers = modifiers != null ? modifiers : defaultPreset.modifiers),
    ...options
  );
  return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(DragDropContext.Provider, { value: manager, children });
}
function useDragDropManager() {
  return (0, import_react2.useContext)(DragDropContext);
}
function useInstance(initializer) {
  var _a4;
  const manager = (_a4 = useDragDropManager()) != null ? _a4 : void 0;
  const [instance] = (0, import_react2.useState)(() => initializer(manager));
  if (instance.manager !== manager) {
    instance.manager = manager;
  }
  useIsomorphicLayoutEffect(instance.register, [manager, instance]);
  return instance;
}
function useDroppable(input) {
  const { collisionDetector, data, disabled, element, id, accept, type } = input;
  const droppable = useInstance(
    (manager) => new Droppable2(
      __spreadProps3(__spreadValues4({}, input), {
        register: false,
        element: currentValue(element)
      }),
      manager
    )
  );
  const trackedDroppalbe = useDeepSignal(droppable);
  useOnValueChange(id, () => droppable.id = id);
  useOnElementChange(element, (element2) => droppable.element = element2);
  useOnValueChange(accept, () => droppable.id = id, void 0, deepEqual);
  useOnValueChange(collisionDetector, () => droppable.id = id);
  useOnValueChange(data, () => data && (droppable.data = data));
  useOnValueChange(disabled, () => droppable.disabled = disabled === true);
  useOnValueChange(type, () => droppable.id = id);
  return {
    droppable: trackedDroppalbe,
    get isDropTarget() {
      return trackedDroppalbe.isDropTarget;
    },
    ref: (0, import_react2.useCallback)(
      (element2) => {
        var _a4, _b2;
        if (!element2 && ((_a4 = droppable.element) == null ? void 0 : _a4.isConnected) && !((_b2 = droppable.manager) == null ? void 0 : _b2.dragOperation.status.idle)) {
          return;
        }
        droppable.element = element2 != null ? element2 : void 0;
      },
      [droppable]
    )
  };
}

// node_modules/@dnd-kit/helpers/dist/index.js
var __defProp5 = Object.defineProperty;
var __defProps4 = Object.defineProperties;
var __getOwnPropDescs4 = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols5 = Object.getOwnPropertySymbols;
var __hasOwnProp5 = Object.prototype.hasOwnProperty;
var __propIsEnum5 = Object.prototype.propertyIsEnumerable;
var __defNormalProp5 = (obj, key, value) => key in obj ? __defProp5(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues5 = (a2, b2) => {
  for (var prop in b2 || (b2 = {}))
    if (__hasOwnProp5.call(b2, prop))
      __defNormalProp5(a2, prop, b2[prop]);
  if (__getOwnPropSymbols5)
    for (var prop of __getOwnPropSymbols5(b2)) {
      if (__propIsEnum5.call(b2, prop))
        __defNormalProp5(a2, prop, b2[prop]);
    }
  return a2;
};
var __spreadProps4 = (a2, b2) => __defProps4(a2, __getOwnPropDescs4(b2));
function arrayMove(array, from, to) {
  if (from === to) {
    return array;
  }
  const newArray = array.slice();
  newArray.splice(to, 0, newArray.splice(from, 1)[0]);
  return newArray;
}
function mutate(items, event, mutation) {
  var _a4, _b2;
  const { source, target, canceled } = event.operation;
  if (!source || !target || canceled) {
    if ("preventDefault" in event)
      event.preventDefault();
    return items;
  }
  const findIndex = (item, id) => item === id || typeof item === "object" && "id" in item && item.id === id;
  if (Array.isArray(items)) {
    const sourceIndex2 = items.findIndex((item) => findIndex(item, source.id));
    const targetIndex2 = items.findIndex((item) => findIndex(item, target.id));
    if (sourceIndex2 === -1 || targetIndex2 === -1) {
      return items;
    }
    if (!canceled && "index" in source && typeof source.index === "number") {
      const projectedSourceIndex = source.index;
      if (projectedSourceIndex !== sourceIndex2) {
        return mutation(items, sourceIndex2, projectedSourceIndex);
      }
    }
    return mutation(items, sourceIndex2, targetIndex2);
  }
  const entries = Object.entries(items);
  let sourceIndex = -1;
  let sourceParent;
  let targetIndex = -1;
  let targetParent;
  for (const [id, children] of entries) {
    if (sourceIndex === -1) {
      sourceIndex = children.findIndex((item) => findIndex(item, source.id));
      if (sourceIndex !== -1) {
        sourceParent = id;
      }
    }
    if (targetIndex === -1) {
      targetIndex = children.findIndex((item) => findIndex(item, target.id));
      if (targetIndex !== -1) {
        targetParent = id;
      }
    }
    if (sourceIndex !== -1 && targetIndex !== -1) {
      break;
    }
  }
  if (!source.manager)
    return items;
  const { dragOperation } = source.manager;
  const position = (_b2 = (_a4 = dragOperation.shape) == null ? void 0 : _a4.current.center) != null ? _b2 : dragOperation.position.current;
  if (targetParent == null) {
    if (target.id in items) {
      const insertionIndex = target.shape && position.y > target.shape.center.y ? items[target.id].length : 0;
      targetParent = target.id;
      targetIndex = insertionIndex;
    }
  }
  if (sourceParent == null || targetParent == null || sourceParent === targetParent && sourceIndex === targetIndex) {
    if ("preventDefault" in event)
      event.preventDefault();
    return items;
  }
  if (sourceParent === targetParent) {
    return __spreadProps4(__spreadValues5({}, items), {
      [sourceParent]: mutation(items[sourceParent], sourceIndex, targetIndex)
    });
  }
  const isBelowTarget = target.shape && Math.round(position.y) > Math.round(target.shape.center.y);
  const modifier = isBelowTarget ? 1 : 0;
  const sourceItem = items[sourceParent][sourceIndex];
  return __spreadProps4(__spreadValues5({}, items), {
    [sourceParent]: [
      ...items[sourceParent].slice(0, sourceIndex),
      ...items[sourceParent].slice(sourceIndex + 1)
    ],
    [targetParent]: [
      ...items[targetParent].slice(0, targetIndex + modifier),
      sourceItem,
      ...items[targetParent].slice(targetIndex + modifier)
    ]
  });
}
function move(items, event) {
  return mutate(items, event, arrayMove);
}

// app/components/admin/column/ColumnContainer.tsx
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/components/admin/column/ColumnContainer.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/components/admin/column/ColumnContainer.tsx"
  );
  import.meta.hot.lastModified = "1748449862987.6116";
}
function ColumnContainer({
  children,
  id,
  title
}) {
  _s();
  const {
    isDropTarget,
    ref
  } = useDroppable({
    id,
    type: "column",
    accept: "item",
    collisionPriority: CollisionPriority.Low
  });
  const style = isDropTarget ? {
    background: "#666666"
  } : void 0;
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "w-full", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("label", { className: "text-lg font-bold", children: title }, void 0, false, {
      fileName: "app/components/admin/column/ColumnContainer.tsx",
      lineNumber: 43,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "mt-3 flex min-h-60 w-full flex-col gap-2.5 rounded-[5px] bg-white p-5", ref, style, children: children.length > 0 ? children : "Drop here..." }, void 0, false, {
      fileName: "app/components/admin/column/ColumnContainer.tsx",
      lineNumber: 44,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/components/admin/column/ColumnContainer.tsx",
    lineNumber: 42,
    columnNumber: 10
  }, this);
}
_s(ColumnContainer, "haqCdT+TdygBd5coiqbgRW1wE28=", false, function() {
  return [useDroppable];
});
_c4 = ColumnContainer;
var _c4;
$RefreshReg$(_c4, "ColumnContainer");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;

// node_modules/@dnd-kit/react/sortable.js
var import_react4 = __toESM(require_react(), 1);

// node_modules/@dnd-kit/dom/sortable.js
var __create4 = Object.create;
var __defProp6 = Object.defineProperty;
var __defProps5 = Object.defineProperties;
var __getOwnPropDesc4 = Object.getOwnPropertyDescriptor;
var __getOwnPropDescs5 = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols6 = Object.getOwnPropertySymbols;
var __hasOwnProp6 = Object.prototype.hasOwnProperty;
var __propIsEnum6 = Object.prototype.propertyIsEnumerable;
var __knownSymbol4 = (name, symbol) => (symbol = Symbol[name]) ? symbol : Symbol.for("Symbol." + name);
var __typeError5 = (msg) => {
  throw TypeError(msg);
};
var __defNormalProp6 = (obj, key, value) => key in obj ? __defProp6(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues6 = (a2, b2) => {
  for (var prop in b2 || (b2 = {}))
    if (__hasOwnProp6.call(b2, prop))
      __defNormalProp6(a2, prop, b2[prop]);
  if (__getOwnPropSymbols6)
    for (var prop of __getOwnPropSymbols6(b2)) {
      if (__propIsEnum6.call(b2, prop))
        __defNormalProp6(a2, prop, b2[prop]);
    }
  return a2;
};
var __spreadProps5 = (a2, b2) => __defProps5(a2, __getOwnPropDescs5(b2));
var __objRest4 = (source, exclude) => {
  var target = {};
  for (var prop in source)
    if (__hasOwnProp6.call(source, prop) && exclude.indexOf(prop) < 0)
      target[prop] = source[prop];
  if (source != null && __getOwnPropSymbols6)
    for (var prop of __getOwnPropSymbols6(source)) {
      if (exclude.indexOf(prop) < 0 && __propIsEnum6.call(source, prop))
        target[prop] = source[prop];
    }
  return target;
};
var __decoratorStart4 = (base) => {
  var _a4;
  return [, , , __create4((_a4 = void 0) != null ? _a4 : null)];
};
var __decoratorStrings4 = ["class", "method", "getter", "setter", "accessor", "field", "value", "get", "set"];
var __expectFn4 = (fn) => fn !== void 0 && typeof fn !== "function" ? __typeError5("Function expected") : fn;
var __decoratorContext4 = (kind, name, done, metadata, fns) => ({ kind: __decoratorStrings4[kind], name, metadata, addInitializer: (fn) => done._ ? __typeError5("Already initialized") : fns.push(__expectFn4(fn || null)) });
var __decoratorMetadata4 = (array, target) => __defNormalProp6(target, __knownSymbol4("metadata"), array[3]);
var __runInitializers4 = (array, flags, self, value) => {
  for (var i2 = 0, fns = array[flags >> 1], n2 = fns && fns.length; i2 < n2; i2++)
    flags & 1 ? fns[i2].call(self) : value = fns[i2].call(self, value);
  return value;
};
var __decorateElement4 = (array, flags, name, decorators, target, extra) => {
  var fn, it, done, ctx, access, k = flags & 7, s2 = !!(flags & 8), p2 = !!(flags & 16);
  var j = array.length + 1, key = __decoratorStrings4[k + 5];
  var initializers = array[j - 1] = [], extraInitializers = array[j] || (array[j] = []);
  var desc = (target = target.prototype, __getOwnPropDesc4({ get [name]() {
    return __privateGet5(this, extra);
  }, set [name](x) {
    return __privateSet5(this, extra, x);
  } }, name));
  for (var i2 = decorators.length - 1; i2 >= 0; i2--) {
    ctx = __decoratorContext4(k, name, done = {}, array[3], extraInitializers);
    {
      ctx.static = s2, ctx.private = p2, access = ctx.access = { has: (x) => name in x };
      access.get = (x) => x[name];
      access.set = (x, y2) => x[name] = y2;
    }
    it = (0, decorators[i2])({ get: desc.get, set: desc.set }, ctx), done._ = 1;
    if (it === void 0)
      __expectFn4(it) && (desc[key] = it);
    else if (typeof it !== "object" || it === null)
      __typeError5("Object expected");
    else
      __expectFn4(fn = it.get) && (desc.get = fn), __expectFn4(fn = it.set) && (desc.set = fn), __expectFn4(fn = it.init) && initializers.unshift(fn);
  }
  return desc && __defProp6(target, name, desc), target;
};
var __accessCheck5 = (obj, member, msg) => member.has(obj) || __typeError5("Cannot " + msg);
var __privateGet5 = (obj, member, getter) => (__accessCheck5(obj, member, "read from private field"), member.get(obj));
var __privateAdd5 = (obj, member, value) => member.has(obj) ? __typeError5("Cannot add the same private member more than once") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);
var __privateSet5 = (obj, member, value, setter) => (__accessCheck5(obj, member, "write to private field"), member.set(obj, value), value);
function isSortable(element) {
  return element instanceof SortableDroppable || element instanceof SortableDraggable;
}
var TOLERANCE = 10;
var SortableKeyboardPlugin = class extends Plugin {
  constructor(manager) {
    super(manager);
    const cleanupEffect = E(() => {
      const { dragOperation } = manager;
      if (!isKeyboardEvent(dragOperation.activatorEvent)) {
        return;
      }
      if (!isSortable(dragOperation.source)) {
        return;
      }
      if (dragOperation.status.initialized) {
        const scroller = manager.registry.plugins.get(Scroller);
        if (scroller) {
          scroller.disable();
          return () => scroller.enable();
        }
      }
    });
    const unsubscribe = manager.monitor.addEventListener(
      "dragmove",
      (event, manager2) => {
        queueMicrotask(() => {
          if (this.disabled || event.defaultPrevented || !event.nativeEvent) {
            return;
          }
          const { dragOperation } = manager2;
          if (!isKeyboardEvent(event.nativeEvent)) {
            return;
          }
          if (!isSortable(dragOperation.source)) {
            return;
          }
          if (!dragOperation.shape) {
            return;
          }
          const { actions, collisionObserver, registry } = manager2;
          const { by } = event;
          if (!by) {
            return;
          }
          const direction = getDirection2(by);
          const { source, target } = dragOperation;
          const { center } = dragOperation.shape.current;
          const potentialTargets = [];
          const cleanup = [];
          r(() => {
            for (const droppable of registry.droppables) {
              const { id: id2 } = droppable;
              if (!droppable.accepts(source) || id2 === (target == null ? void 0 : target.id) && isSortable(droppable) || !droppable.element) {
                continue;
              }
              let previousShape = droppable.shape;
              const shape = new DOMRectangle(droppable.element, {
                getBoundingClientRect: (element) => getVisibleBoundingRectangle(element, void 0, 0.2)
              });
              if (!shape.height || !shape.width)
                continue;
              if (direction == "down" && center.y + TOLERANCE < shape.center.y || direction == "up" && center.y - TOLERANCE > shape.center.y || direction == "left" && center.x - TOLERANCE > shape.center.x || direction == "right" && center.x + TOLERANCE < shape.center.x) {
                potentialTargets.push(droppable);
                droppable.shape = shape;
                cleanup.push(() => droppable.shape = previousShape);
              }
            }
          });
          event.preventDefault();
          collisionObserver.disable();
          const collisions = collisionObserver.computeCollisions(
            potentialTargets,
            closestCorners
          );
          r(() => cleanup.forEach((clean) => clean()));
          const [firstCollision] = collisions;
          if (!firstCollision) {
            return;
          }
          const { id } = firstCollision;
          const { index, group } = source.sortable;
          actions.setDropTarget(id).then(() => {
            const { source: source2, target: target2, shape } = dragOperation;
            if (!source2 || !isSortable(source2) || !shape) {
              return;
            }
            const {
              index: newIndex,
              group: newGroup,
              target: targetElement
            } = source2.sortable;
            const updated = index !== newIndex || group !== newGroup;
            const element = updated ? targetElement : target2 == null ? void 0 : target2.element;
            if (!element)
              return;
            scrollIntoViewIfNeeded(element);
            const updatedShape = new DOMRectangle(element);
            if (!updatedShape) {
              return;
            }
            const delta = Rectangle.delta(
              updatedShape,
              Rectangle.from(shape.current.boundingRectangle),
              source2.alignment
            );
            actions.move({
              by: delta
            });
            if (updated) {
              actions.setDropTarget(source2.id).then(() => collisionObserver.enable());
            } else {
              collisionObserver.enable();
            }
          });
        });
      }
    );
    this.destroy = () => {
      unsubscribe();
      cleanupEffect();
    };
  }
};
function getDirection2(delta) {
  const { x, y: y2 } = delta;
  if (x > 0) {
    return "right";
  } else if (x < 0) {
    return "left";
  } else if (y2 > 0) {
    return "down";
  } else if (y2 < 0) {
    return "up";
  }
}
var __defProp22 = Object.defineProperty;
var __defProps22 = Object.defineProperties;
var __getOwnPropDescs22 = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols22 = Object.getOwnPropertySymbols;
var __hasOwnProp22 = Object.prototype.hasOwnProperty;
var __propIsEnum22 = Object.prototype.propertyIsEnumerable;
var __defNormalProp22 = (obj, key, value) => key in obj ? __defProp22(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues22 = (a2, b2) => {
  for (var prop in b2 || (b2 = {}))
    if (__hasOwnProp22.call(b2, prop))
      __defNormalProp22(a2, prop, b2[prop]);
  if (__getOwnPropSymbols22)
    for (var prop of __getOwnPropSymbols22(b2)) {
      if (__propIsEnum22.call(b2, prop))
        __defNormalProp22(a2, prop, b2[prop]);
    }
  return a2;
};
var __spreadProps22 = (a2, b2) => __defProps22(a2, __getOwnPropDescs22(b2));
function arrayMove2(array, from, to) {
  if (from === to) {
    return array;
  }
  const newArray = array.slice();
  newArray.splice(to, 0, newArray.splice(from, 1)[0]);
  return newArray;
}
function mutate2(items, event, mutation) {
  var _a4, _b2;
  const { source, target, canceled } = event.operation;
  if (!source || !target || canceled) {
    if ("preventDefault" in event)
      event.preventDefault();
    return items;
  }
  const findIndex = (item, id) => item === id || typeof item === "object" && "id" in item && item.id === id;
  if (Array.isArray(items)) {
    const sourceIndex2 = items.findIndex((item) => findIndex(item, source.id));
    const targetIndex2 = items.findIndex((item) => findIndex(item, target.id));
    if (sourceIndex2 === -1 || targetIndex2 === -1) {
      return items;
    }
    if (!canceled && "index" in source && typeof source.index === "number") {
      const projectedSourceIndex = source.index;
      if (projectedSourceIndex !== sourceIndex2) {
        return mutation(items, sourceIndex2, projectedSourceIndex);
      }
    }
    return mutation(items, sourceIndex2, targetIndex2);
  }
  const entries = Object.entries(items);
  let sourceIndex = -1;
  let sourceParent;
  let targetIndex = -1;
  let targetParent;
  for (const [id, children] of entries) {
    if (sourceIndex === -1) {
      sourceIndex = children.findIndex((item) => findIndex(item, source.id));
      if (sourceIndex !== -1) {
        sourceParent = id;
      }
    }
    if (targetIndex === -1) {
      targetIndex = children.findIndex((item) => findIndex(item, target.id));
      if (targetIndex !== -1) {
        targetParent = id;
      }
    }
    if (sourceIndex !== -1 && targetIndex !== -1) {
      break;
    }
  }
  if (!source.manager)
    return items;
  const { dragOperation } = source.manager;
  const position = (_b2 = (_a4 = dragOperation.shape) == null ? void 0 : _a4.current.center) != null ? _b2 : dragOperation.position.current;
  if (targetParent == null) {
    if (target.id in items) {
      const insertionIndex = target.shape && position.y > target.shape.center.y ? items[target.id].length : 0;
      targetParent = target.id;
      targetIndex = insertionIndex;
    }
  }
  if (sourceParent == null || targetParent == null || sourceParent === targetParent && sourceIndex === targetIndex) {
    if ("preventDefault" in event)
      event.preventDefault();
    return items;
  }
  if (sourceParent === targetParent) {
    return __spreadProps22(__spreadValues22({}, items), {
      [sourceParent]: mutation(items[sourceParent], sourceIndex, targetIndex)
    });
  }
  const isBelowTarget = target.shape && Math.round(position.y) > Math.round(target.shape.center.y);
  const modifier = isBelowTarget ? 1 : 0;
  const sourceItem = items[sourceParent][sourceIndex];
  return __spreadProps22(__spreadValues22({}, items), {
    [sourceParent]: [
      ...items[sourceParent].slice(0, sourceIndex),
      ...items[sourceParent].slice(sourceIndex + 1)
    ],
    [targetParent]: [
      ...items[targetParent].slice(0, targetIndex + modifier),
      sourceItem,
      ...items[targetParent].slice(targetIndex + modifier)
    ]
  });
}
function move2(items, event) {
  return mutate2(items, event, arrayMove2);
}
var defaultGroup = "__default__";
var OptimisticSortingPlugin = class extends Plugin {
  constructor(manager) {
    super(manager);
    const getSortableInstances = () => {
      const sortableInstances = /* @__PURE__ */ new Map();
      for (const droppable of manager.registry.droppables) {
        if (droppable instanceof SortableDroppable) {
          const { sortable } = droppable;
          const { group } = sortable;
          let instances = sortableInstances.get(group);
          if (!instances) {
            instances = /* @__PURE__ */ new Set();
            sortableInstances.set(group, instances);
          }
          instances.add(sortable);
        }
      }
      for (const [group, instances] of sortableInstances) {
        sortableInstances.set(group, new Set(sort(instances)));
      }
      return sortableInstances;
    };
    const unsubscribe = [
      manager.monitor.addEventListener("dragover", (event, manager2) => {
        queueMicrotask(() => {
          if (this.disabled || event.defaultPrevented) {
            return;
          }
          const { dragOperation } = manager2;
          const { source, target } = dragOperation;
          if (!isSortable(source) || !isSortable(target)) {
            return;
          }
          if (source.sortable === target.sortable) {
            return;
          }
          const instances = getSortableInstances();
          const sameGroup = source.sortable.group === target.sortable.group;
          const sourceInstances = instances.get(source.sortable.group);
          const targetInstances = sameGroup ? sourceInstances : instances.get(target.sortable.group);
          if (!sourceInstances || !targetInstances)
            return;
          manager2.renderer.rendering.then(() => {
            var _a4, _b2, _c6;
            const newInstances = getSortableInstances();
            for (const [group, sortableInstances] of instances.entries()) {
              const entries = Array.from(sortableInstances).entries();
              for (const [index, sortable] of entries) {
                if (sortable.index !== index || sortable.group !== group || !((_a4 = newInstances.get(group)) == null ? void 0 : _a4.has(sortable))) {
                  return;
                }
              }
            }
            const sourceElement = source.sortable.element;
            const targetElement = target.sortable.element;
            if (!targetElement || !sourceElement) {
              return;
            }
            if (!sameGroup && target.id === source.sortable.group) {
              return;
            }
            const orderedSourceSortables = sort(sourceInstances);
            const orderedTargetSortables = sameGroup ? orderedSourceSortables : sort(targetInstances);
            const sourceGroup = (_b2 = source.sortable.group) != null ? _b2 : defaultGroup;
            const targetGroup = (_c6 = target.sortable.group) != null ? _c6 : defaultGroup;
            const state = {
              [sourceGroup]: orderedSourceSortables,
              [targetGroup]: orderedTargetSortables
            };
            const newState = move2(state, event);
            if (state === newState)
              return;
            const sourceIndex = newState[targetGroup].indexOf(source.sortable);
            const targetIndex = newState[targetGroup].indexOf(target.sortable);
            reorder(sourceElement, sourceIndex, targetElement, targetIndex);
            manager2.collisionObserver.disable();
            r(() => {
              for (const [index, sortable] of newState[sourceGroup].entries()) {
                sortable.index = index;
              }
              if (!sameGroup) {
                for (const [index, sortable] of newState[targetGroup].entries()) {
                  sortable.group = target.sortable.group;
                  sortable.index = index;
                }
              }
            });
            manager2.actions.setDropTarget(source.id).then(() => manager2.collisionObserver.enable());
          });
        });
      }),
      manager.monitor.addEventListener("dragend", (event, manager2) => {
        if (!event.canceled) {
          return;
        }
        const { dragOperation } = manager2;
        const { source } = dragOperation;
        if (!isSortable(source)) {
          return;
        }
        if (source.sortable.initialIndex === source.sortable.index && source.sortable.initialGroup === source.sortable.group) {
          return;
        }
        queueMicrotask(() => {
          const instances = getSortableInstances();
          const initialGroupInstances = instances.get(
            source.sortable.initialGroup
          );
          if (!initialGroupInstances)
            return;
          manager2.renderer.rendering.then(() => {
            var _a4;
            for (const [group, sortableInstances] of instances.entries()) {
              const entries = Array.from(sortableInstances).entries();
              for (const [index, sortable] of entries) {
                if (sortable.index !== index || sortable.group !== group) {
                  return;
                }
              }
            }
            const initialGroup = sort(initialGroupInstances);
            const sourceElement = source.sortable.element;
            const targetElement = (_a4 = initialGroup[source.sortable.initialIndex]) == null ? void 0 : _a4.element;
            if (!targetElement || !sourceElement) {
              return;
            }
            reorder(
              sourceElement,
              source.sortable.initialIndex,
              targetElement,
              source.sortable.initialIndex
            );
            r(() => {
              for (const [_2, sortableInstances] of instances.entries()) {
                const entries = Array.from(sortableInstances).values();
                for (const sortable of entries) {
                  sortable.index = sortable.initialIndex;
                  sortable.group = sortable.initialGroup;
                }
              }
            });
          });
        });
      })
    ];
    this.destroy = () => {
      for (const unsubscribeListener of unsubscribe) {
        unsubscribeListener();
      }
    };
  }
};
function reorder(sourceElement, sourceIndex, targetElement, targetIndex) {
  const position = targetIndex < sourceIndex ? "afterend" : "beforebegin";
  targetElement.insertAdjacentElement(position, sourceElement);
}
function sortByIndex(a2, b2) {
  return a2.index - b2.index;
}
function sort(instances) {
  return Array.from(instances).sort(sortByIndex);
}
var defaultPlugins = [
  SortableKeyboardPlugin,
  OptimisticSortingPlugin
];
var defaultSortableTransition = {
  duration: 250,
  easing: "cubic-bezier(0.25, 1, 0.5, 1)",
  idle: false
};
var _group_dec;
var _index_dec;
var _init6;
var _index;
var _group;
var _element3;
_index_dec = [reactive], _group_dec = [reactive];
var Sortable2 = class {
  constructor(_a4, manager) {
    __privateAdd5(this, _index, __runInitializers4(_init6, 8, this)), __runInitializers4(_init6, 11, this);
    __privateAdd5(this, _group, __runInitializers4(_init6, 12, this)), __runInitializers4(_init6, 15, this);
    __privateAdd5(this, _element3);
    this.register = () => {
      r(() => {
        var _a5, _b3;
        (_a5 = this.manager) == null ? void 0 : _a5.registry.register(this.droppable);
        (_b3 = this.manager) == null ? void 0 : _b3.registry.register(this.draggable);
      });
      return () => this.unregister();
    };
    this.unregister = () => {
      r(() => {
        var _a5, _b3;
        (_a5 = this.manager) == null ? void 0 : _a5.registry.unregister(this.droppable);
        (_b3 = this.manager) == null ? void 0 : _b3.registry.unregister(this.draggable);
      });
    };
    this.destroy = () => {
      r(() => {
        this.droppable.destroy();
        this.draggable.destroy();
      });
    };
    var _b2 = _a4, {
      effects: inputEffects = () => [],
      group,
      index,
      sensors,
      type,
      transition = defaultSortableTransition,
      plugins = defaultPlugins
    } = _b2, input = __objRest4(_b2, [
      "effects",
      "group",
      "index",
      "sensors",
      "type",
      "transition",
      "plugins"
    ]);
    let previousGroup = group;
    this.droppable = new SortableDroppable(input, manager, this);
    this.draggable = new SortableDraggable(
      __spreadProps5(__spreadValues6({}, input), {
        effects: () => [
          () => {
            var _a22;
            return (_a22 = this.manager) == null ? void 0 : _a22.monitor.addEventListener("dragstart", () => {
              this.initialIndex = this.index;
              this.initialGroup = this.group;
              this.previousIndex = this.index;
            });
          },
          () => {
            const { index: index2, group: group2, previousIndex, manager: _2 } = this;
            if (index2 !== previousIndex || group2 !== previousGroup) {
              this.previousIndex = index2;
              previousGroup = group2;
              this.animate();
            }
          },
          () => {
            const { target } = this;
            const { feedback, isDragSource } = this.draggable;
            if (feedback == "move" && isDragSource) {
              this.droppable.disabled = !target;
            }
          },
          () => {
            const { manager: manager2 } = this;
            for (const plugin of plugins) {
              manager2 == null ? void 0 : manager2.registry.register(plugin);
            }
          },
          ...inputEffects()
        ],
        type,
        sensors
      }),
      manager,
      this
    );
    __privateSet5(this, _element3, input.element);
    this.manager = manager;
    this.index = index;
    this.previousIndex = index;
    this.initialIndex = index;
    this.group = group;
    this.type = type;
    this.transition = transition;
  }
  animate() {
    n(() => {
      const { manager, transition } = this;
      const { shape } = this.droppable;
      if (!manager)
        return;
      const { idle } = manager.dragOperation.status;
      if (!shape || !transition || idle && !transition.idle) {
        return;
      }
      manager.renderer.rendering.then(() => {
        const { element } = this;
        if (!element) {
          return;
        }
        const updatedShape = this.refreshShape();
        if (!updatedShape) {
          return;
        }
        queueMicrotask(() => {
          const delta = {
            x: shape.boundingRectangle.left - updatedShape.boundingRectangle.left,
            y: shape.boundingRectangle.top - updatedShape.boundingRectangle.top
          };
          const { translate } = getComputedStyles(element);
          const currentTranslate = computeTranslate(element, translate, false);
          const finalTranslate = computeTranslate(element, translate);
          if (delta.x || delta.y) {
            animateTransform({
              element,
              keyframes: {
                translate: [
                  `${currentTranslate.x + delta.x}px ${currentTranslate.y + delta.y}px ${currentTranslate.z}`,
                  `${finalTranslate.x}px ${finalTranslate.y}px ${finalTranslate.z}`
                ]
              },
              options: transition,
              onFinish: () => {
                if (!manager.dragOperation.status.dragging) {
                  this.droppable.shape = void 0;
                }
              }
            });
          }
        });
      });
    });
  }
  get manager() {
    return this.draggable.manager;
  }
  set manager(manager) {
    r(() => {
      this.draggable.manager = manager;
      this.droppable.manager = manager;
    });
  }
  set element(element) {
    r(() => {
      const previousElement = __privateGet5(this, _element3);
      const droppableElement = this.droppable.element;
      const draggableElement = this.draggable.element;
      if (!droppableElement || droppableElement === previousElement) {
        this.droppable.element = element;
      }
      if (!draggableElement || draggableElement === previousElement) {
        this.draggable.element = element;
      }
      __privateSet5(this, _element3, element);
    });
  }
  get element() {
    var _a4, _b2;
    const element = __privateGet5(this, _element3);
    if (!element)
      return;
    return (_b2 = (_a4 = ProxiedElements.get(element)) != null ? _a4 : element) != null ? _b2 : this.droppable.element;
  }
  set target(target) {
    this.droppable.element = target;
  }
  get target() {
    return this.droppable.element;
  }
  set source(source) {
    this.draggable.element = source;
  }
  get source() {
    return this.draggable.element;
  }
  get disabled() {
    return this.draggable.disabled && this.droppable.disabled;
  }
  set feedback(value) {
    this.draggable.feedback = value;
  }
  set disabled(value) {
    r(() => {
      this.droppable.disabled = value;
      this.draggable.disabled = value;
    });
  }
  set data(data) {
    r(() => {
      this.droppable.data = data;
      this.draggable.data = data;
    });
  }
  set handle(handle) {
    this.draggable.handle = handle;
  }
  set id(id) {
    r(() => {
      this.droppable.id = id;
      this.draggable.id = id;
    });
  }
  get id() {
    return this.droppable.id;
  }
  set sensors(value) {
    this.draggable.sensors = value;
  }
  set modifiers(value) {
    this.draggable.modifiers = value;
  }
  set collisionPriority(value) {
    this.droppable.collisionPriority = value;
  }
  set collisionDetector(value) {
    this.droppable.collisionDetector = value != null ? value : defaultCollisionDetection;
  }
  set alignment(value) {
    this.draggable.alignment = value;
  }
  get alignment() {
    return this.draggable.alignment;
  }
  set type(type) {
    r(() => {
      this.droppable.type = type;
      this.draggable.type = type;
    });
  }
  get type() {
    return this.draggable.type;
  }
  set accept(value) {
    this.droppable.accept = value;
  }
  get accept() {
    return this.droppable.accept;
  }
  get isDropTarget() {
    return this.droppable.isDropTarget;
  }
  /**
   * A boolean indicating whether the sortable item is the source of a drag operation.
   */
  get isDragSource() {
    return this.draggable.isDragSource;
  }
  /**
   * A boolean indicating whether the sortable item is being dragged.
   */
  get isDragging() {
    return this.draggable.isDragging;
  }
  /**
   * A boolean indicating whether the sortable item is being dropped.
   */
  get isDropping() {
    return this.draggable.isDropping;
  }
  get status() {
    return this.draggable.status;
  }
  refreshShape() {
    return this.droppable.refreshShape();
  }
  accepts(draggable) {
    return this.droppable.accepts(draggable);
  }
};
_init6 = __decoratorStart4();
_index = /* @__PURE__ */ new WeakMap();
_group = /* @__PURE__ */ new WeakMap();
_element3 = /* @__PURE__ */ new WeakMap();
__decorateElement4(_init6, 4, "index", _index_dec, Sortable2, _index);
__decorateElement4(_init6, 4, "group", _group_dec, Sortable2, _group);
__decoratorMetadata4(_init6, Sortable2);
var SortableDraggable = class extends Draggable2 {
  constructor(input, manager, sortable) {
    super(input, manager);
    this.sortable = sortable;
  }
  get index() {
    return this.sortable.index;
  }
};
var SortableDroppable = class extends Droppable2 {
  constructor(input, manager, sortable) {
    super(input, manager);
    this.sortable = sortable;
  }
};

// node_modules/@dnd-kit/react/sortable.js
var __defProp7 = Object.defineProperty;
var __defProps6 = Object.defineProperties;
var __getOwnPropDescs6 = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols7 = Object.getOwnPropertySymbols;
var __hasOwnProp7 = Object.prototype.hasOwnProperty;
var __propIsEnum7 = Object.prototype.propertyIsEnumerable;
var __defNormalProp7 = (obj, key, value) => key in obj ? __defProp7(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues7 = (a2, b2) => {
  for (var prop in b2 || (b2 = {}))
    if (__hasOwnProp7.call(b2, prop))
      __defNormalProp7(a2, prop, b2[prop]);
  if (__getOwnPropSymbols7)
    for (var prop of __getOwnPropSymbols7(b2)) {
      if (__propIsEnum7.call(b2, prop))
        __defNormalProp7(a2, prop, b2[prop]);
    }
  return a2;
};
var __spreadProps6 = (a2, b2) => __defProps6(a2, __getOwnPropDescs6(b2));
function useSortable(input) {
  const {
    accept,
    collisionDetector,
    collisionPriority,
    id,
    data,
    element,
    handle,
    index,
    group,
    disabled,
    feedback,
    modifiers,
    sensors,
    target,
    type
  } = input;
  const transition = __spreadValues7(__spreadValues7({}, defaultSortableTransition), input.transition);
  const sortable = useInstance((manager) => {
    return new Sortable2(
      __spreadProps6(__spreadValues7({}, input), {
        transition,
        register: false,
        handle: currentValue(handle),
        element: currentValue(element),
        target: currentValue(target),
        feedback
      }),
      manager
    );
  });
  const trackedSortable = useDeepSignal(sortable, shouldUpdateSynchronously);
  useOnValueChange(id, () => sortable.id = id);
  useIsomorphicLayoutEffect(() => {
    r(() => {
      sortable.group = group;
      sortable.index = index;
    });
  }, [sortable, group, index]);
  useOnValueChange(type, () => sortable.type = type);
  useOnValueChange(
    accept,
    () => sortable.accept = accept,
    void 0,
    deepEqual
  );
  useOnValueChange(data, () => data && (sortable.data = data));
  useOnValueChange(
    index,
    () => {
      var _a4;
      if (((_a4 = sortable.manager) == null ? void 0 : _a4.dragOperation.status.idle) && (transition == null ? void 0 : transition.idle)) {
        sortable.refreshShape();
      }
    },
    useImmediateEffect
  );
  useOnElementChange(handle, (handle2) => sortable.handle = handle2);
  useOnElementChange(element, (element2) => sortable.element = element2);
  useOnElementChange(target, (target2) => sortable.target = target2);
  useOnValueChange(disabled, () => sortable.disabled = disabled === true);
  useOnValueChange(sensors, () => sortable.sensors = sensors);
  useOnValueChange(
    collisionDetector,
    () => sortable.collisionDetector = collisionDetector
  );
  useOnValueChange(
    collisionPriority,
    () => sortable.collisionPriority = collisionPriority
  );
  useOnValueChange(feedback, () => sortable.feedback = feedback != null ? feedback : "default");
  useOnValueChange(
    transition,
    () => sortable.transition = transition,
    void 0,
    deepEqual
  );
  useOnValueChange(
    modifiers,
    () => sortable.modifiers = modifiers,
    void 0,
    deepEqual
  );
  useOnValueChange(
    input.alignment,
    () => sortable.alignment = input.alignment
  );
  return {
    sortable: trackedSortable,
    get isDragging() {
      return trackedSortable.isDragging;
    },
    get isDropping() {
      return trackedSortable.isDropping;
    },
    get isDragSource() {
      return trackedSortable.isDragSource;
    },
    get isDropTarget() {
      return trackedSortable.isDropTarget;
    },
    handleRef: (0, import_react4.useCallback)(
      (element2) => {
        sortable.handle = element2 != null ? element2 : void 0;
      },
      [sortable]
    ),
    ref: (0, import_react4.useCallback)(
      (element2) => {
        var _a4, _b2;
        if (!element2 && ((_a4 = sortable.element) == null ? void 0 : _a4.isConnected) && !((_b2 = sortable.manager) == null ? void 0 : _b2.dragOperation.status.idle)) {
          return;
        }
        sortable.element = element2 != null ? element2 : void 0;
      },
      [sortable]
    ),
    sourceRef: (0, import_react4.useCallback)(
      (element2) => {
        var _a4, _b2;
        if (!element2 && ((_a4 = sortable.source) == null ? void 0 : _a4.isConnected) && !((_b2 = sortable.manager) == null ? void 0 : _b2.dragOperation.status.idle)) {
          return;
        }
        sortable.source = element2 != null ? element2 : void 0;
      },
      [sortable]
    ),
    targetRef: (0, import_react4.useCallback)(
      (element2) => {
        var _a4, _b2;
        if (!element2 && ((_a4 = sortable.target) == null ? void 0 : _a4.isConnected) && !((_b2 = sortable.manager) == null ? void 0 : _b2.dragOperation.status.idle)) {
          return;
        }
        sortable.target = element2 != null ? element2 : void 0;
      },
      [sortable]
    )
  };
}
function shouldUpdateSynchronously(key, oldValue, newValue) {
  if (key === "isDragSource" && !newValue && oldValue)
    return true;
  return false;
}

// app/components/admin/column/ColumnItem.tsx
var import_jsx_dev_runtime2 = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/components/admin/column/ColumnItem.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s2 = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/components/admin/column/ColumnItem.tsx"
  );
  import.meta.hot.lastModified = "1748449881482.595";
}
function ColumnItem({
  id,
  text,
  index,
  group
}) {
  _s2();
  const {
    ref,
    isDragSource
  } = useSortable({
    id,
    index,
    type: "item",
    accept: "item",
    group
  });
  return /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("button", { className: "item", ref, "data-dragging": isDragSource, children: text }, void 0, false, {
    fileName: "app/components/admin/column/ColumnItem.tsx",
    lineNumber: 40,
    columnNumber: 10
  }, this);
}
_s2(ColumnItem, "K/AnnbGZImRutjhubbRd+5PQGE0=", false, function() {
  return [useSortable];
});
_c5 = ColumnItem;
var _c5;
$RefreshReg$(_c5, "ColumnItem");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;

export {
  DragDropProvider,
  move,
  ColumnContainer,
  ColumnItem
};
//# sourceMappingURL=/build/_shared/chunk-DOCJ2TWD.js.map
