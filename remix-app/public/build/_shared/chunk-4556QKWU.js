import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Gun_Violence_Archive_Logo_Icon_default
} from "/build/_shared/chunk-W7JLG7II.js";
import {
  useMatchesData,
  useOptionalUser
} from "/build/_shared/chunk-DFTVPORL.js";
import {
  Form,
  Link,
  NavLink,
  useLocation
} from "/build/_shared/chunk-R4KMRLXS.js";
import {
  require_jsx_dev_runtime
} from "/build/_shared/chunk-TQMAZLEN.js";
import {
  require_react
} from "/build/_shared/chunk-QT64XSGC.js";
import {
  createHotContext
} from "/build/_shared/chunk-ERNSEIP7.js";
import {
  __toESM
} from "/build/_shared/chunk-73CLBT4D.js";

// app/images/Gun-Violence-Archive-Logo.svg
var Gun_Violence_Archive_Logo_default = "/build/_assets/Gun-Violence-Archive-Logo-LJMURNSJ.svg";

// app/components/common/Header.tsx
var import_react = __toESM(require_react(), 1);

// app/images/Icon-Nav-Donate-Reverse.svg
var Icon_Nav_Donate_Reverse_default = "/build/_assets/Icon-Nav-Donate-Reverse-27SHFPAB.svg";

// app/images/Icon-Nav-Search-Standard.svg
var Icon_Nav_Search_Standard_default = "/build/_assets/Icon-Nav-Search-Standard-OYR6WLYW.svg";

// app/images/Icon-Nav-Contact-Standard.svg
var Icon_Nav_Contact_Standard_default = "/build/_assets/Icon-Nav-Contact-Standard-GYP2SFEE.svg";

// app/images/Icon-Nav-About-Standard.svg
var Icon_Nav_About_Standard_default = "/build/_assets/Icon-Nav-About-Standard-24HJLAM4.svg";

// app/images/Icon-Nav-Sign-In-Standard.svg
var Icon_Nav_Sign_In_Standard_default = "/build/_assets/Icon-Nav-Sign-In-Standard-IF4743LV.svg";

// app/images/Icon-Nav-Search-Reverse.svg
var Icon_Nav_Search_Reverse_default = "/build/_assets/Icon-Nav-Search-Reverse-6DKGSM3A.svg";

// app/images/Icon-Nav-Contact-Reverse.svg
var Icon_Nav_Contact_Reverse_default = "/build/_assets/Icon-Nav-Contact-Reverse-FQPFFKF2.svg";

// app/images/Icon-Nav-About-Reverse.svg
var Icon_Nav_About_Reverse_default = "/build/_assets/Icon-Nav-About-Reverse-DLZDCDXX.svg";

// app/images/Icon-Nav-Sign-In-Reverse.svg
var Icon_Nav_Sign_In_Reverse_default = "/build/_assets/Icon-Nav-Sign-In-Reverse-P37YQE7B.svg";

// app/images/Mobile-Nav-Open.svg
var Mobile_Nav_Open_default = "/build/_assets/Mobile-Nav-Open-PJF2EJKR.svg";

// app/images/Mobile-Nav-Back.svg
var Mobile_Nav_Back_default = "/build/_assets/Mobile-Nav-Back-5ZNODFSX.svg";

// app/images/Mobile-Nav-Close.svg
var Mobile_Nav_Close_default = "/build/_assets/Mobile-Nav-Close-MWXWVUUH.svg";

// app/images/Arrow-Next-Screen.svg
var Arrow_Next_Screen_default = "/build/_assets/Arrow-Next-Screen-M3JEJGRO.svg";

// app/components/common/Header.tsx
var import_jsx_dev_runtime = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/components/common/Header.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
var _s = $RefreshSig$();
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/components/common/Header.tsx"
  );
  import.meta.hot.lastModified = "1748450749220.0874";
}
var MENUS = {
  about: {
    name: "About",
    subMenus: [{
      name: "Overview",
      linkTo: "/about"
    }, {
      name: "Board of Directors",
      linkTo: "/about/board"
    }, {
      name: "Methodology",
      linkTo: "/about/methodology"
    }, {
      name: "Explainer",
      linkTo: "/about/explainer"
    }, {
      name: "F.A.Q.",
      linkTo: "/about/faq"
    }, {
      name: "History",
      linkTo: "/about/history"
    }]
  },
  search: {
    name: "Search Database",
    subMenus: [{
      name: "Basic Search",
      linkTo: "/search"
    }, {
      name: "Advanced Search",
      linkTo: "/search/advanced"
    }, {
      name: "Interactive Search",
      linkTo: "/search/interactive"
    }]
  }
};
var Header = () => {
  _s();
  const user = useOptionalUser();
  const location = useLocation();
  const rootData = useMatchesData("root");
  const [mobileMenuOpen, setMobileMenuOpen] = (0, import_react.useState)(false);
  const [mobileSubMenuOpen, setMobileSubMenuOpen] = (0, import_react.useState)(false);
  const [mainMenu, setMainMenu] = (0, import_react.useState)("");
  (0, import_react.useEffect)(() => {
    if (location.pathname == "/about" || location.pathname.startsWith("/about/")) {
      setMainMenu("about");
    } else if (location.pathname == "/search" || location.pathname.startsWith("/search/")) {
      setMainMenu("search");
    }
  }, []);
  const openMobileSubMenu = function(menu) {
    setMainMenu(menu);
    setMobileSubMenuOpen(true);
  };
  let menuDashboards = rootData && rootData.mainMenuDashboards ? rootData.mainMenuDashboards : [];
  menuDashboards = menuDashboards.filter((dashboard) => dashboard.is_active && dashboard.on_main_menu);
  const showAdminHeader = !!(user && user.role && ["Admin", "Editor"].includes(user.role));
  return /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(import_jsx_dev_runtime.Fragment, { children: [
    showAdminHeader && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(AdminHeader, {}, void 0, false, {
      fileName: "app/components/common/Header.tsx",
      lineNumber: 100,
      columnNumber: 24
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "nav-top", className: "hidden md:block border-b border-gray-500 bg-white", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("ul", { className: "flex flex-wrap justify-between text-[15px] md:justify-end", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "py-3 pr-4 md:pr-8", children: user ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { action: "/user/logout", method: "post", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { className: "flex items-center", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Nav_Sign_In_Standard_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 107,
          columnNumber: 11
        }, this),
        "Sign Out"
      ] }, void 0, true, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 106,
        columnNumber: 10
      }, this) }, void 0, false, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 105,
        columnNumber: 16
      }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(NavLink, { className: "flex items-center", to: `/user/login`, children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Nav_Sign_In_Standard_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 111,
          columnNumber: 10
        }, this),
        "Sign In"
      ] }, void 0, true, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 110,
        columnNumber: 19
      }, this) }, void 0, false, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 104,
        columnNumber: 7
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "py-3 pr-4 md:pr-8", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(NavLink, { className: "flex items-center", to: `/about`, children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Nav_About_Standard_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 117,
          columnNumber: 9
        }, this),
        "About"
      ] }, void 0, true, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 116,
        columnNumber: 8
      }, this) }, void 0, false, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 115,
        columnNumber: 7
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "py-3 pr-4 md:pr-8", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(NavLink, { className: "flex items-center", to: `/contact`, children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Nav_Contact_Standard_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 123,
          columnNumber: 9
        }, this),
        "Contact"
      ] }, void 0, true, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 122,
        columnNumber: 8
      }, this) }, void 0, false, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 121,
        columnNumber: 7
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "py-3 pr-4 md:pr-8", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(NavLink, { className: "flex items-center", to: `/search`, children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Nav_Search_Standard_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 129,
          columnNumber: 9
        }, this),
        "Search Database"
      ] }, void 0, true, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 128,
        columnNumber: 8
      }, this) }, void 0, false, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 127,
        columnNumber: 7
      }, this),
      user && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "py-3 pr-4 md:pr-8", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { className: "flex items-center", href: `/user/profile`, children: "My Account" }, void 0, false, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 134,
        columnNumber: 9
      }, this) }, void 0, false, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 133,
        columnNumber: 16
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "bg-orange-500 px-4 py-3 text-white", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(NavLink, { className: "flex items-center", to: `/donate`, children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Nav_Donate_Reverse_default, alt: "", width: 16 }, void 0, false, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 141,
          columnNumber: 9
        }, this),
        "Donate"
      ] }, void 0, true, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 140,
        columnNumber: 8
      }, this) }, void 0, false, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 139,
        columnNumber: 7
      }, this)
    ] }, void 0, true, {
      fileName: "app/components/common/Header.tsx",
      lineNumber: 103,
      columnNumber: 6
    }, this) }, void 0, false, {
      fileName: "app/components/common/Header.tsx",
      lineNumber: 102,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/components/common/Header.tsx",
      lineNumber: 101,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("header", { className: `hidden md:block ${mainMenu && mainMenu in MENUS && MENUS[mainMenu].subMenus.length > 0 ? "" : "shadow-md"} sticky ${showAdminHeader ? "top-[60px] z-40" : "top-0 z-50"}`, children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "nav-main", className: "py-5", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "items-center justify-between md:flex", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "md:pr-8", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: `/`, children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Gun_Violence_Archive_Logo_default, alt: "Gun Violence Archive", width: 230 }, void 0, false, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 154,
          columnNumber: 10
        }, this) }, void 0, false, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 153,
          columnNumber: 9
        }, this) }, void 0, false, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 152,
          columnNumber: 8
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("ul", { className: "flex flex-wrap text-lg md:text-xl", children: [
          menuDashboards.map((dashboard) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "pr-4 lg:pr-8", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(NavLink, { to: dashboard.path, children: dashboard.name }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 159,
            columnNumber: 11
          }, this) }, dashboard.id, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 158,
            columnNumber: 42
          }, this)),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "pr-4 lg:pr-8", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(NavLink, { to: `/past-tolls`, children: "Past Years" }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 162,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 161,
            columnNumber: 9
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(NavLink, { to: `/reports`, children: "Reports" }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 165,
            columnNumber: 10
          }, this) }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 164,
            columnNumber: 9
          }, this)
        ] }, void 0, true, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 157,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 151,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 150,
        columnNumber: 6
      }, this) }, void 0, false, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 149,
        columnNumber: 5
      }, this),
      mainMenu && mainMenu in MENUS && MENUS[mainMenu].subMenus.length > 0 && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "nav-sub", className: "bg-gray-800", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("ul", { className: "flex flex-wrap justify-center text-white md:text-lg", children: mainMenu && mainMenu in MENUS && MENUS[mainMenu].subMenus.map((subMenu) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "px-5", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(NavLink, { className: ({
        isActive
      }) => isActive ? "block pt-[14px] pb-[9px] font-bold border-b-[5px] border-orange-500" : "block pt-[14px] pb-[9px]", to: subMenu.linkTo, end: true, children: subMenu.name }, void 0, false, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 177,
        columnNumber: 11
      }, this) }, `submenu-${subMenu.name}`, false, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 176,
        columnNumber: 83
      }, this)) }, void 0, false, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 175,
        columnNumber: 8
      }, this) }, void 0, false, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 174,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 173,
        columnNumber: 78
      }, this)
    ] }, void 0, true, {
      fileName: "app/components/common/Header.tsx",
      lineNumber: 148,
      columnNumber: 4
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("header", { className: showAdminHeader ? "block md:hidden text-white sticky top-[80px] z-40 shadow-md" : "block md:hidden text-white sticky top-0 z-50 shadow-md", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { id: "mobile-nav-main", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex items-center justify-between h-[100px]", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Link, { to: `/`, children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Gun_Violence_Archive_Logo_default, alt: "Gun Violence Archive", width: 230 }, void 0, false, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 193,
          columnNumber: 9
        }, this) }, void 0, false, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 192,
          columnNumber: 8
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { onClick: () => setMobileMenuOpen(true), children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Mobile_Nav_Open_default, width: 30 }, void 0, false, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 195,
          columnNumber: 56
        }, this) }, void 0, false, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 195,
          columnNumber: 8
        }, this)
      ] }, void 0, true, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 191,
        columnNumber: 7
      }, this) }, void 0, false, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 190,
        columnNumber: 6
      }, this),
      mobileMenuOpen && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-orange-500 fixed inset-0 overflow-y-scroll z-[99]", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex items-center justify-end border-b border-orange-800 h-[100px]", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { onClick: () => setMobileMenuOpen(false), children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Mobile_Nav_Close_default, width: 30 }, void 0, false, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 201,
          columnNumber: 59
        }, this) }, void 0, false, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 201,
          columnNumber: 10
        }, this) }, void 0, false, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 200,
          columnNumber: 9
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("ul", { className: "text-xl border-b border-orange-800 pb-7", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "pt-7", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { href: `/`, children: "Home" }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 205,
            columnNumber: 11
          }, this) }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 204,
            columnNumber: 10
          }, this),
          menuDashboards.map((dashboard) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "pt-7", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { href: dashboard.path, children: dashboard.name }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 208,
            columnNumber: 12
          }, this) }, dashboard.id, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 207,
            columnNumber: 43
          }, this)),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "pt-7", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { href: `/past-tolls`, children: "Past Years" }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 211,
            columnNumber: 11
          }, this) }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 210,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "pt-7", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { href: `/reports`, children: "Reports" }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 214,
            columnNumber: 11
          }, this) }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 213,
            columnNumber: 10
          }, this)
        ] }, void 0, true, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 203,
          columnNumber: 9
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("ul", { className: "text-[15px] pt-10", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "pb-7", children: user ? /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)(Form, { action: "/user/logout", method: "post", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { className: "flex items-center", children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Nav_Sign_In_Reverse_default, alt: "", width: 16 }, void 0, false, {
              fileName: "app/components/common/Header.tsx",
              lineNumber: 221,
              columnNumber: 14
            }, this),
            "Sign Out"
          ] }, void 0, true, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 220,
            columnNumber: 13
          }, this) }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 219,
            columnNumber: 19
          }, this) : /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { className: "flex items-center", href: `/user/login`, children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Nav_Sign_In_Reverse_default, alt: "", width: 16 }, void 0, false, {
              fileName: "app/components/common/Header.tsx",
              lineNumber: 225,
              columnNumber: 13
            }, this),
            "Sign In"
          ] }, void 0, true, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 224,
            columnNumber: 22
          }, this) }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 218,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "pb-7", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { className: "flex items-center", onClick: () => openMobileSubMenu("about"), children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Nav_About_Reverse_default, alt: "", width: 16 }, void 0, false, {
              fileName: "app/components/common/Header.tsx",
              lineNumber: 231,
              columnNumber: 12
            }, this),
            "About",
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "ml-2", src: Arrow_Next_Screen_default, alt: "", width: 16 }, void 0, false, {
              fileName: "app/components/common/Header.tsx",
              lineNumber: 233,
              columnNumber: 12
            }, this)
          ] }, void 0, true, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 230,
            columnNumber: 11
          }, this) }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 229,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "pb-7", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { className: "flex items-center", href: `/contact`, children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Nav_Contact_Reverse_default, alt: "", width: 16 }, void 0, false, {
              fileName: "app/components/common/Header.tsx",
              lineNumber: 238,
              columnNumber: 12
            }, this),
            "Contact"
          ] }, void 0, true, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 237,
            columnNumber: 11
          }, this) }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 236,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "pb-7", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { className: "flex items-center", onClick: () => openMobileSubMenu("search"), children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Nav_Search_Reverse_default, alt: "", width: 16 }, void 0, false, {
              fileName: "app/components/common/Header.tsx",
              lineNumber: 244,
              columnNumber: 12
            }, this),
            "Search Database",
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "ml-2", src: Arrow_Next_Screen_default, alt: "", width: 16 }, void 0, false, {
              fileName: "app/components/common/Header.tsx",
              lineNumber: 246,
              columnNumber: 12
            }, this)
          ] }, void 0, true, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 243,
            columnNumber: 11
          }, this) }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 242,
            columnNumber: 10
          }, this),
          user && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "pb-7", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { className: "flex items-center", href: `/user/profile`, children: "My Account" }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 250,
            columnNumber: 12
          }, this) }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 249,
            columnNumber: 19
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "pb-7", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { className: "flex items-center", href: `/donate`, children: [
            /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { className: "mr-2", src: Icon_Nav_Donate_Reverse_default, alt: "", width: 16 }, void 0, false, {
              fileName: "app/components/common/Header.tsx",
              lineNumber: 257,
              columnNumber: 12
            }, this),
            "Donate"
          ] }, void 0, true, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 256,
            columnNumber: 11
          }, this) }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 255,
            columnNumber: 10
          }, this)
        ] }, void 0, true, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 217,
          columnNumber: 9
        }, this)
      ] }, void 0, true, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 199,
        columnNumber: 8
      }, this) }, void 0, false, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 198,
        columnNumber: 25
      }, this),
      mobileSubMenuOpen && /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "bg-orange-500 fixed inset-0 overflow-y-scroll z-[100]", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "container mx-auto", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "flex items-center justify-between border-b border-orange-800 h-[100px]", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { onClick: () => setMobileSubMenuOpen(false), children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Mobile_Nav_Back_default, width: 30 }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 267,
            columnNumber: 62
          }, this) }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 267,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("div", { className: "text-2xl", children: mainMenu && mainMenu in MENUS ? MENUS[mainMenu].name : "" }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 268,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("button", { onClick: () => {
            setMobileSubMenuOpen(false);
            setMobileMenuOpen(false);
          }, children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("img", { src: Mobile_Nav_Close_default, width: 30 }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 272,
            columnNumber: 18
          }, this) }, void 0, false, {
            fileName: "app/components/common/Header.tsx",
            lineNumber: 269,
            columnNumber: 10
          }, this)
        ] }, void 0, true, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 266,
          columnNumber: 9
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("ul", { className: "text-xl pb-7", children: mainMenu && mainMenu in MENUS ? MENUS[mainMenu].subMenus.map((subMenu) => /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("li", { className: "pt-7", children: /* @__PURE__ */ (0, import_jsx_dev_runtime.jsxDEV)("a", { href: subMenu.linkTo, children: subMenu.name }, void 0, false, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 276,
          columnNumber: 12
        }, this) }, `mobile-submenu-${subMenu.name}`, false, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 275,
          columnNumber: 83
        }, this)) : null }, void 0, false, {
          fileName: "app/components/common/Header.tsx",
          lineNumber: 274,
          columnNumber: 9
        }, this)
      ] }, void 0, true, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 265,
        columnNumber: 8
      }, this) }, void 0, false, {
        fileName: "app/components/common/Header.tsx",
        lineNumber: 264,
        columnNumber: 28
      }, this)
    ] }, void 0, true, {
      fileName: "app/components/common/Header.tsx",
      lineNumber: 189,
      columnNumber: 5
    }, this) }, void 0, false, {
      fileName: "app/components/common/Header.tsx",
      lineNumber: 188,
      columnNumber: 4
    }, this)
  ] }, void 0, true, {
    fileName: "app/components/common/Header.tsx",
    lineNumber: 99,
    columnNumber: 10
  }, this);
};
_s(Header, "Rk0RYcdwX2niJCr8Zt3MPWhw0fk=", false, function() {
  return [useOptionalUser, useLocation, useMatchesData];
});
_c = Header;
var _c;
$RefreshReg$(_c, "Header");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;

// app/images/Icon-Social-Media-Facebook-Standard.svg
var Icon_Social_Media_Facebook_Standard_default = "/build/_assets/Icon-Social-Media-Facebook-Standard-5GL7WGYG.svg";

// app/images/Icon-Social-Media-X-Twitter-Standard.svg
var Icon_Social_Media_X_Twitter_Standard_default = "/build/_assets/Icon-Social-Media-X-Twitter-Standard-CYUDTUZM.svg";

// app/components/common/Footer.tsx
var import_jsx_dev_runtime2 = __toESM(require_jsx_dev_runtime(), 1);
if (!window.$RefreshReg$ || !window.$RefreshSig$ || !window.$RefreshRuntime$) {
  console.warn("remix:hmr: React Fast Refresh only works when the Remix compiler is running in development mode.");
} else {
  prevRefreshReg = window.$RefreshReg$;
  prevRefreshSig = window.$RefreshSig$;
  window.$RefreshReg$ = (type, id) => {
    window.$RefreshRuntime$.register(type, '"app/components/common/Footer.tsx"' + id);
  };
  window.$RefreshSig$ = window.$RefreshRuntime$.createSignatureFunctionForTransform;
}
var prevRefreshReg;
var prevRefreshSig;
if (import.meta) {
  import.meta.hot = createHotContext(
    //@ts-expect-error
    "app/components/common/Footer.tsx"
  );
  import.meta.hot.lastModified = "1736974048093.3667";
}
var Footer = () => {
  return /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("footer", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "footer py-9", children: /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "container mx-auto text-xl", children: /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "flex justify-between sm:items-center", children: [
    /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "flex items-start space-x-5 sm:space-x-10 md:space-x-28", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { children: /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)(Link, { to: `/`, children: /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("img", { src: Gun_Violence_Archive_Logo_Icon_default, alt: "Gun Violence Archive", width: 60 }, void 0, false, {
        fileName: "app/components/common/Footer.tsx",
        lineNumber: 33,
        columnNumber: 10
      }, this) }, void 0, false, {
        fileName: "app/components/common/Footer.tsx",
        lineNumber: 32,
        columnNumber: 9
      }, this) }, void 0, false, {
        fileName: "app/components/common/Footer.tsx",
        lineNumber: 31,
        columnNumber: 8
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "sm:flex sm:space-x-10 md:space-x-28", children: [
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "text-xs text-white", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "text-sm font-bold", children: "Site Information" }, void 0, false, {
            fileName: "app/components/common/Footer.tsx",
            lineNumber: 38,
            columnNumber: 10
          }, this),
          "Copyright \xA9 ",
          (/* @__PURE__ */ new Date()).getFullYear(),
          /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("br", {}, void 0, false, {
            fileName: "app/components/common/Footer.tsx",
            lineNumber: 40,
            columnNumber: 10
          }, this),
          "Gun Violence Archive",
          /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("br", {}, void 0, false, {
            fileName: "app/components/common/Footer.tsx",
            lineNumber: 42,
            columnNumber: 10
          }, this),
          "All rights reserved."
        ] }, void 0, true, {
          fileName: "app/components/common/Footer.tsx",
          lineNumber: 37,
          columnNumber: 9
        }, this),
        /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "text-xs text-white", children: [
          /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "text-sm font-bold", children: "Get in Touch" }, void 0, false, {
            fileName: "app/components/common/Footer.tsx",
            lineNumber: 46,
            columnNumber: 10
          }, this),
          "PO Box 22221",
          /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("br", {}, void 0, false, {
            fileName: "app/components/common/Footer.tsx",
            lineNumber: 48,
            columnNumber: 10
          }, this),
          "Lexington, KY 40522-2221",
          /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("br", {}, void 0, false, {
            fileName: "app/components/common/Footer.tsx",
            lineNumber: 50,
            columnNumber: 10
          }, this),
          /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("a", { href: "mailto:<EMAIL>", children: "<EMAIL>" }, void 0, false, {
            fileName: "app/components/common/Footer.tsx",
            lineNumber: 51,
            columnNumber: 10
          }, this)
        ] }, void 0, true, {
          fileName: "app/components/common/Footer.tsx",
          lineNumber: 45,
          columnNumber: 9
        }, this)
      ] }, void 0, true, {
        fileName: "app/components/common/Footer.tsx",
        lineNumber: 36,
        columnNumber: 8
      }, this)
    ] }, void 0, true, {
      fileName: "app/components/common/Footer.tsx",
      lineNumber: 30,
      columnNumber: 7
    }, this),
    /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("div", { className: "flex space-x-5", children: [
      /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)(Link, { to: `https://facebook.com/gunviolencearchive`, children: /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("img", { src: Icon_Social_Media_Facebook_Standard_default, alt: "Facebook", width: 30 }, void 0, false, {
        fileName: "app/components/common/Footer.tsx",
        lineNumber: 57,
        columnNumber: 9
      }, this) }, void 0, false, {
        fileName: "app/components/common/Footer.tsx",
        lineNumber: 56,
        columnNumber: 8
      }, this),
      /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)(Link, { to: `https://twitter.com/gundeaths`, children: /* @__PURE__ */ (0, import_jsx_dev_runtime2.jsxDEV)("img", { src: Icon_Social_Media_X_Twitter_Standard_default, alt: "X", width: 30 }, void 0, false, {
        fileName: "app/components/common/Footer.tsx",
        lineNumber: 60,
        columnNumber: 9
      }, this) }, void 0, false, {
        fileName: "app/components/common/Footer.tsx",
        lineNumber: 59,
        columnNumber: 8
      }, this)
    ] }, void 0, true, {
      fileName: "app/components/common/Footer.tsx",
      lineNumber: 55,
      columnNumber: 7
    }, this)
  ] }, void 0, true, {
    fileName: "app/components/common/Footer.tsx",
    lineNumber: 29,
    columnNumber: 6
  }, this) }, void 0, false, {
    fileName: "app/components/common/Footer.tsx",
    lineNumber: 28,
    columnNumber: 5
  }, this) }, void 0, false, {
    fileName: "app/components/common/Footer.tsx",
    lineNumber: 27,
    columnNumber: 4
  }, this) }, void 0, false, {
    fileName: "app/components/common/Footer.tsx",
    lineNumber: 26,
    columnNumber: 10
  }, this);
};
_c2 = Footer;
var _c2;
$RefreshReg$(_c2, "Footer");
window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;

export {
  Gun_Violence_Archive_Logo_default,
  Header,
  Footer
};
//# sourceMappingURL=/build/_shared/chunk-4556QKWU.js.map
