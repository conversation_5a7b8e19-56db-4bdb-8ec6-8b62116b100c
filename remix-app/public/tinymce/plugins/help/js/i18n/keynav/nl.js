tinymce.Resource.add('tinymce.html-i18n.help-keynav.nl',
'<h1>Toetsenbordnavigatie starten</h1>\n' +
  '\n' +
  '<dl>\n' +
  '  <dt>Focus op de menubalk instellen</dt>\n' +
  '  <dd>Windows of Linux: Alt+F9</dd>\n' +
  '  <dd>macOS: &#x2325;F9</dd>\n' +
  '  <dt>Focus op de werkbalk instellen</dt>\n' +
  '  <dd>Windows of Linux: Alt+F10</dd>\n' +
  '  <dd>macOS: &#x2325;F10</dd>\n' +
  '  <dt>Focus op de voettekst instellen</dt>\n' +
  '  <dd>Windows of Linux: Alt+F11</dd>\n' +
  '  <dd>macOS: &#x2325;F11</dd>\n' +
  '  <dt>Focus op de melding instellen</dt>\n' +
  '  <dd>Windows of Linux: Alt+F12</dd>\n' +
  '  <dd>macOS: &#x2325;F12</dd>\n' +
  '  <dt>Focus op een contextuele werkbalk instellen</dt>\n' +
  '  <dd>Windows, Linux of macOS: Ctrl+F9</dd>\n' +
  '</dl>\n' +
  '\n' +
  '<p>De navigatie start bij het eerste UI-item, dat wordt gemarkeerd of onderstreept als het eerste item zich in\n' +
  '  in het elementenpad van de voettekst bevindt.</p>\n' +
  '\n' +
  '<h1>Navigeren tussen UI-secties</h1>\n' +
  '\n' +
  '<p>Druk op <strong>Tab</strong> om naar de volgende UI-sectie te gaan.</p>\n' +
  '\n' +
  '<p>Druk op <strong>Shift+Tab</strong> om naar de vorige UI-sectie te gaan.</p>\n' +
  '\n' +
  '<p>De <strong>Tab</strong>-volgorde van deze UI-secties is:</p>\n' +
  '\n' +
  '<ol>\n' +
  '  <li>Menubalk</li>\n' +
  '  <li>Elke werkbalkgroep</li>\n' +
  '  <li>Zijbalk</li>\n' +
  '  <li>Elementenpad in de voettekst</li>\n' +
  '  <li>Wisselknop voor aantal woorden in de voettekst</li>\n' +
  '  <li>Merkkoppeling in de voettekst</li>\n' +
  '  <li>Greep voor het wijzigen van het formaat van de editor in de voettekst</li>\n' +
  '</ol>\n' +
  '\n' +
  '<p>Als een UI-sectie niet aanwezig is, wordt deze overgeslagen.</p>\n' +
  '\n' +
  '<p>Als de focus van de toetsenbordnavigatie is ingesteld op de voettekst en er geen zichtbare zijbalk is, kun je op <strong>Shift+Tab</strong> drukken\n' +
  '  om de focus naar de eerste werkbalkgroep in plaats van de laatste te verplaatsen.</p>\n' +
  '\n' +
  '<h1>Navigeren binnen UI-secties</h1>\n' +
  '\n' +
  '<p>Druk op de <strong>pijltjestoets</strong> om naar het betreffende UI-element te gaan.</p>\n' +
  '\n' +
  '<p>Met de pijltjestoetsen <strong>Links</strong> en <strong>Rechts</strong></p>\n' +
  '\n' +
  '<ul>\n' +
  "  <li>wissel je tussen menu's in de menubalk.</li>\n" +
  '  <li>open je een submenu in een menu.</li>\n' +
  '  <li>wissel je tussen knoppen in een werkbalkgroep.</li>\n' +
  '  <li>wissel je tussen items in het elementenpad in de voettekst.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p>Met de pijltjestoetsen <strong>Omlaag</strong> en <strong>Omhoog</strong></p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>wissel je tussen menu-items in een menu.</li>\n' +
  '  <li>wissel je tussen items in een werkbalkpop-upmenu.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p>Met de <strong>pijltjestoetsen</strong> wissel je binnen de UI-sectie waarop de focus is ingesteld.</p>\n' +
  '\n' +
  '<p>Druk op de toets <strong>Esc</strong> om een geopend menu, submenu of pop-upmenu te sluiten.</p>\n' +
  '\n' +
  "<p>Als de huidige focus is ingesteld 'bovenaan' een bepaalde UI-sectie, kun je op de toets <strong>Esc</strong> drukken\n" +
  '  om de toetsenbordnavigatie af te sluiten.</p>\n' +
  '\n' +
  '<h1>Een menu-item of werkbalkknop uitvoeren</h1>\n' +
  '\n' +
  '<p>Als het gewenste menu-item of de gewenste werkbalkknop is gemarkeerd, kun je op <strong>Return</strong>, <strong>Enter</strong>\n' +
  '  of de <strong>spatiebalk</strong> drukken om het item uit te voeren.</p>\n' +
  '\n' +
  '<h1>Navigeren in dialoogvensters zonder tabblad</h1>\n' +
  '\n' +
  '<p>Als een dialoogvenster zonder tabblad wordt geopend, wordt de focus ingesteld op het eerste interactieve onderdeel.</p>\n' +
  '\n' +
  '<p>Je kunt navigeren tussen interactieve onderdelen van een dialoogvenster door op <strong>Tab</strong> of <strong>Shift+Tab</strong> te drukken.</p>\n' +
  '\n' +
  '<h1>Navigeren in dialoogvensters met tabblad</h1>\n' +
  '\n' +
  '<p>Als een dialoogvenster met tabblad wordt geopend, wordt de focus ingesteld op de eerste knop in het tabbladmenu.</p>\n' +
  '\n' +
  '<p>Je kunt navigeren tussen interactieve onderdelen van dit tabblad van het dialoogvenster door op <strong>Tab</strong> of\n' +
  '  <strong>Shift+Tab</strong> te drukken.</p>\n' +
  '\n' +
  '<p>Je kunt overschakelen naar een ander tabblad van het dialoogvenster door de focus in te stellen op het tabbladmenu en vervolgens op de juiste <strong>pijltjestoets</strong>\n' +
  '  te drukken om tussen de beschikbare tabbladen te wisselen.</p>\n');