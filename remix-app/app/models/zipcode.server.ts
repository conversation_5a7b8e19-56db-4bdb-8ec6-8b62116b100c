import { Prisma } from '@prisma/client';

import { db as prisma } from '~/utils/db.server';

/**
 * ableengine_zip_codes_cities_base_query()
 * @param state
 * @param term
 * @param limit
 * @returns
 */
export function getCitiesByState(state: string, term: string | null, limit = 10): Promise<{ city: string }[]> {
	return prisma.$queryRaw`SELECT DISTINCT "city" FROM "gva_data"."zipcodes" WHERE 1=1
    ${state !== 'nostate' ? Prisma.sql` AND UPPER("statefullname") = ${state.toUpperCase()}` : Prisma.empty}
    ${term ? Prisma.sql` AND "city" ILIKE ${term + '%'}` : Prisma.empty}
    ${Prisma.sql` ORDER BY "city" LIMIT ${limit}`}
    `;
}

/**
 * ableengine_zip_codes_cities_alias_base_query()
 * @param state
 * @param term
 * @param limit
 * @returns
 */
export function getCitiesAliasByState(
	state: string,
	term: string | null,
	limit = 10
): Promise<{ cityaliasname: string }[]> {
	return prisma.$queryRaw`SELECT DISTINCT "cityaliasname" FROM "gva_data"."zipcodes" WHERE 1=1
    ${state !== 'nostate' ? Prisma.sql` AND UPPER("statefullname") = ${state.toUpperCase()}` : Prisma.empty}
    ${term ? Prisma.sql` AND "cityaliasname" ILIKE ${term + '%'}` : Prisma.empty}
    ${Prisma.sql` ORDER BY "cityaliasname" LIMIT ${limit}`}
    `;
}

/**
 * ableengine_zip_codes_counties_base_query()
 * @param city
 * @param state
 * @param term
 * @param limit
 * @returns
 */
export function getCountiesByCityState(
	city: string,
	state: string,
	term: string | null,
	limit = 10
): Promise<{ county: string }[]> {
	return prisma.$queryRaw`SELECT DISTINCT "county" FROM "gva_data"."zipcodes" WHERE 1=1
    ${city !== 'nocity' ? Prisma.sql` AND (UPPER("city") = ${city.toUpperCase()} OR UPPER("cityaliasname") = ${city.toUpperCase()})` : Prisma.empty}
    ${state !== 'nostate' ? Prisma.sql` AND UPPER("statefullname") = ${state.toUpperCase()}` : Prisma.empty}
    ${term ? Prisma.sql` AND "county" ILIKE ${term + '%'}` : Prisma.empty}
    ${Prisma.sql` ORDER BY "county" LIMIT ${limit}`}
    `;
}

/**
 * ableengine_zip_codes_get_states()
 */
export function getStates(): Promise<{ state: string; statefullname: string }[]> {
	// cache?
	return prisma.$queryRaw`SELECT DISTINCT "state", "statefullname" FROM "gva_data"."zipcodes"
	WHERE "statefullname" IS NOT NULL AND "statefullname" <> ''
	ORDER BY "statefullname"
	`;
}

/**
 * ableengine_zip_codes_get_zipcode_by_county()
 * @param state
 * @param city
 * @param county
 * @returns
 */
export function getZipcodeByCounty(state: string, city: string, county: string): Promise<{ zipcode: string }[]> {
	return prisma.$queryRaw`SELECT "zipcode" FROM "gva_data"."zipcodes" WHERE 1=1
	${state ? Prisma.sql` AND "statefullname" ILIKE ${state}` : Prisma.empty}
	${city ? Prisma.sql` AND ("city" ILIKE ${city} OR "cityaliasname" ILIKE ${city})` : Prisma.empty}
	${county ? Prisma.sql` AND "county" ILIKE ${county}` : Prisma.empty}
	ORDER BY "population" DESC LIMIT 1
	`;
}

/**
 * Gets a city name, filtered by city alias.
 * ableengine_zip_codes_get_city_from_alias()
 * @param state
 * @param cityAlias
 * @returns
 */
export function getCityFromAlias(state: string, cityAlias: string): Promise<{ city: string; population: string }[]> {
	return prisma.$queryRaw`SELECT DISTINCT "city", "population" FROM "gva_data"."zipcodes"
  WHERE UPPER("statefullname") = ${state.toUpperCase()}
  AND UPPER("cityaliasname") = ${cityAlias.toUpperCase()}
  ORDER BY "city", "population" DESC LIMIT 1
  `;
}

/**
 * ableengine_zip_codes_get_zipcode_city_as_county()
 *
 */
export async function getZipcodeCityAsCounty(state: string, city: string) {
	if (state && city) {
		const result1 = await prisma.$queryRaw<{ zipcode: string }[]>`SELECT "zipcode" FROM "gva_data"."zipcodes"
		WHERE "statefullname" ILIKE ${state}
		AND ("city" ILIKE ${city} OR "cityaliasname" ILIKE ${city})
		LIMIT 1
		`;

		if (result1.length == 0) {
			const result2 = await prisma.$queryRaw<{ zipcode: string }[]>`SELECT "zipcode" FROM "gva_data"."zipcodes"
			WHERE "statefullname" ILIKE ${state}
			AND "county" ILIKE ${city}
			ORDER BY "population" DESC LIMIT 1
			`;

			if (result2.length > 0) {
				return result2[0].zipcode;
			}
		}
	}
	return false;
}
