import { type gva_entry_column_templates } from '@prisma/client';
import { randomUUID } from 'crypto';
import { db as prisma } from '~/utils/db.server';
import moment from '~/utils/moment';

export type { gva_entry_column_templates as ColumnTemplate } from '@prisma/client';

export const TEMPLATE_SOURCE_CODE: string = 'code';
export const TEMPLATE_SOURCE_DATABASE: string = 'database';
export const TEMPLATE_SOURCE_HIDDEN: string = 'hidden';
// export const SESSION_KEY_PREFIX: string = 'gva_entry_column_templates_';

export function getColumnTemplateList() {
	return prisma.gva_entry_column_templates.findMany({
		where: { source: { not: TEMPLATE_SOURCE_HIDDEN } },
		orderBy: { name: 'asc' },
		include: {
			author: true
		}
	});
}

export function getColumnTemplate(ctuuid: string) {
	return prisma.gva_entry_column_templates.findUnique({ where: { ctuuid } });
}

export function getColumnTemplateByName(name: string) {
	return prisma.gva_entry_column_templates.findFirst({ where: { name: { equals: name, mode: 'insensitive' } } });
}

export function upsertColumnTemplate(template: gva_entry_column_templates) {
	const uuid = template.ctuuid || randomUUID();

	return prisma.gva_entry_column_templates.upsert({
		where: { ctuuid: uuid },
		create: {
			ctuuid: uuid,
			name: template.name,
			type: template.type,
			overridden: 0,
			source: template.source || TEMPLATE_SOURCE_DATABASE,
			supported_export_types: template.supported_export_types,
			columns: template.columns,
			uid: template.uid,
			created: moment().unix()
		},
		update: {
			name: template.name,
			overridden: template.source == TEMPLATE_SOURCE_CODE ? 1 : 0,
			supported_export_types: template.supported_export_types,
			columns: template.columns,
			changed: moment().unix()
		}
	});
}

export function deleteColumnTemplate(ctuuid: string) {
	return prisma.gva_entry_column_templates.delete({ where: { ctuuid } });
}
