import { type gva_entry_imports_queue } from '@prisma/client';
import { db as prisma } from '~/utils/db.server';

export type { gva_entry_imports_queue as ImportQueue } from '@prisma/client';

/**
 * Get the status of an import job by filename
 * @param filename The filename of the import job
 * @returns The import job status or null if not found
 */
export function getImportStatus(filename: string) {
  return prisma.gva_entry_imports_queue.findUnique({
    where: { filepath: `${process.env.DRUPAL_ROOT || ''}/sites/default/files/imports/${filename}.csv` }
  });
}

/**
 * Get all import jobs
 * @returns All import jobs
 */
export function getAllImportJobs() {
  return prisma.gva_entry_imports_queue.findMany({
    orderBy: { added: 'desc' }
  });
}

/**
 * Create a new import job
 * @param importJob The import job to create
 * @returns The created import job
 */
export function createImportJob(importJob: gva_entry_imports_queue) {
  return prisma.gva_entry_imports_queue.create({
    data: importJob
  });
}

/**
 * Update an import job
 * @param filepath The filepath of the import job to update
 * @param data The data to update
 * @returns The updated import job
 */
export function updateImportJob(filepath: string, data: Partial<gva_entry_imports_queue>) {
  return prisma.gva_entry_imports_queue.update({
    where: { filepath },
    data
  });
}

/**
 * Delete an import job
 * @param filepath The filepath of the import job to delete
 * @returns The deleted import job
 */
export function deleteImportJob(filepath: string) {
  return prisma.gva_entry_imports_queue.delete({
    where: { filepath }
  });
}