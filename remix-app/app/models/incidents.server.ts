import type { incidents, Prisma } from '@prisma/client';
import { randomUUID } from 'crypto';
import _ from 'lodash';
import { db as prisma } from '~/utils/db.server';
import moment from '~/utils/moment';
import { getRealCityFromAlias } from './cityalias.server';
import { getTaxonomyById, getTaxonomyDataByName } from './taxonomy.server';
import { addIncidentSourceQueues, updateLocationQueue } from './queue.server';
import { fetchGeolocation, fetchLocationInfo } from '~/services/location.server';

import type { 
	incident_types, 
	incident_validators, 
	incident_participants, 
	incident_guns, 
	incident_sources, 
	incident_location_categories,
	files
} from '@prisma/client';

export type IncidentType = incident_types;
export type IncidentValidator = incident_validators;
export type IncidentParticipant = incident_participants;
export type IncidentGun = incident_guns;
export type IncidentLocationCategory = incident_location_categories;
export type IncidentSource = incident_sources;
export type SourceType = incident_sources & { key: number; imageFile?: files & { preview?: string } };

export type IncidentParticipantFormType = Omit<incident_participants, 'participant_status_tid'> & {
	participant_status_tid: string[];
	key: number;
};
export type IncidentGunFormType = incident_guns & { type_id_1?: number; type_id_2?: number; key?: number };
export type IncidentFormType = incidents & {
	number_of_victims: number;
	number_of_subjects_suspects: number;
	validators: number[];
	characteristics: number[];
	participants: IncidentParticipantFormType[];
	guns: IncidentGunFormType[];
	sources: SourceType[];
	fetch_geolocation: boolean;
	location_categories: number[];
};

/**
 * Creates an incident in the database.
 * @param incident
 * @returns
 */
export async function createIncident(incident: IncidentFormType) {
	let newIncident = await buildIncidentData(incident);
	newIncident.author_uid = incident.author_uid;
	newIncident.created_date = moment().unix();

	const createIncident = prisma.incidents.create({
		data: newIncident
	});

	const fileIds = incident.sources?.filter(s => s.image_fid).map(s => Number(s.image_fid)) || [];
	const updateFiles = prisma.files.updateMany({
		where: {
			fid: fileIds.length > 0 ? { in: fileIds } : 0
		},
		data: {
			status: true
		}
	});

	const [savedIncident, files] = await prisma.$transaction([createIncident, updateFiles]);

	await updateIncidentLocationData(savedIncident.incident_id, incident.fetch_geolocation);

	// if a source has no image, add it to the source capture table so a screen shot of the source is taken and saved on the node later
	await addIncidentSourceQueues(savedIncident.incident_id);

	return savedIncident;
}

/**
 * Updates the incident in the database.
 * @param id
 * @param incident
 * @returns
 */
export async function updateIncident(id: number, incident: IncidentFormType) {
	const oldIncident = await prisma.incidents.findUniqueOrThrow({
		where: { incident_id: id }
	});
	let newIncident = await buildIncidentData(incident);
	newIncident.last_changed_by_uid = incident.last_changed_by_uid;
	newIncident.changed_date = moment().unix();

	const deleteParticipants = prisma.incident_participants.deleteMany({
		where: { incident_id: id }
	});

	const deleteTypes = prisma.incident_types.deleteMany({
		where: { incident_id: id }
	});

	const deleteValidators = prisma.incident_validators.deleteMany({
		where: { incident_id: id }
	});

	const deleteGuns = prisma.incident_guns.deleteMany({
		where: { incident_id: id }
	});

	const deleteSources = prisma.incident_sources.deleteMany({
		where: { incident_id: id }
	});

	const deleteLocationCategories = prisma.incident_location_categories.deleteMany({
		where: { incident_id: id }
	});

	const updateIncident = prisma.incidents.update({
		where: { incident_id: id },
		data: newIncident
	});

	await prisma.$transaction([
		deleteParticipants,
		deleteTypes,
		deleteValidators,
		deleteGuns,
		deleteSources,
		deleteLocationCategories,
		updateIncident
	]);

	const fileIds = incident.sources?.filter(s => s.image_fid).map(s => Number(s.image_fid)) || [];
	await prisma.files.updateMany({
		where: {
			fid: fileIds.length > 0 ? { in: fileIds } : 0
		},
		data: {
			status: true
		}
	});

	await updateIncidentLocationData(id, incident.fetch_geolocation, oldIncident);

	// if a source has no image, add it to the source capture table so a screen shot of the source is taken and saved on the node later
	await addIncidentSourceQueues(id);
}

async function buildIncidentData(incident: IncidentFormType) {
	/** generate title ['IncidentDate', 'IncidentLocation', 'NumberOfParticipants']
	const titleSegments = [];
	titleSegments.push(moment(incident.incident_date || undefined).format('M-D-YY'));
	if (state) {
		titleSegments.push(state.name);
	}
	if (incident.city_county) {
		titleSegments.push(incident.city_county);
	}
	if (incident.county) {
		titleSegments.push(incident.county);
	}
	titleSegments.push(`${incident.number_of_victims}-${incident.number_of_subjects_suspects}`);
	*/
	const incidentState = incident.state_taxonomy_id
		? await getTaxonomyById(Number(incident.state_taxonomy_id))
		: undefined;

	let newIncident: Prisma.incidentsCreateInput = {
		..._.pick(incident, [
			'city_county',
			'county',
			'business',
			'neighborhood',
			'address',
			'notes',
			'private_notes',
			'incident_alias',
			'related',
			'geocode_notes',
			'congressional_district',
			'state_senate_district',
			'state_house_district'
		]),
		uuid: randomUUID()
	};
	// newIncident.title = titleSegments.join(' ');
	newIncident.incident_date = moment(incident.incident_date).unix();
	newIncident.incident_time = null;
	if (incident.incident_time) {
		newIncident.incident_time = moment(`${incident.incident_date} ${incident.incident_time}`, 'YYYY-MM-DD h:mm A').unix();
	}
	newIncident.state = incidentState
		? { connect: { tid: incidentState.tid, value: incidentState.value } }
		: undefined;
	newIncident.guns_involved_counter = Number(incident.guns_involved_counter);
	newIncident.approximate_time = _.isUndefined(incident.approximate_time) ? null : Number(incident.approximate_time);
	newIncident.drop_off = _.isUndefined(incident.drop_off) ? null : Number(incident.drop_off);
	newIncident.pending = _.isUndefined(incident.pending) ? null : Number(incident.pending);
	newIncident.twitter_source = _.isUndefined(incident.twitter_source) ? null : Number(incident.twitter_source);
	newIncident.districts_override = _.isUndefined(incident.districts_override)
		? null
		: Number(incident.districts_override);
	newIncident.gelocation_override = _.isUndefined(incident.gelocation_override)
		? null
		: Number(incident.gelocation_override);
	newIncident.latitude = Number(incident.latitude) || null;
	newIncident.longitude = Number(incident.longitude) || null;
	newIncident.google_auto_complete = !!incident.google_auto_complete;
	newIncident.matched_city = null;
	if (incident.city_county && incidentState) {
		newIncident.matched_city = await getRealCityFromAlias(incidentState.value, incident.city_county);
	}

	if (incident.participants?.length > 0) {
		const newParticipants = incident.participants.map((p, index) => {
			return {
				..._.pick(p, ['participant_type', 'name', 'name_alias', 'gender']),
				age: p.age?.toString().trim() !== '' ? Number(p.age) : null,
				age_group_tid: p.age_group_tid ? Number(p.age_group_tid) : null,
				participant_status_tid: p.participant_status_tid?.join(','),
				participants_characteristic_tid: p.participants_characteristic_tid
					? Number(p.participants_characteristic_tid)
					: null,
				relationship_tid: p.relationship_tid ? Number(p.relationship_tid) : null,
				weight: index,
				changed_date: moment().unix()
			};
		});
		newIncident.incident_participants = {
			create: newParticipants
		};
	}
	if (incident.characteristics?.length > 0) {
		const newTypes = incident.characteristics.map((t, index) => {
			return { type_tid: Number(t), weight: index, changed_date: moment().unix() };
		});
		newIncident.incident_types = {
			create: newTypes
		};
	}
	if (incident.validators?.length > 0) {
		const newValidators = incident.validators.map((v, index) => {
			return { validator_tid: Number(v), weight: index, changed_date: moment().unix() };
		});
		newIncident.incident_validators = {
			create: newValidators
		};
	}
	if (incident.guns?.length > 0) {
		const newGuns = incident.guns.map((g, index) => {
			const type_id = _.last([g.type_id_1, g.type_id_2].filter((type?: number) => type)); // Get the last non-null value
			return {
				gun_type_tid: Number(type_id) || null,
				stolen_value: g.stolen_value,
				weight: index,
				changed_date: moment().unix()
			};
		});
		newIncident.incident_guns = {
			create: newGuns
		};
	}
	if (incident.sources?.length > 0) {
		const newSources = incident.sources.map((s, index) => {
			return {
				source_name: s.source_name,
				source_url: s.source_url,
				image_fid: Number(s.image_fid) || null,
				image_uri: s.image_uri,
				image_width: Number(s.image_width) || null,
				image_height: Number(s.image_height) || null,
				weight: index,
				changed_date: moment().unix()
			};
		});
		newIncident.incident_sources = {
			create: newSources
		};
	}
	if (incident.location_categories?.length > 0) {
		const newCategories = incident.location_categories.map((t, index) => {
			return { category_tid: Number(t), weight: index, changed_date: moment().unix() };
		});
		newIncident.incident_location_categories = {
			create: newCategories
		};
	}

	return newIncident;
}

export async function updateIncidentLocationData(incident_id: number, fetchGeo = true, oldIncident?: incidents) {
	const incident = await prisma.incidents.findUnique({
		where: { incident_id },
		include: {
			state: true
		}
	});
	if (!incident) return false;

	let locationData = {
		..._.pick(incident, [
			'latitude',
			'longitude',
			'address',
			'state_taxonomy_id',
			'city_county',
			'matched_city',
			'county',
			'zipcode',
			'neighborhood',
			'congressional_district',
			'state_senate_district',
			'state_house_district',
			'gelocation_override',
			'incident_timezone_offset'
		])
	};
	if (fetchGeo) {
		const apiResults = [];
		const errorTypes = [];
		let zipFromGoogle = null;
		// fetch geo from Google
 	if (
			!locationData.latitude ||
			!locationData.longitude ||
			!incident.gelocation_override
		) {
			const geoResult = await fetchGeolocation(
				incident.address || '',
				incident.matched_city || incident.city_county || '',
				incident.county || '',
				incident.state?.value || ''
			);
			apiResults.push(geoResult);
			if (geoResult.location) {
				zipFromGoogle = geoResult.location.zipcode;
				// api successed
				locationData.gelocation_override = 0;
				locationData.latitude = geoResult.location.latitude;
				locationData.longitude = geoResult.location.longitude;
				locationData.zipcode = geoResult.location.zipcode;
				if (!incident.google_auto_complete) { // If google_auto_complete = false, fill empty values ​​using google response
					locationData.address = locationData.address || geoResult.location.address1;
					locationData.city_county = locationData.city_county || geoResult.location.city;
					locationData.matched_city = locationData.matched_city || geoResult.location.city;
					locationData.county = locationData.county || geoResult.location.county;
					locationData.neighborhood = locationData.neighborhood || geoResult.location.neighborhood;

					if (geoResult.location.state && !locationData.state_taxonomy_id) {
						locationData.state_taxonomy_id =
							(await getTaxonomyDataByName('states', geoResult.location.state))?.tid || null;
					}
					if (!geoResult.location.county) errorTypes.push('County Name');
					if (!geoResult.location.neighborhood) errorTypes.push('Neighborhood');
				}
				if (!geoResult.location.zipcode) errorTypes.push('Zipcode');
			} else {
				errorTypes.push('Google failed');
				// api failed: fill old data
				if (oldIncident && incident.state_taxonomy_id == oldIncident.state_taxonomy_id && incident.city_county == oldIncident.city_county && incident.county == oldIncident.county && incident.address == oldIncident.address) {
					locationData.latitude = oldIncident.latitude;
					locationData.longitude = oldIncident.longitude;
					locationData.zipcode = oldIncident.zipcode;
					zipFromGoogle = oldIncident.zipcode;
				} else {
					locationData.latitude = null;
					locationData.longitude = null;
					locationData.zipcode = null;
				}
			}
		}

		// fetch info from Geocodio
		if (locationData.latitude && locationData.longitude) {
			const locationInfo = await fetchLocationInfo(locationData.latitude, locationData.longitude);
			apiResults.push(locationInfo);
			if (locationInfo.location) {
				// api successed
				locationData.incident_timezone_offset = locationInfo.location.timezone_offset;
				if (!incident.districts_override) {
					locationData.congressional_district = locationInfo.location.congressional_district;
					locationData.state_senate_district = locationInfo.location.state_senate_district;
					locationData.state_house_district = locationInfo.location.state_house_district;

					if (!locationInfo.location.congressional_district) errorTypes.push('Congresstional District');
				}
				if (locationInfo.location.zip9) {
					locationData.zipcode = locationInfo.location.zip9;
				}
				const incidentLocationFields = {
					..._.pick(locationInfo.location, [
						'metro_micro_statistical_area',
						'metro_micro_statistical_area_type',
						'combined_statistical_area',
						'metropolitan_division',
						'county_fips',
						'state_fips',
						'tract_code',
						'block_code',
						'block_group',
						'full_fips',
						'county_subdivision_name',
						'county_subdivision_fips',
						'county_subdivision_class_code',
						'place_fips'
					]),
					school_districts: JSON.stringify(locationInfo.location.school_districts),
					changed_date: moment().unix(),
				};
				await prisma.incident_locations.upsert({
					where: { incident_id },
					create: { ...incidentLocationFields, incident_id },
					update: { ...incidentLocationFields }
				});

				if (!locationInfo.location.metro_micro_statistical_area)
					errorTypes.push('Metro and Micro Statistical Areas');
				if (!locationInfo.location.combined_statistical_area) errorTypes.push('Combined Statistical Area');
				if (!locationInfo.location.tract_code)
					errorTypes.push('state-county-tract number');
			} else {
				errorTypes.push('Geocodio failed');
				// api failed: fill old data
				const geoUnchanged = oldIncident && locationData.latitude == oldIncident.latitude && locationData.longitude == oldIncident.longitude;
				locationData.incident_timezone_offset = geoUnchanged ? oldIncident.incident_timezone_offset : null;
				locationData.zipcode = geoUnchanged ? oldIncident.zipcode : zipFromGoogle;
				if (!incident.districts_override) {
					locationData.congressional_district = geoUnchanged ? oldIncident.congressional_district : null;
					locationData.state_senate_district = geoUnchanged ? oldIncident.state_senate_district : null;
					locationData.state_house_district = geoUnchanged ? oldIncident.state_house_district : null;
				}
				if (!geoUnchanged) {
					// delete incident locations
					await prisma.incident_locations.deleteMany({ where: { incident_id } });
				}
			}
		} else {
			// geo doesn't exist: cleanup data
			locationData.incident_timezone_offset = null;
			locationData.zipcode = zipFromGoogle;
			if (!incident.districts_override) {
				locationData.congressional_district = null;
				locationData.state_senate_district = null;
				locationData.state_house_district = null;
			}
			// delete incident locations
			await prisma.incident_locations.deleteMany({ where: { incident_id } });
		}

		// save incident
		await prisma.incidents.update({
			where: { incident_id },
			data: locationData
		});

		// log to queue
		await updateLocationQueue(
			'disable',
			incident_id,
			apiResults.filter(s => s.error).map(s => s.error).join('\n'),
			apiResults.map(s => s.raw_data),
			errorTypes.join(', ')
		);
	} else {
		// add to queue
		await updateLocationQueue('update', incident_id);
	}

	// update incident geo field
	if (locationData.longitude && locationData.latitude) {
		await prisma.$executeRaw`UPDATE "gva_data"."incidents" SET "geo" = ST_MakePoint(${locationData.longitude}, ${locationData.latitude})::geography WHERE "incident_id" = ${incident_id}`;
	} else {
		await prisma.$executeRaw`UPDATE "gva_data"."incidents" SET "geo" = null WHERE "incident_id" = ${incident_id}`;
	}
	return true;
}

/**
 * Deletes an incident from the database.
 * @param id
 * @returns
 */
export async function deleteIncident(id: number) {
	return prisma.incidents.delete({
		where: { incident_id: id }
	});
}

/**
 * Retrieves an incident
 * @param id
 */
export function getIncident(id: number) {
	return prisma.incidents.findFirst({
		where: { incident_id: id },
		include: {
			incident_participants: {
				orderBy: {
					weight: 'asc'
				},
				include: {
					age_group: true
				}
			},
			incident_types: {
				orderBy: {
					weight: 'asc'
				},
				include: {
					type: true
				}
			},
			incident_validators: {
				orderBy: {
					weight: 'asc'
				},
				include: {
					validator: true
				}
			},
			incident_guns: {
				orderBy: {
					weight: 'asc'
				},
				include: {
					gun_type: true
				}
			},
			incident_sources: {
				orderBy: {
					weight: 'asc'
				},
				include: {
					imageFile: true
				}
			},
			incident_location_categories: {
				orderBy: {
					weight: 'asc'
				},
				include: {
					category: true
				}
			},
			state: true
		}
	});
}

/**
 * Search incidents
 * @param filter
 * @param page
 * @param pageSize
 * @returns
 */
export function searchIncidentPaginatedList(filter?: { author?: number; status?: number }, page = 1, pageSize = 10) {
	return prisma.incidents.findMany({
		skip: (page - 1) * pageSize,
		take: pageSize,
		where: {
			AND: [filter?.author ? { author_uid: filter?.author } : {}, { status: filter?.status ?? 1 }]
		},
		orderBy: [
			{
				incident_date: 'desc'
			},
			{
				incident_id: 'desc'
			}
		],
		include: {
			state: true,
			incident_participants: true
		}
	});
}

/**
 * Count the number of incidents
 * @param filter
 * @returns
 */
export function searchIncidentPaginatedListCount(filter?: { author?: number; status?: number }) {
	return prisma.incidents.count({
		where: {
			AND: [filter?.author ? { author_uid: filter?.author } : {}, { status: filter?.status ?? 1 }]
		}
	});
}

/**
 * Retrieves an incident source
 * @param id
 */
export function getIncidentSource(id: number) {
	return prisma.incident_sources.findUnique({
		where: { source_entity_id: id }
	});
}

/**
 * Update an incident source
 * @param id
 * @param changes
 */
export function updateIncidentSource(id: number, changes: {}) {
	return prisma.incident_sources.update({
		where: { source_entity_id: id },
		data: { ...changes }
	});
}

/**
 * Retrieves an incident participant
 * @param id
 */
export function getIncidentParticipant(id: number) {
	return prisma.incident_participants.findUnique({
		where: { participant_id: id },
		include: { age_group: true }
	});
}
