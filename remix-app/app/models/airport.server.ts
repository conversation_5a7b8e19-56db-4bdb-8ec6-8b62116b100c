import { db as prisma } from '~/utils/db.server';

/**
 * Get airport codes by state and query
 * 
 * Note: Since the airportCodes model is marked with @@ignore in the Prisma schema,
 * we need to use a raw query to access the data.
 */
export async function getAirportsByState(state: string, query: string) {
  // Use a raw query to get airport codes since the model is marked with @@ignore
  const airports = await prisma.$queryRaw`
    SELECT locid, facilityname, city, county
    FROM "gva_data"."airportCodes"
    WHERE st = ${state}
    AND (
      locid ILIKE ${`%${query}%`} OR
      facilityname ILIKE ${`%${query}%`} OR
      city ILIKE ${`%${query}%`}
    )
    ORDER BY facilityname
    LIMIT 20
  `;

  return airports as Array<{
    locid: string;
    facilityname: string;
    city: string;
    county: string;
  }>;
}