import { type FilterInputProps } from '~/services/search/types';

export function StatusInput({ value, onChange, operator, filter }: FilterInputProps) {
	if (!filter) return null;

	const operators = filter.getOperators();
	const currentOperator = operators.find(op => op.value === operator);

	if (!currentOperator || !currentOperator.options) return null;

	return (
		<div className="flex flex-col gap-2">
			{currentOperator.options.map(option => (
				<label key={option.value} className="flex items-center gap-2">
					<input
						type="radio"
						value={option.value}
						checked={value === option.value}
						onChange={() => onChange(option.value)}
					/>
					<div className="flex items-center gap-2">
						<span className={`size-4 rounded-full ${getStatusColor(option.value)}`} />
						<span>{option.label}</span>
					</div>
				</label>
			))}
		</div>
	);
}

function getStatusColor(status: string): string {
	switch (status) {
		case 'active':
			return 'bg-green-500';
		case 'pending':
			return 'bg-yellow-500';
		case 'closed':
			return 'bg-blue-500';
		case 'archived':
			return 'bg-gray-600';
		default:
			return 'bg-gray-500';
	}
}
