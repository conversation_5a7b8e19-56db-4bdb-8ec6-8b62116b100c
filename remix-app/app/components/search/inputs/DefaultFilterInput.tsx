import type { FilterInputProps } from '~/services/search/types';

export function DefaultFilterInput({ value, onChange, operator, filter, className = '' }: FilterInputProps) {
	if (!filter) return null;

	const operators = filter.getOperators();
	const currentOperator = operators.find(op => op.value === operator);

	if (!currentOperator) return null;

	const isMultiSelect = currentOperator.value === 'in';
	switch (currentOperator.inputType) {
		case 'select':
			return (
				<select
					value={value}
					multiple={isMultiSelect}
					size={isMultiSelect ? currentOperator.options?.length : undefined}
					onChange={e => onChange(isMultiSelect ? Array.from(e.target.selectedOptions, option => option.value) : e.target.value)}
					className={`border ${className} ${isMultiSelect ? 'h-auto py-2' : ''}`}
				>
					{!isMultiSelect && <option value="">Select...</option>}
					{currentOperator.options?.map(option => (
						<option key={option.value} value={option.value}>
							{option.label}
						</option>
					))}
				</select>
			);
		case 'none':
			return null;
		case 'text':
		default:
			return (
				<input
					type="text"
					value={value}
					onChange={e => onChange(e.target.value)}
					className={`border ${className}`}
					placeholder="Enter value..."
				/>
			);
	}
}
