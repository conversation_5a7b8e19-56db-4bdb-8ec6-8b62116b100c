import { format } from 'date-fns';
import { type FilterInputProps } from '~/services/search/types';

export function DateRangeInput({ value, onChange }: FilterInputProps) {
	return (
		<div className="flex items-center gap-2">
			<input
				type="date"
				className="border"
				placeholder="Start Date"
				value={value.start ? format(value.start, 'yyyy-MM-dd') : ''}
				onChange={e => {
					const [year, month, day] = e.target.value.split('-').map(Number);
					const date = new Date(year, month - 1, day);
					if (!isNaN(date.getTime())) {
						onChange({ ...value, start: date });
					} else {
						onChange({ ...value, start: undefined }); // Clear start date if invalid
					}
				}}
				max={value.end ? format(value.end, 'yyyy-MM-dd') : undefined} // Prevent selecting a start date after the end date
			/>
			<span>to</span>
			<input
				type="date"
				className="border"
				placeholder="End Date"
				value={value.end ? format(value.end, 'yyyy-MM-dd') : ''}
				onChange={e => {
					const [year, month, day] = e.target.value.split('-').map(Number);
					const date = new Date(year, month - 1, day);
					if (!isNaN(date.getTime())) {
						onChange({ ...value, end: date });
					} else {
						onChange({ ...value, end: undefined }); // Clear end date if invalid
					}
				}}
				min={value.start ? format(value.start, 'yyyy-MM-dd') : undefined} // Prevent selecting an end date before the start date
			/>
		</div>
	);
}
