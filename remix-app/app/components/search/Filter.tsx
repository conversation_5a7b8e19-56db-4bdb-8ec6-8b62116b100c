import iconDelete from '~/images/Icon-Admin-Delete.svg';
import iconAdd from '~/images/Icon-Admin-Add.svg';
export default ({ title, className = '', add = false, children }: { title: string, className?: string, add?: boolean, children: any }) => {
    return (
        <div className={`filter ${className}`}>
            <div className="flex justify-between title">
                <div>{title}</div>
                <img src={iconDelete} alt="Delete" width={16} />
            </div>
            {children}
            {add && (
                <div className="addfilter">
                    <button type="button" className="flex items-center">
                        <img className="mr-2.5" src={iconAdd} alt="" width={16} />
                        Add Filter
                    </button>
                </div>
            )}
        </div>
    );
}