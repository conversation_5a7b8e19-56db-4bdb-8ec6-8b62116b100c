import { useState } from 'react';

type LabelFormProps = {
	data: {};
	onSubmit: (data: {}) => void;
};

export default ({ data, onSubmit }: LabelFormProps) => {
	const [formData, setFormData] = useState({ name: data.name || '', locked: data.locked || false, editlock: data.editlock || false, limitResults: data.limitResults || false, path: data.path || '' });

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		onSubmit(formData);
	};

	return (
		<form onSubmit={handleSubmit}>
			<div className="rounded-[10px] bg-gray-200 p-10 space-y-5">
				<div>
					<label htmlFor="Label" className="text-lg font-bold">
						Label
					</label>
					<div className="mt-3">
						<input
							id="Label"
							type="text"
							name="name"
							value={formData.name}
							className="w-full"
							onChange={(e) => setFormData({ ...formData, name: e.target.value })}
						/>
					</div>
					<div className="mt-1 text-sm text-gray-600">This will be displayed on the results page for this query.</div>
				</div>
				<div>
					<div className="flex items-start">
						<input
							id="Locked"
							name="locked"
							type="checkbox"
							value={1}
							checked={formData.locked}
							className="mr-4 mt-1 size-[20px] accent-orange-500"
							onChange={(e) => setFormData({ ...formData, locked: e.target.checked ? 1 : 0 })}
						/>
						<label htmlFor="Locked" className="text-lg font-bold">
							Locked
							<div className="text-sm text-gray-600 font-normal">If a query is locked, it will never be deleted.</div>
						</label>
					</div>
				</div>
				<div>
					<div className="flex items-start">
						<input
							id="Uneditable"
							name="editlock"
							type="checkbox"
							value={1}
							checked={formData.editlock}
							onChange={(e) => setFormData({ ...formData, editlock: e.target.checked ? 1 : 0 })}
							className="mr-4 mt-1 size-[20px] accent-orange-500"
						/>
						<label htmlFor="Uneditable" className="text-lg font-bold">
							Uneditable
							<div className="text-sm text-gray-600 font-normal">If a query is edit locked, its search parameters wont be able to be edited.</div>
						</label>
					</div>
				</div>
				<div>
					<div className="flex items-start">
						<input
							id="LimitResults"
							name="limitResults"
							type="checkbox"
							value={1}
							checked={formData.limitResults}
							onChange={(e) => setFormData({ ...formData, limitResults: e.target.checked ? 1 : 0 })}
							className="mr-4 mt-1 size-[20px] accent-orange-500"
						/>
						<label htmlFor="LimitResults" className="text-lg font-bold">
							Limit Results
							<div className="text-sm text-gray-600 font-normal">Whether or not to limit the results of this query for anonymous users.</div>
						</label>
					</div>
				</div>
				<div>
					<label htmlFor="Path" className="text-lg font-bold">
						Path
					</label>
					<div className="mt-3">
						<input
							id="Path"
							name="path"
							type="text"
							value={formData.path}
							className="w-full"
							onChange={(e) => setFormData({ ...formData, path: e.target.value })}
						/>
					</div>
					<div className="mt-1 text-gray-600">The path (without the leading /) to the query.</div>
				</div>
			</div>
			<div className="mt-10">
				<button type="submit" className="rounded-full bg-orange-500 px-5 py-2 text-lg text-white">
					Save
				</button>
			</div>
		</form>
	);
};
