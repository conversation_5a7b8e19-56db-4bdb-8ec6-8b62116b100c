import DOMPurify from 'isomorphic-dompurify';
import parse from 'html-react-parser';

export interface Body {
	contentid: string;
	content: string;
}

export const tinyConfig = {
	inline: true,
	image_title: true,
	menubar: false,
	plugins: [
		'preview',
		'autolink',
		'code',
		'visualblocks',
		'visualchars',
		'image',
		'link',
		'media',
		'charmap',
		'pagebreak',
		'nonbreaking',
		'anchor',
		'lists',
		'wordcount',
		'help'
	],
	toolbar:
		'undo redo | aidialog aishortcuts | blocks fontsizeinput | bold italic lineheight align numlist bullist | link image | strikethrough forecolor backcolor | code | charmap preview',
	images_upload_handler
};

export function htmlFrom(htmlString: string) {
	const cleanHtmlString = DOMPurify.sanitize(htmlString, { USE_PROFILES: { html: true } });

	return parse(cleanHtmlString);
}

interface BlobInfo {
	blob: () => Blob;
	filename: () => string;
}

type ProgressFn = (percent: number) => void;

export async function images_upload_handler(blobInfo: BlobInfo, progress: ProgressFn) {
	const formData = new FormData();
	formData.append('file', blobInfo.blob(), blobInfo.filename());

	const uploadedFile = await fetch('/admin/uploadfile', {
		method: 'POST',
		body: formData
	});

	const file = await uploadedFile.json();

	return file.uri;
}
