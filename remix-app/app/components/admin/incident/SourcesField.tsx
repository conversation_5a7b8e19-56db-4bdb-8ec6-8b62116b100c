import { useEffect, useState } from 'react';

import FileUploader, { type FileData } from './FileUploader';

import iconDelete from '~/images/Icon-Admin-Delete.svg';
import iconClear from '~/images/Icon-Admin-Clear.svg';
import iconAdd from '~/images/Icon-Admin-Add.svg';

interface SourceType {
	key: number;
	source_name: string;
	source_url: string;
	image_fid?: number;
	image_uri?: string;
	image_width?: number;
	image_height?: number;
	imageFile?: { filename?: string; uri?: string; filemime?: string; preview?: string };
}

export default function IncidentSourcesField({ sources }: { sources: SourceType[] }) {
	const [sourceList, setSourceList] = useState(sources);
	const [numberOfSources, setNumberOfSources] = useState(sources.length.toString());
	const [validNumberOfSources, setValidNumberOfSources] = useState(sources.length);

	const [currentKey, setCurrentKey] = useState(Math.max(...sources.map(s => s.key)));
	const [message, setMessage] = useState('');

	useEffect(() => {
		if (sourceList.length < validNumberOfSources && validNumberOfSources <= 50) {
			const newSourceList = [...sourceList];
			let newSourceKey = currentKey;
			for (let i = 0; i < validNumberOfSources - sourceList.length; i++) {
				newSourceList.push({
					key: ++newSourceKey,
					source_name: '',
					source_url: '',
					image_fid: undefined,
					image_uri: undefined,
					image_width: undefined,
					image_height: undefined,
					imageFile: undefined
				});
			}
			setSourceList(newSourceList);
			setCurrentKey(newSourceList.length > 0 ? Math.max(...newSourceList.map(s => s.key)) : 0);
		}
	}, [validNumberOfSources]);

	const onBlurNumberOfSources = () => {
		const wantNumber = Number(numberOfSources);
		if (wantNumber >= 0 && wantNumber < sourceList.length) {
			setMessage(
				`You have indicated that you want ${wantNumber} Sources, but you currently have data for ${sourceList.length} Sources. Please click on the <strong>Clear</strong> or <strong>Delete</strong> buttons for the sources you don't want to include before changing this field again.`
			);
		} else {
			setMessage('');
		}
		const validNumber = wantNumber < sourceList.length ? sourceList.length : wantNumber;
		setValidNumberOfSources(validNumber);
		setNumberOfSources(validNumber.toString());
	};

	const onChangeSource = (key: number, values: {}) => {
		const source = sourceList.find(s => s.key === key);
		if (source) {
			const newSource = { ...source, ...values };
			updateSourceList(newSource);
		}
	};

	const addSource = () => {
		setValidNumberOfSources(sourceList.length + 1);
	};

	const delSource = (key: number) => {
		const newSourceList = sourceList.filter(s => s.key !== key);
		setSourceList(newSourceList);
		setValidNumberOfSources(newSourceList.length);
		setNumberOfSources(newSourceList.length.toString());
	};

	const clearSource = (key: number) => {
		const source = sourceList.find(s => s.key === key);
		if (source) {
			const newSource = {
				...source,
				source_name: '',
				source_url: '',
				image_fid: undefined,
				image_uri: undefined,
				image_width: undefined,
				image_height: undefined,
				imageFile: undefined
			};
			updateSourceList(newSource);
		}
	};

	const updateSourceList = (newSource: SourceType) => {
		const sourceIndex = sourceList.findIndex(s => s.key === newSource.key);
		if (sourceIndex !== -1) {
			const newSourceList = [...sourceList];
			newSourceList.splice(sourceIndex, 1, newSource);
			setSourceList(newSourceList);
		}
	};

	const onFileUpload = (key: number, file: FileData) => {
		const source = sourceList.find(s => s.key === key);
		if (source) {
			const newSource = {
				...source,
				image_fid: file.fid,
				image_uri: file.uri,
				image_width: file.width,
				image_height: file.height,
				imageFile: file.fid
					? { filename: file.filename, filemime: file.filemime, preview: file.preview }
					: undefined
			};
			updateSourceList(newSource);
		}
	};

	const delUploadedFile = (key: number) => {
		onFileUpload(key, { fid: undefined, width: undefined, height: undefined });
	};

	return (
		<>
			{/* <div className="grid grid-cols-4 gap-10">
				<div>
					<label htmlFor="NumberOfSources" className="text-lg font-bold">
						Number of Sources
					</label>
					<div className="mt-3">
						<input
							id="NumberOfSources"
							name="number_of_sources"
							type="number"
							min={0}
							step={1}
							value={numberOfSources}
							onChange={e => setNumberOfSources(e.target.value)}
							onBlur={onBlurNumberOfSources}
							className="block w-full"
						/>
					</div>
				</div>
			</div> */}

			{message.length > 0 && <div className="text-red-500" dangerouslySetInnerHTML={{ __html: message }}></div>}

			{validNumberOfSources > 0 &&
				validNumberOfSources <= 50 && (
					<>
						{sourceList.map((source, index) => (
							<div key={`source-${source.key}`} className="mt-8">
								<div className="flex items-center space-x-10">
									<div className="w-2/5">
										<label htmlFor={`source-name-${source.key}`} className="text-lg font-bold">
											Title
										</label>
										<div className="mt-3">
											<input
												id={`source-name-${source.key}`}
												name={`sources[${source.key}][source_name]`}
												type="text"
												value={source.source_name || ''}
												onChange={e => onChangeSource(source.key, { source_name: e.target.value })}
												className="block w-full"
											/>
										</div>
									</div>
								</div>
								<div className="mt-3 flex items-center space-x-10">
									<div className="w-2/5">
										<label htmlFor={`source-url-${source.key}`} className="text-lg font-bold">
											URL
										</label>
										<div className="mt-3">
											<input
												id={`source-url-${source.key}`}
												name={`sources[${source.key}][source_url]`}
												type="text"
												value={source.source_url || ''}
												onChange={e => onChangeSource(source.key, { source_url: e.target.value })}
												className="block w-full"
											/>
										</div>
									</div>
									<div>
										<label htmlFor={`source-screenshot-${source.key}`} className="text-lg font-bold">
											Screenshot
										</label>
										<div className="mt-3 flex">
											{/* <input id={`source-screenshot-${source.key}`} name={`sources[${source.key}][screenshot]`} type="file"
                className="block w-80" /> */}
											<div className="h-[50px] w-36 pt-1">
												<FileUploader
													inputIndex={source.key}
													inputId={`source-screenshot-${source.key}`}
													onUpload={onFileUpload}
												/>
												<input
													id={`source-screenshot-fid-${source.key}`}
													name={`sources[${source.key}][image_fid]`}
													type="hidden"
													value={source.image_fid || ''}
												/>
												<input
													id={`source-screenshot-uri-${source.key}`}
													name={`sources[${source.key}][image_uri]`}
													type="hidden"
													value={source.image_uri || ''}
												/>
												<input
													id={`source-screenshot-width-${source.key}`}
													name={`sources[${source.key}][image_width]`}
													type="hidden"
													value={source.image_width || ''}
												/>
												<input
													id={`source-screenshot-height-${source.key}`}
													name={`sources[${source.key}][image_height]`}
													type="hidden"
													value={source.image_height || ''}
												/>
											</div>
											{source.imageFile && (
												<div className="flex items-center">
													<a
														className="text-lg text-blue-500 underline"
														href={source.imageFile.preview}
														target="_blank"
														rel="noreferrer"
													>
														{source.imageFile.filemime?.includes('image') && (
															<img src={source.imageFile.preview} alt="" className="h-10" />
														)}
														{source.imageFile.filename}
													</a>
													<button
														type="button"
														className="ml-4 flex items-center text-sm font-bold text-blue-500"
														onClick={() => delUploadedFile(source.key)}
													>
														<img className="mr-2.5" src={iconDelete} alt="" width={16} />
														Remove
													</button>
												</div>
											)}
										</div>
									</div>
									<div>
										<label>&nbsp;</label>
										<div className="mt-3 flex space-x-10">
											<button
												type="button"
												className="flex items-center text-sm font-bold text-blue-500"
												onClick={() => clearSource(source.key)}
											>
												<img className="mr-2.5" src={iconClear} alt="" width={16} />
												Clear
											</button>
											{sourceList.length > 1 && (
												<button
													type="button"
													className="flex items-center text-sm font-bold text-blue-500"
													onClick={() => delSource(source.key)}
												>
													<img className="mr-2.5" src={iconDelete} alt="" width={16} />
													Delete
												</button>
											)}
										</div>
									</div>
								</div>
							</div>
						))}

						{sourceList.length < 50 && (
							<div className="mt-10">
								<button
									type="button"
									className="flex items-center text-sm font-bold text-blue-500"
									onClick={addSource}
								>
									<img className="mr-2.5" src={iconAdd} alt="" width={16} />
									Add Another Source
								</button>
							</div>
						)}
					</>
				)}

			{validNumberOfSources > 50 && (
				<div>Source details have been removed since there are more than 50 sources involved.</div>
			)}
		</>
	);
}
