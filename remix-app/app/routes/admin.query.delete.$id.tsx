import { type ActionFunctionArgs, json, redirect } from '@remix-run/node';
import { requireUser } from '~/utils/session.server';
import { SearchBuilder } from '~/services/search/SearchBuilder';

/**
 * Action function for the query delete endpoint
 * This is a conversion of the action_delete function from query.php
 */
export async function action({ params, request }: ActionFunctionArgs) {
  const user = await requireUser(request);
  
  const id = params.id;
  
  if (!id) {
    return json({ error: 'Invalid query ID' }, { status: 400 });
  }
  
  try {
    // Delete the saved search
    await SearchBuilder.delete(id);
    
    // Return success message and redirect to home page
    return json(
      { 
        success: true, 
        message: 'Query has been deleted.' 
      }, 
      { 
        headers: { 
          'Set-Cookie': 'flash=' + encodeURIComponent(JSON.stringify({
            type: 'success',
            message: 'Query has been deleted.'
          }))
        } 
      }
    );
  } catch (error) {
    console.error('Error deleting query:', error);
    return json({ error: 'Failed to delete query' }, { status: 500 });
  }
}

/**
 * Loader function to handle GET requests
 * Redirects to home page since this is an action-only route
 */
export async function loader() {
  return redirect('/');
}

/**
 * Default export is required for Remix routes
 */
export default function QueryDelete() {
  // This is an API route, so we don't need to render anything
  return null;
}