import type { ActionFunctionArgs, LoaderFunctionArgs } from '@remix-run/node';
import { redirect } from '@remix-run/node';
import { Form, useLoaderData, isRouteErrorResponse, useRouteError, Link, useActionData } from '@remix-run/react';
import invariant from 'tiny-invariant';
import { getConfiguration, getConfigurationByKey, upsertConfiguration, type Configuration } from '~/models/config.server';
import { parseId } from '~/utils/client-utils';
import { formSubmitMessage } from '~/utils/cookies.server';
import { authenticator } from '~/utils/auth.server';

import iconError from '~/images/Icon-Admin-Delete.svg';

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
	invariant(params.id, 'ID must be included in the route params.');

	if (params.id === 'new') {
		return { config: {} as Configuration };
	}

	const id = parseId(params.id);
	if (id === null) {
		throw new Response('Invalid configuration ID', { status: 400 });
	}

	const config = await getConfiguration(id);
	if (!config) {
		throw new Response('Configuration not found', { status: 404 });
	}

	return { config };
};

export const action = async ({ params, request }: ActionFunctionArgs): Promise<Response | { error: string }> => {
	const user = await authenticator.isAuthenticated(request, { failureRedirect: '/user/login' });

	const cookieHeader = request.headers.get('Cookie');
	const cookie = (await formSubmitMessage.parse(cookieHeader)) || {};

	// Special handling for creating a new source
	const isCreatingNew = params.id === 'new';
	let id = 0;
	const formData = await request.formData();

	if (!isCreatingNew) {
		id = parseInt(params.id!);
		if (isNaN(id)) {
			throw new Response('Invalid configuration ID', { status: 400 });
		}
	} else {
		const config = await getConfigurationByKey(String(formData.get('key')));
		if (config) {
			return { error: 'The key already exists.' };
		}
	}

	try {
		await upsertConfiguration({
			id,
			key: String(formData.get('key')),
			values: String(formData.get('values'))
		} as Configuration);
	} catch (error: any) {
		console.error(error);
		throw new Error('Unable to insert or update the database. Check your inputs to make sure they are valid.');
	}

	cookie.message = 'The configuration was saved successfully!';
	return redirect(`/admin/configuration`, {
		headers: {
			'Set-Cookie': await formSubmitMessage.serialize(cookie)
		}
	});
};

export default function ConfigurationFormPage() {
	const actionData = useActionData<typeof action>();
	const { config } = useLoaderData<typeof loader>();

	return (
		<main className="container mx-auto pb-10 pt-8">
			<div className="text-[25px] font-bold">Manage Configurations</div>
			<div className="mt-8">
				<Form method="post">
					{actionData && 'error' in actionData && (
							<div className="mb-5 flex border border-red-500 bg-white px-5 py-3.5 text-sm font-light">
								<img src={iconError} alt="" width={16} />
								<div className="pl-2 text-red-500">{actionData.error}</div>
							</div>
						)
					}
					<div className="rounded-[5px] bg-gray-200 p-10">
						<div className="grid grid-cols-1 gap-x-10 gap-y-8">
							<div>
								<label
									htmlFor="Key"
									className="text-lg font-bold after:ml-0.5 after:text-orange-500 after:content-['*']"
								>
									Key
								</label>
								<div className="mt-3">
									<input
										id="Key"
										name="key"
										required={true}
										type="text"
										disabled={!!config.id}
										defaultValue={config.key}
										className="block h-[50px] w-full rounded-[5px] px-5 text-lg outline-0"
									/>
								</div>
							</div>
							<div>
								<label htmlFor="Values" className="text-lg font-bold">
									Values
								</label>
								<div className="mt-3">
									<textarea
										id="Values"
										name="values"
										rows={4}
										defaultValue={config.values || ''}
										className="block w-full rounded-[5px] px-5 py-2.5 text-lg outline-0"
									/>
								</div>
							</div>
						</div>
					</div>

					<div className="my-10">
						<button
							type="submit"
							className="rounded-full bg-orange-500 px-5 py-2 text-lg text-white hover:bg-orange-800 focus:bg-orange-800"
						>
							Save
						</button>
						<Link
							to="/admin/configuration"
							className="ml-4 inline-block rounded-full bg-gray-500 px-5 py-2 text-lg text-white hover:bg-gray-600 focus:bg-gray-600"
						>
							Cancel
						</Link>
					</div>
				</Form>
			</div>
		</main>
	);
}

export function ErrorBoundary() {
	const error = useRouteError();

	if (error instanceof Error) {
		return <div>An unexpected error occurred: {error.message}</div>;
	}

	if (!isRouteErrorResponse(error)) {
		return <h1>Unknown Error</h1>;
	}

	if (error.status === 404) {
		return <div>Configuration not found</div>;
	}

	return <div>An unexpected error occurred: {error.statusText}</div>;
}
