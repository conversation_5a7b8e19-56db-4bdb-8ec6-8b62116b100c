import { type LoaderFunctionArgs, type LinksFunction } from '@remix-run/node';
import { useLoaderData } from '@remix-run/react';
import moment from '~/utils/moment';

import logo from '~/images/Gun-Violence-Archive-Logo-Reverse.svg';

export const links: LinksFunction = () => [{ rel: 'stylesheet', href: '/styles/framed-map.css' }];

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
	let title = 'Test';
	let image = `/files/charts-and-maps/${params.quuid}.png`;

	return { title, image };
};

export default function ChartsMapsHtml() {
	const { title, image } = useLoaderData<typeof loader>();

	return (
		<>
			<div className="map-frame-above">
				<div className="first">
					<span className="branding-logo">
						<img src={logo} alt="Gun Violence Archive" />
					</span>
				</div>
				<div className="map-frame-right">
					<h1 id="map-title">{title}</h1>
				</div>
			</div>
			<img className="map" src={image} alt="Map" />
			<div className="map-frame-below">
				<div>
					<span className="date">January 1 - {moment().format('MMMM D, YYYY')}</span>
				</div>
				<div className="map-frame-right">
					<span className="url">gunviolencearchive.org</span>
				</div>
			</div>
		</>
	);
}
