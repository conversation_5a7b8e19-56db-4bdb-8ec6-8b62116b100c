import { useEffect, useState } from 'react';
import { useLoaderData, useSubmit } from '@remix-run/react';
import { type ActionFunctionArgs, type LoaderFunctionArgs, redirect } from '@remix-run/node';
import { SearchProvider } from '~/context/SearchContext';
import type { FilterGroup, SearchState, SearchFilter } from '~/types/search';
import { FilterRegistry } from '~/services/search';
import { SearchBuilder } from '~/services/search/SearchBuilder.server';
import { getUser } from '~/utils/auth.server';
import { Header } from '~/components/common/Header';
import { Footer } from '~/components/common/Footer';
import Operator from '~/components/search/Operator';
import AddAnotherFilter from '~/components/search/AddAnotherFilter';
import FilterGroupComponent from '~/components/search/FilterGroupComponent2';

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
	const user = await getUser(request).catch(() => null);
	let filterGroups = [];

	let searchBuilder;
	if (params.id) {
		searchBuilder = await SearchBuilder.load(params.id);
		const isOwnerOrAdmin = user?.id === Number(searchBuilder.userId) || user?.role?.toLowerCase() === 'admin';
		if (searchBuilder.locked && !isOwnerOrAdmin) {
			throw new Error('You do not have permission to view this search.');
		}

		filterGroups = searchBuilder.getFilterGroups();
		console.log('filterGroups from loader================', filterGroups);
	}
	return { search: searchBuilder, filterGroups };
};

export const action = async ({ params, request }: ActionFunctionArgs) => {
	const user = await getUser(request).catch(() => null);

	const { baseTable, baseAlias, filterGroups, columns, userRoles } = await request.json();
	console.log('filterGroups===============', filterGroups);
	if (params.id) {
		// If an ID is provided, load the existing search
		const builder = await SearchBuilder.load(params.id);
		const isOwnerOrAdmin = user?.id === Number(builder.userId) || user?.role?.toLowerCase() === 'admin';
		if (builder.editlock || (builder.locked && !isOwnerOrAdmin)) {
			throw new Error('You do not have permission to edit this search.');
		}

		builder.clearFilterGroups();
		// Add filter groups
		for (const group of filterGroups) {
			builder.addFilterGroup(group);
		}

		// Update the search
		builder.update(params.id, {});
		return redirect(`/search/${params.id}/result`);
	} else {
		// Create a new SearchBuilder instance
		const builder = new SearchBuilder(baseTable, baseAlias, baseTable === 'gva_data.incident_participants' ? 'participants' : 'incidents');
		builder.setFormType('advanced');
		// Add filter groups
		for (const group of filterGroups) {
			builder.addFilterGroup(group);
		}

		// Set columns
		if (columns && columns.length > 0) {
			builder.setColumns(columns, userRoles || ['user']);
		}

		// Save the search
		const quuid = await builder.save('', String(user?.id || 0));
		return redirect(`/search/${quuid}/result`);
	}
};

export default function AdvancedSearch() {
	const { search, filterGroups } = useLoaderData<typeof loader>();
	const submit = useSubmit();

	// Get all available filters
	const availableFilters = FilterRegistry.getInstance().getAllAsMap();

	const [searchState, setSearchState] = useState<SearchState>({
		groups: filterGroups,
		page: 1,
		perPage: 20
	});

	const [displayAs, setDisplayAs] = useState('incident');

	const [isSearching, setIsSearching] = useState(false);

	const addGroup = (field: string, operator: string, value: any) => {
		setSearchState(prev => ({
			...prev,
			groups: [
				...prev.groups,
				{
					id: Date.now().toString(),
					type: 'group',
					operator: 'AND',
					filters: [
						{
							id: Date.now().toString(),
							type: 'filter',
							field,
							operator: operator as any, // Cast to correct type if needed
							value
						}
					]
				}
			]
		}));
	};

	const deleteGroup = (index: number) => {
		setSearchState(prev => ({
			...prev,
			groups: prev.groups.filter((_, i) => i !== index)
		}));
	};

	const updateGroup = (index: number, updatedGroup: FilterGroup) => {
		setSearchState(prev => ({
			...prev,
			groups: prev.groups.map((group, i) => i === index ? updatedGroup : group)
		}));
	};

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		if (searchState.groups[0].filters.length === 0) {
			return;
		}
		setSearchState(prev => ({ ...prev, page: 1 }));
		onSearch({ ...searchState, page: 1 });
	};

	const onSearch = (search: SearchState) => {
		setIsSearching(true);

		// Convert filter groups
		const filterGroups = [];
		search.groups.forEach(group => {
			if (group.filters.length > 0) {
				const backendGroup = convertFilterGroup(group);
				filterGroups.push(backendGroup);
			}
		});
		console.log('search.groups:', JSON.stringify(search.groups));
		const rootGroup = search.groups[0];
		if (rootGroup.filters.length > 0) {
			const backendGroup = convertFilterGroup(rootGroup);
			filterGroups.push(backendGroup);
		}
		console.log('frontend filterGroups:', JSON.stringify(search.groups));
		console.log('backend filterGroups:', JSON.stringify(filterGroups));

		submit(
			{
				baseTable: displayAs === 'participant' ? 'gva_data.incident_participants' : 'gva_data.incidents',
				baseAlias: displayAs === 'participant' ? 'p' : 'i',
				filterGroups: filterGroups,
				columns:
					displayAs === 'participant'
						? ['participant_id', 'name']
						: ['incident_id', 'incident_date', 'state', 'city', 'address'],
				userRoles: ['user']
			},
			{ method: 'post', encType: 'application/json' }
		);
	};

	useEffect(() => {
		console.log('searchState-------------', JSON.stringify(searchState));
	}, [searchState]);

	return (
		<SearchProvider filters={availableFilters}>
			<div className="min-h-screen">
				<Header />
				<main className="py-10 md:py-20">
					<div className="container mx-auto">
						{/* Search form */}
						<div>
							<div className="querybuilder advanced">
								{searchState.groups.map((group, index) => (
									<div key={`group-${group.id}`}>
										<FilterGroupComponent
											key={group.id}
											group={group}
											onUpdate={(updated) => updateGroup(index, updated)}
											onDelete={() => deleteGroup(index)}
											root={true}
										/>
										<Operator />
									</div>
								))}
								<AddAnotherFilter onChange={addGroup} />
							</div>
							<div className="mt-10 text-lg">
								Display results as
								<select className="w-full sm:w-fit sm:ml-5 sm:min-w-[200px] bg-gray-200 h-[50px] rounded-[5px] outline-0 px-2 sm:px-3 lg:px-5"
									value={displayAs}
									onChange={e => setDisplayAs(e.target.value)}
								>
									<option value="incident">Incidents</option>
									<option value="participant">Participants</option>
								</select>
							</div>
							<div className="mt-10">
								{!search && (
									<button type="button" onClick={isSearching ? null : handleSubmit} className="rounded-full bg-orange-500 px-5 py-2 text-lg text-white">
										{isSearching ? 'Searching...' : 'Search'}
									</button>
								)}
								{search && !search.editlock && (
									<button type="button" onClick={isSearching ? null : handleSubmit} className="rounded-full bg-orange-500 px-5 py-2 text-lg text-white">
										{isSearching ? 'Saving...' : 'Save'}
									</button>
								)}
							</div>
						</div>
					</div>
				</main>
				<Footer />
			</div>
		</SearchProvider>
	);
}

const isFilterGroup = (filter: SearchFilter): filter is FilterGroup => {
	return filter.type === 'group';
};

// Convert frontend filter group to backend filter group
const convertFilterGroup = (group: FilterGroup): import('~/services/search').FilterGroup => {
	return {
		operator: group.operator,
		conditions: group.filters.map(filter => {
			if (isFilterGroup(filter)) {
				return convertFilterGroup(filter);
			} else {
				return {
					id: filter.field,
					value: {
						operator: filter.operator,
						value: filter.value
					}
				};
			}
		})
	};
};
