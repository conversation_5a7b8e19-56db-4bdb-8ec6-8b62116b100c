import { json, type LoaderFunctionArgs } from '@remix-run/node';
import { db } from '~/utils/db.server';
import { requireUserId } from '~/utils/session.server';

/**
 * API endpoint for reconnecting to an incident after a disconnection
 * 
 * This endpoint replaces the action_reconnect function from the original Drupal module
 */
export async function loader({ request, params }: LoaderFunctionArgs) {
  // Ensure the user is authenticated
  const userId = await requireUserId(request);
  
  // Get the incident ID from the URL params
  const incidentId = parseInt(params.id || '0', 10);
  
  if (!incidentId) {
    return json({ error: 'Invalid incident ID' }, { status: 400 });
  }
  
  try {
    // Check if there's an existing lock for this incident
    const existingLock = await db.gva_entry_locks.findFirst({
      where: {
        incident_id: incidentId
      }
    });
    
    let allow = false;
    
    // If there is no entry for this incident, allow the user to continue editing
    if (!existingLock) {
      allow = true;
    } 
    // If it has been more than 30 seconds since this incident was reported as being edited, allow the user to continue editing
    else if (Math.floor(Date.now() / 1000) - existingLock.timestamp > 30) {
      allow = true;
    } 
    // If the current user is the same as the last user reported to edit, allow the user to continue editing
    else if (userId === existingLock.uid) {
      allow = true;
    }
    
    if (allow) {
      // Delete any existing locks for this incident
      await db.gva_entry_locks.deleteMany({
        where: {
          incident_id: incidentId
        }
      });
      
      // Create a new lock for the current user
      await db.gva_entry_locks.create({
        data: {
          incident_id: incidentId,
          uid: userId,
          timestamp: Math.floor(Date.now() / 1000) // Current time in seconds
        }
      });
    }
    
    // Return the allow status
    return json({ allow });
  } catch (error) {
    console.error('Error reconnecting to incident:', error);
    return json({ error: 'Failed to reconnect to incident', allow: false }, { status: 500 });
  }
}