import { useState } from 'react';
import { type ActionFunctionArgs, redirect, redirectDocument, type LoaderFunctionArgs } from '@remix-run/node';
import { useLoaderData, Link, Form, useSubmit } from '@remix-run/react';
import invariant from 'tiny-invariant';
import type { incidents, incident_participants } from '@prisma/client';
import { ColumnRegistry } from '~/services/search';
import { SearchBuilder } from '~/services/search/SearchBuilder.server';
import { getSession, commitSession } from '~/utils/session.server';
import { getUser } from '~/utils/auth.server';
import { hasPermission } from '~/utils/client-utils';
import { Header } from '~/components/common/Header';
import { Footer } from '~/components/common/Footer';
import Pagination from '~/components/Pagination';
import EditLabelForm from '~/components/search/EditLabelForm';
import ChooseColumnForm from '~/components/search/ChooseColumnForm';

import iconIncidents from '~/images/Icon-Admin-Incidents.svg';
import iconColumns from '~/images/Icon-Admin-Columns.svg';
import iconDelete from '~/images/Icon-Admin-Delete.svg';
import iconEdit from '~/images/Icon-Admin-Edit.svg';
import iconExport from '~/images/Icon-Admin-Export.svg';

const PAGE_SIZE = 20;

// Define the search result type that includes the state value
interface SearchResultIncident extends incidents {
	value?: string; // State name from the taxonomy relation
}

interface SearchResultParticipant extends incident_participants {
	incident?: incidents;
	value?: string;
}

// Helper function to handle potentially null IDs
const getValidId = (id: string | number | null | undefined): number => {
	if (typeof id === 'string') {
		return parseInt(id, 10);
	}
	if (typeof id === 'number') {
		return id;
	}
	throw new Error('Invalid ID');
};

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
	invariant(params.id, 'Search ID is required');
	const user = await getUser(request).catch(() => null);

	const url = new URL(request.url);
	const page = Number(url.searchParams.get('page')) || 1;
	const sort = url.searchParams.get('sort');

	const session = await getSession(request);
	const sessionColumns = session.get(`search:${params.id}:columns`);

	const searchBuilder = await SearchBuilder.load(params.id);
	searchBuilder.setPage(page);
	searchBuilder.setPageSize(PAGE_SIZE);
	if (sort) {
		const [key, direction] = sort.split(':');
		searchBuilder.setSort(key, direction);
	}

	// Use columns from session if available
	const isOwnerOrAdmin = user?.id === Number(searchBuilder.userId) || user?.role?.toLowerCase() === 'admin';
	if (!isOwnerOrAdmin && sessionColumns && Array.isArray(sessionColumns) && sessionColumns.length > 0) {
		searchBuilder.setColumns(sessionColumns);
	}

	const results = await searchBuilder.execute(user?.role?.toLowerCase() === 'admin' ? ['user', 'admin'] : ['user'], 'array');

	return { search: searchBuilder, results, totalCount: searchBuilder.getTotalCount() };
};

export const action = async ({ params, request }: ActionFunctionArgs) => {
	invariant(params.id, 'Search ID is required');

	const user = await getUser(request).catch(() => null);

	const searchBuilder = await SearchBuilder.load(params.id);
	const isOwnerOrAdmin = user?.id === Number(searchBuilder.userId) || user?.role?.toLowerCase() === 'admin';

	const formData = await request.formData();
	const action = formData.get('action');
	if (action === 'delete' && isOwnerOrAdmin) {
		await SearchBuilder.delete(params.id);
		return redirect('/search');
	} else if (action === 'editLabel' && isOwnerOrAdmin) {
		const name = formData.get('name')?.toString() || '';
		const locked = !!Number(formData.get('locked'));
		const editlock = !!Number(formData.get('editlock'));
		const limitResults = !!Number(formData.get('limitResults'));
		const path = formData.get('path')?.toString() || '';

		// update the search with new label details
		await searchBuilder.update(params.id, { name, locked, editlock, limitResults, path });

		return redirectDocument(`/search/${params.id}/result`);
	} else if (action === 'chooseColumns') {
		const enabled = formData.get('enabled')?.toString() || '';
		const enabledColumns = enabled.split(',').filter(Boolean);

		if (isOwnerOrAdmin) {
			// Owner or Admin: update in DB
			searchBuilder.setColumns(enabledColumns);
			await searchBuilder.update(params.id, {});
			return redirectDocument(`/search/${params.id}/result`);
		} else {
			// Not owner/admin or anonymous: store columns in session
			const session = await getSession(request);
			session.set(`search:${params.id}:columns`, enabledColumns);
			return redirectDocument(`/search/${params.id}/result`, {
				headers: {
					'Set-Cookie': await commitSession(session)
				}
			});
		}
	}
	throw new Error('Invalid action');
};

export default function SearchResults() {
	const { search, results, totalCount } = useLoaderData<typeof loader>();

	const submit = useSubmit();

	// Get all available filters
	const availableColumns = ColumnRegistry.getInstance().getAll().map(column => ({
		key: column.key,
		label: column.label,
		sortable: column.sortable,
	}));

	const [tab, setTab] = useState('result');
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(PAGE_SIZE);

	const changeSort = (sortColumn: string) => {
		let sortDirection = 'ASC';
		if (search.sortColumn === sortColumn) {
			// If already sorted by this key, toggle sort direction
			sortDirection = search.sortDirection === 'ASC' ? 'DESC' : 'ASC';
		}
		window.location.href = `/search/${search.id}/result?sort=${sortColumn}:${sortDirection}`;
	};

	const submitLabelForm = (data: any) => {
		submit({ ...data, action: 'editLabel' }, { method: 'post' });
	};

	const submitColumnForm = (enabled: string[]) => {
		submit({ enabled: enabled.join(','), action: 'chooseColumns' }, { method: 'post' });
	};

	const isIncidents = search.entityType === 'incidents';
	return (
		<div className="min-h-screen">
			<Header />
			<main className="py-5 md:py-10">
				<div className="container mx-auto">
					{/* Results Header 
					<div className="flex justify-between items-center">
						<div className="text-gray-600">
							Showing {results.length} results
						</div>
						<div className="flex gap-4">
							<select className="bg-gray-200">
								<option>Sort by: Recent</option>
								<option>Sort by: Oldest</option>
							</select>
							<select className="bg-gray-200" value={pageSize} onChange={(e) => setPageSize(Number(e.target.value))}>
								<option value={20}>20 per page</option>
								<option value={50}>50 per page</option>
								<option value={100}>100 per page</option>
							</select>
						</div>
					</div>
					*/}
					{search.name && (
						<div className="text-center text-xl font-bold uppercase md:text-2xl mb-5">{search.name}</div>
					)}
					<div className="bg-gray-200 px-10 py-4">
						<ul className="flex space-x-10 text-sm">
							<li>
								<button className="flex items-center hover:text-orange-600" onClick={() => setTab('result')}>
									<img className="mr-2" src={iconIncidents} alt="" width={16} /> Incidents
								</button>
							</li>
							<li>
								<button className="flex items-center hover:text-orange-600" onClick={() => setTab('column')}>
									<img className="mr-2" src={iconColumns} alt="" width={16} /> Choose Columns
								</button>
							</li>
							{hasPermission('Admin') && (
								<>
									<li>
										<Form
											method="post"
											onSubmit={e => {
												if (!confirm('Please confirm you want to delete this record.')) {
													e.preventDefault();
												}
											}}
										>
											<input type="hidden" name="action" value="delete" />
											<button type="submit" className="flex items-center hover:text-orange-600">
												<img className="mr-2" src={iconDelete} alt="" width={16} /> Delete
											</button>
										</Form>
									</li>
									<li>
										<button className="flex items-center hover:text-orange-600" onClick={() => setTab('label')}>
											<img className="mr-2" src={iconEdit} alt="" width={16} /> Edit Label
										</button>
									</li>
								</>
							)}
							{(!search.locked || hasPermission('Admin')) && (
								<li>
									<Link className="flex items-center hover:text-orange-600" to={search.formType == 'advanced' ? `/search/advanced/${search.id}` : `/search/${search.id}`}>
										<img className="mr-2" src={iconEdit} alt="" width={16} /> Edit Search
									</Link>
								</li>
							)}
							<li>
								<a className="flex items-center hover:text-orange-600" href={`export`}>
									<img className="mr-2" src={iconExport} alt="" width={16} /> Export as CSV
								</a>
							</li>
						</ul>
					</div>

					{/* Results Table */}
					{tab === 'result' && (
						<div>
							<div className="mt-5 overflow-x-auto">
								<table className="table-gray w-full table-auto text-sm">
									<thead>
										<tr>
											{search.columns.map(key => availableColumns.find(s => s.key == key)?.sortable ? (
												<th key={key} className="uppercase cursor-pointer" align="left" onClick={e => changeSort(key)}>
													{key.replace(/_/g, ' ')} {search.sortColumn === key ? (search.sortDirection === 'ASC' ? '▲' : '▼') : ''}
												</th>
											) : (
												<th key={key} className="uppercase" align="left">
													{key.replace(/_/g, ' ')}
												</th>
											))}
											<th className="uppercase" align="left">Actions</th>
										</tr>
									</thead>
									<tbody>
										{results.map(result => {
											try {
												// Get valid IDs or throw error if invalid
												const itemId = isIncidents
													? getValidId((result as SearchResultIncident).incident_id)
													: getValidId((result as SearchResultParticipant).participant_id);
												return (
													<tr key={`row-${itemId}`} className="hover:bg-gray-50">
														{search.columns.map((col: any) => {
															const value = result[col];
															return (
																<td key={`row-${itemId}-${col}`}>
																	{typeof value === 'object' && value !== null && value.__html ? (
																		<div dangerouslySetInnerHTML={{ __html: value.__html }} />
																	) : typeof value === 'object' && value !== null ? (
																		<pre>{JSON.stringify(value)}</pre>
																	) : (
																		value?.toString() || ''
																	)}
																</td>
															);
														})}
														<td>
															<Link
																to={isIncidents ? `/incident/${itemId}` : `/participant/${itemId}`}
																className="font-medium text-blue-500 hover:text-orange-600"
															>
																View Details
															</Link>
														</td>
													</tr>
												);
											} catch (error) {
												// Skip rendering this row if ID is invalid
												return null;
											}
										})}

										{totalCount === 0 && (
											<tr>
												<td colSpan={Object.keys(results[0]).length + 1}>
													No incidents found matching your search criteria.
												</td>
											</tr>
										)}
									</tbody>
								</table>
							</div>

							{/* Pagination */}
							<div className="mt-10 flex justify-center text-sm">
								<Pagination
									total={totalCount}
									current={page}
									defaultPageSize={pageSize}
								// onPageChange={onPageChange}
								/>
							</div>
						</div>
					)}

					{/* Choose Columns */}
					{tab === 'column' && (
						<div className="mt-5">
							<ChooseColumnForm enabled={search.columns} availableColumns={availableColumns} onSubmit={submitColumnForm} />
						</div>
					)}

					{/* Edit Label */}
					{tab === 'label' && (
						<div className="mt-5">
							<EditLabelForm data={search} onSubmit={submitLabelForm} />
						</div>
					)}
				</div>
			</main>
			<Footer />
		</div>
	);
}
