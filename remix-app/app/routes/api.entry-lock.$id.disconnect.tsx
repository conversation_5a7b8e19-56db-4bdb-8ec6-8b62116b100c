import { json, type LoaderFunctionArgs } from '@remix-run/node';
import { db } from '~/utils/db.server';

/**
 * API endpoint for disconnecting from an incident
 * 
 * This endpoint replaces the action_disconnect function from the original Drupal module
 */
export async function loader({ params }: LoaderFunctionArgs) {
  // Get the incident ID from the URL params
  const incidentId = parseInt(params.id || '0', 10);
  
  if (!incidentId) {
    return json({ error: 'Invalid incident ID' }, { status: 400 });
  }
  
  try {
    // Delete any existing locks for this incident
    await db.gva_entry_locks.deleteMany({
      where: {
        incident_id: incidentId
      }
    });
    
    // Return a success response
    return json({ success: true });
  } catch (error) {
    console.error('Error disconnecting from incident:', error);
    return json({ error: 'Failed to disconnect from incident' }, { status: 500 });
  }
}