import { type LoaderFunctionArgs, json } from '@remix-run/node';
import { getImportStatus } from '~/models/import-queue.server';

/**
 * Loader function for the import status endpoint
 * This is a conversion of the action_status function from import.php
 */
export async function loader({ params }: LoaderFunctionArgs) {
  const filename = params.filename;
  
  if (!filename) {
    return json({ error: 'Filename is required' }, { status: 400 });
  }
  
  const importStatus = await getImportStatus(filename);
  
  if (!importStatus) {
    return json({ error: 'Import not found' }, { status: 404 });
  }
  
  return json({
    status: {
      imported: importStatus.imported,
      total: importStatus.total,
      message: `${importStatus.imported}/${importStatus.total} incidents have been imported.`
    }
  });
}

/**
 * Default export is required for Remix routes
 */
export default function ImportStatus() {
  // This is an API route, so we don't need to render anything
  return null;
}