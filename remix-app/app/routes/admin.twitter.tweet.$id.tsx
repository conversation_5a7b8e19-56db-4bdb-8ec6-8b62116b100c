import { type LoaderFunctionArgs, json } from '@remix-run/node';
import { requireUser } from '~/utils/session.server';
import { getIncident } from '~/models/incidents.server';

/**
 * Loader function for the twitter tweet endpoint
 * This is a conversion of the action_tweet function from twitter.php
 */
export async function loader({ params, request }: LoaderFunctionArgs) {
  const user = await requireUser(request);
  
  const id = params.id ? parseInt(params.id, 10) : null;
  
  if (!id || isNaN(id)) {
    return json({ error: 'Invalid incident ID' }, { status: 400 });
  }
  
  try {
    // Get the incident to check if it exists
    const incident = await getIncident(id);
    
    if (!incident) {
      return json({ error: 'Incident not found' }, { status: 404 });
    }
    
    // Get the referrer URL
    const referrer = request.headers.get('Referer') || '/';
    
    // Return the incident data and referrer for the tweet form
    return json({
      incident,
      referrer
    });
  } catch (error) {
    console.error('Error getting incident for tweet:', error);
    return json({ error: 'Failed to get incident for tweet' }, { status: 500 });
  }
}

/**
 * Default export is required for Remix routes
 * This component would render a tweet form in a real implementation
 */
export default function TwitterTweet() {
  // In a real implementation, this would render a tweet form
  // For now, we'll just return a placeholder
  return (
    <div>
      <h1>Tweet about Incident</h1>
      <p>This is a placeholder for the tweet form.</p>
    </div>
  );
}