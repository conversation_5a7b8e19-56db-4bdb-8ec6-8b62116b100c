import type { LoaderFunctionArgs, MetaFunction, LinksFunction } from '@remix-run/node';
import { NavLink, useLoaderData, useRouteError, isRouteErrorResponse } from '@remix-run/react';
import { useEffect } from 'react';
import pkg from '@superset-ui/embedded-sdk';

import { fetchGuestToken } from '~/utils/superset.server';
import { getPathDashboards } from '~/models/dashboard.server';
import { Title } from '~/components/common/Title';

import styles from '~/styles/dashboard.css';

const { embedDashboard } = pkg;

export const links: LinksFunction = () => [{ rel: 'stylesheet', href: styles }];

export const meta: MetaFunction = () => {
	return [{ title: 'GVA Dashboards' }, { name: 'description', content: 'Gun Violence Archive - Dashboards' }];
};

interface ILoaderData {
	token: string;
	dashboardId: string;
	dashboardName: string;
	dashboards: Array<any>;
}

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
	const { pathname } = new URL(request.url);
	const path = pathname.substring(0, pathname.lastIndexOf('/'));
	const data: ILoaderData = { token: '', dashboardId: '', dashboardName: '', dashboards: [] };

	try {
		data.dashboards = await getPathDashboards({ path: path });

		if (data.dashboards.length > 0) {
			const dashboard = data.dashboards.find(dashboard => dashboard.id === Number(params.id));
			if (dashboard) {
				data.dashboardId = dashboard.dashboard_id;
				data.dashboardName = dashboard.name;
			}
			data.token = await fetchGuestToken(data.dashboardId);
			return data;
		}
		throw new Error('No dashboards found for the requested URL.');
	} catch (error: any) {
		throw new Error(`Unable to load the dashboard. Error: ${error.message}`);
	}
};

export default function DisplayDashboard() {
	const { token, dashboardId, dashboardName, dashboards } = useLoaderData<typeof loader>();

	useEffect(() => {
		const embed = async () => {
			embedDashboard({
				id: dashboardId, // given by the Superset embedding UI
				supersetDomain: 'https://gva-ss.sdev.site',
				mountPoint: document.getElementById('my-superset-container')!,
				fetchGuestToken: async () => token,
				dashboardUiConfig: {
					// dashboard UI config: hideTitle, hideTab, hideChartControls, filters.visible, filters.expanded (optional)
					hideTitle: true,
					hideTab: true,
					hideChartControls: false,
					filters: {
						expanded: false,
						visible: true
					}
				}
			});
		};

		if (document.getElementById('my-superset-container') && token !== '') {
			embed();
		}
	}, [dashboardId]);
	return (
		<>
			<Title title={dashboardName} />
			<div className="flex grow overflow-auto">
				{dashboards.length > 1 ? (
					<div className="h-full w-64 overflow-auto bg-orange-500 p-4 text-white">
						<ul>
							{dashboards.map(dashboard => (
								<li key={dashboard.dashboard_id}>
									<NavLink
										className={({ isActive }) =>
											`block border-b p-4 text-xl ${isActive ? 'bg-orange-800' : ''}`
										}
										relative="path"
										to={`../${dashboard.id}`}
									>
										{dashboard.name}
									</NavLink>
								</li>
							))}
						</ul>
					</div>
				) : null}
				<div className="grow">
					<div id="my-superset-container" className="size-full overflow-auto" />
				</div>
			</div>
		</>
	);
}

export function ErrorBoundary() {
	const error = useRouteError();

	if (error instanceof Error) {
		return <div>An unexpected error occurred: {error.message}</div>;
	}

	if (!isRouteErrorResponse(error)) {
		return <h1>Unknown Error</h1>;
	}

	if (error.status === 404) {
		return <div>Dashboard not found</div>;
	}

	return <div>An unexpected error occurred: {error.statusText}</div>;
}
