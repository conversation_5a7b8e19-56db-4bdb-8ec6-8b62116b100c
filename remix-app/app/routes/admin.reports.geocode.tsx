import { useState, useEffect, Fragment } from 'react';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { useLoaderData, Link, Form } from '@remix-run/react';
import { JsonView, collapseAllNested, darkStyles, defaultStyles } from 'react-json-view-lite';
import 'react-json-view-lite/dist/index.css';
import moment from '~/utils/moment';
import { formSubmitMessage } from '~/utils/cookies.server';
import { searchLocationQueuePaginatedList, searchLocationQueuePaginatedListCount } from '~/models/queue.server';

import { AdminPagination } from '~/components/admin/Pagination';

const PAGE_SIZE = 25;

export async function loader({ request }: LoaderFunctionArgs) {
	const cookieHeader = request.headers.get('Cookie');
	const cookie = (await formSubmitMessage.parse(cookieHeader)) || {};

	const url = new URL(request.url);
	const page = Number(url.searchParams.get('page')) || 1;

	const filter = {
		status: 0,
		error_type: url.searchParams.get('error_type') || undefined
	};

	const logs = await searchLocationQueuePaginatedList(filter, page, PAGE_SIZE);

	const logsCount = await searchLocationQueuePaginatedListCount(filter);

	return { logs, logsCount, filter, formSubmitMessage: cookie.message };
}

export default function GeocodeIndexPage() {
	const { logs, logsCount, filter, formSubmitMessage } = useLoaderData<typeof loader>();
	const [showMessage, setShowMessage] = useState(!!formSubmitMessage);

	useEffect(() => {
		if (showMessage) {
			const timeoutId = setTimeout(() => setShowMessage(false), 5000);
			return () => clearTimeout(timeoutId);
		}
	}, [showMessage]);

	const errorTypes = [
		'Zipcode',
		'Congresstional District',
		'County Name',
		'Neighborhood',
		'Metro and Micro Statistical Areas',
		'Combined Statistical Area',
		'state-county-tract number'
	];
	return (
		<main className="container mx-auto pb-10 pt-8">
			{showMessage ? <p className="animate-fade bg-black text-white">{formSubmitMessage}</p> : null}

			<Form id="search-form" role="search">
				<div className="flex flex-col space-y-4">
					<div className="flex space-x-4">
						<div>
							<select
								id="ErrorType"
								name="error_type"
								defaultValue={filter.error_type || ''}
								autoComplete="off"
								className="border-2"
							>
								<option value="">-- Error Type --</option>
								{errorTypes.map(type => (
									<option key={`type-${type}`} value={type}>
										{type}
									</option>
								))}
							</select>
						</div>
						<div>
							<button
								type="submit"
								className="rounded-full bg-orange-500 px-5 py-1.5 text-lg text-white hover:bg-orange-800 focus:bg-orange-800"
							>
								Search
							</button>
						</div>
					</div>
				</div>
			</Form>

			<table className="table-gray mt-5 w-full table-auto text-sm">
				<thead>
					<tr>
						<th align="left">Incident Id</th>
						<th align="left">Address</th>
						<th align="left">Date Added</th>
						<th align="left">Error Type</th>
						<th align="left">Notes</th>
						<th align="left">Raw Response</th>
					</tr>
				</thead>
				<tbody>
					{logs.map(log => (
						<tr key={`log-${log.entity_id}`}>
							<td>
								<Link className="text-blue-500" to={`/incident/${log.entity_id}`}>
									{log.entity_id}
								</Link>
							</td>
							<td>
								{log.incident?.address}
								<br />
								{log.incident?.county} {log.incident?.city_county}, {log.incident?.state?.value}
							</td>
							<td>{moment.unix(log.added).format('MMM D, YYYY')}</td>
							<td>{log.error_type}</td>
							<td>{log.comment}</td>
							<td>
								{log.json_response && (
									<Fragment>
										<JsonView
											data={JSON.parse(log.json_response)}
											shouldExpandNode={collapseAllNested}
											style={defaultStyles}
										/>
									</Fragment>
								)}
							</td>
						</tr>
					))}
					{logs.length === 0 && (
						<tr>
							<td colSpan={6}>No logs yet.</td>
						</tr>
					)}
				</tbody>
			</table>

			<div className="my-10 flex justify-center text-sm">
				<AdminPagination total={logsCount} defaultPageSize={PAGE_SIZE} />
			</div>
		</main>
	);
}
