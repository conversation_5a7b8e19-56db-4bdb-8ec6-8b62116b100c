import { type ActionFunctionArgs, json, redirect } from '@remix-run/node';
import { deleteIncident, getIncident } from '~/models/incidents.server';
import { requireUser } from '~/utils/session.server';

/**
 * Action function for the incident delete endpoint
 * This is a conversion of the action_delete function from incident.php
 */
export async function action({ params, request }: ActionFunctionArgs) {
  const user = await requireUser(request);
  
  const id = params.id ? parseInt(params.id, 10) : null;
  
  if (!id || isNaN(id)) {
    return json({ error: 'Invalid incident ID' }, { status: 400 });
  }
  
  try {
    // Get the incident to check if it exists and to get its title
    const incident = await getIncident(id);
    
    if (!incident) {
      return json({ error: 'Incident not found' }, { status: 404 });
    }
    
    // Delete the incident
    await deleteIncident(id);
    
    // Return success message and redirect to home page
    return json(
      { 
        success: true, 
        message: `Incident ${incident.title} has been deleted.` 
      }, 
      { 
        headers: { 
          'Set-Cookie': 'flash=' + encodeURIComponent(JSON.stringify({
            type: 'success',
            message: `Incident ${incident.title} has been deleted.`
          }))
        } 
      }
    );
  } catch (error) {
    console.error('Error deleting incident:', error);
    return json({ error: 'Failed to delete incident' }, { status: 500 });
  }
}

/**
 * Loader function to handle GET requests
 * Redirects to home page since this is an action-only route
 */
export async function loader() {
  return redirect('/');
}

/**
 * Default export is required for Remix routes
 */
export default function IncidentDelete() {
  // This is an API route, so we don't need to render anything
  return null;
}