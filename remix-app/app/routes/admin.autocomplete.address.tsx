import { type LoaderFunctionArgs } from '@remix-run/node';
import _ from 'lodash';
import { db as prisma } from '~/utils/db.server';

export async function loader({ request }: LoaderFunctionArgs) {
	const url = new URL(request.url);
	const q = url.searchParams.get('q');

	/** action_address() in sites\all\modules\gva_entry\callbacks\autocomplete.php */
	let results: { value: string; label: string }[] = [];
	if (q) {
		// Get unique addresses from incidents table that match the query
		const addresses = await prisma.incidents.findMany({
			where: {
				address: {
					contains: q,
					mode: 'insensitive'
				}
			},
			select: {
				address: true
			},
			distinct: ['address'],
			take: 20, // Limit results to 20
			orderBy: {
				address: 'asc'
			}
		});

		// Map addresses to the expected format
		results = addresses
			.filter(a => a.address) // Filter out null addresses
			.map(a => ({ 
				value: a.address as string, 
				label: a.address as string 
			}));
	}

	return results;
}