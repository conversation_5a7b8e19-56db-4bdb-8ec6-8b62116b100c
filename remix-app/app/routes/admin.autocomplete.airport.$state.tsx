import { type LoaderFunctionArgs } from '@remix-run/node';
import _ from 'lodash';
import { getAirportsByState } from '~/models/airport.server';

export async function loader({ params, request }: LoaderFunctionArgs) {
	const url = new URL(request.url);

	const state = params.state || 'nostate';
	const q = url.searchParams.get('q');

	/** action_airport() in sites\all\modules\gva_entry\callbacks\autocomplete.php */
	let results: { value: string; label: string }[] = [];
	if (q) {
		const airports = await getAirportsByState(state, q);
		
		// Map airports to the expected format
		results = airports.map(airport => {
			const label = `${airport.locid} - ${airport.facilityname} (${airport.city}, ${airport.county})`;
			return {
				value: airport.locid,
				label
			};
		});

		// Sort by label
		results = _.sortBy(results, 'label');
	}

	return results;
}