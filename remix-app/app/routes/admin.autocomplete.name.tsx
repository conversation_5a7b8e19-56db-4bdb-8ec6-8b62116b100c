import { type LoaderFunctionArgs } from '@remix-run/node';
import _ from 'lodash';
import { db as prisma } from '~/utils/db.server';

export async function loader({ request }: LoaderFunctionArgs) {
	const url = new URL(request.url);
	const q = url.searchParams.get('q');

	/** action_name() in sites\all\modules\gva_entry\callbacks\autocomplete.php */
	let results: { value: string; label: string }[] = [];
	if (q) {
		// Get unique participant names from incident_participants table that match the query
		const participants = await prisma.incident_participants.findMany({
			where: {
				name: {
					contains: q,
					mode: 'insensitive'
				}
			},
			select: {
				name: true
			},
			distinct: ['name'],
			take: 20, // Limit results to 20
			orderBy: {
				name: 'asc'
			}
		});

		// Map participant names to the expected format
		results = participants
			.filter(p => p.name) // Filter out null names
			.map(p => ({ 
				value: p.name as string, 
				label: p.name as string 
			}));

		// Also check for name aliases
		const participantsWithAliases = await prisma.incident_participants.findMany({
			where: {
				name_alias: {
					contains: q,
					mode: 'insensitive'
				}
			},
			select: {
				name_alias: true
			},
			distinct: ['name_alias'],
			take: 20, // Limit results to 20
			orderBy: {
				name_alias: 'asc'
			}
		});

		// Add aliases to results
		const aliasResults = participantsWithAliases
			.filter(p => p.name_alias) // Filter out null aliases
			.map(p => ({ 
				value: p.name_alias as string, 
				label: p.name_alias as string 
			}));

		// Combine results and remove duplicates
		results = _.uniqBy([...results, ...aliasResults], 'value');
		
		// Sort by label
		results = _.sortBy(results, 'label');
	}

	return results;
}