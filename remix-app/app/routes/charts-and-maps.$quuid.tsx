import { type LoaderFunctionArgs } from '@remix-run/node';

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
	// default center of continental USA
	let center_x = 38.5;
	let center_y = -96;
	let zoom = 4;
	let markers = [
		{ latitude: 39.381266, longitude: -97.922211 },
		{ latitude: 39.576138, longitude: -98.57996 },
		{ latitude: 39.651598, longitude: -96.633684 }
	];

	return { center_x, center_y, zoom, markers };
};
