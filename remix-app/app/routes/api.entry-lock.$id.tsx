import { json, type LoaderFunctionArgs } from '@remix-run/node';
import { db } from '~/utils/db.server';
import { requireUserId } from '~/utils/session.server';

/**
 * API endpoint for polling the lock status of an incident
 * 
 * This endpoint replaces the action_lock_poll function from the original Drupal module
 */
export async function loader({ request, params }: LoaderFunctionArgs) {
  // Ensure the user is authenticated
  const userId = await requireUserId(request);
  
  // Get the incident ID from the URL params
  const incidentId = parseInt(params.id || '0', 10);
  
  if (!incidentId) {
    return json({ error: 'Invalid incident ID' }, { status: 400 });
  }
  
  try {
    // Delete any existing locks for this incident
    await db.gva_entry_locks.deleteMany({
      where: {
        incident_id: incidentId
      }
    });
    
    // Create a new lock for the current user
    await db.gva_entry_locks.create({
      data: {
        incident_id: incidentId,
        uid: userId,
        timestamp: Math.floor(Date.now() / 1000) // Current time in seconds
      }
    });
    
    // Return a success response
    return json({ allow: true });
  } catch (error) {
    console.error('Error managing incident lock:', error);
    return json({ error: 'Failed to manage incident lock' }, { status: 500 });
  }
}