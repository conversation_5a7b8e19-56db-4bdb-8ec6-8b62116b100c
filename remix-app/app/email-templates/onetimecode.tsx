import React from "react";

export function OneTime({ code }: { code: number }) {
  return (
    <html>
    <head>
      <meta httpEquiv={"Content-Type"} content={"text/html; charset=utf-8"} />
      <meta name={"viewport"} content={"width=device-width, initial-scale=1.0"} />
      <title>Gun Violence Archive Password Reset</title>

      <style type="text/css" dangerouslySetInnerHTML={{
        __html: `
    body, table, td, a { font-family: Arial, Helvetica, sans-serif !important; }
    `,
      }} />
    </head>

    <body  style={{fontFamily: "Helvetica, Arial, sans-serif", margin: "0px", padding: "0px", backgroundColor: "#ffffff"}}>
    <table role="presentation"
           style={{width: "100%", borderCollapse: "collapse", border: "0px", borderSpacing: "0px", fontFamily: "Arial, Helvetica, sans-serif", backgroundColor: "rgb(239, 239, 239)"}}>
      <tbody>
      <tr>
        <td align="center" style={{padding: "1rem 2rem", verticalAlign: "top", width: "100%"}}>
          <table role="presentation" style={{maxWidth: "600px", borderCollapse: "collapse", border: "0px", borderSpacing: "0px", textAlign: "left"}}>
            <tbody>
            <tr>
              <td style={{padding: "40px 0px 0px"}}>
                <div style={{textAlign: "left"}}>
                  <div style={{paddingBottom: "20px"}}><img src="https://www.gunviolencearchive.org/sites/all/themes/gva_theme/images/new-logo.jpg" alt="Gun Violence Archive" style={{width: "56px"}} /></div>
                </div>
                <div style={{padding: "20px", backgroundColor: "rgb(255, 255, 255)"}}>
                  <div style={{color: "rgb(0, 0, 0)", textAlign: "left"}}>
                    <h1 style={{margin: "1rem 0"}}>Verification code</h1>
                    <p style={{paddingBottom: "16px"}}>Please use the verification code below to sign in.</p>
                    <p style={{paddingBottom: "16px"}}><strong style={{fontSize: "130%"}}>{code}</strong></p>
                    <p style={{paddingBottom: "16px"}}>If you didn’t request this, you can ignore this email.</p>
                    <p style={{paddingBottom: "16px"}}>Thanks,<br/>Gun Violence Archive</p>
                  </div>
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </td>
      </tr>
      </tbody>
    </table>
    </body>
    </html>
  );
}
