import IncidentTypeToll from '../../IncidentTypeToll.server';

export default class PerpetratorShotKilledOldToll extends IncidentTypeToll {
	protected getIncidentType() {
		return ['officer involved shooting - subject/suspect/perpetrator shot', 'officer involved shooting - subject/suspect/perpetrator killed'];
	}

	public getReportLinkBase() {
		return 'subject-suspect-perpetrator-shot-killed';
	}

	public getLabel() {
		return 'Officer Involved Incident</br>Subject-Suspect Shot or Killed <sup>2</sup>';
	}
}
