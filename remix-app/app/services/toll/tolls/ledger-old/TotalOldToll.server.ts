import TollItem from '../../TollItem.server';
import { db as prisma } from '~/utils/db.server';
import { QueryBuilder } from "~/services/query/QueryBuilder.server";

export default class TotalOldToll extends TollItem {
	public getLabel() {
		return 'Total Number of Incidents';
	}

	public getReportLinkBase() {
		return 'total-number-of-incidents';
	}

	public getQuery(qb: QueryBuilder) {
		const clname = this.constructor.name;
		// qb.select('inc.incident_id as ' + clname);

		qb.addFilteredAggregation({
			function: 'COUNT',
			field: 'inc.incident_id',
			filter: [],
			alias: clname,
			distinct: true
		});

		return clname;
	}
}
