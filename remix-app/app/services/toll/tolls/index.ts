import AccidentalShootingToll from './ledger/AccidentalShootingToll.server';
import DefensiveUseToll from './ledger/DefensiveUseToll.server';
import HomeInvasionOldToll from './ledger-old/HomeInvasionOldToll.server';
import Ho<PERSON><PERSON>Toll from './ledger/HomicidesToll.server';
import MassMurderToll from './ledger/MassMurderToll.server';
import MassShootingToll from './ledger/MassShootingToll.server';
import MurderSuicideToll from './ledger/MurderSuicideToll.server';
import NumberOfChildrenInjuredToll from './ledger/NumberOfChildrenInjuredToll.server';
import NumberOfChildrenKilledInjuredOldToll from './ledger-old/NumberOfChildrenKilledInjuredOldToll.server';
import NumberOfChildrenKilledToll from './ledger/NumberOfChildrenKilledToll.server';
import NumberOfDeathsOldToll from './ledger-old/NumberOfDeathsOldToll.server';
import NumberOfInjuriesOldToll from './ledger-old/NumberOfInjuriesOldToll.server';
import NumberOfInjuriesToll from './ledger/NumberOfInjuriesToll.server';
import NumberOfTeensInjuredToll from './ledger/NumberOfTeensInjuredToll.server';
import NumberOfTeensKilledInjuredOldToll from './ledger-old/NumberOfTeensKilledInjuredOldToll.server';
import NumberOfTeensKilledToll from './ledger/NumberOfTeensKilledToll.server';
import OfficerInvolvedIncidentToll from './ledger/OfficerInvolvedIncidentToll.server';
import OfficerInvolvedPerpetratorToll from './ledger/OfficerInvolvedPerpetratorToll.server';
import OfficerShotKilledOldToll from './ledger-old/OfficerShotKilledOldToll.server';
import OfficerShotKilledToll from './ledger/OfficerShotKilledToll.server';
import PerpetratorShotKilledOldToll from './ledger-old/PerpetratorShotKilledOldToll.server';
import PerpetratorShotKilledToll from './ledger/PerpetratorShotKilledToll.server';
import SuicidesToll from './ledger/SuicidesToll.server';
import TotalDeathsToll from './ledger/TotalDeathsToll.server';
import TotalOldToll from './ledger-old/TotalOldToll.server';
import type TollItem from '../TollItem.server';
import { type TollYearRange } from '../TollItem.server';
import MurderSuicidesToll from "~/services/toll/tolls/review/MurderSuicidesToll.server";
import DefensiveGunUseToll from "~/services/toll/tolls/review/DefensiveGunUseToll.server";
import InjuriesToll from "~/services/toll/tolls/review/InjuriesToll.server";
import DeathsToll from "~/services/toll/tolls/review/DeathsToll.server";
import ChildrenKilledToll from "~/services/toll/tolls/review/ChildrenKilledToll.server";
import ChildrenInjuredToll from "~/services/toll/tolls/review/ChildrenInjuredToll.server";
import TeensInjuredToll from "~/services/toll/tolls/review/TeensInjuredToll.server";
import TeensKilledToll from "~/services/toll/tolls/review/TeensKilledToll.server";
import OisOfficerInjuredToll from "~/services/toll/tolls/review/OisOfficerInjured.server";
import OisOfficerKilledToll from "~/services/toll/tolls/review/OisOfficerKilled.server";
import OisSuspectKilledToll from "~/services/toll/tolls/review/OisSuspectKilled.server";
import OisSuspectInjuredToll from "~/services/toll/tolls/review/OisSuspectInjured.server";

export type TollItemConstructor = new (
    year_range: TollYearRange,
    state?: string,
    state_tid?: number,
    congressional_district?: string
) => TollItem;

export const tollClasses: { [name: string]: TollItemConstructor } = {
    AccidentalShooting: AccidentalShootingToll,
    DefensiveUse: DefensiveUseToll,
    HomeInvasionOld: HomeInvasionOldToll,
    Homicides: HomicidesToll,
    MassMurder: MassMurderToll,
    MassShooting: MassShootingToll,
    MurderSuicide: MurderSuicideToll,
    NumberOfChildrenInjured: NumberOfChildrenInjuredToll,
    NumberOfChildrenKilledInjuredOld: NumberOfChildrenKilledInjuredOldToll,
    NumberOfChildrenKilled: NumberOfChildrenKilledToll,
    NumberOfDeathsOld: NumberOfDeathsOldToll,
    NumberOfInjuriesOld: NumberOfInjuriesOldToll,
    NumberOfInjuries: NumberOfInjuriesToll,
    NumberOfTeensInjured: NumberOfTeensInjuredToll,
    NumberOfTeensKilledInjuredOld: NumberOfTeensKilledInjuredOldToll,
    NumberOfTeensKilled: NumberOfTeensKilledToll,
    OfficerInvolvedIncident: OfficerInvolvedIncidentToll,
    OfficerInvolvedPerpetrator: OfficerInvolvedPerpetratorToll,
    OfficerShotKilledOld: OfficerShotKilledOldToll,
    OfficerShotKilled: OfficerShotKilledToll,
    PerpetratorShotKilledOld: PerpetratorShotKilledOldToll,
    PerpetratorShotKilled: PerpetratorShotKilledToll,
    // @ts-ignore
    Suicides: SuicidesToll,
    TotalDeaths: TotalDeathsToll,
    TotalOld: TotalOldToll
};

export const reviewClasses: { [name: string]: TollItemConstructor } = {
    AccidentalShooting: AccidentalShootingToll,
    DefensiveGunUse: DefensiveGunUseToll,
    MassMurder: MassMurderToll,
    MassShooting: MassShootingToll,
    MurderSuicides: MurderSuicidesToll,
    ChildrenInjured: ChildrenInjuredToll,
    ChildrenKilled: ChildrenKilledToll,
    Deaths: DeathsToll,
    Injuries: InjuriesToll,
    TeensInjured: TeensInjuredToll,
    TeensKilled: TeensKilledToll,
    OisOfficerInjured: OisOfficerInjuredToll,
    OisOfficerKilled: OisOfficerKilledToll,
    OisSuspectKilled: OisSuspectKilledToll,
    OisSuspectInjured: OisSuspectInjuredToll
};
