import IncidentTypeToll from '../../IncidentTypeToll.server';

export default class OfficerInvolvedPerpetratorToll extends IncidentTypeToll {
	protected getIncidentType() {
		return ['officer involved shooting - subject/suspect/perpetrator killed'];
	}

	public getReportLinkBase() {
		return 'subject-suspect-killed';
	}

	public getLabel() {
		return 'Officer Involved Incident <sup>1</sup>';
	}

	public getValueLabel() {
		return 'Killed';
	}
}
