import TollItem from '../../TollItem.server';
import TollItemFactory from '../../TollItemFactory.server';
import { type QueryBuilder } from '~/services/query/QueryBuilder.server';

export default class TotalDeathsToll extends TollItem {
	public getLabel() {
		return 'Total Number of GV Deaths - ALL Causes<sup>4</sup>';
	}

	public getReportLinkBase() {
		return 'number-of-gun-deaths';
	}

	public async getQuery(qb: QueryBuilder) {
		const statusTid = await qb.getTaxonomyItem('participant_statuses', 'killed');
		const clname = this.constructor.name;

		const iJoin = qb.join('gva_data.incident_participants', 'ip.incident_id = inc.incident_id', 'left', 'ip');

		qb.addFilteredAggregation({
			function: 'COUNT',
			field: 'DISTINCT ' + iJoin + '.participant_id',
			filter: [
				{ column: "CONCAT(',', " + iJoin + '.participant_status_tid' + ", ',')", operator: 'contains', value: `,${statusTid},` }
			],
			alias: clname
		});

		return clname;
	}

	public async hookValue(value: number) {
		const factory = new TollItemFactory('toll');

		// Check if year_range is defined
		if (!this.year_range) {
			return Number(value);
		}

		const toll_item = factory.factory(
			'Suicides',
			this.year_range,
			this.state,
			this.state_tid,
			this.congressional_district
		);
		const suicides = toll_item.getRawValue();

		return Number(value) + suicides;
	}
}
