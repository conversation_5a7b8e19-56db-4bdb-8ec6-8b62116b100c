import _ from 'lodash';
import moment from '~/utils/moment';
import TollItem from '../../TollItem.server';
import { db as prisma } from '~/utils/db.server';

export default class SuicidesToll extends TollItem {
	public getLabel() {
		return 'Suicide <sup>3</sup>';
	}

	public getReportLinkBase() {
		return '';
	}

	public getQuery() {
		return;
	}

	public getRawValue(): number {
		let suicides = 0;
		if (
			!_.isUndefined(this.year_range) &&
			this.year_range &&
			moment.unix(this.year_range['start']).year() !== moment().year()
		) {
			// $year_start = new \DateTime();
			// $year_start->setTimestamp($this->year_range['start']);
			let year_start = moment.unix(this.year_range['start']);
			// $year_end = new \DateTime();
			// $year_end->setTimestamp($this->year_range['end']);
			// $year_end->modify('-1 day');
			let year_end = moment.unix(this.year_range['end']);
			year_end.subtract(1, 'day');

			if (year_end.year() == moment().year()) {
				// $yNow = new \DateTime();
				// $yNow->setTimestamp(time());
				// $yNow->modify('+1 day');
				let yNow = moment();
				yNow.add(1, 'day');

				// $diff = $year_start->diff($yNow);
				// $days = $diff->format('%a');
				const days = year_start.diff(yNow, 'days');

				suicides = 66 * Math.abs(days);
			} else {
				// $year_end->modify('+2 day');
				year_end.add(2, 'days');

				// $diff = $year_start->diff($year_end);
				// $days = $diff->format('%a');
				const days = year_start.diff(year_end, 'days');

				suicides = 66 * Math.abs(days);
			}
		} else {
			// $suicides = 66 * (date('z') + 1);
			suicides = 66 * moment().dayOfYear();
		}

		return suicides;
	}

	public getParentItem() {
		return 'TotalDeaths';
	}

	public getLinkOverride() {
		// TODO
		// const link = variable_get('suicides_link', '');
		const link = '';

		if (link) return link;

		return false;
	}
}
