import TollItem from '../../TollItem.server';
import { type QueryBuilder } from '~/services/query/QueryBuilder.server';

export default class NumberOfChildrenKilledToll extends TollItem {
	public getLabel() {
		return 'Number of Children (age 0-11)<sup>1</sup>';
	}

	public getReportLinkBase() {
		return 'child-killed';
	}

	public async getQuery(qb: QueryBuilder) {
		const statusTid = await qb.getTaxonomyItem('participant_statuses', 'killed');
		const ageGroupTid = await qb.getTaxonomyItem('age_groups', 'child 0-11', 'integer');
		const clname = this.constructor.name;

		const iJoin = qb.join('gva_data.incident_participants', 'ip.incident_id = inc.incident_id', 'left', 'ip');

		qb.addFilteredAggregation({
			function: 'COUNT',
			field: 'DISTINCT ' + iJoin + '.participant_id',
			filter: [
				{ column: "CONCAT(',', " + iJoin + '.participant_status_tid' + ", ',')", operator: 'contains', value: `,${statusTid},` },
				{
					conditions: [
						{ column: iJoin + '.age_group_tid', operator: 'equals', value: ageGroupTid },
						{
							conditions: [
								{ column: iJoin + '.age', operator: 'gte', value: 0 },
								{ column: iJoin + '.age', operator: 'lte', value: 11 }
							],
							boolean: 'AND'
						}
					],
					boolean: 'OR'
				}
			],
			filterBoolean: 'AND',
			alias: clname
		});

		return clname;
	}

	public getValueLabel() {
		return 'Killed';
	}
}
