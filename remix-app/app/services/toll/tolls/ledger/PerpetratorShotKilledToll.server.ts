import IncidentTypeToll from '../../IncidentTypeToll.server';

export default class PerpetratorShotKilledToll extends IncidentTypeToll {
	protected getIncidentType() {
		return ['officer involved shooting - subject/suspect/perpetrator shot'];
	}

	public getReportLinkBase() {
		return 'subject-suspect-shot';
	}

	public getLabel() {
		return 'Subject-Suspect Killed or Injured';
	}

	public getParentItem() {
		return 'OfficerInvolvedPerpetrator';
	}

	public getValueLabel() {
		return 'Injured';
	}
}
