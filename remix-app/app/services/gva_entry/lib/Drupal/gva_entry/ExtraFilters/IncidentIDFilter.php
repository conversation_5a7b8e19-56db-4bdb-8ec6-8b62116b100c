<?php

namespace Drupal\gva_entry\ExtraFilters;

use Drupal\gva_entry\QueryFilterable;

class IncidentIDFilter extends ExtraFilter
{

	use QueryFilterable;

	public function filter(\SelectQueryInterface &$query, \QueryConditionInterface &$condition,
		\QueryConditionInterface &$havingCondition, $comparator, array $arguments)
	{
		$compare = $comparator == 'is' ? '=' : '!=';
		$condition->condition('n.nid', $arguments['value'], $compare);
	}

	public function access()
	{
		return user_access('view restricted filters');
	}

	public function filterForm($id, array $arguments, $comparator)
	{
		return array(
			'value' => array(
				'#type' => 'textfield',
				'#element_validate' => array('element_validate_integer_positive'),
				'#size' => 10,
				'#default_value' => 0,
				'#title' => t('Incident ID'),
				'#title_display' => 'invisible',
			),
			'help-tip' => $this->getHelpTip(),
		);
	}

	public function validateFilter($comparator, array $arguments)
	{
		if (!array_key_exists('value', $arguments)) return false;
		if (!is_numeric($arguments['value'])) return false;
		if ($arguments['value'] < 0) return false;

		return true;
	}

	public function comparators($id, array $arguments)
	{
		return array(
			'is',
			'is not',
		);
	}

	public function getShortName()
	{
		return gva_entry_class_from_full(get_called_class(), 'Filter');
	}

}
