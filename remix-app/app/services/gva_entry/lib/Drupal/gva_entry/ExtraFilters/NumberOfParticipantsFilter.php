<?php

namespace Drupal\gva_entry\ExtraFilters;

use Drupal\gva_entry\QueryFilterable;

class NumberOfParticipantsFilter extends ExtraFilter
{

	use QueryFilterable;

	public function filter(\SelectQueryInterface &$query, \QueryConditionInterface &$condition,
		\QueryConditionInterface &$havingCondition, $comparator, array $arguments)
	{
		$operator = self::translateComparator($comparator);
		if (!$operator) return;

		self::addJoinToQuery($query, 'left', 'field_data_field_participants', 'participants_having', 'participants_having.entity_id = n.nid');
		$query->addExpression('count(distinct participants_having.field_participants_target_id)', 'participants_count');
		$havingCondition->condition('participants_count', $arguments['value'], $operator);
	}

	public function filterForm($id, array $arguments, $comparator)
	{
		return array(
			'value' => array(
				'#type' => 'textfield',
				'#element_validate' => array('element_validate_integer_positive_or_zero'),
				'#size' => 10,
				'#default_value' => 0,
				'#title' => t('Number of Participants'),
				'#title_display' => 'invisible',
			),
			'help-tip' => $this->getHelpTip(),
		);
	}

	public function comparators($id, array $arguments)
	{
		return array(
			'is greater than',
			'is less than',
			'is equal to',
			'is not equal to',
		);
	}

	public function validateFilter($comparator, array $arguments)
	{
		if (!array_key_exists('value', $arguments)) return false;
		if (!is_numeric($arguments['value'])) return false;
		if ($arguments['value'] < 0) return false;

		return true;
	}

	public function getShortName()
	{
		return gva_entry_class_from_full(get_called_class(), 'Filter');
	}
}
