<?php

namespace Drupal\gva_entry\ExtraFilters;

use Drupal\gva_entry\QueryFilterable;
use AbleCore\TaxonomyTerm;


class ParticipantsTypeFilter extends ExtraFilter
{
	use QueryFilterable;

	public function filter(\SelectQueryInterface &$query, \QueryConditionInterface &$condition,
	                       \QueryConditionInterface &$havingCondition, $comparator, array $arguments)
	{
		$field = trim($arguments['value']);

		self::addJoinToQuery($query, 'left', 'field_data_field_participants', 'participants', 'participants.entity_id = n.nid');
		self::addJoinToQuery($query, 'left', 'field_data_field_participant_type', 'participant_types', 'participant_types.entity_id = participants.field_participants_target_id');
		if ($comparator == 'contains') {
			$condition->condition('participant_types.field_participant_type_value', $field, '=');
		} elseif ($comparator == 'does not contain') {
			$condition->condition(db_or()->condition('participant_types.field_participant_type_value', $field, '!=')->isNull('participant_types.field_participant_type_value'));
		}
	}

	public function filterForm($id, array $arguments, $comparator)
	{
		$form = array(
			'value' => array(
				'#type' => 'select',
				'#attributes' => array('class' => array('fancy', 'participant-type')),
				'#theme_wrappers' => array('gva_form_element'),
				'#options' => gva_general_incident_get_participant_types(),	
			),
			'help-tip' => $this->getHelpTip(),
		);

		return $form;
	}

	public function comparators($id, array $arguments)
	{
		return array(
			'contains',
			'does not contain',
		);
	}

	public function getShortName()
	{
		return gva_entry_class_from_full(get_called_class(), 'Filter');
	}

}
