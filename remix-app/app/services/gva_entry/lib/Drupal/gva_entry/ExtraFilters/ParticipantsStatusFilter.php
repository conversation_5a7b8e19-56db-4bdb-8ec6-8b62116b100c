<?php

namespace Drupal\gva_entry\ExtraFilters;

use Drupal\gva_entry\QueryFilterable;

class ParticipantsStatusFilter extends ExtraFilter
{
	use QueryFilterable;

	public function filter(\SelectQueryInterface &$query, \QueryConditionInterface &$condition,
		\QueryConditionInterface &$havingCondition, $comparator, array $arguments)
	{
		$field = trim($arguments['value']);

		self::addJoinToQuery($query, 'left', 'field_data_field_participants', 'participants', 'participants.entity_id = n.nid');
		self::addJoinToQuery($query, 'left', 'field_data_field_participant_status', 'participant_status', 'participant_status.entity_id = participants.field_participants_target_id');
		if ($comparator == 'contains') {
			$condition->condition('participant_status.field_participant_status_tid', $field , '=');
		} elseif ($comparator == 'does not contain') {
			$query->addField('participant_status', 'field_participant_status_tid');
			$or = db_or();
			$or->where("concat(group_concat(DISTINCT participant_status.field_participant_status_tid SEPARATOR ','), ',') NOT LIKE '%$field%'");
			$or->isNull('participant_status.field_participant_status_tid');
			$havingCondition->condition($or);
		}
	}

	public function filterForm($id, array $arguments, $comparator)
	{
		$form = array(
			'value' => array(
				'#type' => 'select',
				'#attributes' => array('class' => array('fancy')),
				'#theme_wrappers' => array('gva_form_element'),
				'#options' => gva_general_participant_get_statuses(),
			),
			'help-tip' => $this->getHelpTip(),
		);

		return $form;
	}

	public function comparators($id, array $arguments)
	{
		return array(
			'contains',
			'does not contain',
		);
	}

	public function getShortName()
	{
		return gva_entry_class_from_full(get_called_class(), 'Filter');
	}

}
