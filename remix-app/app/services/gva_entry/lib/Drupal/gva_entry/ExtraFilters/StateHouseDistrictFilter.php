<?php

namespace Drupal\gva_entry\ExtraFilters;

use Drupal\gva_entry\QueryFilterable;


class StateHouseDistrictFilter extends ExtraFilter
{
	use QueryFilterable;

	public function filter(\SelectQueryInterface &$query, \QueryConditionInterface &$condition,
	                       \QueryConditionInterface &$havingCondition, $comparator, array $arguments)
	{
		$field = trim($arguments['value']);

		self::addJoinToQuery($query, 'left', 'field_data_field_state_house_district', 'statehousedistrict', 'statehousedistrict.entity_id = n.nid');
		if ($comparator == 'is in') {
			$condition->condition('statehousedistrict.field_state_house_district_value', $field, '=');
		} elseif ($comparator == 'is not in') {
			$condition->condition(db_or()->condition('statehousedistrict.field_state_house_district_value', $field, '!=')->isNull('statehousedistrict.field_state_house_district_value'));
		}
	}

	public function filterForm($id, array $arguments, $comparator)
	{
		$result = null;
		$cache = cache_get('gva_state_house_districts');
		if ($cache && $cache->data) {
			$result = $cache->data;
		} else {
			$query = db_select('field_data_field_state_house_district', 'd');
			$query->fields('d', array('field_state_house_district_value'));
			$query->distinct();
			$query->isNotNull('d.field_state_house_district_value');
			$query->orderBy('field_state_house_district_value');
			$result = $query->execute()->fetchAllKeyed(0, 0);

			cache_set('gva_state_house_districts', $result);
		}

		$form = array(
			'value' => array(
				'#type' => 'select',
				'#attributes' => array('class' => array('fancy', 'state-house-district')),
				'#theme_wrappers' => array('gva_form_element'),
				'#options' => $result,
			),
			'help-tip' => $this->getHelpTip(),
		);

		return $form;
	}

	public function comparators($id, array $arguments)
	{
		return array(
			'is in',
			'is not in',
		);
	}

	public function getShortName()
	{
		return gva_entry_class_from_full(get_called_class(), 'Filter');
	}

}
