<?php

namespace Drupal\gva_entry\QueryExporters;

use <PERSON>upal\gva_entry\QueryBuilder;
use FlorianWolters\Component\Util\Singleton\SingletonTrait;

class QueryExporterFactory {

	use SingletonTrait;

	/**
	 * Cache for storing all query exporters.
	 * @var array
	 */
	protected $all = array();

	/**
	 * @param string       $type       The type of exporter to get.
	 * @param QueryBuilder $builder    The query builder.
	 * @param array        $year_range The year or range of years to filter by.
	 * @param string       $state      The state to filter by.
	 * @param string       $district   The district to filter by.
	 * @param string       $characteristic   The characteristic to filter by.
	 *
	 * @return QueryExporter
	 * @throws QueryExporterFactoryException
	 */
	public function factory($type, QueryBuilder $builder, $options = array())
	{
		//($type, QueryBuilder $builder, $year_range = null, $state = null, $district = null, $characteristic = null)
		$prefixes = array_merge(array('Drupal\\gva_entry\\QueryExporters\\'),
			module_invoke_all('gva_entry_exporter_prefixes'));

		$suffix = 'Exporter';

		$candidates = array();
		foreach ($prefixes as $prefix) {
			$candidates[] = $prefix . $type . $suffix;
			$candidates[] = $prefix . strtoupper($type) . $suffix;
		}
		$class_name = false;

		foreach ($candidates as $candidate) {
			if (class_exists($candidate) && is_subclass_of($candidate, 'Drupal\\gva_entry\\QueryExporters\\QueryExporter')) {
				$class_name = $candidate;
				break;
			}
		}

		if (!$class_name) {
			throw new QueryExporterFactoryException('The export type ' . $type . ' does not exist.');
		}

		// Check to see if the class is abstract.
		$reflect = new \ReflectionClass($class_name);
		if ($reflect->isAbstract()) {
			throw new QueryExporterFactoryException('The export type ' . $type . ' is invalid.');
		}

		// Create a new instance of the class and return it.
		$instance = new $class_name($builder, $options);
		return $instance;
	}

	/**
	 * Gets all query exporters.
	 *
	 * @return array
	 */
	public function all($columnForm = false)
	{
		if (empty($this->all)) {
			$this->all = array();
			// Create a dummy builder.
			$builder = new QueryBuilder();
			foreach (glob(__DIR__ . '/*Exporter.php') as $filename) {
				$segments = explode('/', $filename);
				$class_name = str_replace('Exporter.php', '', end($segments));
				if ($class_name != 'Query') {
					// Try to instantiate it. If we get an error, don't add it.
					try {
						$exp = QueryExporterFactory::getInstance()
							->factory($class_name, $builder);
					} catch (\Exception $ex) {
						continue;
					}
					if (!$exp->hidden() && !$columnForm)
            $this->all[] = $class_name;
          if ($columnForm && !$exp->columnConfigHidden() && !$exp->hidden())
            $this->all[] = $class_name;

				}
			}
			$this->all += module_invoke_all('gva_entry_exporters');
		}

		return $this->all;
	}

}

class QueryExporterFactoryException extends \Exception {}
