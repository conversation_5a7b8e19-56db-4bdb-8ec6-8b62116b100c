<?php

namespace Drupal\gva_entry\QueryExporters;

use Drupal\gva_entry\Columns\Column;
use TableSort;

class TableExporter extends QueryExporter {

	/**
	 * A list of columns that support sorting and the fields
	 * they should be sorted on.
	 * @var array
	 */
	protected $sort_fields = array();

	/**
	 * Contains the new header (the table exporter overwrites the general
	 * exporter header).
	 * @var array
	 */
	protected $header = array();

	public function export()
	{
		$results = $this->execute();
		$columns = $this->getColumns($this->builder);
		$data = $this->getTabularData($results, $columns);

		// Prepare the table.
		return array(
			'#theme' => 'table',
			'#header' => $this->header,
			'#rows' => $data['rows'],
			'#prefix' => '<section class="row"><div class="small-12 columns">',
			'#suffix' => '</div></section>',
			'#empty' => t('There are currently no incidents available.'),
			'#attributes' => array('class' => array('responsive')),
		);
	}

	protected function alterSortingQuery(\SelectQueryInterface &$sorting_query)
	{
		// Get the columns.
		$header = array();
		$columns = $this->getColumns($this->builder);
		foreach ($columns as $column) {
			/** @var Column $column */
			/*
			 * Create a bogus query because we don't actually do any of the
			 * query modifications unless the column has been marked as the
			 * active sort.
			 */
			$bogus_query = db_select('node', 'n');
			$sort_field = $column->getSortField($bogus_query);
			$header[$column::name()] = array(
				'data' => $column->label(),
				'column' => $column::name(),
			);
			if ($sort_field) {
				$header[$column::name()]['field'] = $sort_field;
			}
		}

		// Sort by the date field by default, otherwise sort by the first column.
		if (array_key_exists('Base.IncidentDate', $header)) {
			$header['Base.IncidentDate']['sort'] = 'desc';
		} else {
			$keys = array_keys($header);
			if (count($keys) <= 0) {
				return; // No results anyway, who gives a damn how they're sorted.
			} else {
				$header[reset($keys)]['sort'] = 'asc';
			}
		}

		/** @var TableSort $sorting_query */
		$sorting_query = $sorting_query->extend('Drupal\\gva_entry\\QueryExtenders\\ColumnSort');
		$sorting_query->orderByHeader($header);

		// Set the header.
		$this->header = $header;
	}

	public function requiresPlainText()
	{
		return false;
	}

}
