<?php

namespace Drupal\gva_entry\QueryExporters;

use Drupal\gva_entry\Columns\Column;
use Drupal\gva_entry\EntryFields\EntryFieldFactory;
use TableSort;

class StatsExporter extends QueryExporter {

  /**
   * A list of columns that support sorting and the fields
   * they should be sorted on.
   * @var array
   */
  protected $sort_fields = array();

  /**
   * Contains the new header (the table exporter overwrites the general
   * exporter header).
   * @var array
   */
  protected $header = array();

  /**
   * Sort results array, grouping incidents by their year, month, etc
   *
   * @param $arr
   * @param string $dateVar
   *
   * @return array
   */
  private function cleanSort($arr, $dateVar = 'Y') {
    $outArr = array();

    $g = array_map(function($v) use ($dateVar) {
      return date($dateVar, $v['incident_date']);
    }, $arr);

    $years = array_values(array_unique($g));

    foreach ($years as $yr) {
      $items = array_flip(array_keys($g, $yr, true));
      $outArr[$yr] = array_intersect_key($arr, $items);
    }

    //free php memory
    unset($g);
    unset($items);
    unset($years);

    return $outArr;
  }
  public function export()
  {
    $columns = \Drupal\gva_entry\Columns\Templates\ColumnTemplate::create()
      ->addColumns(array('Base.IncidentID', 'Base.IncidentDate', 'Base.MassShooting', 'Counts.NumberOfChildrenKilled', 'Counts.NumberOfChildrenInjured', 'Location.State', 'Counts.NumberOfVictimsKilled', 'Counts.NumberOfVictimsInjured', 'Counts.NumberOfPerpertratorsKilled', 'Counts.NumberOfPerpertratorsInjured', 'Counts.NumberOfPerpertratorsArrested'));
    $sorted_columns = $this->getSortedColumns($columns->columns());
    $scolumns = $this->getColumnInstances($sorted_columns);


    $results = $this->execute(false, false, $scolumns);
    $typeToTitle = EntryFieldFactory::getAllTypes();

    //$columns = $this->getColumns($this->builder);
    //$data = $this->getTabularData($results, $columns);

    $outY = $this->cleanSort($results);
    ksort($outY);

    unset($results);

$jsonData = array();

foreach ($outY as $key => $ns) {
  $outM = $this->cleanSort($ns, 'F');

  $dataRunM = array();

  foreach ($outM as $keyM => $nsM) {
    $outD = $this->cleanSort($nsM, 'j');
    $dataRunD = array();

    foreach ($outD as $keyD => $nsD) {
      $outH = $this->cleanSort($nsD, 'G');
      $dataRunH = array();

      foreach ($outH as $keyH => $nsH) {
//        $detailHr = array_reduce($nsH, function($detailH, $itemH) {
//          $detailH['v_killed'] += $itemH['number_victims_killed'];
//          $detailH['v_injured'] += $itemH['number_victims_injured'];
//          $detailH['p_killed'] += $itemH['number_perpetrators_killed'];
//          $detailH['p_injured'] += $itemH['number_perpetrators_injured'];
//          $detailH['p_arrested'] += $itemH['number_perpetrators_arrested'];
//
//          $detailH['states'][$itemH['state']]['state'] = $itemH['state'];
//          $detailH['states'][$itemH['state']]['count']++;
//          $detailH['states'][$itemH['state']]['v_killed'] += $itemH['number_victims_killed'];
//          $detailH['states'][$itemH['state']]['v_injured'] += $itemH['number_victims_injured'];
//          $detailH['states'][$itemH['state']]['p_killed'] += $itemH['number_perpetrators_killed'];
//          $detailH['states'][$itemH['state']]['p_injured'] += $itemH['number_perpetrators_injured'];
//          $detailH['states'][$itemH['state']]['p_arrested'] += $itemH['number_perpetrators_arrested'];
//
//          return $detailH;
//        }, array());
//
//        ksort($detailHr['states']);
//        $statesH = array_values($detailHr['states']);

        $statesH = array_count_values(array_map(function($vv) { return $vv['state']; }, $nsH));
        ksort($statesH);
        $statesH = array_map(function ($key, $value) { return array( 'state' => $key, 'count' => $value ); }, array_keys($statesH), $statesH);

        $dataRunH[$keyH] = [
          'hour' => $keyH,
          'count' => count($nsH),
          'states' => $statesH
        ];

        //unset($detailHr);
        unset($statesH);
      }

      $parseDay = date('l', strtotime($keyM . ' ' . $keyD . ' ' . $key));
      $rawDate = strtotime($keyM . ' ' . $keyD );
      ksort($dataRunH);

//      $detailDr = array_reduce($nsD, function($detailD, $itemD) {
//        $detailD['v_killed'] += $itemD['number_victims_killed'];
//        $detailD['v_injured'] += $itemD['number_victims_injured'];
//        $detailD['p_killed'] += $itemD['number_perpetrators_killed'];
//        $detailD['p_injured'] += $itemD['number_perpetrators_injured'];
//        $detailD['p_arrested'] += $itemD['number_perpetrators_arrested'];
//
//        $detailD['states'][$itemD['state']]['state'] = $itemD['state'];
//        $detailD['states'][$itemD['state']]['count']++;
//        $detailD['states'][$itemD['state']]['v_killed'] += $itemD['number_victims_killed'];
//        $detailD['states'][$itemD['state']]['v_injured'] += $itemD['number_victims_injured'];
//        $detailD['states'][$itemD['state']]['p_killed'] += $itemD['number_perpetrators_killed'];
//        $detailD['states'][$itemD['state']]['p_injured'] += $itemD['number_perpetrators_injured'];
//        $detailD['states'][$itemD['state']]['p_arrested'] += $itemD['number_perpetrators_arrested'];
//
//        return $detailD;
//      }, array());
//
//      ksort($detailDr['states']);
//      $statesD = array_values($detailDr['states']);

      $statesD = array_count_values(array_map(function($vv) { return $vv['state']; }, $nsD));
      ksort($statesD);
      $statesD = array_map(function ($key, $value) { return array( 'state' => $key, 'count' => $value ); }, array_keys($statesD), $statesD);

      $dataRunD[$keyD] = [
        'day' => $keyD,
        'count' => count($nsD),
        'label' => $parseDay,
        'rawDate' => $rawDate,
        'states' => $statesD,
        'hours' => array_values($dataRunH)
      ];

      //unset($detailDr);
      unset($outH);
      unset($nsH);
      unset($statesD);
      unset($dataRunH);
    }

    $parseMonth = date_parse($keyM);
    ksort($dataRunD);

    $detailMr = array_reduce($nsM, function($detailM, $itemM) {
      if (!isset($detailM['v_injured'])) {
        $detailM['v_killed'] = 0;
        $detailM['v_injured'] = 0;
        $detailM['p_killed'] = 0;
        $detailM['p_injured'] = 0;
        $detailM['p_arrested'] = 0;
        $detailM['c_killed'] = 0;
        $detailM['c_injured'] = 0;
        $detailM['mass'] = 0;
      }
      if (!isset($detailM['states'][$itemM['state']]['state'])) {
        $detailM['states'][$itemM['state']]['state'] = '';
        $detailM['states'][$itemM['state']]['count'] = 0;
        $detailM['states'][$itemM['state']]['v_killed'] = 0;
        $detailM['states'][$itemM['state']]['v_injured'] = 0;
        $detailM['states'][$itemM['state']]['p_killed'] = 0;
        $detailM['states'][$itemM['state']]['p_injured'] = 0;
        $detailM['states'][$itemM['state']]['p_arrested'] = 0;
        $detailM['states'][$itemM['state']]['c_killed'] = 0;
        $detailM['states'][$itemM['state']]['c_injured'] = 0;
        $detailM['states'][$itemM['state']]['mass'] = 0;
      }

        $detailM['v_killed'] += $itemM['number_victims_killed'];
        $detailM['v_injured'] += $itemM['number_victims_injured'];
        $detailM['p_killed'] += $itemM['number_perpetrators_killed'];
        $detailM['p_injured'] += $itemM['number_perpetrators_injured'];
        $detailM['p_arrested'] += $itemM['number_perpetrators_arrested'];
        $detailM['c_killed'] += $itemM['number_children_killed'];
        $detailM['c_injured'] += $itemM['number_children_injured'];
        if (!empty($itemM['mass_shooting']))
          $detailM['mass']++;

        $detailM['states'][$itemM['state']]['state'] = $itemM['state'];
        $detailM['states'][$itemM['state']]['count']++;
        $detailM['states'][$itemM['state']]['v_killed'] += $itemM['number_victims_killed'];
        $detailM['states'][$itemM['state']]['v_injured'] += $itemM['number_victims_injured'];
        $detailM['states'][$itemM['state']]['p_killed'] += $itemM['number_perpetrators_killed'];
        $detailM['states'][$itemM['state']]['p_injured'] += $itemM['number_perpetrators_injured'];
        $detailM['states'][$itemM['state']]['p_arrested'] += $itemM['number_perpetrators_arrested'];
        $detailM['states'][$itemM['state']]['c_killed'] += $itemM['number_children_killed'];
        $detailM['states'][$itemM['state']]['c_injured'] += $itemM['number_children_injured'];
        if (!empty($itemM['mass_shooting']))
          $detailM['states'][$itemM['state']]['mass']++;

      return $detailM;
    }, array());

    ksort($detailMr['states']);
    $statesM = array_values($detailMr['states']);

//    $statesM = array_count_values(array_map(function($vv) { return $vv['state']; }, $nsM));
//    ksort($statesM);
//    $statesM = array_map(function ($key, $value) { return array( 'state' => $key, 'count' => $value ); }, array_keys($statesM), $statesM);

    $dataRunM[$parseMonth['month']] = [
      'month' => $keyM,
      'count' => count($nsM),
      'v_killed' => $detailMr['v_killed'],
      'v_injured' => $detailMr['v_injured'],
      'p_killed' => $detailMr['p_killed'],
      'p_injured' => $detailMr['p_injured'],
      'p_arrested' => $detailMr['p_arrested'],
      'c_killed' => $detailMr['c_killed'],
      'c_injured' => $detailMr['c_injured'],
      'mass' => $detailMr['mass'],
      'states' => $statesM,
      'days' => array_values($dataRunD)
    ];

    unset($outD);
    unset($nsD);
    unset($detailMr);
    unset($statesM);
    unset($dataRunD);
  }

  ksort($dataRunM);

  $detailr = array_reduce($ns, function($detail, $item) {
    if (!isset($detail['v_injured'])) {
      $detail['v_killed'] = 0;
      $detail['v_injured'] = 0;
      $detail['p_killed'] = 0;
      $detail['p_injured'] = 0;
      $detail['p_arrested'] = 0;
      $detail['c_killed'] = 0;
      $detail['c_injured'] = 0;
      $detail['mass'] = 0;
    }
    if (!isset($detail['states'][$item['state']]['state'])) {
      $detail['states'][$item['state']]['state'] = '';
      $detail['states'][$item['state']]['count'] = 0;
      $detail['states'][$item['state']]['v_killed'] = 0;
      $detail['states'][$item['state']]['v_injured'] = 0;
      $detail['states'][$item['state']]['p_killed'] = 0;
      $detail['states'][$item['state']]['p_injured'] = 0;
      $detail['states'][$item['state']]['p_arrested'] = 0;
      $detail['states'][$item['state']]['c_killed'] = 0;
      $detail['states'][$item['state']]['c_injured'] = 0;
      $detail['states'][$item['state']]['mass'] = 0;
    }

    $detail['v_killed'] += $item['number_victims_killed'];
    $detail['v_injured'] += $item['number_victims_injured'];
    $detail['p_killed'] += $item['number_perpetrators_killed'];
    $detail['p_injured'] += $item['number_perpetrators_injured'];
    $detail['p_arrested'] += $item['number_perpetrators_arrested'];
    $detail['c_killed'] += $item['number_children_killed'];
    $detail['c_injured'] += $item['number_children_injured'];
    if (!empty($item['mass_shooting']))
      $detail['mass']++;

    $detail['states'][$item['state']]['state'] = $item['state'];
    $detail['states'][$item['state']]['count']++;
    $detail['states'][$item['state']]['v_killed'] += $item['number_victims_killed'];
    $detail['states'][$item['state']]['v_injured'] += $item['number_victims_injured'];
    $detail['states'][$item['state']]['p_killed'] += $item['number_perpetrators_killed'];
    $detail['states'][$item['state']]['p_injured'] += $item['number_perpetrators_injured'];
    $detail['states'][$item['state']]['p_arrested'] += $item['number_perpetrators_arrested'];
    $detail['states'][$item['state']]['c_killed'] += $item['number_children_killed'];
    $detail['states'][$item['state']]['c_injured'] += $item['number_children_injured'];
    if (!empty($item['mass_shooting']))
      $detail['states'][$item['state']]['mass']++;


    return $detail;
  }, array());

  ksort($detailr['states']);
  $stateDropdownD = array_keys($detailr['states']);
  $stateDropdown = '';
  foreach ($stateDropdownD as $keyS => $state) {
    $stateDropdown .= '<option'.(($keyS === 0)? ' selected="selected"' : '').' value="'.$state.'">'.$state.'</option>';
  }
  unset($stateDropdownD);

  $states = array_values($detailr['states']);
  //$states = array_count_values(array_map(function($vv) { return $vv['state']; }, $ns));

  //$states = array_map(function ($key, $value) { return array( 'state' => $key, 'count' => $value ); }, array_keys($states), $states);
  //$states = array_map(function ($key, $value) { return array( 'state' => $key, 'count' => $value ); }, array_keys($states), $states);

  //children killed, mass shootings, children injured
  $jsonData['years'][] = array(
    'year' => $key,
    'count' => count($ns),
    'v_killed' => $detailr['v_killed'],
    'v_injured' => $detailr['v_injured'],
    'p_killed' => $detailr['p_killed'],
    'p_injured' => $detailr['p_injured'],
    'p_arrested' => $detailr['p_arrested'],
    'c_killed' => $detailr['c_killed'],
    'c_injured' => $detailr['c_injured'],
    'mass' => $detailr['mass'],
    'states' => $states,
    'months' => array_values($dataRunM)
  );

  unset($outM);
  unset($nsM);
  unset($detailr);
  unset($states);
  unset($dataRunM);
  unset($ns);
}

unset($outY);

$oOutput = "";
$yearCount = count($jsonData['years']);

foreach ($jsonData['years'] as $ind => $yearData) {
  $showChange = true;
  //first element
  if ($ind === 0) {
    $showChange = false;
  }

  //last element check
  if ($ind+1 === $yearCount) {
    $oOutput .= "<div class='small-12 large-6 columns end'>";
  } else {
    $oOutput .= "<div class='small-12 large-6 columns'>";
  }

  $oOutput .= "<h2>" . $yearData['year'] . "</h2>";
  $oOutput .= "<table><tbody>";
  $oOutput .= "<tr><td>Total Incidents: " . $yearData['count'] . ($showChange? ' (' . sprintf('%+d', $yearData['count'] - $jsonData['years'][$ind - 1]['count']) . ' change from previous year)' : '') . "</td></tr>";

  $oOutput .= "<tr><td>Total Victims Killed: " . $yearData['v_killed'] . ($showChange? ' (' . sprintf('%+d', $yearData['v_killed'] - $jsonData['years'][$ind - 1]['v_killed']) . ' change from previous year)' : '') . "</td></tr>";
  $oOutput .= "<tr><td>Total Victims Injured: " . $yearData['v_injured'] . ($showChange? ' (' . sprintf('%+d', $yearData['v_injured'] - $jsonData['years'][$ind - 1]['v_injured']) . ' change from previous year)' : '') . "</td></tr>";
  $oOutput .= "<tr><td>Total Subjects-Suspects Killed: " . $yearData['p_killed'] . ($showChange? ' (' . sprintf('%+d', $yearData['p_killed'] - $jsonData['years'][$ind - 1]['p_killed']) . ' change from previous year)' : '') . "</td></tr>";
  $oOutput .= "<tr><td>Total Subjects-Suspects Injured: " . $yearData['p_injured'] . ($showChange? ' (' . sprintf('%+d', $yearData['p_injured'] - $jsonData['years'][$ind - 1]['p_injured']) . ' change from previous year)' : '') . "</td></tr>";
  $oOutput .= "<tr><td>Total Subjects-Suspects Arrested: " . $yearData['p_arrested'] . ($showChange? ' (' . sprintf('%+d', $yearData['p_arrested'] - $jsonData['years'][$ind - 1]['p_arrested']) . ' change from previous year)' : '') . "</td></tr>";
  $oOutput .= "<tr><td>Total Children Killed: " . $yearData['c_killed'] . ($showChange? ' (' . sprintf('%+d', $yearData['c_killed'] - $jsonData['years'][$ind - 1]['c_killed']) . ' change from previous year)' : '') . "</td></tr>";
  $oOutput .= "<tr><td>Total Children Injured: " . $yearData['c_injured'] . ($showChange? ' (' . sprintf('%+d', $yearData['c_injured'] - $jsonData['years'][$ind - 1]['c_injured']) . ' change from previous year)' : '') . "</td></tr>";
  $oOutput .= "<tr><td>Total Mass Shootings: " . $yearData['mass'] . ($showChange? ' (' . sprintf('%+d', $yearData['mass'] - $jsonData['years'][$ind - 1]['mass']) . ' change from previous year)' : '') . "</td></tr>";

  $oOutput .= "</tbody></table></div>";
}

unset($yearData);

  $jsonData = json_encode($jsonData);

  $filters = $this->builder->getFilters();
    $filterOut = '';
    if (!empty($filters)) {
      $filterOut = '<div style="font-size: 18px;">';
      foreach ($filters as $filter) {
        if ($filter['type'] === 'YearToDate') {
          if ($filter['comparator'] === 'is in years to today') {
            $filterOut .= '<span>Jan 1 to today for each year between ';
          } else {
            if (!empty($filter['filter']['field']['todate']['date-from'])) {
              $mdf = explode('/', $filter['filter']['field']['todate']['date-from']);
              $df = date('F', mktime(0, 0, 0, $mdf[0], 10));
            }

            if (!empty($filter['filter']['field']['todate']['date-to'])) {
              $mdt = explode('/', $filter['filter']['field']['todate']['date-to']);
              $dt = date('F', mktime(0, 0, 0, $mdt[0], 10));
            }

            $filterOut .= '<span>' . (!empty($filter['filter']['field']['todate']['date-from'])? $df . ' ' . $mdf[1] : 'Jan 1');
            $filterOut .= ' to ';
            $filterOut .= !empty($filter['filter']['field']['todate']['date-to'])? $dt . ' ' . $mdt[1] : 'today';
            $filterOut .= ' for each year between ';
          }
          $filterOut .= $filter['filter']['field']['years']['year-from'] . ' to ' . $filter['filter']['field']['years']['year-to'];
          $filterOut .= ' </span>';
        }
      }
      $filterOut .= '</div>';
    }

  $render = array(
    'data' => array(
      '#type' => 'markup',
      '#markup' => '<script type="application/javascript">
let dataE = '. $jsonData .'
</script>',
    ),
    'intro' => array(
      '#type' => 'markup',
      '#markup' => '
<div class="callout columns">
<h2 class="align-center" style="font-size: 24px;">Showing Results for </h2>
'.$filterOut.'</div>',
      '#prefix' => '<section>',
      '#suffix' => '</section>',
    ),
    'overview' => array(
      '#type' => 'markup',
      '#markup' => '
<h2>Data Overview</h2>
<div class="callout columns">'.$oOutput.'</div>',
      '#prefix' => '<section style="clear:both;padding-top:10px;">',
      '#suffix' => '</section>',
    ),
    'charts' => array(
      '#type' => 'markup',
      '#markup' => '
<h2>Totals</h2>
<div class="small-12 large-6 columns"><canvas id="incidents"></canvas></div>
<div class="small-12 large-6 columns"><canvas id="imonths"></canvas></div>
<div class="small-12 large-12 columns end"><canvas id="imonths-d"></canvas></div>
<h2 style="padding-top: 50px; clear: both;">Mass Shootings</h2>
<div class="small-12 large-6 columns"><canvas id="mincidents"></canvas></div>
<div class="small-12 large-6 columns end"><canvas id="mmonths"></canvas></div>
<h2 style="padding-top: 50px; clear: both;">Children Killed</h2>
<div class="small-12 large-6 columns"><canvas id="ckincidents"></canvas></div>
<div class="small-12 large-6 columns end"><canvas id="ckmonths"></canvas></div>
<h2 style="padding-top: 50px; clear: both;">Children Injured</h2>
<div class="small-12 large-6 columns"><canvas id="ciincidents"></canvas></div>
<div class="small-12 large-6 columns end"><canvas id="cimonths"></canvas></div>',
      '#prefix' => '<section style="clear:both;padding-top:10px;">',
      '#suffix' => '</section>',
    ),
    'statecharts' => array(
      '#type' => 'markup',
      '#markup' => '
<h2>State specific totals</h2>
<div class="small-12 large-12 columns"><canvas id="allstates"></canvas></div>
',
      '#prefix' => '<section style="clear:both;padding-top:10px;">',
      '#suffix' => '</section>',
    )
  );
//<div class="small-12 large-12 columns end">
//<select class="form-control browser-default sdropdown" id="sdd">
//    '. $stateDropdown .'
//</select>
//<canvas id="states"></canvas>
//</div>
    unset ($jsonData);

    return $render;
  }

  protected function alterSortingQuery(\SelectQueryInterface &$sorting_query)
  {
    return;
  }

  public function requiresPlainText()
  {
    return false;
  }

  public function columnConfigHidden() {
    return true;
  }

}
