<?php

namespace Drupal\gva_entry\EntryFields;

use <PERSON>bleCore\Entity;

class RelatedField extends EntryField {

  protected function field($field_state)
  {
    return array(
      'header' => array(
        '#access' => user_access('administer gva'),
        '#type' => 'html_tag',
        '#tag' => 'h2',
        '#value' => 'Related Incidents',
      ),
      'note' => array(
        '#type' => 'html_tag',
        '#tag' => 'p',
        '#value' => t('list incident ids as comma separated list'),
      ),
      'fields' => array(
        '#type' => 'container',
        '#attributes' => array('class' => array('medium-8', 'row')),
        'field' => array(
          '#type' => 'textfield',
          '#attributes' => array(
            'maxlength' => '60',
            'class' => array('add-counter')
          ),
        ),
      ),
    );
  }

  public function getExtraInfo($value)
  {
    // TODO: Implement getExtraInfo() method.
  }

  public function submit($value, &$node, $extra)
  {
    $node->field_related = array(
      LANGUAGE_NONE => array(
        array(
          'value' => $value['fields']['field']
        )
      )
    );
  }

  protected function fillDefaults(array &$field, $value)
  {
    if( is_array($value) &&
      array_key_exists('fields', $field) &&
      array_key_exists('field', $field['fields']) &&
      array_key_exists('fields', $value)
    ) {
      $field['fields']['field']['#default_value'] = $value['fields']['field'];
    }
  }

  protected function entityToFieldState(Entity $entity)
  {
    $field_state = array();

    if ($entity->field_related) {
      $field_state['fields']['field'] = $entity->field_related->value;
    }

    return $field_state;
  }

  public function supportedFormTypes()
  {
    return array('incident');
  }
}
