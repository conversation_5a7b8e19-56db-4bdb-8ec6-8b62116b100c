<?php
/**
 * @file TitleSegmentEntryField.php
 */
namespace Drupal\gva_entry\EntryFields;

abstract class TitleSegmentEntryField extends EntryField {

	/**
	 * Given the node, returns the title segment for the current
	 * field's value.
	 *
	 * @param object $node The incident node.
	 *
	 * @return string The title of the field. An empty string if the segment
	 *                has no value.
	 */
	protected abstract function generateTitleSegment($node);

	public function titleSegment($node)
	{
		// Generate the title segment and return the result.
		return $this->generateTitleSegment($node);
	}

} 
