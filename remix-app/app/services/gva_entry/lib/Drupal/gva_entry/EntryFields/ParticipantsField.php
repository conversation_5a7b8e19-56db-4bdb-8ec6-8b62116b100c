<?php

/**
 * @file ParticipantsField.php
 */

namespace Drupal\gva_entry\EntryFields;

use AbleCore\Debug;
use AbleCore\Entity;
use AbleCore\Inflector;
use Drupal\gva_entry\EntryForm;

/**
 * The field for handling numbers of participants on an entry.
 * See ParticipantField for fields for the individual participants.
 * Class ParticipantsField
 * @package Drupal\gva_entry\EntryFields
 */
class ParticipantsField extends EntryField
{

	/**
	 * {@inheritDoc}
	 */
	protected function field($field_state)
	{
		// Prepare the render result.
		$render = array(
			'#tree' => true,
			'#prefix' => '<section id="participants" class="row">',
			'#suffix' => '</section>',
		);

		// Make sure the field state is an array (an empty one if we have no value).
		if (!is_array($field_state)) {
			$field_state = array();
		}

		$factory = EntryFieldFactory::getInstance();
		$participant_field = $factory->factory('Participant');

		// Get the number of victims and perpetrators.
		/** @var NumberOfParticipantsField $number_of_participants */
		$number_of_participants = $factory->factory('NumberOfParticipants');
		$victims = $number_of_participants->getNumberOfVictims($this->form_state);
		$perps = $number_of_participants->getNumberOfPerpetrators($this->form_state);

		// Store the values in the form state for use later.
		$this->form_state['storage']['number_of_fields']['victims'] = $victims;
		$this->form_state['storage']['number_of_fields']['perpetrators'] = $perps;

		// If the number of participants exceeds 550 for each type, show a warning message.
		$message = 'You have indicated that you want @want @type-want. There are only 550 @type-wants allowed, so it has ' .
			'been limited to 550. Please add more details in the notes section about the remaining participants.';
		if ($victims > 550) {
			drupal_set_message(t($message, array(
				'@want' => $victims,
				'@type-want' => Inflector::getInstance()->quantize($victims, 'victim'),
				'@type-wants' => 'victims',
			)), 'warning');
			$victims = 550;
		}
		if ($perps > 550) {
			drupal_set_message(t($message, array(
				'@want' => $perps,
				'@type-want' => Inflector::getInstance()->quantize($perps, 'perpetrator'),
				'@type-wants' => 'perpetrators',
			)), 'warning');
			$perps = 550;
		}

		/* Removed on 2023/03/13 by yanh
		// Check to see if a delete button was clicked.
		if (
			!empty($this->form_state['triggering_element']['#value'])
			&& $this->form_state['triggering_element']['#value'] == 'Delete'
			&& isset($this->form_state['triggering_element']['#parents'][2])
			&& $this->form_state['triggering_element']['#parents'][0] == 'Participants'
		) {
			$index = $this->form_state['triggering_element']['#parents'][2];
			if (array_key_exists($index, $field_state)) {
				// Get the type.
				switch ($field_state[$index]['field']['left-column']['type']) {
					case 'victim':
						$victims--;
						break;
					case 'perpetrator':
						$perps--;
						break;
				}

				unset($field_state[$index]);
				unset($this->form_state['values']['Participants']['field'][$index]);
				unset($this->form_state['input']['Participants']['field'][$index]);
			}
		}
		*/

		// Make sure the current number of victims and perps is currently greater than or equal to what we already have.
		$current_victims = 0;
		$current_perps = 0;
		$new_field_state = array();
		$deletable_victims = 0;
		$deletable_perps = 0;

		/* Changed on 2023/03/13 by yanh
		foreach ($field_state as $index => $participant) {

			// Check to see if this field has any values in it. If it doesn't, skip it.
			if (!$this->participantHasValues($participant)) {
				unset($this->form_state['values']['Participants']['field'][$index]);
				unset($this->form_state['input']['Participants']['field'][$index]);
				continue;
			}

			// Add it to the new field state.
			$new_field_state[$index] = $participant;

			// Increment the counters.
			switch (strtolower($participant['field']['left-column']['type'])) {
				case 'victim':
					$current_victims++;
					break;
				case 'perpetrator':
					$current_perps++;
					break;
			}
		}
		*/
		$participants_data = isset($this->form_state['input']['Participants']['field']) ? $this->form_state['input']['Participants']['field'] : $field_state;
		foreach ($participants_data as $index => $participant) {
			// Add it to the new field state.
			$new_field_state[$index] = $participant;

			$participant_type = isset($participant['field']['participant_type']) ? $participant['field']['participant_type'] : strtolower($participant['field']['left-column']['type']);
			$new_field_state[$index]['field']['left-column']['type'] = $participant_type;

			// Increment the counters.
			$deleted = 0;
			if (isset($participant['field']['delete'])) {
				$deleted = $participant['field']['delete'];
			}
			if (empty($deleted)) {
				switch ($participant_type) {
					case 'victim':
						if (!$this->participantHasValues($participant)) {
							$deletable_victims++;
						}
						$current_victims++;
						break;
					case 'perpetrator':
						if (!$this->participantHasValues($participant)) {
							$deletable_perps++;
						}
						$current_perps++;
						break;
				}
			}
		}

		//		foreach ($new_field_state as $index => $part) {
		//			if (!$this->deletableParticipant($part)) continue;
		//
		//			switch (strtolower($part['field']['left-column']['type'])) {
		//				case 'victim':
		//					if ($current_victims > $victims) {
		//						unset($new_field_state[$index]);
		//						unset($this->form_state['values']['Participants']['field'][$index]);
		//						$current_victims--;
		//					}
		//					break;
		//				case 'perpetrator':
		//					if ($current_perps > $perps) {
		//						unset($new_field_state[$index]);
		//						unset($this->form_state['values']['Participants']['field'][$index]);
		//						$current_perps--;
		//					}
		//					break;
		//			}
		//		}

		// Update the field state.
		$field_state = $new_field_state;

		// Check to see if we have values that exceed what is required.
		$message = 'You have indicated that you want @want @type-want, but you currently have data for @have @type-have. ' .
			'Please click on the <strong>Clear</strong> or <strong>Delete</strong> buttons for the participants you don\'t ' .
			'want to include before changing this field again.';
		if ($current_victims > $victims) {
			if (($current_victims - $deletable_victims) > $victims) {
				drupal_set_message(t($message, array(
					'@want' => $victims,
					'@type-want' => Inflector::getInstance()->quantize($victims, 'victim'),
					'@have' => $current_victims,
					'@type-have' => Inflector::getInstance()->quantize($current_victims, 'victim'),
				)), 'warning');
			} else {
				foreach ($field_state as $index => $item) {
					if (!is_array($item)) continue;
					if (!is_numeric($index)) continue;
					if ($this->participantHasValues($item)) continue;

					if ($item['field']['left-column']['type'] === 'victim') {
						$field_state[$index]['field']['delete'] = 1;
						$current_victims--;
					}
				}
			}
		}
		if ($current_perps > $perps) {
			if (($current_perps - $deletable_perps) > $perps) {
				drupal_set_message(t($message, array(
					'@want' => $perps,
					'@type-want' => Inflector::getInstance()->quantize($perps, 'perpetrator'),
					'@have' => $current_perps,
					'@type-have' => Inflector::getInstance()->quantize($current_perps, 'perpetrator'),
				)), 'warning');
			} else {
				foreach ($field_state as $index => $item) {
					if (!is_array($item)) continue;
					if (!is_numeric($index)) continue;
					if ($this->participantHasValues($item)) continue;

					if ($item['field']['left-column']['type'] === 'perpetrator') {
						$field_state[$index]['field']['delete'] = 1;
						$current_perps--;
					}
				}
			}
		}

		// Add empty fields to the form state for the new participants.
		for ($i = 0; $i < $victims - $current_victims; $i++) {
			$field_state[] = array('field' => array('left-column' => array('type' => 'victim')));
		}
		for ($i = 0; $i < $perps - $current_perps; $i++) {
			$field_state[] = array('field' => array('left-column' => array('type' => 'perpetrator')));
		}

		// Add the fields at their respective indexes back to the form array.
		foreach ($field_state as $index => $item) {
			if (!is_array($item)) continue;
			if (!is_numeric($index)) continue;
			if ($item['field']['left-column']['type'] === 'victim') {
				EntryField::addIndexToFieldState($item, $index); // Set the index so we can use it in the participant form.
				$render[$index] = $participant_field->form($this->form_state, $item);
			}
		}
		foreach ($field_state as $index => $item) {
			if (!is_array($item)) continue;
			if (!is_numeric($index)) continue;
			if ($item['field']['left-column']['type'] === 'perpetrator') {
				EntryField::addIndexToFieldState($item, $index); // Set the index so we can use it in the participant form.
				$render[$index] = $participant_field->form($this->form_state, $item);
			}
		}

		// Add a message saying they need to add some participants if there aren't any currently.
		if (count(element_children($render)) === 0) {
			$render['#markup'] = '<p>There are currently no participants. Update the values of the ' .
				'<strong>Number of Victims</strong> and <strong>Number of Subjects-Suspects</strong> ' .
				'fields above to add some.</p>';
		}

		return $render;
	}

	protected function participantHasValues($participant_state)
	{
		// Remove the field index if it's there...
		if (array_key_exists('field', $participant_state)) {
			$participant_state = $participant_state['field'];
		}
		//if (!empty($participant_state['left-column']['type'])) return true;
		if (isset($participant_state['left-column']['relationship']) && !empty($participant_state['left-column']['relationship'])) return true;
		if (!empty($participant_state['column']['fieldset'][0]['name'])) return true;
		if (!empty($participant_state['column']['fieldset'][0]['age'])) return true;
		if (!empty($participant_state['column']['fieldset'][0]['age_group'])) return true;

		// Check for positive values on gender, characteristic and status.
		if (isset($participant_state['column']['fieldset'][1]['type']['gender']) && is_array($participant_state['column']['fieldset'][1]['type']['gender'])) {
			foreach ($participant_state['column']['fieldset'][1]['type']['gender'] as $gender) {
				if ($gender) return true;
			}
		}
		if (isset($participant_state['column']['fieldset'][1]['type']['characteristic']) && is_array($participant_state['column']['fieldset'][1]['type']['characteristic'])) {
			foreach ($participant_state['column']['fieldset'][1]['type']['characteristic'] as $char) {
				if (!empty($char)) return true;
			}
		}
		if (is_array($participant_state['column']['fieldset'][1]['status'])) {
			foreach ($participant_state['column']['fieldset'][1]['status'] as $status) {
				if ($status) return true;
			}
		}

		return false;
	}

	protected function deletableParticipant($participant_state)
	{
		// Remove the field index if it's there...
		if (array_key_exists('field', $participant_state)) {
			$participant_state = $participant_state['field'];
		}

		if (isset($participant_state['left-column']['relationship']) && !empty($participant_state['left-column']['relationship'])) return false;
		if (!empty($participant_state['column']['fieldset'][0]['name'])) return false;
		if (!empty($participant_state['column']['fieldset'][0]['age'])) return false;
		if (!empty($participant_state['column']['fieldset'][0]['age_group'])) return false;

		// Check for positive values on gender, characteristic and status.
		if (isset($participant_state['column']['fieldset'][1]['type']['gender']) && is_array($participant_state['column']['fieldset'][1]['type']['gender'])) {
			foreach ($participant_state['column']['fieldset'][1]['type']['gender'] as $gender) {
				if ($gender) return true;
			}
		}
		if (isset($participant_state['column']['fieldset'][1]['type']['characteristic']) && is_array($participant_state['column']['fieldset'][1]['type']['characteristic'])) {
			foreach ($participant_state['column']['fieldset'][1]['type']['characteristic'] as $char) {
				if (!empty($char)) return true;
			}
		}
		if (is_array($participant_state['column']['fieldset'][1]['status'])) {
			foreach ($participant_state['column']['fieldset'][1]['status'] as $status) {
				if ($status) return false;
			}
		}

		return true;
	}

	public function getExtraInfo($value)
	{
		// TODO: Implement getExtraInfo() method.
	}

	/**
	 * {@inheritDoc}
	 */
	public function submit($value, &$node, $extra)
	{
		$participant_nodes = array();
		/** @var ParticipantField $participant_form */
		$participant_form = EntryFieldFactory::getInstance()->factory('Participant');

		if ($value) {
			foreach ($value as $participant) {
				if ($participant['field']['delete']) continue; // Added on 2023/03/13 by yanh
				$participant_node = new \stdClass();
				$participant_node->type = 'participant';
				$participant_node->language = LANGUAGE_NONE;
				node_object_prepare($participant_node);

				$participant_form->submit($participant['field'], $participant_node, []);
				node_save($participant_node);

				$participant_nodes[] = $participant_node;
			}
		}

		$new_values = array();
		foreach ($participant_nodes as $item) {
			// Save the node to our array...
			$new_values[] = array('target_id' => $item->nid);
		}

		//save array to node
		$node->field_participants = array(LANGUAGE_NONE => $new_values);
	}

	protected function entityToFieldState(Entity $entity)
	{
		$value = array();
		if ($entity->field_participants) {
			$field_factory = EntryFieldFactory::getInstance()->factory('Participant');
			foreach ($entity->field_participants as $participant) {
				$entity = Entity::import($participant->raw_entity);
				if (!$entity) continue; // Skip the entity if it's invalid.
				$value[] = array('field' => $field_factory->entityToFieldState($entity));
			}
		}

		return $value;
	}

	public function supportedFormTypes()
	{
		return array('incident');
	}
}
