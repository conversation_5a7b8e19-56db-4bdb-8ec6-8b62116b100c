<?php
/**
 * @file TwitterField.php
 */
namespace Drupal\gva_entry\EntryFields;

use AbleCore\Entity;
use Drupal\gva_entry\QueryFilterable;

class TwitterField extends EntryField {

    use QueryFilterable;

    /**
     * {@inheritDoc}
     */
    protected function field($field_state)
    {
        $field = array(
            '#type' => 'container',
            '#attributes' => array('class' => array('checkbox')),
            'twitter' => array(
                '#type' => 'checkbox',
                '#title' => 'Twitter Source',
            ),
        );

        return $field;
    }

	public function getExtraInfo($value)
	{
		// TODO: Implement getExtraInfo() method.
	}

    /**
     * {@inheritDoc}
     */
	public function submit($value, &$node, $extra)
    {
        if ($value) {
            $node->field_twitter_source = array(LANGUAGE_NONE => array(array(
                'value' => $value,
            )));
        } else {
	        $node->field_twitter_source = array();
        }
    }

	protected function fillDefaults(array &$field, $value)
	{
		if (isset($value['twitter'])) {
			$field['twitter']['#default_value'] = $value['twitter'];
		}
	}

	protected function entityToFieldState(Entity $entity)
	{
		$converted_bool = ($entity->field_twitter_source->value == 'Yes')? true : false;
		return array('twitter' => $converted_bool);
	}

    public function comparators($id, array $arguments)
    {
        return array(
            'is',
            'is not',
        );
    }

    public function filterForm($id, array $arguments, $comparator)
    {
        $field = array(
            'value' => array(
	            '#type' => 'html_tag',
	            '#tag' => 'span',
	            '#attributes' => array('class' => array('')),
	            '#value' => 'Twitter Source',
            ),
        );
        return $field;
    }

    public function filter(\SelectQueryInterface &$query, \QueryConditionInterface &$condition,
        \QueryConditionInterface &$havingCondition, $comparator, array $arguments)
    {
        self::addJoinToQuery($query, 'inner', 'field_data_field_twitter_source', 'twitter', 'twitter.entity_id = n.nid');
        if ($comparator == 'is') {
            $condition->condition('twitter.field_twitter_source_value', 1);
        } elseif ($comparator == 'is not') {
            $condition->condition('twitter.field_twitter_source_value', 0);
        }
    }

	public function getArrayParents()
	{
		return array('sources_information');
	}

    /**
     * Set Twitter source field on entity
     *
     * @param $status String
     * @param $entity Entity
     */
    public static function setEntity($status, &$entity)
    {
        if ($status == 'twit')
            $entity->field_twitter_source = 1;
        else
            $entity->field_twitter_source = 0;
    }

}
