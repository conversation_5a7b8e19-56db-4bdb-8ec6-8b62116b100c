<?php
/**
 * @file HeadlinesField.php
 */
namespace Drupal\gva_entry\EntryFields;

use Able<PERSON>ore\Entity;
use Drupal\gva_entry\QueryFilterable;


class HeadlinesField extends EntryField {

  use QueryFilterable;

  /**
   * {@inheritDoc}
   */
  protected function field($field_state)
  {
    $field = array(
      '#type' => 'container',
      '#theme_wrappers' => array('gva_entry_section'),
      '#section' => 'row collapse',
      'title' => array(
        '#type' => 'html_tag',
        '#tag' => 'h2',
        '#value' => t('Headlines For Incident'),
      ),
      'container' => array(
        '#type' => 'container',
        '#attributes' => array('class' => array('medium-8', 'columns', 'float-left')),
        'field' => array(
          '#type' => 'textfield',
        ),
      ),
    );

    return $field;
  }

  public function getExtraInfo($value)
  {
    // TODO: Implement getExtraInfo() method.
  }

  /**
   * {@inheritDoc}
   */
  public function submit($value, &$node, $extra)
  {
    if ($value) {
      $node->field_headlines = array(LANGUAGE_NONE => array(array(
        'value' => trim($value['container']['field']),
      )));
    } else {
      $node->field_headlines = array();
    }
  }

  protected function fillDefaults(array &$field, $value)
  {
    if (!empty($value))
      $this->fillDefault($field['container']['field']['#default_value'], $value['container']['field']);
  }

  protected function entityToFieldState(Entity $entity)
  {
    $value = array('container' => array('field' => ''));
    if ($entity->field_headlines) {
      $value['container']['field'] = html_entity_decode($entity->field_headlines, ENT_QUOTES, 'UTF-8');
    }

    return $value;
  }

  public function comparators($id, array $arguments)
  {
    return array(
      'contains',
      'does not contain',
    );
  }

  public function filterForm($id, array $arguments, $comparator)
  {
    $field = array(
      'value' => array(
        '#type' => 'textfield',
        '#container_attributes' => array('class' => array('small-9', 'column')),
      ),
    );
    return $field;
  }

  public function filter(\SelectQueryInterface &$query, \QueryConditionInterface &$condition,
    \QueryConditionInterface &$havingCondition, $comparator, array $arguments)
  {
    $field = trim($arguments['value']);
    // Actually perform the filter.
    if ($field) {
      self::addJoinToQuery($query, 'inner', 'field_data_field_headlines', 'headlines', 'headlines.entity_id = n.nid');
      if ($comparator == 'contains') {
        $condition->condition('headlines.field_headlines_value', '%'.$field.'%', 'LIKE');
      } elseif ($comparator == 'does not contain') {
        $condition->condition('headlines.field_headlines_value', '%'.$field.'%', 'NOT LIKE');
      }
    }
  }

  /**
   * Set headlines field on entity
   *
   * @param $headlines String
   * @param $entity Entity
   */
  public static function setEntity($headlines, &$entity)
  {
    if (!empty($headlines))
      $entity->field_headlines = $headlines;
  }

  public function supportedFormTypes()
  {
    return array('incident');
  }
}
