<?php

namespace Drupal\gva_entry\EntryFields;

use AbleCore\Entity;
use Drupal\gva_entry\QueryFilterable;

class IncidentDateField extends TitleSegmentEntryField implements ImportableEntryField
{

    use QueryFilterable;

    protected function field($field_state)
    {
        $form = array(
            '#type' => 'fieldset',
            '#attributes' => array('class' => array('label-top', 'medium-12', 'fix-fieldset')),
            '#title' => t('Incident Date <span class="form-required" title="This field is required.">*</span>'),
            '#required' => TRUE,
        );
        $form['row'] = array(
            '#type' => 'container',
            '#attributes' => array('class' => array('row')),
        );
        $form['row'] += $this->dateField();

        return $form;
    }

    public function getExtraInfo($value)
    {
        // TODO: Implement getExtraInfo() method.
    }

    public function getValueFromImport($import)
    {
        $delimiter = !strpos($import, '-') ? '/' : '-';
        $date_array = explode($delimiter, $import);
        $date_array[2] = '20' . $date_array[2];

        return ['row' => ['picker' => implode('/', $date_array)]];
    }

    public function submit($value, &$node, $extra)
    {
        if ($value) {
            $date_array = explode('/', $value['row']['picker']);
            $year = !empty($date_array[2]) ? $date_array[2] : time('Y');
            $month = !empty($date_array[0]) ? $date_array[0] : time('F');
            $day = !empty($date_array[1]) ? $date_array[1] : time('j');

            $date_time = new \DateTime();
            $date_time->setDate($year, $month, $day);
            $timestamp = $date_time->getTimestamp();

            $field_info = field_info_field('field_date');
            $node->field_date = array(
                LANGUAGE_NONE => array(
                    array(
                        'value' => $timestamp,
                        'timezone' => date_default_timezone(),
                        'timezone_db' => $field_info['settings']['timezone_db'],
                        'date_type' => $field_info['type'],
                    )
                )
            );
        } else {
            $node->field_date = array();
        }
    }

    protected function fillDefaults(array &$field, $value)
    {
      if (!empty($value))
        $this->fillDefault($field['row']['picker']['#default_value'], $value['row']['picker']);
    }

    protected function entityToFieldState(Entity $entity)
    {
        $value = array();
        if ($entity->field_date) {
            $value = array(
                'row' => array(
                    'picker' => intval($entity->field_date->format('n')) . '/' . intval($entity->field_date->format('j')) . '/' . intval($entity->field_date->format('Y')),
                ),
            );
        }
        return $value;
    }

    /**
     * {@inheritDoc}
     */
    protected function generateTitleSegment($node)
    {
        if (isset($node->field_date[LANGUAGE_NONE]) || $node->field_date->value) {
            return isset($node->field_date[LANGUAGE_NONE])? date('n-j-y', $node->field_date[LANGUAGE_NONE][0]['value']) : date('n-j-y', $node->field_date->value);
        } else {
            return date('n-j-y');
        }
    }

    /**
     * {@inheritDoc}
     */
    public function comparators($id, array $arguments)
    {
        return array(
            'is in',
            'is not in',
            'is in the last',
            'is not in the last',
            'is current year',
            'is year',
            'is not year',
        );
    }

    /**
     * {@inheritDoc}
     */
    public function filterForm($id, array $arguments, $comparator)
    {
        if ($comparator == 'is in' || $comparator == 'is not in') {
            $form = array(
                'date-from' => array(
                    '#type' => 'textfield',
                    '#title' => t('From'),
                    '#attributes' => array(
                        'class' => array('fancy', 'date-picker-single'),
                    ),
                ),
                'date-to' => array(
                    '#type' => 'textfield',
                    '#title' => t('To'),
                    '#attributes' => array(
                        'class' => array('fancy', 'date-picker-single'),
                    ),
                ),
            );
        } elseif ($comparator == 'is in the last' || $comparator == 'is not in the last') {
            $form = array(
                '#type' => 'container',
                '#attributes' => array('class' => array('row', 'form-item')),
                'time' => array(
                    '#type' => 'textfield',
                    '#element_validate' => array('element_validate_integer_positive_or_zero'),
                    '#container_attributes' => array('class' => array('')),
                    '#theme_wrappers' => array('gva_form_element'),
                    '#title' => t('Time'),
                    '#title_display' => 'invisible',
                    '#default_value' => 0,
                ),
                'unit' => array(
                    '#type' => 'select',
                    '#attributes' => array('class' => array('fancy')),
                    '#container_attributes' => array('class' => array('medium-6', 'columns')),
                    '#theme_wrappers' => array('gva_form_element'),
                    '#options' => array(
                        'D' => 'days',
                        'M' => 'months',
                        'Y' => 'years',
                    ),
                ),
            );
        } elseif ($comparator == 'is current year') {
            $form = array();
        } elseif ($comparator == 'is year') {
            $current_year = date('Y');
            for($i = 1900; $i <= $current_year; $i++){
                $options[$i] = $i;
            }
            $form = array(
                'date-year' => array(
                    '#type' => 'select',
                    '#title' => t('Year'),
                    '#options' => array_reverse($options, true),
                    ),
                );
        } elseif ($comparator == 'is not year') {
            $current_year = date('Y');
            for($i = 1900; $i <= $current_year; $i++){
                $options[$i] = $i;
            }

            $form = array(
                'date-year' => array(
                    '#type' => 'select',
                    '#title' => t('Year'),
                    '#options' => array_reverse($options, true),
                    ),
            );
        } else {
            throw new \Exception('Incorrect comparator selected.');
        }

        return $form;
    }

    /**
     * Generates a render array representing a date picker with an optional default value.
     *
     * Default values:
     *  - If the default value supplied is false, the current date is used.
     *  - If the default value supplied is null, no default value is set.
     *  - If anything else is supplied, the default value is set to that value.
     *
     * @param mixed $default_month The default value for the month.
     * @param mixed $default_day The default value for the day.
     * @param mixed $default_year The default value for the year.
     *
     * @return array
     */
    protected function dateField($default_month = false, $default_day = false, $default_year = false)
    {
        $form = array(
            'picker' => array(
                '#type' => 'textfield',
                '#title' => t('Date'),
                '#attributes' => array(
                    'class' => array('fancy', 'date-picker-single'),
                    'readonly' => 'readonly',
                ),
                '#theme_wrappers' => array('gva_entry_column'),
                '#default_value' => intval(date('n')) . '/' . intval(date('j')) . '/' . intval(date('Y')),
            ),
        );

        return $form;
    }

    public function filter(\SelectQueryInterface &$query, \QueryConditionInterface &$condition,
                           \QueryConditionInterface &$havingCondition, $comparator, array $arguments)
    {
        if ($comparator == 'is in' || $comparator == 'is not in') {
            // Get the UNIX timestamps from the month, day and year.
            if (isset($arguments['date-from'])) {
                $start = $this->convertDateToTimestamp(array('string' => $arguments['date-from']), 'start');
            } elseif (isset($arguments['from']['row'])) {
                $start = $this->convertDateToTimestamp($arguments['from']['row'], 'start');
            } else {
                $start = false;
            }
            if (isset($arguments['date-to'])) {
                $end = $this->convertDateToTimestamp(array('string' => $arguments['date-to']), 'end');
            } elseif (isset($arguments['to']['row'])) {
                $end = $this->convertDateToTimestamp($arguments['to']['row'], 'end');
            } else {
                $end = false;
            }

            // Actually perform the filter.
            if ($start || $end) {
                self::addJoinToQuery($query, 'inner', 'field_data_field_date', 'date', 'date.entity_id = n.nid');
                if ($comparator == 'is in') {
                    if ($start && $end) {
                        $and = db_and();
                        $and->condition('date.field_date_value', $start, '>=');
                        $and->condition('date.field_date_value', $end, '<=');
                        $condition->condition($and);
                    } elseif ($start) {
                        $condition->condition('date.field_date_value', $start, '>=');
                    } elseif ($end) {
                        $condition->condition('date.field_date_value', $end, '<=');
                    }
                } elseif ($comparator == 'is not in') {
                    if ($start && $end) {
                        $query_or = db_or();
                        $query_or->condition('date.field_date_value', $start, '<');
                        $query_or->condition('date.field_date_value', $end, '>');
                        $condition->condition($query_or);
                    } elseif ($start && !$end) {
                        $condition->condition('date.field_date_value', $start, '<');
                    } elseif (!$start && $end) {
                        $condition->condition('date.field_date_value', $end, '>');
                    }
                }
            }
        } elseif ($comparator == 'is in the last' || $comparator == 'is not in the last') {
            $interval_spec = 'P';
            switch ($arguments['unit']) {
                case 'days':
                    $interval_spec .= $arguments['time'] . 'D';
                    break;
                case 'months':
                    $interval_spec .= $arguments['time'] . 'M';
                    break;
                case 'years':
                    $interval_spec .= $arguments['time'] . 'Y';
                    break;
                default:
                    $interval_spec .= $arguments['time'] . $arguments['unit'];
            }
            $datetime = new \DateTime();
            $datetime->sub(new \DateInterval($interval_spec));
            $timestamp = $datetime->getTimestamp();

            //$alias = $query->addJoin('inner', 'field_data_field_date', null, '%alias.entity_id = n.nid');
            self::addJoinToQuery($query, 'inner', 'field_data_field_date', 'date', 'date.entity_id = n.nid');
            $condition->condition('date.field_date_value', $timestamp, self::translateComparator($comparator));
        } elseif ($comparator == 'is current year') {
            $current_year = date('Y');
            // Prepare a timestamp for the first/last day of this year.
            $first_day = mktime(0, 0, 0, 1, 1, $current_year);
            $last_day = mktime(23,59,59,12,31, $current_year);

            self::addJoinToQuery($query, 'inner', 'field_data_field_date', 'date', 'date.entity_id = n.nid');
            //$alias = $query->addJoin('inner', 'field_data_field_date', null, '%alias.entity_id = n.nid');
            $and = db_and();
            $and->condition('date.field_date_value', $first_day, '>=');
            $and->condition('date.field_date_value', $last_day, '<=');
            $condition->condition($and);
        } else if ($comparator == 'is year') {
            $year = $arguments['date-year'];
            $first_day = mktime(0,0,0,1,1,$year);
            $last_day = mktime(23,59,59,12,31,$year);
            self::addJoinToQuery($query,'inner','field_data_field_date','date','date.entity_id = n.nid');
            $and = db_and();
            $and->condition('date.field_date_value', $first_day,'>=');
            $and->condition('date.field_date_value',$last_day,'<=');
            $condition->condition($and);
        } elseif($comparator == 'is not year'){
            $year = $arguments['date-year'];
            $first_day = mktime(0,0,0,1,1,$year);
            $last_day = mktime(23,59,59,12,31,$year);
            self::addJoinToQuery($query,'inner','field_data_field_date','date','date.entity_id = n.nid');
            $or = db_or();
            $or->condition('date.field_date_value',$first_day, '<');
            $or->condition('date.field_date_value',$last_day, '>');
            $condition->condition($or);
        }
    }

    protected static function translateComparator($comparator)
    {
        if ($comparator == 'is in the last') return '>=';
        elseif ($comparator == 'is not in the last') return '<';
        else return false;
    }

    public function getArrayParents()
    {
        return array('general_information', 0);
    }

    protected function alterRenderedForm(array &$form)
    {
        $this->addColumnTheme($form, 3);
    }

    /**
     * Set date field on entity
     *
     * @param $date String
     * @param $entity Entity
     */
    public static function setEntity($date, &$entity)
    {
        if (date_create($date))
            $entity->field_date = date_create($date)->getTimestamp();
    }

    public function translateOldValue(array $filter = array())
    {
        $new_values = array();

        if (isset($filter['filter']['field']['from'])) {
            $new_values['date-from'][] = !empty($filter['filter']['field']['from']['row']['month']) ? $filter['filter']['field']['from']['row']['month'] : '';
            $new_values['date-from'][] = !empty($filter['filter']['field']['from']['row']['month']) ? (!empty($filter['filter']['field']['from']['row']['day']) ? $filter['filter']['field']['from']['row']['day'] : '') : '';
            $new_values['date-from'][] = !empty($filter['filter']['field']['from']['row']['year']) ? $filter['filter']['field']['from']['row']['year'] : '';
            unset($filter['filter']['field']['from']);
            $filter['filter']['field']['date-from'] = implode('/', array_filter($new_values['date-from']));
        }

        if (isset($filter['filter']['field']['to'])) {
            $new_values['date-to'][] = !empty($filter['filter']['field']['to']['row']['month']) ? $filter['filter']['field']['to']['row']['month'] : '';
            $new_values['date-to'][] = !empty($filter['filter']['field']['to']['row']['month']) ? (!empty($filter['filter']['field']['to']['row']['day']) ? $filter['filter']['field']['to']['row']['day'] : '') : '';
            $new_values['date-to'][] = !empty($filter['filter']['field']['to']['row']['year']) ? $filter['filter']['field']['to']['row']['year'] : '';
            unset($filter['filter']['field']['to']);
            $filter['filter']['field']['date-to'] = implode('/', array_filter($new_values['date-to']));
        }

        return $filter;
    }
}
