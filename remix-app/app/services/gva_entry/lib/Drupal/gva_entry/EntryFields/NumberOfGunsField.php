<?php
/**
 * @file NumberOfGunsField.php
 */
namespace Drupal\gva_entry\EntryFields;


use AbleCore\Entity;
use Drupal\gva_entry\QueryFilterable;

class NumberOfGunsField extends NumberOfXField {

    use QueryFilterable;

    protected function field($field_state)
    {
        $ajax = array(
            'callback' => 'gva_entry_guns_ajax_callback',
            'wrapper' => 'guns',
        );

        $form = array(
            '#prefix' => '<section id="gun-type" class="row collapse">',
            '#suffix' => '</section>',
            'header' => array(
              '#type' => 'html_tag',
              '#tag' => 'h2',
              '#value' => t('Gun Type'),
            ),
            'guns' => array(
                '#type' => 'textfield',
                '#title' => t('Number of Guns'),
                '#default_value' => 1,
                '#attributes' => array('class' => array('numeric')),
                '#container_attributes' => array('class' => array('medium-6', 'columns')),
                '#ajax' => $ajax,
            ),
	        'details' => array(
                '#prefix' => '<div class="medium-2 columns">',
                '#suffix' => '</div>',
		        '#type' => 'button',
		        '#attributes' => array('class' => array('button', 'guns-show-details', 'float-right', 'align-right')),
		        '#value' => t('Show Details'),
	        ),
        );

        return $form;
    }

	public function getExtraInfo($value)
	{
		// TODO: Implement getExtraInfo() method.
	}

	public function submit($value, &$node, $extra)
    {
	    if (isset($extra['import']) && $extra['import']) {
		    $node->field_guns_involved_counter = array(LANGUAGE_NONE => array(array(
		    	'value' => 1,
		    )));
	    } else {
		    if ($value && $value['guns']) {
			    $node->field_guns_involved_counter = array(LANGUAGE_NONE => array(array(
				    'value' => $value['guns'],
			    )));
		    } else {
			    $node->field_guns_involved_counter = array();
		    }
	    }

    }

    public function comparators($id, array $arguments)
    {
        return array(
            'is greater than',
            'is less than',
            'is equal to',
            'is not equal to',
        );
    }

    public function filterForm($id, array $arguments, $comparator)
    {
        $form = array(
        	'value' => array(
		        '#type' => 'textfield',
		        '#element_validate' => array('element_validate_integer_positive_or_zero'),
		        '#size' => 10,
		        '#default_value' => 0,
		        '#title' => t('Number of Guns'),
		        '#title_display' => 'invisible',
	        ),
        );
        return $form;
    }

    public function filter(\SelectQueryInterface &$query, \QueryConditionInterface &$condition,
        \QueryConditionInterface &$havingCondition, $comparator, array $arguments)
    {
        $operator = self::translateComparator($comparator);
        if (!$operator) return;

	    $alias = $query->addJoin('left', 'field_data_field_guns_involved_counter', null, '%alias.entity_id = n.nid');
	    $condition->condition($alias . '.field_guns_involved_counter_value', $arguments['value'], $operator);
    }

    protected static function translateComparator($comparator)
    {
        if ($comparator == 'is greater than') return '>';
        if ($comparator == 'is less than') return '<';
        if ($comparator == 'is equal to') return '=';
        if ($comparator == 'is not equal to') return '!=';
        return false;
    }

    public function validateFilter($comparator, array $arguments)
    {
        return self::validateSingleValue($arguments);
    }

    /**
     * Gets the number of guns currently in the form state.
     *
     * @param array $form_state The form state to get the number from.
     *
     * @return mixed Either the number of guns or FALSE.
     */
    public function getNumberOfGuns($form_state)
    {
    	if (isset($form_state['values']) && isset($form_state['values']['NumberOfGuns']) && isset($form_state['values']['NumberOfGuns']['field']))
    		$total = $form_state['values']['NumberOfGuns']['field']['guns'];
	    elseif ($this->getNumberOfIndex('guns', $form_state))
		    $total = $this->getNumberOfIndex('guns', $form_state);
	    else
	    	$total = 1;
        return $total;
    }

	protected function entityToFieldState(Entity $entity)
	{
		if ($entity->field_guns_involved_counter) {
			return array('guns' => $entity->field_guns_involved_counter->value);
		} elseif ($entity->field_guns_involved) {
			return array('guns' => count($entity->field_guns_involved));
		} else return array();
	}

	protected function fillDefaults(array &$field, $value)
	{
		if (!empty($value['guns'])) {
			$this->fillDefault($field['guns']['#default_value'], $value['guns']);
		}
	}
}
