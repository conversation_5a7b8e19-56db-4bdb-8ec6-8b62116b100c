<?php

namespace Drupal\gva_entry\Forms;

use AbleCore\Forms\FormBase;

class SearchForm extends FormBase
{
	public function build($form, &$form_state)
	{
		$topper = gva_general_get_snippet('gva_entry_search_top', 'Some introductory text for how to use the filtering features goes here');
		$limit_text = gva_general_get_snippet('gva_entry_search_limit_text', 'Thanks for your interest in our statistics! Unfortunately, we must limit result sets for ' .
			'the general public. If you would like more information or the full result set, please provide us with ' .
			'your email using the form below and we\'ll get back with you as soon as possible.');

		$limit = variable_get('gva_entry_search_limit', 300);

		$form = array();
		$form['gva_entry_search_top'] = array(
			'#type' => 'text_format',
			'#format' => 'filtered_html',
			'#title' => t('Search Page Instructions'),
			'#default_value' => $topper['value'],
		);
		$form['gva_entry_search_limit_text'] = array(
			'#type' => 'text_format',
			'#format' => 'filtered_html',
			'#title' => t('Search Limiter Text'),
			'#default_value' => $limit_text['value'],
		);
		$form['gva_entry_search_limit'] = array(
			'#type' => 'textfield',
			'#attributes' => array('class' => array('numeric')),
			'#container_attributes' => array('class' => array('small-3', 'column')),
			'#title' => t('Limit'),
			'#description' => t('The number of results to display for a user who doesn\'t have permission to view all the results.'),
			'#default_value' => $limit,
		);
		$form['actions'] = array('#type' => 'actions');
		$form['actions']['submit'] = array(
			'#type' => 'submit',
			'#value' => t('Save'),
		);

		return $form;
	}

	public function submit($form, &$form_state)
	{
		if ($form_state['values']['gva_entry_search_top']) {
			gva_general_save_snippet('gva_entry_search_top', $form_state['values']['gva_entry_search_top']);
		}
		if ($form_state['values']['gva_entry_search_limit_text']) {
			gva_general_save_snippet('gva_entry_search_limit_text', $form_state['values']['gva_entry_search_limit_text']);
		}
		if ($form_state['values']['gva_entry_search_limit']) {
			variable_set('gva_entry_search_limit', $form_state['values']['gva_entry_search_limit']);
		}
		drupal_set_message(t('The search page settings have been saved.'));
	}
}
