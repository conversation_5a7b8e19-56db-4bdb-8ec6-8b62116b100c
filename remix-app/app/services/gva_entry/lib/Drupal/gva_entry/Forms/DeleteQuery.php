<?php

namespace Drupal\gva_entry\Forms;

use AbleCore\Forms\FormBase;
use Drupal\gva_entry\QueryBuilder;

class DeleteQuery extends FormBase {

	public function build($form, &$form_state)
	{
		if (!isset($form_state['build_info']['args'][0])) {
			drupal_not_found();
			return array();
		}
		$query_uuid = $form_state['build_info']['args'][0];
		$form = array();
		return confirm_form($form, t('Are you sure you want to delete query :query?', array(':query' => $query_uuid)), 'admin/content/queries');
	}

	public function submit($form, &$form_state)
	{
		if (!isset($form_state['build_info']['args'][0])) {
			drupal_not_found();
		}
		$query_uuid = $form_state['build_info']['args'][0];
		QueryBuilder::delete($query_uuid);
		drupal_set_message('The query was deleted successfully!');
		drupal_goto('admin/content/queries');
	}

}
