<?php

namespace Drupal\gva_entry\Columns\Location;

use Drupal\gva_entry\Columns\Column;
use Drupal\gva_entry\Columns\ColumnFactory;
use Drupal\gva_entry\Columns\IncidentsColumnInterface;
use Drupal\gva_entry\Columns\ParticipantsColumnInterface;

class CityCountyAndStateColumn extends Column implements IncidentsColumnInterface, ParticipantsColumnInterface {

	public function display(array $row = array(), array &$tags = array())
	{
		$factory = ColumnFactory::getInstance();
		$state = $factory->factory('Location.State')->preDisplay($row, $this->exporter);
		$city_county = $factory->factory('Location.CityOrCounty')->preDisplay($row, $this->exporter);
		return $city_county . ', ' . $state;
	}

	public function alter(\SelectQueryInterface $query)
	{
		$factory = ColumnFactory::getInstance();
		$factory->factory('Location.CityOrCounty')->alter($query);
		$factory->factory('Location.State')->alter($query);
	}

	public static function label()
	{
		return 'City/County and State';
	}

	public function getSortField(\SelectQueryInterface &$query)
	{
		return ColumnFactory::getInstance()->factory('Location.CityOrCounty')->getSortField($query);
	}

}
