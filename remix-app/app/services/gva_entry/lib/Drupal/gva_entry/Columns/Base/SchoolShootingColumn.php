<?php

namespace Drupal\gva_entry\Columns\Base;

use Drupal\gva_entry\Columns\Column;
use Drupal\gva_entry\Columns\IncidentsColumnInterface;

class SchoolShootingColumn extends Column implements IncidentsColumnInterface
{

	public function display(array $row = array(), array &$tags = array())
	{
		$result = (!empty($row['school_shooting'])) ? 'X' : '';
		return $result;
	}

	public function alter(\SelectQueryInterface $query)
	{
		$alias = self::addJoinToQuery($query, 'left', 'field_data_field_incident_type', 'field_data_field_incident_type', '%alias.entity_id = n.nid && (%alias.field_incident_type_tid = 196 OR %alias.field_incident_type_tid = 197)');
		$query->addField($alias, 'field_incident_type_tid', 'school_shooting');
	}

	public function getSortField(\SelectQueryInterface &$query)
	{
		$alias = self::addJoinToQuery($query, 'left', 'field_data_field_incident_type', 'field_data_field_incident_type', '%alias.entity_id = n.nid && (%alias.field_incident_type_tid = 196 OR %alias.field_incident_type_tid = 197)');
		return $alias . '.field_incident_type_tid';
	}

}
