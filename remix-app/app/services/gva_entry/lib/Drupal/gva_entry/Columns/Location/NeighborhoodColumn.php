<?php

namespace Drupal\gva_entry\Columns\Location;

use Drupal\gva_entry\Columns\Column;
use Drupal\gva_entry\Columns\IncidentsColumnInterface;
use Drupal\gva_entry\Columns\ParticipantsColumnInterface;

class NeighborhoodColumn extends Column implements IncidentsColumnInterface, ParticipantsColumnInterface {

	public function display(array $row = array(), array &$tags = array())
	{
		return $this->checkEmptyValue($row, 'neighborhood');
	}

	public function alter(\SelectQueryInterface $query)
	{
		$alias = $query->addJoin('left', 'field_data_field_neighborhood', null, '%alias.entity_id = n.nid');
		$query->addField($alias, 'field_neighborhood_value', 'neighborhood');
	}

	public function getSortField(\SelectQueryInterface &$query)
	{
		$alias = $query->addJoin('left', 'field_data_field_neighborhood', null, '%alias.entity_id = n.nid');
		return $alias . '.field_neighborhood_value';
	}

	public static function label() {
		return 'Neighborhood';
	}

}
