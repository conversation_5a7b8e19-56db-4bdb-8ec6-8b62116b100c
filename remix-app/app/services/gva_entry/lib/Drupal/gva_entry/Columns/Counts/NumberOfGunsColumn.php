<?php

namespace Drupal\gva_entry\Columns\Counts;

use Drupal\gva_entry\Columns\Column;
use Drupal\gva_entry\Columns\IncidentsColumnInterface;

class NumberOfGunsColumn extends Column implements IncidentsColumnInterface {

	public function display(array $row = array(), array &$tags = array())
	{
		return $this->checkEmptyValue($row, 'guns_involved_count', null, '0');
	}

	public function alter(\SelectQueryInterface $query)
	{
		$guns_subquery = $query->addJoin('left', 'field_data_field_guns_involved_counter', null, '%alias.entity_id = n.nid');
		$query->addField($guns_subquery, 'field_guns_involved_counter_value', 'guns_involved_count');
	}

	public function getSortField(\SelectQueryInterface &$query)
	{
		$this->alter($query);
		return 'guns_involved_count';
	}

  public static function access() {
    Return false;
  }

}
