<?php

namespace Drupal\gva_entry\Columns\Participant;

use Drupal\gva_entry\Columns\Column;
use Drupal\gva_entry\Columns\ParticipantsColumnInterface;

class AgeColumn extends Column implements ParticipantsColumnInterface
{

	public function display(array $row = array(), array &$tags = array())
	{
		return $this->checkEmptyValue($row, 'field_age_value');
	}

	public function alter(\SelectQueryInterface $query)
	{
		$age_alias = $query->addJoin('left', 'field_data_field_age', null, '%alias.entity_id = participants.field_participants_target_id');
		$query->addField($age_alias, 'field_age_value');
	}

	public static function label()
	{
		return 'Participant Age';
	}
}
