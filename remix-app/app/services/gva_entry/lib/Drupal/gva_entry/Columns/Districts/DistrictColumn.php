<?php

namespace Drupal\gva_entry\Columns\Districts;

use Drupal\gva_entry\Columns\Column;
use Drupal\gva_entry\Columns\IncidentsColumnInterface;
use Drupal\gva_entry\Columns\ParticipantsColumnInterface;

abstract class DistrictColumn extends Column implements IncidentsColumnInterface, ParticipantsColumnInterface
{
	protected $column_name;
	protected $field_name;

	public function display(array $row = array(), array &$tags = array())
	{
		return $this->checkEmptyValue($row, $this->column_name);
	}

	public function alter(\SelectQueryInterface $query)
	{
		$district = $query->addJoin('left', 'field_data_' . $this->field_name, null, '%alias.entity_id = n.nid');
		$query->addField($district, $this->field_name . '_value', $this->column_name);
	}

	public function getSortField(\SelectQueryInterface &$query)
	{
		return $this->alter($query);
	}
}
