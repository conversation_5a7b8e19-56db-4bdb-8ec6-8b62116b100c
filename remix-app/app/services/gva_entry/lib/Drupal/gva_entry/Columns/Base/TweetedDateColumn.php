<?php

namespace Drupal\gva_entry\Columns\Base;

use Drupal\gva_entry\Columns\Column;
use Drupal\gva_entry\Columns\IncidentsColumnInterface;

class TweetedDateColumn extends Column implements IncidentsColumnInterface {

	public function display(array $row = array(), array &$tags = array())
	{
		return $this->checkEmptyValue($row, 'tweeted_date', function($row, $key, $value) {
			return format_date($value, 'custom', 'F j, Y');
		});
	}

	public function alter(\SelectQueryInterface $query)
	{
		$alias = $query->addJoin('left', 'field_data_field_tweeted_date', null, '%alias.entity_id = n.nid');
		$query->addField($alias, 'field_tweeted_date_value', 'tweeted_date');
	}

	public function getSortField(\SelectQueryInterface &$query)
	{
		$tweeted_date_alias = $query->addJoin('left', 'field_data_field_tweeted_date', null, '%alias.entity_id = n.nid');
		return $tweeted_date_alias . '.field_tweeted_date_value';
	}

  public static function access() {
    Return false;
  }

}
