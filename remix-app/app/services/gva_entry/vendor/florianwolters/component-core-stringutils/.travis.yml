# .travis.yml
#
# YAML configuration file for the continuous integration platform "Travis CI".
#
# This program is free software: you can redistribute it and/or modify it under
# the terms of the GNU Lesser General Public License as published by the Free
# Software Foundation, either version 3 of the License, or (at your option) any
# later version.
#
# This program is distributed in the hope that it will be useful, but WITHOUT
# ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
# FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
# details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with this program. If not, see <http://gnu.org/licenses/lgpl.txt>.
#
# Author:    <PERSON><PERSON><PERSON> <<EMAIL>>
# Copyright: 2010-2013 Florian <PERSON>olters
# License:   http://gnu.org/licenses/lgpl.txt LGPL-3.0+
# Link:      http://github.com/FlorianWolters/PHP-Component-Core-StringUtils

language: php

php:
  - 5.3
  - 5.4
  - 5.5

before_script:
  - curl -s http://getcomposer.org/installer | php
  - php composer.phar install

script: phpunit --coverage-text

notifications:
  email:
    recipients:
      - <EMAIL>
    on_success: change
    on_failure: always
