<?xml version="1.0" encoding="UTF-8"?>
<!-- build file for phing -->
<project default="help" basedir=".">
  <!-- Human-readable info about our component -->
  <property file="build.properties" />
  <taskdef name="now" classname="Phix_Project.ComponentManager.Phing.NowTask" />
  <now name="date.now"/>
  <if>
    <and>
      <isset property="project.snapshot"/>
      <istrue value="${project.snapshot}"/>
    </and>
    <then>
      <property name="project.version" value="${project.majorVersion}.${project.minorVersion}.${project.patchLevel}snapshot${date.now}" />
      <property name="project.stability" value="snapshot" />
    </then>
    <else>
      <property name="project.version" value="${project.majorVersion}.${project.minorVersion}.${project.patchLevel}" />
      <property name="project.stability" value="stable" />
    </else>
  </if>
  <property name="project.apiversion"      value="${project.majorVersion}.${project.minorVersion}" />

  <!-- Paths to the directories that we work with -->
  <property name="project.srcdir"          value="${project.basedir}/src" override="true" />
  <property name="project.src.phpdir"      value="${project.srcdir}/php" override="true" />
  <property name="project.src.bindir"      value="${project.srcdir}/bin" override="true" />
  <property name="project.src.datadir"     value="${project.srcdir}/data" override="true" />
  <property name="project.src.docdir"      value="${project.srcdir}/docs" override="true" />
  <property name="project.src.testdir"     value="${project.srcdir}/tests" override="true" />
  <property name="project.src.wwwdir"      value="${project.srcdir}/www" override="true" />
  <property name="project.src.testunitdir" value="${project.src.testdir}/unit-tests" override="true" />
  <property name="project.src.testintdir"  value="${project.src.testdir}/integration-tests" override="true" />
  <property name="project.src.testfuncdir" value="${project.src.testdir}/functional-tests" override="true" />

  <property name="project.reviewdir"       value="${project.basedir}/review" override="true" />
  <property name="project.review.logsdir"  value="${project.basedir}/review/logs" override="true" />
  <property name="project.review.docsdir"  value="${project.reviewdir}/docs" override="true" />
  <property name="project.review.codebrowserdir" value="${project.reviewdir}/code-browser" override="true" />
  <property name="project.review.codecoveragedir" value="${project.reviewdir}/code-coverage" override="true" />

  <property name="project.builddir"        value="${project.basedir}/.build"  override="true" />
  <property name="project.pkgdir"          value="${project.builddir}/${project.name}-${project.version}" override="true" />
  <property name="project.tmpdir"          value="${project.basedir}/.tmp" override="true" />

  <property name="project.vendordir"       value="${project.basedir}/vendor" override="true" />
  <property name="project.vendor.bindir"   value="${project.vendordir}/bin" override="true" />
  <property name="project.vendor.datadir"  value="${project.vendordir}/data" override="true" />
  <property name="project.vendor.phpdir"   value="${project.vendordir}/php" override="true" />
  <property name="project.vendor.testdir"  value="${project.vendordir}/tests" override="true" />
  <property name="project.vendor.docdir"   value="${project.vendordir}/docs" override="true" />
  <property name="project.vendor.wwwdir"   value="${project.vendordir}/www" override="true" />

  <property name="project.distdir"         value="${project.basedir}/dist" />
  <property name="project.distdir.lastBuilt" value="${project.basedir}/dist/lastBuilt" />
  <property name="project.tarfilename"     value="${project.name}-${project.version}.tgz" />
  <property name="project.tarfile"         value="${project.distdir}/${project.tarfilename}" />

  <!-- what was the last PEAR package we created, if any? -->
  <if>
    <available file="${project.distdir.lastBuilt}"/>
    <then>
      <property file="${project.distdir.lastBuilt}"/>
    </then>
    <else>
      <property name="project.lastBuiltTarfile" value="false"/>
    </else>
  </if>

  <!-- override this if you want to run additional PEAR commands -->
  <property name="pear.cmd" value="" override="true" />

  <!-- lists of the files that make up our package -->
  <fileset dir="${project.src.bindir}" id="binfiles">
    <include name="**/**"/>
    <exclude name="**/.DS_Store"/>
    <exclude name="**/.empty"/>
    <exclude name="**/.svn"/>
  </fileset>
  <fileset dir="${project.src.datadir}" id="datafiles">
    <include name="**/**"/>
    <exclude name="**/.DS_Store"/>
    <exclude name="**/.empty"/>
    <exclude name="**/.svn"/>
  </fileset>
  <fileset dir="${project.src.phpdir}" id="phpfiles">
    <include name="**/**"/>
    <exclude name="**/.DS_Store"/>
    <exclude name="**/.empty"/>
    <exclude name="**/.svn"/>
  </fileset>
  <fileset dir="${project.src.testunitdir}/php" id="testfiles">
    <include name="**/**"/>
    <exclude name="**/.DS_Store"/>
    <exclude name="**/.empty"/>
    <exclude name="**/.svn"/>
  </fileset>
  <fileset dir="${project.src.wwwdir}" id="wwwfiles">
    <include name="**/**" />
    <exclude name="**/.DS_Store"/>
    <exclude name="**/.empty"/>
    <exclude name="**/.svn"/>
  </fileset>
  <fileset dir="${project.src.docdir}" id="docfiles">
    <include name="**/**" />
    <exclude name="**/.DS_Store"/>
    <exclude name="**/.empty"/>
    <exclude name="**/.svn"/>
  </fileset>
  <fileset dir="${project.basedir}" id="topleveldocfiles">
    <include name="*.txt" />
    <include name="*.md" />
  </fileset>

  <taskdef name="phingcallifexists" classname="Phix_Project.ComponentManager.Phing.PhingCallIfExistsTask" />
  <import file="build.local.xml"/>

  <!-- Tell the user what this build file supports -->
  <target name="help">
    <echo message="${project.name} ${project.version}: build.xml targets:" />
    <echo message="" />
    <echo message="Setup your dev environment:" />
    <echo message="" />
    <echo message="  build-vendor" />
    <echo message="    Populate vendor/ with this package's dependencies" />
    <echo message="  vendor-pear" />
    <echo message="    Run additional PEAR commands inside the vendor folder" />
    <echo message="" />
    <echo message="Develop your code:" />
    <echo message="" />
    <echo message="  lint" />
    <echo message="    Check the PHP files for syntax errors" />
    <echo message="  test" />
    <echo message="    Run the component's PHPUnit tests" />
    <echo message="  code-review" />
    <echo message="    Run all of the code quality targets:" />
    <echo message="" />
    <echo message="    code-browser" />
    <echo message="      Run code quality tests for PHP_CodeBrowser" />
    <echo message="    phpcpd" />
    <echo message="      Check for cut and paste problems" />
    <echo message="    phploc" />
    <echo message="      Calculate the size of your PHP project" />
    <echo message="    phpdoc" />
    <echo message="      Create the PHP docs from source code" />
    <echo message="" />
    <echo message="Publish your component:" />
    <echo message="" />
    <echo message="  pear-package" />
    <echo message="    Create a PEAR-compatible package" />
    <echo message="  publish-local" />
    <echo message="    Publish your PEAR-compatible package into a local copy" />
    <echo message="    of your PEAR channel" />
    <echo message="  install-vendor" />
    <echo message="    Install this component from source into vendor/" />
    <echo message="  install-system" />
    <echo message="    Install this component from source for all local users" />
    <echo message="    You must be root to run this target on Linux!!" />
    <echo message=""/>
    <echo message="Maintain your component:"/>
    <echo message=""/>
    <echo message="  upgrade-skeleton"/>
    <echo message="    Upgrade the skeleton files for this component"/>
    <echo message=""/>
    <echo message="Additional targets:" />
    <echo message=""/>
    <echo message="  clean" />
    <echo message="    Remove all temporary folders created by this build file" />
    <echo message="  version" />
    <echo message="    Show this component's version from build.properties" />
    <echo message="" />
    <phingcallifexists target="local.help" />
  </target>

  <!-- Show the current version, as set in build.properties -->
  <!-- This is just to be a time-saver -->
  <target name="version">
    <echo message="${project.version}" />
  </target>

  <!-- Run PHP lint on all of the source code -->
  <target name="lint">
    <phplint>
      <fileset dir="${project.src.phpdir}">
        <include name="**/*.php" />
      </fileset>
    </phplint>
    <phingcallifexists target="local.lint" />
  </target>

  <!-- Run the unit tests for this module -->
  <target name="run-unittests" depends="lint">
    <!-- Make sure vendor/ folder exists -->
    <if>
      <not>
        <available file="${project.vendordir}" type="dir"/>
      </not>
      <then>
        <phingcall target="build-vendor"/>
      </then>
    </if>

    <!-- do we have any tests? -->
    <!-- currently cannot think of a reliable way to test this in phing -->

    <!-- run the tests -->
    <delete dir="${project.review.codecoveragedir}" />
    <mkdir dir="${project.review.codecoveragedir}" />
    <mkdir dir="${project.review.logsdir}" />
    <exec command="phpunit" checkreturn="true" logoutput="true"/>
    <echo/>
    <echo>The code coverage report is in file://${project.review.codecoveragedir}</echo>
    <echo/>
  </target>

  <!-- Run all the tests for this module -->
  <target name="test" depends="run-unittests">
    <phingcallifexists target="local.test"/>
  </target>

  <!-- Run the code review quality tests -->
  <target name="code-review" depends="run-unittests, code-browser, phpcpd, pdepend, phploc">
    <phingcallifexists target="local.code-review"/>
  </target>

  <!-- Run all of the targets for setting up the code browser -->
  <target name="code-browser" depends="phpmd, phpcs, phpcb">
    <phingcallifexists target="local.code-browser"/>
  </target>

  <target name="pdepend">
    <mkdir dir="${project.review.logsdir}" />
    <exec command="pdepend --phpunit-xml=${project.review.logsdir}/pdepend.xml --jdepend-xml=${project.review.logsdir}/jdepend.xml --jdepend-chart=${project.review.logsdir}/dependencies.svg --overview-pyramid=${project.review.logsdir}/overview-pyramid.svg ${project.src.phpdir}" logoutput="true"/>
  </target>

  <!-- Generate package docs -->
  <target name="phpdoc">
    <mkdir dir="${project.review.logsdir}" />
    <exec command="phpdoc -d ${project.src.phpdir} -t ${project.review.docsdir}" logoutput="true"/>
    <echo message="You will find the PHPDoc for your project at: ${project.review.docsdir}/index.html"/>
    <phingcallifexists target="local.phpdoc"/>
  </target>

  <!-- Check code for code smells -->
  <target name="phpmd">
    <mkdir dir="${project.review.logsdir}" />
    <exec command="phpmd ${project.src.phpdir} xml codesize,design,naming,unusedcode --reportfile ${project.review.logsdir}/phpmd.xml" logoutput="true" />
  </target>

  <target name="phpcpd">
    <mkdir dir="${project.review.logsdir}"/>
    <exec command="phpcpd --log-pmd ${project.review.logsdir}/pmd-cpd.xml ${project.src.phpdir}" logoutput="true" />
  </target>

  <!-- Check the code for style violations -->
  <target name="phpcs">
    <mkdir dir="${project.review.logsdir}" />
    <exec command="phpcs --report=xml --report-file=${project.review.logsdir}/checkstyle.xml --standard=${checkstyle.standard} --extensions=php ${project.src.phpdir}" logoutput="true"/>
  </target>

  <!-- Build the code-browser files -->
  <target name="phpcb" depends="phpmd">
    <delete dir="${project.review.codebrowserdir}" />
    <mkdir dir="${project.review.codebrowserdir}" />
    <exec command="phpcb --log ${project.review.logsdir} --source ${project.src.phpdir} --output ${project.review.codebrowserdir}" logoutput="true" />
  </target>

  <!-- Work out the size of the project -->
  <target name="phploc">
    <mkdir dir="${project.review.logsdir}" />
    <exec command="phploc --log-xml ${project.review.logsdir}/phploc.xml --log-csv ${project.review.logsdir}/phploc.csv ${project.src.phpdir}" logoutput="true" />
  </target>

  <!-- Populate vendor with the dependencies for this component -->
  <target name="build-vendor" depends="pear-package,setup-vendor">
    <echo>Populating vendor/ with dependencies</echo>
    <exec command="phix pear:register-channels" checkreturn="true" logoutput="true" />
    <exec command="pear -c ${project.tmpdir}/pear-config install --alldeps ${project.tarfile}" logoutput="true" checkreturn="true"/>
    <echo/>
    <echo>Your vendor/ folder has been built.</echo>
    <echo>You only need to run 'phing build-vendor' again if you change the</echo>
    <echo>dependencies listed in your package.xml file.</echo>
    <echo/>
    <phingcallifexists target="local.buildvendor"/>
  </target>

  <!-- Setup the vendor folder -->
  <target name="setup-vendor">
    <echo>Creating vendor/ as a sandboxed PEAR install folder</echo>
    <delete dir="${project.vendordir}" />
    <mkdir dir="${project.vendordir}" />
    <delete dir="${project.tmpdir}" />
    <mkdir dir="${project.tmpdir}" />
    <exec command="pear config-create ${project.tmpdir} ${project.tmpdir}/pear-config" checkreturn="true" logoutput="true" />
    <exec command="pear -c ${project.tmpdir}/pear-config config-set preferred_state alpha" checkreturn="true" logoutput="true" />
    <exec command="pear -c ${project.tmpdir}/pear-config config-set php_dir ${project.vendor.phpdir}" checkreturn="true" logoutput="true" />
    <exec command="pear -c ${project.tmpdir}/pear-config config-set bin_dir ${project.vendor.bindir}" checkreturn="true" logoutput="true" />
    <exec command="pear -c ${project.tmpdir}/pear-config config-set data_dir ${project.vendor.datadir}" checkreturn="true" logoutput="true" />
    <exec command="pear -c ${project.tmpdir}/pear-config config-set doc_dir ${project.vendor.docdir}" checkreturn="true" logoutput="true" />
    <exec command="pear -c ${project.tmpdir}/pear-config config-set test_dir ${project.vendor.testdir}" checkreturn="true" logoutput="true" />
    <exec command="pear -c ${project.tmpdir}/pear-config config-set www_dir ${project.vendor.wwwdir}" checkreturn="true" logoutput="true" />
  </target>

  <!-- Create the PEAR package, ready for release -->
  <target name="pear-package">
    <echo>Building release directory</echo>
    <delete dir="${project.builddir}" />
    <mkdir dir="${project.pkgdir}" />
    <if>
      <available file="${project.src.bindir}"/>
      <then>
        <copy todir="${project.pkgdir}">
          <fileset refid="binfiles"/>
        </copy>
      </then>
    </if>
    <if>
      <available file="${project.src.datadir}"/>
      <then>
        <copy todir="${project.pkgdir}">
          <fileset refid="datafiles"/>
        </copy>
      </then>
    </if>
    <if>
      <available file="${project.src.docdir}"/>
      <then>
        <copy todir="${project.pkgdir}">
          <fileset refid="docfiles"/>
        </copy>
      </then>
    </if>
    <if>
      <available file="${project.src.phpdir}"/>
      <then>
        <copy todir="${project.pkgdir}">
          <fileset refid="phpfiles"/>
        </copy>
      </then>
    </if>
    <if>
      <available file="${project.src.testunitdir}"/>
      <then>
        <copy todir="${project.pkgdir}">
          <fileset refid="testfiles"/>
        </copy>
      </then>
    </if>
    <if>
      <available file="${project.src.wwwdir}"/>
      <then>
        <copy todir="${project.pkgdir}">
          <fileset refid="wwwfiles"/>
        </copy>
      </then>
    </if>
    <copy todir="${project.pkgdir}">
      <fileset refid="topleveldocfiles"/>
    </copy>
    <copy todir="${project.builddir}">
      <fileset dir=".">
        <include name="package.xml" />
      </fileset>
    </copy>

    <exec command="phix pear:expand-package-xml" checkreturn="yes" logoutput="yes"/>

    <echo>Creating ${project.tarfile} PEAR package</echo>

    <mkdir dir="${project.distdir}" />
    <delete file="${project.tarfile}" />
    <tar destfile="${project.tarfile}" compression="gzip">
      <fileset dir="${project.builddir}">
        <include name="**/**" />
      </fileset>
    </tar>

    <!-- remember the tarball we have just build -->
    <property name="project.lastBuiltTarfile" value="${project.tarfile}" override="true"/>
    <echo file="${project.distdir.lastBuilt}" append="false">project.lastBuiltTarfile=${project.tarfile}</echo>

    <!-- write a message to say which file we built last -->
    <echo>Your PEAR package is in ${project.tarfile}</echo>
    <phingcallifexists target="local.pear-package"/>
  </target>

  <!-- Install the code -->
  <target name="install-vendor">
    <if>
      <not>
        <contains string="${project.lastBuiltTarfile}" substring="${project.name}"/>
      </not>
      <then>
        <fail message="Please run 'phing pear-package' first, then try again."/>
      </then>
    </if>

    <if>
      <not>
        <available file="${project.vendordir"/>
      </not>
      <then>
        <phingcall target="build-vendor" />
      </then>
    </if>

    <if>
      <available file="${project.lastBuiltTarfile}"/>
      <then>
        <exec command="pear -c ${project.tmpdir}/pear-config install --alldeps -f ${project.lastBuiltTarfile}" logoutput="true" checkreturn="true"/>
        <phingcallifexists target="local.install-vendor"/>
      </then>
      <else>
        <echo>Cannot find PEAR package file ${project.lastBuiltTarfile}</echo>
        <fail message="Run 'phing pear-package' to create a new PEAR package, then try again."/>
      </else>
    </if>
  </target>

  <!-- install a package system-wide -->
  <target name="install-system">
    <if>
      <not>
        <contains string="${project.lastBuiltTarfile}" substring="${project.name}"/>
      </not>
      <then>
        <echo>Please run 'phing pear-package' first, then try again.</echo>
      </then>
      <elseif>
        <available file="${project.lastBuiltTarfile}"/>
        <then>
          <exec command="pear install -f -a ${project.lastBuiltTarfile}" checkreturn="true" logoutput="true" />
          <phingcallifexists target="local.install-system"/>
        </then>
      </elseif>
      <else>
        <echo>Cannot find PEAR package file ${project.lastBuiltTarfile}</echo>
        <echo>Run 'phing pear-package' to create a new PEAR package, then try again</echo>
      </else>
    </if>
  </target>

  <!-- Publish to local copy of PEAR channel -->
  <target name="publish-local" depends="pear-package">
    <if>
      <not>
        <contains string="${project.lastBuiltTarfile}" substring="${project.name}"/>
      </not>
      <then>
        <echo>Please run 'phing pear-package' first, then try again.</echo>
      </then>
      <elseif>
        <available file="${project.lastBuiltTarfile}"/>
        <then>
          <!-- get rid of any existing snapshots we may have published -->
          <foreach param="packagefile" absparam="abspackagefile" target="pirum-remove-package">
            <fileset dir="${pear.local}/get">
              <include name="${project.name}*snapshot*.tgz" />
            </fileset>
          </foreach>

          <!-- publish the new PEAR package -->
          <exec command="pirum add ${pear.local} ${project.lastBuiltTarfile}" checkreturn="true" logoutput="true" />
          <phingcallifexists target="local.publish-local"/>
        </then>
      </elseif>
      <else>
        <echo>Cannot find PEAR package file ${project.lastBuiltTarfile}</echo>
        <echo>Run 'phing pear-package' to create a new PEAR package, then try again</echo>
      </else>
    </if>
  </target>

  <target name="pirum-remove-package">
    <exec command="pirum remove ${pear.local} ${packagefile}" logoutput="true" checkreturn="true" />
  </target>

  <!-- Run additional PEAR commands in the vendor folder -->
  <target name="vendor-pear">
    <exec command="pear -c ${project.tmpdir}/pear-config ${pear.cmd}" logoutput="true" checkreturn="true" />
  </target>

  <!-- Upgrade the skeleton files here and now -->
  <target name="upgrade-skeleton">
    <exec command="phix ${component.type}:upgrade ." logoutput="true" checkreturn="true" />
    <phingcallifexists target="local.upgrade-skeleton"/>
  </target>

  <!-- Clean up the mess -->
  <target name="clean">
    <delete dir="${project.builddir}" />
    <delete dir="${project.distdir}" />
    <delete dir="${project.reviewdir}" />
    <delete dir="${project.pkgdir}" />
    <delete dir="${project.distdir}" />
    <delete dir="${project.tmpdir}" />
    <phingcallifexists target="local.clean"/>
  </target>
</project>
<!-- vim: set tabstop=2 shiftwidth=2 expandtab: -->
