# build.properties
#
# Property configuration file for the build tool "Phing".
#
# This program is free software: you can redistribute it and/or modify it under
# the terms of the GNU Lesser General Public License as published by the Free
# Software Foundation, either version 3 of the License, or (at your option) any
# later version.
#
# This program is distributed in the hope that it will be useful, but WITHOUT
# ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
# FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
# details.
#
# You should have received a copy of the GNU Lesser General Public License along
# with this program. If not, see <http://gnu.org/licenses/lgpl.txt>.
#
# Author:    Florian <PERSON>olters <<EMAIL>>
# Copyright: 2011-2013 Florian Wolters
# License:   http://gnu.org/licenses/lgpl.txt LGPL-3.0+
# Link:      http://github.com/FlorianWolters/PHP-Component-Util-Reflection

project.name=Reflection
project.majorVersion=0
project.minorVersion=2
project.patchLevel=0
project.snapshot=false
project.apistability=beta

project.summary="Provides operations for the PHP Reflection API as a PHP component."
project.description="Provides operations for the PHP Reflection API as a PHP component."
project.notes="Added methods."
project.channel=pear.florianwolters.de

project.author.name=Florian Wolters
project.author.user=FlorianWolters
project.author.email=<EMAIL>

project.license.name=LGPL-3.0+
project.license.uri=http://gnu.org/licenses/lgpl.txt

checkstyle.standard=PSR2

pear.local=D:/Users/<USER>/Dropbox/Quelltexte/www/${project.channel}

component.type=php-library
component.version=11
