<?php
/**
 * FlorianWolters\Example\SingletonWithoutArguments
 *
 * PHP Version 5.4
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2012-2014 Florian Wolters (http://blog.florianwolters.de)
 * @license   http://gnu.org/licenses/lgpl.txt LGPL-3.0+
 * @link      http://github.com/FlorianWolters/PHP-Component-Util-Singleton
 */

namespace FlorianWolters\Example;

use FlorianWolters\Component\Util\Singleton\SingletonTrait;

/**
 * The SingletonWithoutArguments class implements a *Singleton* **without**
 * constructor arguments.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2012-2013 Florian <PERSON>olters
 * @license   http://gnu.org/licenses/lgpl.txt LGPL-3.0+
 * @link      http://github.com/FlorianWolters/PHP-Component-Util-Singleton
 * @since     Class available since Release 0.3.0
 */
class SingletonWithoutArguments
{
    use SingletonTrait;
}
