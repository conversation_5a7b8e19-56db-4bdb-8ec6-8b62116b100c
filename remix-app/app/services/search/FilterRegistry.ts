import type { BaseFilter } from './BaseFilter';
import type { FilterConstructor } from './types';
import { ISearchFilter } from './types';
import {
	Date<PERSON><PERSON>e<PERSON>ilter,
	StatusFilter,
	LocationFilter,
	<PERSON><PERSON>ms<PERSON><PERSON>er,
	IncidentTypeFilter,
	<PERSON>dress<PERSON><PERSON><PERSON>,
	Notes<PERSON>ilter,
	BusinessFilter,
	ZipCodeFilter,
	GunTypeFilter,
	ParticipantFilter,
	SourcesFilter
} from './filters';

/**
 * Registry for search filters
 * Provides a central place to register and retrieve filters
 */
export class FilterRegistry {
	private static instance: FilterRegistry;
	private filters: Map<string, BaseFilter> = new Map();

	private constructor() {
		// Private constructor to enforce singleton pattern
		this.registerDefaultFilters();
	}

	/**
	 * Register default filters
	 * @private
	 */
	private registerDefaultFilters(): void {
		// Register standard filters
		this.register(new DateRangeFilter());
		this.register(new StatusFilter());
		this.register(new LocationFilter());
		this.register(new VictimsFilter());
		this.register(new IncidentTypeFilter());

		// Register additional filters
		this.register(new AddressFilter());
		this.register(new NotesFilter());
		this.register(new BusinessFilter());
		this.register(new ZipCodeFilter());
		this.register(new GunTypeFilter());
		this.register(new ParticipantFilter());
		this.register(new SourcesFilter());
	}

	/**
	 * Get the singleton instance of the registry
	 */
	public static getInstance(): FilterRegistry {
		if (!FilterRegistry.instance) {
			FilterRegistry.instance = new FilterRegistry();
		}
		return FilterRegistry.instance;
	}

	/**
	 * Register a filter with the registry
	 * @param filter The filter to register
	 */
	public register(filter: BaseFilter): void {
		this.filters.set(filter.id, filter);
	}

	/**
	 * Register a filter class with the registry
	 * @param filterClass The filter class to register
	 */
	public registerClass(filterClass: FilterConstructor): void {
		const filter = new filterClass();
		this.register(filter);
	}

	/**
	 * Get a filter by ID
	 * @param id The ID of the filter to retrieve
	 * @returns The filter, or undefined if not found
	 */
	public get(id: string): BaseFilter | undefined {
		return this.filters.get(id);
	}

	/**
	 * Get all registered filters
	 * @returns An array of all registered filters
	 */
	public getAll(): BaseFilter[] {
		return Array.from(this.filters.values());
	}

	/**
	 * Get all registered filters as a map
	 * @returns A map of all registered filters, keyed by ID
	 */
	public getAllAsMap(): Map<string, BaseFilter> {
		return new Map(this.filters);
	}

	/**
	 * Find a filter by field name
	 * @param field The field name to search for
	 * @returns The filter, or undefined if not found
	 */
	public findByField(field: string): BaseFilter | undefined {
		return Array.from(this.filters.values()).find(filter => {
			return filter.getFieldName().toLowerCase() === field.toLowerCase();
		});
	}

	/**
	 * Clear all registered filters
	 */
	public clear(): void {
		this.filters.clear();
	}
}
