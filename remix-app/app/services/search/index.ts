// Export base classes
// Export SearchBuilder based on environment
// In client-side code, use the client version
// In server-side code, use the server version
import { SearchBuilder as ServerSearchBuilder } from './SearchBuilder.server';
import { SearchBuilder as ClientSearchBuilder } from './SearchBuilder.client';

export { BaseFilter } from './BaseFilter';
export { BaseColumn } from './BaseColumn';
export { BaseVisualization } from './BaseVisualization';
export { ColumnGroup } from './ColumnGroup';

// Determine if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

// Export the appropriate version
export const SearchBuilder = isBrowser ? ClientSearchBuilder : ServerSearchBuilder;

// Export registries
export { FilterRegistry } from './FilterRegistry';
export { ColumnRegistry } from './ColumnRegistry';
export { VisualizationRegistry } from './VisualizationRegistry';

// Export types
export * from './types';

// Export all filters
export * from './filters';

// Export all columns
export * from './columns';

// Export all visualizations
export * from './visualizations';
