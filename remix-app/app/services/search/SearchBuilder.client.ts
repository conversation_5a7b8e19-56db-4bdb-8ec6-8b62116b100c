// Client-side version of SearchBuilder
// This is a simplified version that doesn't import server-only code

export class SearchBuilder {
	private baseTable: string;
	private baseAlias: string;
	private filterGroups: any[] = [];
	private page: number = 1;
	private pageSize: number = 20;
	private totalCount: number = 0;
	private columns: string[] = [];

	constructor(baseTable: string, baseAlias: string) {
		this.baseTable = baseTable;
		this.baseAlias = baseAlias;
	}

	addFilterGroup(group: any): this {
		this.filterGroups.push(group);
		return this;
	}

	setPage(page: number): this {
		this.page = Math.max(1, page);
		return this;
	}

	setPageSize(pageSize: number): this {
		this.pageSize = Math.max(1, pageSize);
		return this;
	}

	setColumns(columns: string[]): this {
		this.columns = columns;
		return this;
	}

	getTotalCount(): number {
		return this.totalCount;
	}

	async execute(userRoles: string[], format: string = 'table'): Promise<any> {
		// In the client version, we need to make an API call to the server
		// to execute the search
		const response = await fetch('/api/search', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				baseTable: this.baseTable,
				baseAlias: this.baseAlias,
				filterGroups: this.filterGroups,
				page: this.page,
				pageSize: this.pageSize,
				columns: this.columns,
				format,
				userRoles
			})
		});

		if (!response.ok) {
			throw new Error('Search failed');
		}

		const result = await response.json();
		this.totalCount = result.totalCount || 0;
		return result.data;
	}
}
