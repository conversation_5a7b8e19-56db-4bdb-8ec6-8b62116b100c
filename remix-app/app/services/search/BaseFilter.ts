import type { ISearchFilter, SearchPermission, FilterOperatorOption, FilterInputProps, FilterCondition } from './types';
import type { QueryBuilder } from '../query/QueryBuilder.server';
import { DefaultFilterInput } from '~/components/search/inputs/DefaultFilterInput';

export abstract class BaseFilter implements ISearchFilter {
	abstract id: string;
	abstract name: string;
	description?: string;
	abstract permissions: SearchPermission[];
	abstract field: string;

	constructor() {
		// Subclasses should define id, name, field, and permissions as class properties
	}

	/**
	 * Apply the filter to the query builder
	 * @deprecated Filters should not directly modify the QueryBuilder. Use getConditions instead.
	 */
	apply(builder: QueryBuilder, value: any): void {
		// Default implementation that uses getConditions
		const conditions = this.getConditions(value);
		if (conditions) {
			conditions.forEach(condition => {
				if (condition.type === 'raw') {
					builder.whereRaw(condition.sql, condition.params);
				} else {
					builder.where(condition.field, condition.operator, condition.value);
				}
			});
		}
	}

	/**
	 * Get the conditions that should be applied to the query builder
	 * @param value The filter value
	 * @returns An array of conditions to apply
	 */
	abstract getConditions(value: any): FilterCondition[];

	abstract validate(value: any): boolean;

	getFieldName(): string {
		return this.field;
	}

	// Default operators - override in specific filters
	getOperators(): FilterOperatorOption[] {
		return [
			{ value: 'equals', label: 'Equals', inputType: 'text' },
			{ value: 'contains', label: 'Contains', inputType: 'text' },
			{ value: 'not', label: 'Does not equal', inputType: 'text' }
		];
	}

	// Default input component - override for custom inputs
	getInputComponent(operator: string): React.ComponentType<FilterInputProps> {
		const op = this.getOperators().find(o => o.value === operator);
		return op?.component || DefaultFilterInput;
	}

	// Default value for operator
	getDefaultValue(operator: string): any {
		return '';
	}

	// Format value for display
	formatValue(value: any, operator: string): string {
		return String(value);
	}

	hasPermission(userRoles: string[], userPermissions: string[] = []): boolean {
		return this.permissions.some(permission => {
			// Check if user has any of the required roles
			const hasRole = permission.roles.some(role => userRoles.includes(role));

			// If no specific permissions are required, role check is sufficient
			if (!permission.permissions || permission.permissions.length === 0) {
				return hasRole;
			}

			// If specific permissions are required, check if user has any of them
			const hasPermission = permission.permissions.some(perm => userPermissions.includes(perm));

			// User must have both a required role AND a required permission (if specified)
			return hasRole && hasPermission;
		});
	}
}
