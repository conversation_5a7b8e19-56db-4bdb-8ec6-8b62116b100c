import type { BaseColumn } from './BaseColumn';
import type { ColumnConstructor } from './types';
import { ISearchColumn } from './types';
import {
	IncidentIDColumn,
	IncidentDateColumn,
	LocationColumn,
	VictimsColumn,
	IncidentTypeColumn,
	AddressColumn,
	NotesColumn,
	BusinessColumn,
	ZipCodeColumn,
	GunTypeColumn,
	ParticipantColumn,
	SourcesColumn
} from './columns';

/**
 * Registry for search columns
 * Provides a central place to register and retrieve columns
 */
export class ColumnRegistry {
	private static instance: ColumnRegistry;
	private columns: Map<string, BaseColumn> = new Map();

	private constructor() {
		// Private constructor to enforce singleton pattern
		this.registerDefaultColumns();
	}

	/**
	 * Register default columns
	 * @private
	 */
	private registerDefaultColumns(): void {
		// Register standard columns
		this.register(new IncidentIDColumn());
		this.register(new IncidentDateColumn());
		this.register(new LocationColumn());
		this.register(new VictimsColumn());
		this.register(new IncidentTypeColumn());

		// Register additional columns
		this.register(new AddressColumn());
		this.register(new NotesColumn());
		this.register(new BusinessColumn());
		this.register(new ZipCodeColumn());
		this.register(new GunTypeColumn());
		this.register(new ParticipantColumn());
		this.register(new SourcesColumn());
	}

	/**
	 * Get the singleton instance of the registry
	 */
	public static getInstance(): ColumnRegistry {
		if (!ColumnRegistry.instance) {
			ColumnRegistry.instance = new ColumnRegistry();
		}
		return ColumnRegistry.instance;
	}

	/**
	 * Register a column with the registry
	 * @param column The column to register
	 */
	public register(column: BaseColumn): void {
		this.columns.set(column.key, column);
	}

	/**
	 * Register a column class with the registry
	 * @param columnClass The column class to register
	 */
	public registerClass(columnClass: ColumnConstructor): void {
		const column = new columnClass();
		this.register(column);
	}

	/**
	 * Get a column by key
	 * @param key The key of the column to retrieve
	 * @returns The column, or undefined if not found
	 */
	public get(key: string): BaseColumn | undefined {
		return this.columns.get(key);
	}

	/**
	 * Get all registered columns
	 * @returns An array of all registered columns
	 */
	public getAll(): BaseColumn[] {
		return Array.from(this.columns.values());
	}

	/**
	 * Get all registered columns as a map
	 * @returns A map of all registered columns, keyed by key
	 */
	public getAllAsMap(): Map<string, BaseColumn> {
		return new Map(this.columns);
	}

	/**
	 * Clear all registered columns
	 */
	public clear(): void {
		this.columns.clear();
	}
}
