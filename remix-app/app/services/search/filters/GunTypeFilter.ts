import { BaseFilter } from '../BaseFilter';
import type { QueryBuilder } from '../../query/QueryBuilder.server';
import type { SearchPermission, FilterOperatorOption } from '~/services/search/types';
import { DefaultFilterInput } from '~/components/search/inputs/DefaultFilterInput';

export class GunTypeFilter extends BaseFilter {
	id: string = 'gun_type';
	name: string = 'Gun Type';
	field: string = 'gun_type';
	permissions: SearchPermission[] = [{ roles: ['user', 'admin'] }];

	constructor() {
		super();
	}

	getOperators(): FilterOperatorOption[] {
		return [
			{
				value: 'equals',
				label: 'Is',
				inputType: 'select' as const,
				component: DefaultFilterInput,
				options: [
					{ value: 'handgun', label: 'Handgun' },
					{ value: 'rifle', label: 'Rifle' },
					{ value: 'shotgun', label: 'Shotgun' },
					{ value: 'assault_rifle', label: 'Assault Rifle' },
					{ value: 'unknown', label: 'Unknown' }
				]
			},
			{
				value: 'not',
				label: 'Is not',
				inputType: 'select' as const,
				component: DefaultFilterInput,
				options: [
					{ value: 'handgun', label: 'Handgun' },
					{ value: 'rifle', label: 'Rifle' },
					{ value: 'shotgun', label: 'Shotgun' },
					{ value: 'assault_rifle', label: 'Assault Rifle' },
					{ value: 'unknown', label: 'Unknown' }
				]
			},
			{
				value: 'in',
				label: 'Is one of',
				inputType: 'select' as const,
				component: DefaultFilterInput,
				options: [
					{ value: 'handgun', label: 'Handgun' },
					{ value: 'rifle', label: 'Rifle' },
					{ value: 'shotgun', label: 'Shotgun' },
					{ value: 'assault_rifle', label: 'Assault Rifle' },
					{ value: 'unknown', label: 'Unknown' }
				]
			},
			{
				value: 'has_any',
				label: 'Has any gun',
				inputType: 'none' as const
			},
			{
				value: 'has_none',
				label: 'Has no guns',
				inputType: 'none' as const
			}
		];
	}

	getDefaultValue(operator: string) {
		if (operator === 'in') {
			return ['handgun'];
		}
		if (operator === 'has_any' || operator === 'has_none') {
			return undefined;
		}
		return 'handgun';
	}

	formatValue(value: string | string[], operator: string): string {
		if (operator === 'has_any') {
			return 'Has any gun';
		}
		if (operator === 'has_none') {
			return 'Has no guns';
		}
		if (operator === 'in' && Array.isArray(value)) {
			return value.map(v => this.getOptionLabel(v)).join(', ');
		}
		return this.getOptionLabel(value as string);
	}

	private getOptionLabel(value: string): string {
		const options = this.getOperators()[0].options;
		if (!options) return value;

		const option = options.find(opt => opt.value === value);
		return option ? option.label : value;
	}

	apply(builder: QueryBuilder, value: any): void {
		// Add necessary joins if they don't exist
		builder.join('incident_guns', 'incident_guns.incident_id = i.incident_id', 'left', 'ig');
		builder.join('taxonomy', 'taxonomy.tid = ig.gun_type_tid', 'left', 't_gun');

		switch (value.operator) {
			case 'equals':
				builder.whereRaw(`t_gun.value = '${value.value}'`);
				break;
			case 'not':
				builder.whereRaw(`i.incident_id NOT IN (
          SELECT incident_id FROM incident_guns 
          JOIN taxonomy ON taxonomy.tid = incident_guns.gun_type_tid 
          WHERE taxonomy.value = '${value.value}'
        )`);
				break;
			case 'in':
				if (Array.isArray(value.value)) {
					const values = value.value.map((v: string) => `'${v}'`).join(', ');
					builder.whereRaw(`t_gun.value IN (${values})`);
				} else {
					builder.whereRaw(`t_gun.value = '${value.value}'`);
				}
				break;
			case 'has_any':
				builder.whereRaw(`i.guns_involved_counter > 0`);
				break;
			case 'has_none':
				builder.whereRaw(`i.guns_involved_counter = 0 OR i.guns_involved_counter IS NULL`);
				break;
		}
	}

	validate(value: any): boolean {
		if (!value.operator) return false;

		const validOperators = ['equals', 'not', 'in', 'has_any', 'has_none'];
		if (!validOperators.includes(value.operator)) return false;

		// For operators that require a value
		if (['equals', 'not'].includes(value.operator)) {
			return typeof value.value === 'string' && value.value.length > 0;
		}

		// For 'in' operator
		if (value.operator === 'in') {
			return Array.isArray(value.value) && value.value.length > 0;
		}

		// For operators that don't require a value
		return true;
	}
}
