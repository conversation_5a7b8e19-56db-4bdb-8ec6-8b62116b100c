import { BaseFilter } from '../BaseFilter';
import type { QueryBuilder } from '../../query/QueryBuilder.server';
import type { SearchPermission, FilterOperatorOption } from '~/services/search/types';
import { StatusInput } from '~/components/search/inputs/StatusInput';

export class StatusFilter extends BaseFilter {
	id: string = 'status';
	name: string = 'Status';
	field: string = 'status';
	permissions: SearchPermission[] = [{ roles: ['user', 'admin'] }];

	constructor() {
		super();
	}

	getOperators(): FilterOperatorOption[] {
		return [
			{
				value: 'equals',
				label: 'Is',
				inputType: 'radio' as const,
				component: StatusInput,
				options: [
					{ value: 'active', label: 'Active' },
					{ value: 'pending', label: 'Pending' },
					{ value: 'closed', label: 'Closed' },
					{ value: 'archived', label: 'Archived' }
				]
			},
			{
				value: 'not',
				label: 'Is not',
				inputType: 'radio' as const,
				component: StatusInput,
				options: [
					{ value: 'active', label: 'Active' },
					{ value: 'pending', label: 'Pending' },
					{ value: 'closed', label: 'Closed' },
					{ value: 'archived', label: 'Archived' }
				]
			}
		];
	}

	getDefaultValue(operator: string) {
		return '';
	}

	formatValue(value: string, operator: string): string {
		return value.charAt(0).toUpperCase() + value.slice(1);
	}

	apply(builder: QueryBuilder, value: any): void {
		// Handle the case where value.operator might be undefined
		const operator = value.operator || 'equals';

		switch (operator) {
			case 'equals':
				builder.where(this.field, 'equals', value.value);
				break;
			case 'not':
				builder.where(this.field, 'not', value.value);
				break;
			default:
				// Default to equals if operator is not recognized
				builder.where(this.field, 'equals', value.value);
				break;
		}
	}

	validate(value: any): boolean {
		// If value.value is missing, validation fails
		if (!value.value) return false;

		// If operator is missing, default to 'equals'
		const operator = value.operator || 'equals';

		const validOperators = ['equals', 'not'];
		const validStatuses = ['active', 'pending', 'closed', 'archived'];

		return validOperators.includes(operator) && validStatuses.includes(value.value);
	}
}
