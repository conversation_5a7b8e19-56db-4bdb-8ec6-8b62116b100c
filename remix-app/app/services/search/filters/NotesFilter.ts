import { BaseFilter } from '../BaseFilter';
import type { QueryBuilder } from '../../query/QueryBuilder.server';
import type { SearchPermission, FilterOperatorOption } from '~/services/search/types';
import { DefaultFilterInput } from '~/components/search/inputs/DefaultFilterInput';

export class NotesFilter extends BaseFilter {
	id: string = 'notes';
	name: string = 'Notes';
	field: string = 'notes';
	permissions: SearchPermission[] = [{ roles: ['user', 'admin'] }];

	constructor() {
		super();
	}

	getOperators(): FilterOperatorOption[] {
		return [
			{
				value: 'contains',
				label: 'Contains',
				inputType: 'text' as const,
				component: DefaultFilterInput
			},
			{
				value: 'not_contains',
				label: 'Does not contain',
				inputType: 'text' as const,
				component: DefaultFilterInput
			},
			{
				value: 'is_empty',
				label: 'Is empty',
				inputType: 'none' as const
			},
			{
				value: 'is_not_empty',
				label: 'Is not empty',
				inputType: 'none' as const
			}
		];
	}

	getDefaultValue(operator: string) {
		if (operator === 'is_empty' || operator === 'is_not_empty') {
			return undefined;
		}
		return '';
	}

	formatValue(value: string | undefined, operator: string): string {
		if (operator === 'is_empty') {
			return 'Is empty';
		}
		if (operator === 'is_not_empty') {
			return 'Is not empty';
		}
		return value || '';
	}

	apply(builder: QueryBuilder, value: any): void {
		// Ensure value.value is not null or undefined for operators that require it
		const filterValue = value.value !== null && value.value !== undefined ? value.value : '';

		switch (value.operator) {
			case 'contains':
				builder.where(this.field, 'contains', filterValue);
				break;
			case 'not_contains':
				builder.whereRaw(`${this.field} NOT ILIKE '%${filterValue}%'`);
				break;
			case 'is_empty':
				builder.whereRaw(`(${this.field} IS NULL OR ${this.field} = '')`);
				break;
			case 'is_not_empty':
				builder.whereRaw(`(${this.field} IS NOT NULL AND ${this.field} != '')`);
				break;
		}
	}

	validate(value: any): boolean {
		if (!value.operator) return false;

		const validOperators = ['contains', 'not_contains', 'is_empty', 'is_not_empty'];
		if (!validOperators.includes(value.operator)) return false;

		// For operators that require a value
		if (['contains', 'not_contains'].includes(value.operator)) {
			return typeof value.value === 'string' && value.value.length > 0;
		}

		// For operators that don't require a value
		return true;
	}
}
