import { BaseFilter } from '../BaseFilter';
import type { QueryBuilder } from '../../query/QueryBuilder.server';
import type { SearchPermission, FilterOperatorOption } from '~/services/search/types';
import { DefaultFilterInput } from '~/components/search/inputs/DefaultFilterInput';

export class ZipCodeFilter extends BaseFilter {
	id: string = 'zipcode';
	name: string = 'ZIP Code';
	field: string = 'zipcode';
	permissions: SearchPermission[] = [{ roles: ['user', 'admin'] }];

	constructor() {
		super();
	}

	getOperators(): FilterOperatorOption[] {
		return [
			{
				value: 'equals',
				label: 'Is',
				inputType: 'text' as const,
				component: DefaultFilterInput
			},
			{
				value: 'not',
				label: 'Is not',
				inputType: 'text' as const,
				component: DefaultFilterInput
			},
			{
				value: 'starts_with',
				label: 'Starts with',
				inputType: 'text' as const,
				component: DefaultFilterInput
			},
			{
				value: 'in',
				label: 'Is one of',
				inputType: 'text' as const,
				component: DefaultFilterInput
			}
		];
	}

	getDefaultValue(operator: string) {
		if (operator === 'in') {
			return '';
		}
		return '';
	}

	formatValue(value: string, operator: string): string {
		if (operator === 'in' && value.includes(',')) {
			return value.split(',').join(', ');
		}
		return value;
	}

	apply(builder: QueryBuilder, value: any): void {
		switch (value.operator) {
			case 'equals':
				builder.where(this.field, 'equals', value.value);
				break;
			case 'not':
				builder.where(this.field, 'not', value.value);
				break;
			case 'starts_with':
				builder.whereRaw(`${this.field} LIKE '${value.value}%'`);
				break;
			case 'in':
				if (value.value && value.value.includes(',')) {
					const zipCodes = value.value.split(',').map((zip: string) => zip.trim());
					builder.where(this.field, 'in', zipCodes);
				} else {
					builder.where(this.field, 'equals', value.value);
				}
				break;
		}
	}

	validate(value: any): boolean {
		if (!value.operator || !value.value) return false;
		const validOperators = ['equals', 'not', 'starts_with', 'in'];

		if (!validOperators.includes(value.operator)) return false;

		if (value.operator === 'in' && value.value.includes(',')) {
			// For 'in' operator with multiple values, check each ZIP code
			const zipCodes = value.value.split(',').map((zip: string) => zip.trim());
			return zipCodes.every((zip: string) => this.isValidZipCode(zip));
		}

		return this.isValidZipCode(value.value);
	}

	private isValidZipCode(zip: string): boolean {
		// Basic validation for US ZIP codes (5 digits or 5+4 format)
		return /^\d{5}(-\d{4})?$/.test(zip);
	}
}
