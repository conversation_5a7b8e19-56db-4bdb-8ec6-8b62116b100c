import { BaseFilter } from '../BaseFilter';
import type { QueryBuilder } from '../../query/QueryBuilder.server';
import type { SearchPermission, FilterOperatorOption } from '~/services/search/types';
import { IncidentTypeInput } from '~/components/search/inputs/IncidentTypeInput';

export class IncidentTypeFilter extends BaseFilter {
	id: string = 'incident_type';
	name: string = 'Incident Type';
	field: string = 'incident_type';
	permissions: SearchPermission[] = [{ roles: ['user', 'admin'] }];

	constructor() {
		super();
	}

	getOperators(): FilterOperatorOption[] {
		const incidentTypeOptions = [
			{ value: 'mass_shooting', label: 'Mass Shooting' },
			{ value: 'domestic_violence', label: 'Domestic Violence' },
			{ value: 'officer_involved', label: 'Officer Involved' },
			{ value: 'defensive_use', label: 'Defensive Use' },
			{ value: 'unintentional', label: 'Unintentional' },
			{ value: 'suicide', label: 'Suicide' },
			{ value: 'other', label: 'Other' }
		];

		return [
			{
				value: 'equals',
				label: 'Is',
				inputType: 'radio' as const,
				component: IncidentTypeInput,
				options: incidentTypeOptions
			},
			{
				value: 'not',
				label: 'Is not',
				inputType: 'radio' as const,
				component: IncidentTypeInput,
				options: incidentTypeOptions
			},
			{
				value: 'in',
				label: 'Is one of',
				inputType: 'select' as const,
				component: IncidentTypeInput,
				options: incidentTypeOptions
			}
		];
	}

	getDefaultValue(operator: string) {
		return operator === 'in' ? ['mass_shooting'] : 'mass_shooting';
	}

	formatValue(value: string | string[], operator: string): string {
		if (operator === 'in' && Array.isArray(value)) {
			return value.map(v => this.getOptionLabel(v)).join(', ');
		}
		return this.getOptionLabel(value as string);
	}

	private getOptionLabel(value: string): string {
		const options = this.getOperators()[0].options;
		if (!options) return value;

		const option = options.find(opt => opt.value === value);
		return option ? option.label : value;
	}

	apply(builder: QueryBuilder, value: any): void {
		switch (value.operator) {
			case 'equals':
				builder.where(this.field, 'equals', value.value);
				break;
			case 'not':
				builder.where(this.field, 'not', value.value);
				break;
			case 'in':
				if (Array.isArray(value.value)) {
					builder.where(this.field, 'in', value.value);
				} else {
					builder.where(this.field, 'equals', value.value);
				}
				break;
		}
	}

	validate(value: any): boolean {
		if (!value.operator) return false;
		const validOperators = ['equals', 'not', 'in'];
		const validTypes = [
			'mass_shooting',
			'domestic_violence',
			'officer_involved',
			'defensive_use',
			'unintentional',
			'suicide',
			'other'
		];

		if (!validOperators.includes(value.operator)) return false;

		if (value.operator === 'in') {
			if (!Array.isArray(value.value) || value.value.length === 0) return false;
			return value.value.every((v: string) => validTypes.includes(v));
		}

		return validTypes.includes(value.value);
	}
}
