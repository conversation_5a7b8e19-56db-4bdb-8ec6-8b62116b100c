import { BaseFilter } from '../BaseFilter';
import type { QueryBuilder } from '../../query/QueryBuilder.server';
import type { SearchPermission, FilterOperatorOption } from '~/services/search/types';
import { LocationInput } from '~/components/search/inputs/LocationInput';

export class LocationFilter extends BaseFilter {
	id: string = 'location';
	name: string = 'Location';
	field: string = 'city_or_county';
	permissions: SearchPermission[] = [{ roles: ['user', 'admin'] }];

	constructor() {
		super();
	}

	getOperators(): FilterOperatorOption[] {
		return [
			{
				value: 'equals',
				label: 'Is',
				inputType: 'autocomplete' as const,
				component: LocationInput
			},
			{
				value: 'contains',
				label: 'Contains',
				inputType: 'autocomplete' as const,
				component: LocationInput
			},
			{
				value: 'not',
				label: 'Is not',
				inputType: 'autocomplete' as const,
				component: LocationInput
			},
			{
				value: 'dwithin',
				label: 'Within distance',
				inputType: 'object' as const,
				component: LocationInput
			}
		];
	}

	getDefaultValue(operator: string) {
		if (operator === 'dwithin') {
			return { point: '', distance: 1000 }; // Default 1000 meters
		}
		return '';
	}

	formatValue(value: any, operator: string): any {
		if (operator === 'dwithin') {
			if (typeof value === 'object' && value !== null) {
				return value;
			}
			return { point: value, distance: 1000 }; // Default 1000 meters
		}
		return value;
	}

	apply(builder: QueryBuilder, value: any): void {
		// Log the value object for debugging
		console.log('LocationFilter apply called with value:', JSON.stringify(value, null, 2));

		switch (value.operator) {
			case 'equals':
				if (typeof value.value === 'object' && value.value !== null) {
					// Handle location object with state and city properties
					if ('state' in value.value) {
						builder.where('state', 'equals', value.value.state);

						// If city is provided and not null, add it to the query
						if (value.value.city) {
							builder.where(this.field, 'equals', value.value.city);
						}
					} else {
						// Fall back to original behavior
						builder.where(this.field, 'equals', value.value);
					}
				} else {
					// Original behavior for string values
					builder.where(this.field, 'equals', value.value);
				}
				break;
			case 'contains':
				if (typeof value.value === 'object' && value.value !== null && 'state' in value.value) {
					// Handle location object with state and city properties
					builder.where('state', 'contains', value.value.state);

					// If city is provided and not null, add it to the query
					if (value.value.city) {
						builder.where(this.field, 'contains', value.value.city);
					}
				} else {
					// Original behavior
					builder.where(this.field, 'contains', value.value);
				}
				break;
			case 'not':
				if (typeof value.value === 'object' && value.value !== null && 'state' in value.value) {
					// Handle location object with state and city properties
					builder.where('state', 'not', value.value.state);

					// If city is provided and not null, add it to the query
					if (value.value.city) {
						builder.where(this.field, 'not', value.value.city);
					}
				} else {
					// Original behavior
					builder.where(this.field, 'not', value.value);
				}
				break;
			case 'dwithin':
				// For geography columns, we need to use the dwithin operator
				// The value should be an object with point and distance properties
				if (
					typeof value.value === 'object' &&
					value.value !== null &&
					'point' in value.value &&
					'distance' in value.value
				) {
					builder.where(this.field, 'dwithin', value.value);
				} else if (typeof value.value === 'string') {
					// If the value is a string, assume it's a point and use default distance
					builder.where(this.field, 'dwithin', {
						point: { type: 'geography', value: value.value },
						distance: 1000 // Default 1000 meters
					});
				}
				break;
		}
	}

	validate(value: any): boolean {
		if (!value.operator || value.value === undefined) return false;
		const validOperators = ['equals', 'contains', 'not', 'dwithin'];

		if (!validOperators.includes(value.operator)) return false;

		if (value.operator === 'dwithin') {
			if (typeof value.value === 'object' && value.value !== null) {
				return 'point' in value.value && 'distance' in value.value;
			}
			return typeof value.value === 'string';
		}

		// Handle location object with state and city properties
		if (typeof value.value === 'object' && value.value !== null) {
			// For location objects, we require at least the state property to exist and not be null
			// The city property can be null or undefined
			return 'state' in value.value && value.value.state !== null;
		}

		// Original validation for string values
		return typeof value.value === 'string';
	}
}
