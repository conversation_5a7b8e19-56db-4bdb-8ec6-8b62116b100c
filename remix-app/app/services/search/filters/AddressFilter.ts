import { BaseFilter } from '../BaseFilter';
import type { QueryBuilder } from '../../query/QueryBuilder.server';
import type { SearchPermission, FilterOperatorOption, FilterFormProps } from '~/services/search/types';
import { DefaultFilterInput } from '~/components/search/inputs/DefaultFilterInput';
import { AddressFilterForm } from '~/components/search/forms/AddressFilterForm';
import type { ComponentType } from 'react';

export class AddressFilter extends BaseFilter {
	id: string = 'address';
	name: string = 'Address';
	field: string = 'address';
	permissions: SearchPermission[] = [{ roles: ['user', 'admin'] }];

	constructor() {
		super();
	}

	/** Use default input component
	getFormComponent(): ComponentType<FilterFormProps> {
		return AddressFilterForm;
	}
	*/

	handleSubmit(value: any): any {
		// Process the form data before applying the filter
		// This is where you can transform the data if needed
		return {
			operator: value.operator,
			value: value.address
		};
	}

	getOperators(): FilterOperatorOption[] {
		return [
			{
				value: 'equals',
				label: 'Is',
				inputType: 'text' as const,
				component: DefaultFilterInput
			},
			{
				value: 'contains',
				label: 'Contains',
				inputType: 'text' as const,
				component: DefaultFilterInput
			},
			{
				value: 'not',
				label: 'Is not',
				inputType: 'text' as const,
				component: DefaultFilterInput
			},
			{
				value: 'starts_with',
				label: 'Starts with',
				inputType: 'text' as const,
				component: DefaultFilterInput
			},
			{
				value: 'ends_with',
				label: 'Ends with',
				inputType: 'text' as const,
				component: DefaultFilterInput
			}
		];
	}

	getDefaultValue(operator: string) {
		return '';
	}

	formatValue(value: string, operator: string): string {
		return value;
	}

	apply(builder: QueryBuilder, value: any): void {
		// Ensure value.value is not null or undefined for operators that require it
		const filterValue = value.value !== null && value.value !== undefined ? value.value : '';

		switch (value.operator) {
			case 'equals':
				builder.where(this.field, 'equals', filterValue);
				break;
			case 'contains':
				builder.where(this.field, 'contains', filterValue);
				break;
			case 'not':
				builder.where(this.field, 'not', filterValue);
				break;
			case 'starts_with':
				builder.whereRaw(`${this.field} LIKE '${filterValue}%'`);
				break;
			case 'ends_with':
				builder.whereRaw(`${this.field} LIKE '%${filterValue}'`);
				break;
		}
	}

	validate(value: any): boolean {
		if (!value.operator || !value.value) return false;
		const validOperators = ['equals', 'contains', 'not', 'starts_with', 'ends_with'];
		return validOperators.includes(value.operator) && typeof value.value === 'string';
	}
}
