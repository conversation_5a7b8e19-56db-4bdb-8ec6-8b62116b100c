import { BaseFilter } from '../BaseFilter';
import type { QueryBuilder } from '../../query/QueryBuilder.server';
import type { SearchPermission, FilterOperatorOption } from '~/services/search/types';
import { DefaultFilterInput } from '~/components/search/inputs/DefaultFilterInput';

export class SourcesFilter extends BaseFilter {
	id: string = 'sources';
	name: string = 'Sources';
	field: string = 'sources';
	permissions: SearchPermission[] = [{ roles: ['user', 'admin'] }];

	constructor() {
		super();
	}

	getOperators(): FilterOperatorOption[] {
		return [
			{
				value: 'name_contains',
				label: 'Source name contains',
				inputType: 'text' as const,
				component: DefaultFilterInput
			},
			{
				value: 'url_contains',
				label: 'URL contains',
				inputType: 'text' as const,
				component: DefaultFilterInput
			},
			{
				value: 'has_image',
				label: 'Has image',
				inputType: 'none' as const
			},
			{
				value: 'min_sources',
				label: 'Minimum sources',
				inputType: 'text' as const,
				component: DefaultFilterInput
			},
			{
				value: 'has_any',
				label: 'Has any source',
				inputType: 'none' as const
			},
			{
				value: 'has_none',
				label: 'Has no sources',
				inputType: 'none' as const
			}
		];
	}

	getDefaultValue(operator: string) {
		switch (operator) {
			case 'name_contains':
			case 'url_contains':
				return '';
			case 'min_sources':
				return '1';
			case 'has_image':
			case 'has_any':
			case 'has_none':
				return undefined;
			default:
				return '';
		}
	}

	formatValue(value: any, operator: string): string {
		switch (operator) {
			case 'name_contains':
				return `Source name contains "${value}"`;
			case 'url_contains':
				return `URL contains "${value}"`;
			case 'has_image':
				return 'Has image';
			case 'min_sources':
				return `At least ${value} source${parseInt(value, 10) !== 1 ? 's' : ''}`;
			case 'has_any':
				return 'Has any source';
			case 'has_none':
				return 'Has no sources';
			default:
				return String(value);
		}
	}

	apply(builder: QueryBuilder, value: any): void {
		// Add join to incident_sources table if needed
		builder.join('incident_sources', 'incident_sources.incident_id = i.incident_id', 'left', 'is');

		switch (value.operator) {
			case 'name_contains':
				builder.whereRaw(`is.source_name ILIKE '%${value.value}%'`);
				break;
			case 'url_contains':
				builder.whereRaw(`is.source_url ILIKE '%${value.value}%'`);
				break;
			case 'has_image':
				builder.whereRaw(`is.image_fid IS NOT NULL AND is.image_fid > 0`);
				break;
			case 'min_sources':
				const minSources = parseInt(value.value, 10);
				if (!isNaN(minSources)) {
					builder.having(`COUNT(DISTINCT is.source_entity_id) >= ${minSources}`);
				}
				break;
			case 'has_any':
				builder.whereRaw(`is.source_entity_id IS NOT NULL`);
				break;
			case 'has_none':
				builder.whereRaw(`is.source_entity_id IS NULL`);
				break;
		}
	}

	validate(value: any): boolean {
		if (!value.operator) return false;

		const validOperators = ['name_contains', 'url_contains', 'has_image', 'min_sources', 'has_any', 'has_none'];
		if (!validOperators.includes(value.operator)) return false;

		switch (value.operator) {
			case 'name_contains':
			case 'url_contains':
				return typeof value.value === 'string' && value.value.length > 0;
			case 'min_sources':
				const minSources = parseInt(value.value, 10);
				return !isNaN(minSources) && minSources >= 0;
			case 'has_image':
			case 'has_any':
			case 'has_none':
				return true;
			default:
				return false;
		}
	}
}
