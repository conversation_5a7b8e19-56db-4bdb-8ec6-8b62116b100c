import { BaseFilter } from '../BaseFilter';
import type { QueryBuilder } from '../../query/QueryBuilder.server';
import type { SearchPermission, FilterOperatorOption } from '~/services/search/types';
import { VictimsInput } from '~/components/search/inputs/VictimsInput';

export class VictimsFilter extends BaseFilter {
	id: string = 'victims';
	name: string = 'Victims';
	field: string = 'victims_total';
	permissions: SearchPermission[] = [{ roles: ['user', 'admin'] }];

	constructor() {
		super();
	}

	getOperators(): FilterOperatorOption[] {
		return [
			{
				value: 'equals',
				label: 'Equals',
				inputType: 'text' as const,
				component: VictimsInput
			},
			{
				value: 'gt',
				label: 'Greater than',
				inputType: 'text' as const,
				component: VictimsInput
			},
			{
				value: 'lt',
				label: 'Less than',
				inputType: 'text' as const,
				component: VictimsInput
			},
			{
				value: 'between',
				label: 'Between',
				inputType: 'daterange' as const,
				component: VictimsInput
			}
		];
	}

	getDefaultValue(operator: string) {
		return operator === 'between' ? '1,10' : '1';
	}

	formatValue(value: string, operator: string): string {
		if (operator === 'between' && value.includes(',')) {
			const [min, max] = value.split(',');
			return `${min} - ${max}`;
		}
		return value;
	}

	apply(builder: QueryBuilder, value: any): void {
		switch (value.operator) {
			case 'equals':
				builder.where(this.field, 'equals', parseInt(value.value, 10));
				break;
			case 'gt':
				builder.where(this.field, 'gt', parseInt(value.value, 10));
				break;
			case 'lt':
				builder.where(this.field, 'lt', parseInt(value.value, 10));
				break;
			case 'between':
				if (value.value && value.value.includes(',')) {
					const [min, max] = value.value.split(',').map((v: string) => parseInt(v.trim(), 10));
					builder.where(this.field, 'gte', min);
					builder.where(this.field, 'lte', max);
				}
				break;
		}
	}

	validate(value: any): boolean {
		if (!value.operator || !value.value) return false;
		const validOperators = ['equals', 'gt', 'lt', 'between'];

		if (!validOperators.includes(value.operator)) return false;

		if (value.operator === 'between') {
			if (!value.value.includes(',')) return false;
			const [min, max] = value.value.split(',').map((v: string) => parseInt(v.trim(), 10));
			return !isNaN(min) && !isNaN(max) && min <= max;
		}

		return !isNaN(parseInt(value.value, 10));
	}
}
