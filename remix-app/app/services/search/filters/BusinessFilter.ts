import { BaseFilter } from '../BaseFilter';
import type { QueryBuilder } from '../../query/QueryBuilder.server';
import type { SearchPermission, FilterOperatorOption } from '~/services/search/types';
import { DefaultFilterInput } from '~/components/search/inputs/DefaultFilterInput';

export class BusinessFilter extends BaseFilter {
	id: string = 'business';
	name: string = 'Business';
	field: string = 'business';
	permissions: SearchPermission[] = [{ roles: ['user', 'admin'] }];

	constructor() {
		super();
	}

	getOperators(): FilterOperatorOption[] {
		return [
			{
				value: 'equals',
				label: 'Is',
				inputType: 'text' as const,
				component: DefaultFilterInput
			},
			{
				value: 'contains',
				label: 'Contains',
				inputType: 'text' as const,
				component: DefaultFilterInput
			},
			{
				value: 'not',
				label: 'Is not',
				inputType: 'text' as const,
				component: DefaultFilterInput
			},
			{
				value: 'is_empty',
				label: 'Is empty',
				inputType: 'none' as const
			},
			{
				value: 'is_not_empty',
				label: 'Is not empty',
				inputType: 'none' as const
			}
		];
	}

	getDefaultValue(operator: string) {
		if (operator === 'is_empty' || operator === 'is_not_empty') {
			return undefined;
		}
		return '';
	}

	formatValue(value: string | undefined, operator: string): string {
		if (operator === 'is_empty') {
			return 'Is empty';
		}
		if (operator === 'is_not_empty') {
			return 'Is not empty';
		}
		return value || '';
	}

	apply(builder: QueryBuilder, value: any): void {
		switch (value.operator) {
			case 'equals':
				builder.where(this.field, 'equals', value.value);
				break;
			case 'contains':
				builder.where(this.field, 'contains', value.value);
				break;
			case 'not':
				builder.where(this.field, 'not', value.value);
				break;
			case 'is_empty':
				builder.whereRaw(`(${this.field} IS NULL OR ${this.field} = '')`);
				break;
			case 'is_not_empty':
				builder.whereRaw(`(${this.field} IS NOT NULL AND ${this.field} != '')`);
				break;
		}
	}

	validate(value: any): boolean {
		if (!value.operator) return false;

		const validOperators = ['equals', 'contains', 'not', 'is_empty', 'is_not_empty'];
		if (!validOperators.includes(value.operator)) return false;

		// For operators that require a value
		if (['equals', 'contains', 'not'].includes(value.operator)) {
			return typeof value.value === 'string' && value.value.length > 0;
		}

		// For operators that don't require a value
		return true;
	}
}
