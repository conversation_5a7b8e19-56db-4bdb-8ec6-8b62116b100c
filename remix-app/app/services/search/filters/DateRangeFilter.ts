import { BaseFilter } from '../BaseFilter';
import type { QueryBuilder } from '../../query/QueryBuilder.server';
import type { SearchPermission, FilterOperatorOption } from '~/services/search/types';
import { DateRangeInput } from '~/components/search/inputs/DateRangeInput';
import { format } from 'date-fns';

type DateRangeValue = {
	start?: Date;
	end?: Date;
};

export class DateRangeFilter extends BaseFilter {
	id: string = 'date_range';
	name: string = 'Date Range';
	protected field: string = 'incident_date';
	permissions: SearchPermission[] = [{ roles: ['user', 'admin'] }];

	constructor() {
		super();
	}

	getOperators(): FilterOperatorOption[] {
		return [
			{
				value: 'between',
				label: 'Between',
				inputType: 'daterange' as const,
				component: DateRangeInput
			},
			{
				value: 'this_year',
				label: 'This Year',
				inputType: 'none' as const
			},
			{
				value: 'last_year',
				label: 'Last Year',
				inputType: 'none' as const
			},
			{
				value: 'next_year',
				label: 'Next Year',
				inputType: 'none' as const
			}
		];
	}

	getDefaultValue(operator: string): DateRangeValue | undefined {
		switch (operator) {
			case 'between':
				return { start: undefined, end: undefined };
			case 'this_year':
			case 'last_year':
			case 'next_year':
				return undefined;
			default:
				return undefined;
		}
	}

	formatValue(value: DateRangeValue | undefined, operator: string): string {
		switch (operator) {
			case 'between':
				if (!value?.start || !value?.end) return '';
				return `${format(value.start, 'MMM d, yyyy')} - ${format(value.end, 'MMM d, yyyy')}`;
			case 'this_year':
				return 'This Year';
			case 'last_year':
				return 'Last Year';
			case 'next_year':
				return 'Next Year';
			default:
				return '';
		}
	}

	apply(builder: QueryBuilder, value: any): void {
		// Log the value object for debugging
		console.log('DateRangeFilter apply called with value:', JSON.stringify(value, null, 2));

		switch (value.operator) {
			case 'equals':
			case 'between':
				// Check if value has a nested value property with start/end dates
				const dateValue = value.value || value;

				if (dateValue.start) {
					// Ensure date is properly formatted
					const startDate = dateValue.start instanceof Date ? dateValue.start : new Date(dateValue.start);
					builder.where(this.field, 'gte', startDate);
				}
				if (dateValue.end) {
					// Ensure date is properly formatted
					const endDate = dateValue.end instanceof Date ? dateValue.end : new Date(dateValue.end);
					builder.where(this.field, 'lte', endDate);
				}
				break;
			case 'this_year':
				builder.whereRaw(`EXTRACT(YEAR FROM ${this.field}) = EXTRACT(YEAR FROM CURRENT_DATE)`);
				break;
			case 'last_year':
				builder.whereRaw(`EXTRACT(YEAR FROM ${this.field}) = EXTRACT(YEAR FROM CURRENT_DATE) - 1`);
				break;
			case 'next_year':
				builder.whereRaw(`EXTRACT(YEAR FROM ${this.field}) = EXTRACT(YEAR FROM CURRENT_DATE) + 1`);
				break;
			default:
				console.warn(`DateRangeFilter: Unhandled operator "${value.operator}"`);
				break;
		}
	}

	validate(value: any): boolean {
		if (value.operator === 'between' || value.operator === 'equals') {
			// Check if value has a nested value property with start/end dates
			const dateValue = value.value || value;

			// For date range filters, we only require at least one of start or end to be present
			// Handle both direct properties and nested value objects
			return dateValue && (dateValue.start || dateValue.end);
		}
		return ['this_year', 'last_year', 'next_year'].includes(value.operator);
	}
}
