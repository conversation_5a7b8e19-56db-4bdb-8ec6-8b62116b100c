import { BaseFilter } from '../BaseFilter';
import type { QueryBuilder } from '../../query/QueryBuilder.server';
import type { SearchPermission, FilterOperatorOption } from '~/services/search/types';
import { DefaultFilterInput } from '~/components/search/inputs/DefaultFilterInput';

export class ParticipantFilter extends BaseFilter {
	id: string = 'participant';
	name: string = 'Participant';
	field: string = 'participant';
	permissions: SearchPermission[] = [{ roles: ['user', 'admin'] }];

	constructor() {
		super();
	}

	getOperators(): FilterOperatorOption[] {
		return [
			{
				value: 'name_contains',
				label: 'Name contains',
				inputType: 'text' as const,
				component: DefaultFilterInput
			},
			{
				value: 'type_equals',
				label: 'Type is',
				inputType: 'select' as const,
				component: DefaultFilterInput,
				options: [
					{ value: 'Victim', label: 'Victim' },
					{ value: 'Subject-Suspect', label: 'Subject/Suspect' },
					{ value: 'Officer', label: 'Officer' }
				]
			},
			{
				value: 'age_equals',
				label: 'Age equals',
				inputType: 'text' as const,
				component: DefaultFilterInput
			},
			{
				value: 'age_range',
				label: 'Age between',
				inputType: 'text' as const,
				component: DefaultFilterInput
			},
			{
				value: 'gender_equals',
				label: 'Gender is',
				inputType: 'select' as const,
				component: DefaultFilterInput,
				options: [
					{ value: 'Male', label: 'Male' },
					{ value: 'Female', label: 'Female' },
					{ value: 'Unknown', label: 'Unknown' }
				]
			},
			{
				value: 'min_victims',
				label: 'Minimum victims',
				inputType: 'text' as const,
				component: DefaultFilterInput
			}
		];
	}

	getDefaultValue(operator: string) {
		switch (operator) {
			case 'name_contains':
				return '';
			case 'type_equals':
				return 'Victim';
			case 'age_equals':
				return '';
			case 'age_range':
				return '18,30';
			case 'gender_equals':
				return 'Male';
			case 'min_victims':
				return '1';
			default:
				return '';
		}
	}

	formatValue(value: any, operator: string): string {
		switch (operator) {
			case 'name_contains':
				return `Name contains "${value}"`;
			case 'type_equals':
				return `Type is ${value}`;
			case 'age_equals':
				return `Age is ${value}`;
			case 'age_range':
				if (typeof value === 'string' && value.includes(',')) {
					const [min, max] = value.split(',');
					return `Age between ${min} and ${max}`;
				}
				return `Age range ${value}`;
			case 'gender_equals':
				return `Gender is ${value}`;
			case 'min_victims':
				return `At least ${value} victim${parseInt(value, 10) !== 1 ? 's' : ''}`;
			default:
				return String(value);
		}
	}

	apply(builder: QueryBuilder, value: any): void {
		// Add join to incident_participants table if needed
		builder.join('incident_participants', 'incident_participants.incident_id = i.incident_id', 'left', 'ip');

		switch (value.operator) {
			case 'name_contains':
				builder.whereRaw(`ip.name ILIKE '%${value.value}%'`);
				break;
			case 'type_equals':
				builder.whereRaw(`ip.participant_type = '${value.value}'`);
				break;
			case 'age_equals':
				const age = parseInt(value.value, 10);
				if (!isNaN(age)) {
					builder.whereRaw(`ip.age = ${age}`);
				}
				break;
			case 'age_range':
				if (typeof value.value === 'string' && value.value.includes(',')) {
					const [minStr, maxStr] = value.value.split(',');
					const min = parseInt(minStr.trim(), 10);
					const max = parseInt(maxStr.trim(), 10);
					if (!isNaN(min) && !isNaN(max)) {
						builder.whereRaw(`ip.age >= ${min} AND ip.age <= ${max}`);
					}
				}
				break;
			case 'gender_equals':
				builder.whereRaw(`ip.gender = '${value.value}'`);
				break;
			case 'min_victims':
				const minVictims = parseInt(value.value, 10);
				if (!isNaN(minVictims)) {
					builder.having(
						`COUNT(DISTINCT CASE WHEN ip.participant_type = 'Victim' THEN ip.participant_id END) >= ${minVictims}`
					);
				}
				break;
		}
	}

	validate(value: any): boolean {
		if (!value.operator) return false;

		const validOperators = [
			'name_contains',
			'type_equals',
			'age_equals',
			'age_range',
			'gender_equals',
			'min_victims'
		];
		if (!validOperators.includes(value.operator)) return false;

		switch (value.operator) {
			case 'name_contains':
				return typeof value.value === 'string' && value.value.length > 0;
			case 'type_equals':
				return ['Victim', 'Subject-Suspect', 'Officer'].includes(value.value);
			case 'age_equals':
				const age = parseInt(value.value, 10);
				return !isNaN(age) && age >= 0 && age <= 120;
			case 'age_range':
				if (typeof value.value !== 'string' || !value.value.includes(',')) return false;
				const [minStr, maxStr] = value.value.split(',');
				const min = parseInt(minStr.trim(), 10);
				const max = parseInt(maxStr.trim(), 10);
				return !isNaN(min) && !isNaN(max) && min >= 0 && max <= 120 && min <= max;
			case 'gender_equals':
				return ['Male', 'Female', 'Unknown'].includes(value.value);
			case 'min_victims':
				const minVictims = parseInt(value.value, 10);
				return !isNaN(minVictims) && minVictims >= 0;
			default:
				return false;
		}
	}
}
