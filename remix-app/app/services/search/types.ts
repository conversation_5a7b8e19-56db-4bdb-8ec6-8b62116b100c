import type { QueryBuilder } from '../query/QueryBuilder.server';
import type { ComponentType } from 'react';
import type { BaseColumn } from './BaseColumn';
import type { BaseFilter } from '~/services/search/BaseFilter';

export interface SearchPermission {
	roles: string[];
	permissions?: string[];
}

export type VisualizationType = 'bar' | 'line' | 'pie' | 'table' | 'map';

export type FilterInputType = 'text' | 'select' | 'daterange' | 'radio' | 'none' | 'autocomplete' | 'object';

export type FilterOption = {
	value: string;
	label: string;
};

export type FilterOperatorOption = {
	value: string;
	label: string;
	inputType: FilterInputType;
	component?: ComponentType<FilterInputProps>;
	options?: FilterOption[];
};

export interface FilterInputProps {
	value: any;
	onChange: (value: any) => void;
	operator?: string;
	filter?: ISearchFilter | null;
	className?: string;
}

export interface ISearchColumn {
	key: string;
	label: string;
	sortable?: boolean;
	permissions?: SearchPermission[];

	// Query hooks
	beforeQuery?(builder: QueryBuilder): void;
	addToQuery(builder: QueryBuilder): void;
	afterQuery?(builder: QueryBuilder): void;

	// Render hooks and output
	// data contains the fields relevant to this column, grouped by column key
	beforeRender?(data: any): any;
	render(value: any, row: any): any;
	afterRender?(output: any): any;

	// Optional CSS classes for the column
	getCssClasses?(): string[];

	// Check if the user has permission to use this column
	hasPermission?(userRoles: string[], userPermissions?: string[]): boolean;
}

export type SearchColumnDefinition = ISearchColumn;

export interface ISearchFilter {
	id: string;
	name: string;
	description?: string;
	permissions: SearchPermission[];

	apply(builder: QueryBuilder, value: any): void;
	validate(value: any): boolean;
	getOperators(): FilterOperatorOption[];
	getInputComponent(operator: string): ComponentType<FilterInputProps>;
	getDefaultValue(operator: string): any;
	formatValue(value: any, operator: string): string;

	// Optional methods for custom form structure and submit handlers
	getFormComponent?(): ComponentType<FilterFormProps>;
	handleSubmit?(value: any): any;
}

export interface FilterFormProps {
	value: any;
	onChange: (value: any) => void;
	onSubmit: (value: any) => void;
	filter: ISearchFilter;
	className?: string;
}

export type FilterCondition = {
	id?: string;
	field?: string;
	operator?: string;
	value?: any;
	type?: 'standard' | 'raw';
	sql?: string;
	params?: any[];
};

export type SearchFilterGroup = {
	conditions: Array<FilterCondition | SearchFilterGroup>;
	operator: 'AND' | 'OR';
};

export type SavedSearch = {
	id: string;
	name: string;
	filters: Array<FilterCondition | SearchFilterGroup>;
	columns: string[];
	grouping: 'AND' | 'OR';
	createdAt: Date;
	updatedAt: Date;
	userId: string;
	entityType: 'incidents' | 'participants';
	formType: 'basic' | 'advanced';
	locked?: boolean;
	editlock?: boolean;
	limitResults?: number;
	path?: string;
};

export type ColumnConstructor = new () => BaseColumn;
export type FilterConstructor = new () => BaseFilter;

export interface ISearchVisualization {
	key: string;
	label: string;
	type: VisualizationType;
	description?: string;
	permissions: SearchPermission[];

	// Configuration options for the visualization
	getConfigOptions(): any;

	// Prepare data for visualization
	prepareData(data: any[]): any;

	// Render the visualization
	render(data: any, config: any): JSX.Element;

	// Check if the user has permission to use this visualization
	hasPermission(userRoles: string[], userPermissions?: string[]): boolean;
}

export type VisualizationConstructor = new () => ISearchVisualization;
