import { QueryBuilder } from '../query/QueryBuilder.server';
import type { OutputFormat } from '../query/QueryExporter.server';
import { QueryExporter } from '../query/QueryExporter.server';
import type { QueryOperators } from '../query/types';
import type { BaseFilter } from './BaseFilter';
import type { BaseVisualization } from './BaseVisualization';
import type { ISearchColumn, SavedSearch, SearchFilterGroup } from './types';
import { ISearchVisualization, SearchColumnDefinition, FilterCondition } from './types';
import { db as prisma } from '~/utils/db.server';
import { FilterRegistry } from './FilterRegistry';
import { ColumnRegistry } from './ColumnRegistry';
import { VisualizationRegistry } from './VisualizationRegistry';
import { LRUCache } from 'lru-cache';
import { DateRangeFilter } from './filters';
import type { Filter, FilterGroup } from '~/types/search';
import type { BaseColumn } from './BaseColumn';
import { ColumnGroup } from './ColumnGroup';

export class SearchBuilder {
	protected filterRegistry: FilterRegistry = FilterRegistry.getInstance();
	protected columnRegistry: ColumnRegistry = ColumnRegistry.getInstance();
	protected visualizationRegistry: VisualizationRegistry = VisualizationRegistry.getInstance();

	// Helper function to check if a string is a valid QueryOperator
	private isValidQueryOperator(operator: string | undefined): operator is QueryOperators {
		if (!operator) return false;
		const validOperators: QueryOperators[] = [
			'equals',
			'not',
			'in',
			'notIn',
			'lt',
			'lte',
			'gt',
			'gte',
			'contains',
			'startsWith',
			'endsWith',
			'search',
			'dwithin',
			'between'
		];
		return validOperators.includes(operator as QueryOperators);
	}

	/**
	 * Creates a SearchBuilder instance from an AI query
	 * @param query The natural language query to process
	 * @param baseTable The base table to query
	 * @param baseAlias The alias for the base table
	 * @returns A new SearchBuilder instance configured based on the AI query
	 */
	static async fromAIQuery(query: string, baseTable: string, baseAlias: string): Promise<SearchBuilder> {
		// Create a new SearchBuilder instance
		const builder = new SearchBuilder(baseTable, baseAlias);

		try {
			// Process the query using AI to extract search parameters
			// This is a simplified implementation - in a real application, you would
			// use the OpenAI API to analyze the query and extract structured parameters

			// For now, we'll just set up some basic filters based on the query text
			if (query.toLowerCase().includes('date')) {
				builder.setFilter('date_range', {
					start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
					end: new Date()
				});
			}

			if (query.toLowerCase().includes('location')) {
				builder.setFilter('location', { value: 'United States' });
			}

			// Add more filter logic based on query content

			return builder;
		} catch (error) {
			console.error('Error processing AI query:', error);
			// Return a default builder if AI processing fails
			return builder;
		}
	}

	// Static cache for search results (shared across all instances)
	private static cache = new LRUCache<string, any>({
		max: 100, // Maximum number of items to store in the cache
		ttl: 1000 * 60 * 5, // Time to live: 5 minutes
		updateAgeOnGet: true, // Update the "recently used" status on get
		maxSize: 50 * 1024 * 1024, // Maximum cache size: 50MB
		sizeCalculation: (value, key) => {
			// Estimate size based on JSON stringification
			try {
				// Only count the rawData if it exists
				if (value && value.rawData) {
					// Estimate size of rawData
					const rawDataSize = JSON.stringify(value.rawData).length * 2; // Approximate 2 bytes per character
					// Estimate size of result
					const resultSize = value.result ? JSON.stringify(value.result).length * 2 : 0;
					return rawDataSize + resultSize;
				}
				// If no rawData, just estimate the whole object
				return JSON.stringify(value).length * 2;
			} catch (e) {
				// If stringification fails, use a conservative estimate
				return 1024 * 1024; // 1MB
			}
		}
	});
	private activeFilters: Map<string, any> = new Map();
	private filterGroups: SearchFilterGroup[] = [];
	private grouping: 'AND' | 'OR' = 'AND';
	private selectedColumns: ISearchColumn[] = [];
	private readonly baseTable: string;
	private readonly baseAlias: string;
	private entityType: 'incidents' | 'participants';
	private page: number = 1;
	private pageSize: number = 20;
	private totalCount: number = 0;
	private sortColumn: string | null = null;
	private sortDirection: 'ASC' | 'DESC' = 'ASC';
	private activeVisualization: string | null = null;
	private visualizationConfig: any = {};
	private userId: string | null = null;
	private formType: 'basic' | 'advanced' = 'basic';

	constructor(baseTable: string, baseAlias: string, entityType: 'incidents' | 'participants' = 'incidents') {
		this.baseTable = baseTable;
		this.baseAlias = baseAlias;
		this.entityType = entityType;
		this.registerDefaultFilters();
	}

	registerFilter(filter: BaseFilter): this {
		this.filterRegistry.register(filter);
		return this;
	}

	registerColumn(column: BaseColumn): this {
		this.columnRegistry.register(column);
		return this;
	}

	/**
	 * Register a visualization with the registry
	 * @param visualization The visualization to register
	 */
	registerVisualization(visualization: BaseVisualization): this {
		this.visualizationRegistry.register(visualization);
		return this;
	}

	setFilter(filterId: string, value: any): this {
		if (!this.filterRegistry.get(filterId)) {
			throw new Error(`Filter ${filterId} not found`);
		}
		this.activeFilters.set(filterId, value);
		return this;
	}

	setColumns(columnKeys: string[], userRoles: string[] = ['user'], userPermissions: string[] = []): this {
		this.selectedColumns = columnKeys
			.map(key => this.columnRegistry.get(key))
			.filter(
				(col): col is BaseColumn =>
					col !== undefined && (col.hasPermission?.(userRoles, userPermissions) ?? true)
			);
		return this;
	}

	/**
	 * Create a column group from multiple columns
	 * @param key The unique key for the column group
	 * @param label The display label for the column group
	 * @param columnKeys The keys of the columns to include in the group
	 * @param userRoles The roles of the user creating the group
	 * @param userPermissions The permissions of the user creating the group
	 * @returns The column group
	 */
	createColumnGroup(
		key: string,
		label: string,
		columnKeys: string[],
		userRoles: string[] = ['user'],
		userPermissions: string[] = []
	): ColumnGroup {
		// Get the columns from the registry
		const columns = columnKeys
			.map(key => this.columnRegistry.get(key))
			.filter(
				(col): col is BaseColumn =>
					col !== undefined && (col.hasPermission?.(userRoles, userPermissions) ?? true)
			);

		// Create a new column group with the columns
		return new ColumnGroup(key, label, columns);
	}

	/**
	 * Add a column group to the selected columns
	 * @param group The column group to add
	 * @returns This SearchBuilder instance for method chaining
	 */
	addColumnGroup(group: ColumnGroup): this {
		this.selectedColumns.push(group);
		return this;
	}

	setGrouping(grouping: 'AND' | 'OR'): this {
		this.grouping = grouping;
		return this;
	}

	/**
	 * Add a filter group
	 * @param group The filter group to add
	 */
	addFilterGroup(group: SearchFilterGroup): this {
		console.log('=== ADDING FILTER GROUP ===');
		console.log('Original filter group:', JSON.stringify(group, null, 2));

		// Validate that the group has a conditions array before adding it
		if (!group.conditions) {
			console.warn('Warning: Attempted to add a filter group without conditions', group);
			// Initialize conditions as an empty array if it's missing
			group.conditions = [];
		} else if (!Array.isArray(group.conditions)) {
			console.warn('Warning: group.conditions is not an array', group);
			// Initialize as empty array
			group.conditions = [];
		}

		// Validate that the group has a valid operator
		if (!group.operator || (group.operator !== 'AND' && group.operator !== 'OR')) {
			console.warn('Warning: group has invalid operator, defaulting to AND', group);
			group.operator = 'AND';
		}

		// Validate each condition in the group
		if (group.conditions.length > 0) {
			group.conditions = group.conditions.filter(condition => {
				// If it's a nested group, validate it recursively
				if ('conditions' in condition) {
					// Create a temporary group to validate
					const tempGroup: SearchFilterGroup = {
						conditions: condition.conditions,
						operator: condition.operator
					};
					// Add it recursively, which will validate it
					this.addFilterGroup(tempGroup);
					// Update the original condition with the validated group
					condition.conditions = tempGroup.conditions;
					condition.operator = tempGroup.operator;
					return true;
				}
				// If it's a filter condition with a field property
				else if ('field' in condition) {
					if (!condition.field) {
						console.warn('Warning: filter condition missing field, skipping', condition);
						return false;
					}
					return true;
				}
				// If it's a filter condition with an id property
				else if ('id' in condition) {
					const filter = this.filterRegistry.get(condition.id);
					if (!filter) {
						console.warn(`Warning: filter with id "${condition.id}" not found, skipping`, condition);
						return false;
					}

					// Validate the condition using the filter's validate method
					if (!filter.validate(condition.value)) {
						console.warn(`Warning: invalid condition for filter "${condition.id}", skipping`, condition);
						return false;
					}

					return true;
				}
				// If it's neither, it's invalid
				else {
					console.warn('Warning: invalid condition, skipping', condition);
					return false;
				}
			});
		}

		console.log('=== FILTER GROUP AFTER VALIDATION ===');
		console.log('Validated filter group:', JSON.stringify(group, null, 2));
		console.log('Current filter groups count:', this.filterGroups.length);

		this.filterGroups.push(group);
		console.log('New filter groups count:', this.filterGroups.length);
		return this;
	}

	/**
	 * Remove a filter group at the specified index
	 * @param index The index of the filter group to remove
	 */
	removeFilterGroup(index: number): this {
		if (index >= 0 && index < this.filterGroups.length) {
			this.filterGroups.splice(index, 1);
		}
		return this;
	}

	/**
	 * Clear all filter groups
	 */
	clearFilterGroups(): this {
		this.filterGroups = [];
		return this;
	}

	/**
	 * Get all filter groups
	 * @returns All filter groups
	 */
	getFilterGroups(): SearchFilterGroup[] {
		return [...this.filterGroups];
	}

	/**
	 * Apply a filter group to a query builder
	 * @param queryBuilder The query builder to apply the filter group to
	 * @param group The filter group to apply
	 * @param userRoles The roles of the user executing the search
	 * @param userPermissions
	 * @private
	 */
	private applyFilterGroup(
		queryBuilder: QueryBuilder,
		group: SearchFilterGroup,
		userRoles: string[],
		userPermissions: string[] = []
	): void {
		// Log the filter group for debugging
		console.log('=== APPLYING FILTER GROUP ===');
		console.log('Filter group operator:', group.operator);
		console.log('Filter group conditions:', JSON.stringify(group.conditions, null, 2));
		console.log('QueryBuilder state before applying filter group:', {
			table: queryBuilder.table,
			alias: queryBuilder.tableAlias,
			conditionsCount: queryBuilder.conditions?.size || 0
		});

		// Skip empty groups
		if (!group.conditions || !Array.isArray(group.conditions) || group.conditions.length === 0) {
			console.warn('Warning: Skipping empty or invalid filter group', group);
			return;
		}

		// Create the appropriate filter group based on the operator
		const filterGroup = group.operator === 'OR' ? queryBuilder.createOrGroup() : queryBuilder.createAndGroup();

		// Process each condition in the group
		group.conditions.forEach(condition => {
			// If the condition is a nested filter group, apply it recursively
			if ('conditions' in condition) {
				// Skip invalid nested groups
				if (
					!condition.conditions ||
					!Array.isArray(condition.conditions) ||
					condition.conditions.length === 0
				) {
					console.warn('Warning: Skipping empty or invalid nested filter group', condition);
					return;
				}

				// Create a nested filter group with the appropriate operator
				const nestedGroup =
					condition.operator === 'OR' ? queryBuilder.createOrGroup() : queryBuilder.createAndGroup();

				// Process each condition in the nested group recursively
				condition.conditions.forEach(nestedCondition => {
					if ('conditions' in nestedCondition) {
						// Handle nested-nested groups (recursive case)
						const subNestedGroup =
							nestedCondition.operator === 'OR'
								? queryBuilder.createOrGroup()
								: queryBuilder.createAndGroup();

						// Apply the nested-nested group recursively
						// This is a simplified approach - for deeply nested groups,
						// a more comprehensive recursive solution would be needed
						nestedGroup.addGroup(subNestedGroup);
					} else {
						// Handle filter condition in nested group
						let filter: BaseFilter | undefined;
						if (nestedCondition.id) {
							filter = this.filterRegistry.get(nestedCondition.id);
						} else if (nestedCondition.field) {
							filter = this.filterRegistry.findByField(nestedCondition.field);
						}

						if (filter && (filter.hasPermission?.(userRoles, userPermissions) ?? true)) {
							// Prepare the value object
							let valueObj = nestedCondition.value;
							const operators = filter.getOperators();

							// If the value is not an object or doesn't have an operator, create a proper value object
							if (typeof valueObj !== 'object' || valueObj === null || !valueObj.operator) {
								const defaultOperator = operators.length > 0 ? operators[0].value : 'equals';
								valueObj = {
									operator: defaultOperator,
									value: nestedCondition.value
								};
							}

							// If the value object has a nested 'value' property, merge it with the parent object
							if (valueObj.value && typeof valueObj.value === 'object') {
								valueObj = { ...valueObj, ...valueObj.value };
							}

							// Get the conditions from the filter
							const conditions = filter.getConditions(valueObj);

							// Log the conditions for debugging
							console.log(
								`Nested filter conditions for ${filter.id}:`,
								JSON.stringify(conditions, null, 2)
							);

							// Apply each condition to the nested group
							conditions?.forEach(condition => {
								if (condition.type === 'raw' && condition.sql) {
									// For raw SQL conditions, create a temporary builder to apply the condition
									const tempBuilder = new QueryBuilder(queryBuilder.table, queryBuilder.tableAlias);
									tempBuilder.whereRaw(condition.sql, condition.params);

									// Extract the SQL from the temporary builder to log it
									try {
										const { text, params } = tempBuilder.toSQL();
										console.log(`Nested filter raw SQL for ${filter.id}:`, text);
										console.log(`Nested filter raw params for ${filter.id}:`, params);
									} catch (error) {
										console.error(`Error generating SQL for nested filter ${filter.id}:`, error);
									}

									// Add the raw condition to the nested group
									nestedGroup.addRawCondition(condition.sql, condition.params);
								} else if (condition.field && condition.operator) {
									// For standard conditions, add them directly to the nested group
									console.log(
										`Adding condition to nested group: ${condition.field}, ${condition.operator}, ${JSON.stringify(condition.value)}`
									);
									// Ensure operator is a valid QueryOperator
									const operator = condition.operator;
									if (this.isValidQueryOperator(operator)) {
										nestedGroup.addCondition(condition.field, operator, condition.value);
									} else {
										console.warn(
											`Nested filter ${filter.id} has invalid operator: ${operator}, using 'equals' instead`
										);
										nestedGroup.addCondition(condition.field, 'equals', condition.value);
									}
								} else {
									console.warn(
										`Nested filter ${filter.id} returned an invalid condition:`,
										JSON.stringify(condition, null, 2)
									);
								}
							});
						}
					}
				});

				// Add the nested group to the parent group
				filterGroup.addGroup(nestedGroup);
			}
			// Otherwise, it's a filter condition
			else {
				// Try to get the filter by id first, then by field if id is not available
				let filter: BaseFilter | undefined;
				if (condition.id) {
					filter = this.filterRegistry.get(condition.id);
				} else if (condition.field) {
					filter = this.filterRegistry.findByField(condition.field);
				}

				if (filter && (filter.hasPermission?.(userRoles, userPermissions) ?? true)) {
					// Log the filter condition for debugging
					console.log(`Processing filter condition for ${filter.id}:`, JSON.stringify(condition, null, 2));

					// Get the filter's operators
					const operators = filter.getOperators();

					// Prepare the value object
					let valueObj = condition.value;

					// If the value is not an object or doesn't have an operator, create a proper value object
					if (typeof valueObj !== 'object' || valueObj === null || !valueObj.operator) {
						// Use the first operator as default if available
						const defaultOperator = operators.length > 0 ? operators[0].value : 'equals';

						valueObj = {
							operator: defaultOperator,
							value: condition.value
						};
					}

					// If the value object has a nested 'value' property, merge it with the parent object
					if (valueObj.value && typeof valueObj.value === 'object') {
						valueObj = { ...valueObj, ...valueObj.value };
						console.log(`Merged value object for ${filter.id}:`, JSON.stringify(valueObj, null, 2));
					}

					// Log the final value object being passed to the filter
					console.log(`Applying filter ${filter.id} with value:`, JSON.stringify(valueObj, null, 2));
					console.log(`Filter field: ${filter.field || 'N/A'}, Filter type: ${filter.constructor.name}`);

					try {
						// Get the conditions from the filter
						const conditions = filter.getConditions(valueObj);

						// Log the conditions for debugging
						console.log(`Filter conditions for ${filter.id}:`, JSON.stringify(conditions, null, 2));

						// Apply each condition to the filter group
						conditions.forEach(condition => {
							if (condition.type === 'raw' && condition.sql) {
								// For raw SQL conditions, create a temporary builder to apply the condition
								const tempBuilder = new QueryBuilder(queryBuilder.table, queryBuilder.tableAlias);
								tempBuilder.whereRaw(condition.sql, condition.params);

								// Extract the SQL from the temporary builder to log it
								try {
									const { text, params } = tempBuilder.toSQL();
									console.log(`Filter raw SQL for ${filter.id}:`, text);
									console.log(`Filter raw params for ${filter.id}:`, params);
								} catch (error) {
									console.error(`Error generating SQL for filter ${filter.id}:`, error);
								}

								// Add the raw condition to the filter group
								if (typeof filterGroup.addRawCondition === 'function') {
									filterGroup.addRawCondition(condition.sql, condition.params);
								} else {
									// Fallback if addRawCondition is not available
									console.warn(
										`Filter group does not support addRawCondition, using whereRaw directly`
									);
									queryBuilder.whereRaw(condition.sql, condition.params);
								}
							} else if (condition.field && condition.operator) {
								// For standard conditions, add them directly to the filter group
								console.log(
									`Adding condition to filter group: ${condition.field}, ${condition.operator}, ${JSON.stringify(condition.value)}`
								);
								// Ensure operator is a valid QueryOperator
								const operator = condition.operator;
								if (this.isValidQueryOperator(operator)) {
									filterGroup.addCondition(condition.field, operator, condition.value);
								} else {
									console.warn(
										`Filter ${filter.id} has invalid operator: ${operator}, using 'equals' instead`
									);
									filterGroup.addCondition(condition.field, 'equals', condition.value);
								}
							} else {
								console.warn(
									`Filter ${filter.id} returned an invalid condition:`,
									JSON.stringify(condition, null, 2)
								);
							}
						});
					} catch (error) {
						console.error(`Error applying filter ${filter.id}:`, error);
					}
				}
			}
		});

		// Apply the filter group to the query builder
		queryBuilder.applyFilterGroup(filterGroup);
	}

	/**
	 * Set the active visualization and its configuration
	 * @param visualizationKey The key of the visualization to set as active
	 * @param config Optional configuration for the visualization
	 */
	setVisualization(visualizationKey: string, config?: any): this {
		const visualization = this.visualizationRegistry.get(visualizationKey);
		if (!visualization) {
			throw new Error(`Visualization ${visualizationKey} not found`);
		}

		this.activeVisualization = visualizationKey;

		// Merge the provided config with the default config
		const defaultConfig = visualization.getConfigOptions();
		this.visualizationConfig = { ...defaultConfig, ...(config || {}) };

		return this;
	}

	/**
	 * Clear the active visualization
	 */
	clearVisualization(): this {
		this.activeVisualization = null;
		this.visualizationConfig = {};
		return this;
	}

	/**
	 * Get the active visualization
	 * @returns The active visualization, or null if none is set
	 */
	getVisualization(): BaseVisualization | null {
		if (!this.activeVisualization) return null;
		return this.visualizationRegistry.get(this.activeVisualization) || null;
	}

	/**
	 * Get the configuration for the active visualization
	 * @returns The configuration for the active visualization, or an empty object if none is set
	 */
	getVisualizationConfig(): any {
		return this.visualizationConfig;
	}

	/**
	 * Set the current page for pagination
	 */
	setPage(page: number): this {
		this.page = Math.max(1, page); // Ensure page is at least 1
		return this;
	}

	/**
	 * Set the page size for pagination
	 */
	setPageSize(pageSize: number): this {
		this.pageSize = Math.max(1, pageSize); // Ensure pageSize is at least 1
		return this;
	}

	/**
	 * Get the current page
	 */
	getPage(): number {
		return this.page;
	}

	/**
	 * Get the current page size
	 */
	getPageSize(): number {
		return this.pageSize;
	}

	/**
	 * Get the total count of items matching the search criteria
	 */
	getTotalCount(): number {
		return this.totalCount;
	}

	/**
	 * Get the total number of pages based on the total count and page size
	 */
	getTotalPages(): number {
		return Math.ceil(this.totalCount / this.pageSize);
	}

	/**
	 * Set the sort column and direction
	 */
	setSort(column: string, direction: 'ASC' | 'DESC' = 'ASC'): this {
		this.sortColumn = column;
		this.sortDirection = direction;
		return this;
	}

	/**
	 * Clear the sort column and reset direction to 'ASC'
	 */
	clearSort(): this {
		this.sortColumn = null;
		this.sortDirection = 'ASC';
		return this;
	}

	/**
	 * Get the current sort column
	 */
	getSortColumn(): string | null {
		return this.sortColumn;
	}

	/**
	 * Get the current sort direction
	 */
	getSortDirection(): 'ASC' | 'DESC' {
		return this.sortDirection;
	}

	/**
	 * Toggle the sort direction for the given column
	 * If the column is already being sorted, toggle the direction
	 * If it's a different column, set it as the new sort column with 'ASC' direction
	 */
	toggleSort(column: string): this {
		if (this.sortColumn === column) {
			// Toggle direction if same column
			this.sortDirection = this.sortDirection === 'ASC' ? 'DESC' : 'ASC';
		} else {
			// Set new column with 'ASC' direction
			this.sortColumn = column;
			this.sortDirection = 'ASC';
		}
		return this;
	}

	/**
	 * Generate a cache key based on the search parameters
	 */
	private generateCacheKey(userRoles: string[]): string {
		// Create a more efficient representation of the search parameters
		// Use a string builder approach to avoid creating large intermediate objects
		const parts: string[] = [];

		// Add basic parameters
		parts.push(`table:${this.baseTable}`);
		parts.push(`alias:${this.baseAlias}`);
		parts.push(`entity:${this.entityType}`);
		parts.push(`grouping:${this.grouping}`);

		// Add filters in a deterministic order
		const sortedFilters = Array.from(this.activeFilters.entries()).sort(([keyA], [keyB]) =>
			keyA.localeCompare(keyB)
		);

		if (sortedFilters.length > 0) {
			parts.push(
				'filters:[' +
					sortedFilters
						.map(([key, value]) => `${key}:${typeof value === 'object' ? JSON.stringify(value) : value}`)
						.join(',') +
					']'
			);
		}

		// Add filter groups
		if (this.filterGroups.length > 0) {
			parts.push(
				'filterGroups:[' +
					this.filterGroups
						.map(
							group =>
								`${group.operator}:[${group.conditions
									.map(c => (typeof c === 'object' ? JSON.stringify(c) : c))
									.join(',')}]`
						)
						.join(',') +
					']'
			);
		}

		// Add columns in a deterministic order
		const columnKeys = this.selectedColumns.map(col => col.key).sort();
		parts.push('columns:[' + columnKeys.join(',') + ']');

		// Add sorting
		if (this.sortColumn) {
			parts.push(`sort:${this.sortColumn}:${this.sortDirection}`);
		}

		// Add pagination
		parts.push(`page:${this.page}:${this.pageSize}`);

		// Add visualization
		if (this.activeVisualization) {
			parts.push(`viz:${this.activeVisualization}`);

			// Only include non-empty visualization config
			if (Object.keys(this.visualizationConfig).length > 0) {
				parts.push(`vizConfig:${JSON.stringify(this.visualizationConfig)}`);
			}
		}

		// Add user roles in a deterministic order
		const sortedRoles = [...userRoles].sort();
		parts.push('roles:[' + sortedRoles.join(',') + ']');

		// Join all parts with a separator that's unlikely to appear in the values
		return parts.join('||');
	}

	/**
	 * Clear the entire search cache
	 */
	static clearCache(): void {
		SearchBuilder.cache.clear();
	}

	/**
	 * Get the current cache size
	 */
	static getCacheSize(): number {
		return SearchBuilder.cache.size;
	}

	async execute(userRoles: string[], format: OutputFormat = 'table', userPermissions: string[] = []) {
		// Filter columns based on user permissions
		const permittedColumns = this.selectedColumns.filter(
			column => column.hasPermission?.(userRoles, userPermissions) ?? true
		);
		this.selectedColumns = permittedColumns;

		// Generate a cache key based on the search parameters
		let cacheKey = this.generateCacheKey(userRoles);

		// Check if the results are already in the cache
		const cachedResult = SearchBuilder.cache.get(cacheKey);
		if (cachedResult) {
			// Fixed by yanh on 2025-06-12
			// Update the total count from the cache
			this.totalCount = cachedResult.totalCount;

			// If the format matches the cached format, return the cached result directly
			if (cachedResult.format === format) {
				return cachedResult.result;
			}

			// If the format is different but we have the raw data, format it and return
			if (cachedResult.rawData) {
				const exporter = new QueryExporter(null, {
					format,
					headers: true,
					queryType: this.entityType,
					customHeaders: Object.fromEntries(this.selectedColumns.map(col => [col.key, col.label])),
					columnClasses: Object.fromEntries(
						this.selectedColumns.map(col => [col.key, col.getCssClasses?.() ?? []])
					)
				});

				const result = await exporter.execute(cachedResult.rawData);

				// Update the cache with the new format
				SearchBuilder.cache.set(cacheKey, {
					format,
					result,
					rawData: cachedResult.rawData,
					totalCount: cachedResult.totalCount
				});

				return result;
			}
		}

		// If not in cache or can't reuse cached data, execute the query
		// Create the main query builder with the appropriate alias
		// When entityType is 'incidents', use 'i' as the alias to match column references
		const baseAlias = this.entityType === 'incidents' ? 'i' : this.baseAlias;
		const builder = new QueryBuilder(this.baseTable, baseAlias, format);

		// Create a separate count query builder for getting total count
		const countBuilder = new QueryBuilder(this.baseTable, baseAlias);

		// Add entity-specific joins to both builders
		if (this.entityType === 'participants') {
			// For participants, join the incidents table
			builder.join('incidents', 'incidents.incident_id = ip.incident_id', 'left', 'i');
			countBuilder.join('incidents', 'incidents.incident_id = ip.incident_id', 'left', 'i');
		}
		// No need for an else clause now, since we're using 'i' as the alias for incidents

		// Create a tracking builder and get the column fields map
		const [trackingBuilder, columnFieldsMap] = this.createTrackingBuilder(builder);

		// Apply column query modifications to main query
		this.applyColumnQueryModifications(trackingBuilder);

		// Store the column fields map for later use
		(builder as any).__columnFieldsMap = columnFieldsMap;

		// Apply filters to both queries using the specified grouping logic
		if (this.activeFilters.size > 0) {
			if (this.grouping === 'OR') {
				// For OR logic, we need to use whereOr with a callback
				builder.where(qb => {
					for (const [filterId, value] of this.activeFilters) {
						const filter = this.filterRegistry.get(filterId);
						if (filter && (filter.hasPermission?.(userRoles, userPermissions) ?? true)) {
							qb.orWhere(subQb => {
								filter.apply(subQb, value);
							});
						}
					}
				});

				// Apply the same logic to the count query
				countBuilder.where(qb => {
					for (const [filterId, value] of this.activeFilters) {
						const filter = this.filterRegistry.get(filterId);
						if (filter && (filter.hasPermission?.(userRoles, userPermissions) ?? true)) {
							qb.orWhere(subQb => {
								filter.apply(subQb, value);
							});
						}
					}
				});
			} else {
				// For AND logic (default), apply filters sequentially
				for (const [filterId, value] of this.activeFilters) {
					const filter = this.filterRegistry.get(filterId);
					if (filter && (filter.hasPermission?.(userRoles, userPermissions) ?? true)) {
						filter.apply(builder, value);
						filter.apply(countBuilder, value);
					}
				}
			}
		}

		// Apply filter groups to both queries
		if (this.filterGroups.length > 0) {
			for (const group of this.filterGroups) {
				this.applyFilterGroup(builder, group, userRoles, userPermissions);
				this.applyFilterGroup(countBuilder, group, userRoles, userPermissions);
			}
		}

		// Get total count first (for pagination info)
		countBuilder.selectCount('*', 'count');
		//countBuilder.select('COUNT(*) as count');
		const countResult = await countBuilder.execute();
		this.totalCount = parseInt(countResult[0].count);

		// Apply sorting if a sort column is specified
		if (this.sortColumn) {
			// Find the column definition to get the actual field name
			const column = this.selectedColumns.find(col => col.key === this.sortColumn);
			if (column) {
				// If the column is found in selected columns, use its key for sorting
				builder.orderBy(this.sortColumn, this.sortDirection);
			}
		}

		// Apply pagination to main query
		const offset = (this.page - 1) * this.pageSize;
		builder.limit(this.pageSize).offset(offset);

		// Log all filter conditions right before executing the query for debugging
		console.log('=== SEARCH QUERY FILTER CONDITIONS ===');

		// Log active filters
		if (this.activeFilters.size > 0) {
			console.log('Active Filters:');
			for (const [filterId, value] of this.activeFilters.entries()) {
				const filter = this.filterRegistry.get(filterId);
				if (filter) {
					console.log(`  ${filter.name} (${filterId}):`, JSON.stringify(value, null, 2));
				}
			}
		} else {
			console.log('No active filters');
		}

		// Log filter groups
		if (this.filterGroups.length > 0) {
			console.log('Filter Groups:');
			this.filterGroups.forEach((group, index) => {
				console.log(`  Group ${index + 1} (${group.operator}):`, JSON.stringify(group.conditions, null, 2));
			});
		} else {
			console.log('No filter groups');
		}

		// Log the final SQL query
		try {
			const { text, params } = builder.toSQL();
			console.log('=== FINAL SQL QUERY ===');
			console.log('SQL:', text);
			console.log('Parameters:', JSON.stringify(params, null, 2));
			console.log('=== END SQL QUERY ===');
		} catch (error) {
			console.error('Error generating SQL query:', error);
		}

		console.log('=== END FILTER CONDITIONS ===');

		// Execute main query with pagination
		console.log('Executing main query...');
		let results = await builder.execute();
		console.log(`Query execution complete. Results count: ${results.length}`);

		// Log the count query SQL
		try {
			const { text, params } = countBuilder.toSQL();
			console.log('=== COUNT SQL QUERY ===');
			console.log('SQL:', text);
			console.log('Parameters:', JSON.stringify(params, null, 2));
			console.log('=== END COUNT SQL QUERY ===');
		} catch (error) {
			console.error('Error generating count SQL query:', error);
		}

		// Process results through column render pipeline
		results = results.map(row => {
			let processedRow = { ...row };

			// Get the column fields map that was stored during query building
			const columnFieldsMap = (builder as any).__columnFieldsMap as Map<string, Set<string>>;

			// Process each column with its grouped data
			for (const column of this.selectedColumns) {
				// Check if this is a column group
				if (column instanceof ColumnGroup) {
					// For a column group, we need to collect data for all columns in the group
					const groupData: any = {};

					// Get all columns in the group
					const groupColumns = column.getColumns();

					// Process each column in the group
					for (const groupColumn of groupColumns) {
						// Create an object with just the fields needed for this column
						const columnData: any = {};

						// If we have tracked fields for this column, use them
						if (columnFieldsMap && columnFieldsMap.has(groupColumn.key)) {
							const fields = columnFieldsMap.get(groupColumn.key)!;

							// Add each tracked field to the column data
							fields.forEach(fieldName => {
								if (fieldName in row) {
									columnData[fieldName] = row[fieldName];
								}
							});

							// Always include the column's key field if it exists
							if (groupColumn.key in row && !fields.has(groupColumn.key)) {
								columnData[groupColumn.key] = row[groupColumn.key];
							}
						} else {
							// Fallback to the previous heuristic approach
							Object.keys(row).forEach(fieldName => {
								if (
									fieldName === groupColumn.key ||
									fieldName.startsWith(groupColumn.key + '_') ||
									(groupColumn.key === 'location' &&
										(fieldName === 'city_county' || fieldName === 'state')) ||
									(groupColumn.key === 'victims_total' &&
										(fieldName === 'victims_killed' || fieldName === 'victims_injured'))
								) {
									columnData[fieldName] = row[fieldName];
								}
							});
						}

						// Add the column data to the group data
						groupData[groupColumn.key] = columnData;
					}

					// Pass the group data to the column group's beforeRender method
					const processedGroupData = column.beforeRender?.(groupData) ?? groupData;

					// Pass the processed group data to the column group's render method
					processedRow[column.key] = column.render(processedGroupData, row);

					// Pass the rendered data to the column group's afterRender method
					if (column.afterRender) {
						processedRow[column.key] = column.afterRender(processedRow[column.key]);
					}
				} else {
					// For a regular column, process it as before
					// Create an object with just the fields needed for this column
					const columnData: any = {};

					// If we have tracked fields for this column, use them
					if (columnFieldsMap && columnFieldsMap.has(column.key)) {
						const fields = columnFieldsMap.get(column.key)!;

						// Add each tracked field to the column data
						fields.forEach(fieldName => {
							if (fieldName in row) {
								columnData[fieldName] = row[fieldName];
							}
						});

						// Always include the column's key field if it exists
						if (column.key in row && !fields.has(column.key)) {
							columnData[column.key] = row[column.key];
						}
					} else {
						// Fallback to the previous heuristic approach
						Object.keys(row).forEach(fieldName => {
							if (
								fieldName === column.key ||
								fieldName.startsWith(column.key + '_') ||
								(column.key === 'location' && (fieldName === 'city_county' || fieldName === 'state')) ||
								(column.key === 'victims_total' &&
									(fieldName === 'victims_killed' || fieldName === 'victims_injured'))
							) {
								columnData[fieldName] = row[fieldName];
							}
						});
					}

					// Create a filtered row object that only contains data relevant to this column
					const filteredRow: any = {};
					filteredRow[column.key] = columnData[column.key] ?? row[column.key];

					// Pass the grouped data to beforeRender
					const value = column.beforeRender?.(columnData) ?? columnData[column.key] ?? row[column.key];

					// Set the processed value in the result
					processedRow[column.key] = column.render(value, filteredRow);

					if (column.afterRender) {
						// Only pass the rendered value to afterRender, not the entire processedRow
						processedRow[column.key] = column.afterRender(processedRow[column.key]);
					}
				}
			}

			return processedRow;
		});

		// Export results with entity type
		const exporter = new QueryExporter(null, {
			format,
			headers: true,
			queryType: this.entityType,
			customHeaders: Object.fromEntries(this.selectedColumns.map(col => [col.key, col.label])),
			columnClasses: Object.fromEntries(this.selectedColumns.map(col => [col.key, col.getCssClasses?.() ?? []]))
		});

		// Get the formatted result
		const result = await exporter.execute(results);

		// Store both the formatted result and the raw data in the cache
		cacheKey = this.generateCacheKey(userRoles);
		SearchBuilder.cache.set(cacheKey, {
			format,
			result,
			rawData: results,
			totalCount: this.totalCount
		});

		return result;
	}

	/**
	 * Set the user ID for the search
	 * @param userId The ID of the user performing the search
	 */
	setUserId(userId: string): this {
		this.userId = userId;
		return this;
	}

	/**
	 * Export search results in CSV format
	 */
	async exportCSV(userRoles: string[], userPermissions: string[] = []): Promise<string> {
		return (await this.execute(userRoles, 'csv', userPermissions)) as Promise<string>;
	}

	/**
	 * Export search results in JSON format
	 */
	async exportJSON(userRoles: string[], userPermissions: string[] = []): Promise<string> {
		return (await this.execute(userRoles, 'json', userPermissions)) as Promise<string>;
	}

	/**
	 * Export search results as an HTML table
	 */
	async exportHTML(userRoles: string[], userPermissions: string[] = []): Promise<string> {
		return (await this.execute(userRoles, 'html', userPermissions)) as Promise<string>;
	}

	/**
	 * Export search results as a JavaScript array
	 */
	async exportArray(userRoles: string[], userPermissions: string[] = []): Promise<any[]> {
		return (await this.execute(userRoles, 'array', userPermissions)) as Promise<any[]>;
	}

	/**
	 * Generate a visualization based on the active visualization and search results
	 * @param userRoles The roles of the user executing the search
	 * @returns The rendered visualization component, or null if no visualization is active
	 */
	async generateVisualization(userRoles: string[], userPermissions: string[] = []): Promise<JSX.Element | null> {
		// Check if an active visualization is set
		const visualization = this.getVisualization();
		if (!visualization) {
			return null;
		}

		// Check if the user has permission to use this visualization
		if (!(visualization.hasPermission?.(userRoles, userPermissions) ?? true)) {
			throw new Error(`User does not have permission to use visualization ${visualization.key}`);
		}

		// Execute the search to get the data
		const data = await this.exportArray(userRoles, userPermissions);

		// Prepare the data for visualization
		const preparedData = visualization.prepareData(data);

		// Render the visualization with the prepared data and configuration
		return visualization.render(preparedData, this.visualizationConfig);
	}

	/**
	 * Stream search results for large datasets
	 */
	async *streamResults(
		userRoles: string[],
		format: OutputFormat = 'table',
		batchSize: number = 1000,
		userPermissions: string[] = []
	): AsyncGenerator<any, void, unknown> {
		// Use 'i' as the alias when entityType is 'incidents' to match column references
		const baseAlias = this.entityType === 'incidents' ? 'i' : this.baseAlias;
		const builder = new QueryBuilder(this.baseTable, baseAlias);

		// Add entity-specific joins
		if (this.entityType === 'participants') {
			builder.join('incidents', 'incidents.incident_id = ip.incident_id', 'left', 'i');
		}

		// Create a tracking builder and get the column fields map
		const [trackingBuilder, columnFieldsMap] = this.createTrackingBuilder(builder);

		// Apply column query modifications
		this.applyColumnQueryModifications(trackingBuilder);

		// Store the column fields map for later use
		(builder as any).__columnFieldsMap = columnFieldsMap;

		// Apply filters using the specified grouping logic
		if (this.activeFilters.size > 0) {
			if (this.grouping === 'OR') {
				// For OR logic, we need to use whereOr with a callback
				builder.where(qb => {
					for (const [filterId, value] of this.activeFilters) {
						const filter = this.filterRegistry.get(filterId);
						if (filter && (filter.hasPermission?.(userRoles, userPermissions) ?? true)) {
							qb.orWhere(subQb => {
								filter.apply(subQb, value);
							});
						}
					}
				});
			} else {
				// For AND logic (default), apply filters sequentially
				for (const [filterId, value] of this.activeFilters) {
					const filter = this.filterRegistry.get(filterId);
					if (filter && (filter.hasPermission?.(userRoles, userPermissions) ?? true)) {
						filter.apply(builder, value);
					}
				}
			}
		}

		// Apply filter groups
		if (this.filterGroups.length > 0) {
			for (const group of this.filterGroups) {
				this.applyFilterGroup(builder, group, userRoles, userPermissions);
			}
		}

		// Log all filter conditions right before executing the streaming query for debugging
		console.log('=== STREAMING SEARCH QUERY FILTER CONDITIONS ===');

		// Log active filters
		if (this.activeFilters.size > 0) {
			console.log('Active Filters:');
			for (const [filterId, value] of this.activeFilters.entries()) {
				const filter = this.filterRegistry.get(filterId);
				if (filter) {
					console.log(`  ${filter.name} (${filterId}):`, JSON.stringify(value, null, 2));
				}
			}
		} else {
			console.log('No active filters');
		}

		// Log filter groups
		if (this.filterGroups.length > 0) {
			console.log('Filter Groups:');
			this.filterGroups.forEach((group, index) => {
				console.log(`  Group ${index + 1} (${group.operator}):`, JSON.stringify(group.conditions, null, 2));
			});
		} else {
			console.log('No filter groups');
		}

		console.log('=== END FILTER CONDITIONS ===');

		// Create exporter
		const exporter = new QueryExporter(builder, {
			format,
			headers: true,
			queryType: this.entityType,
			customHeaders: Object.fromEntries(this.selectedColumns.map(col => [col.key, col.label])),
			columnClasses: Object.fromEntries(this.selectedColumns.map(col => [col.key, col.getCssClasses?.() ?? []]))
		});

		// Stream results
		yield* exporter.stream(batchSize);
	}

	async save(name: string, userId: string): Promise<string> {
		// Convert active filters to filter conditions
		const filterConditions = Array.from(this.activeFilters.entries()).map(([id, value]) => ({
			id,
			value
		}));

		// Combine filter conditions and filter groups
		const filters = [...filterConditions, ...this.filterGroups];

		const search: Omit<SavedSearch, 'id' | 'createdAt' | 'updatedAt'> = {
			name,
			filters,
			columns: this.selectedColumns.map(col => col.key),
			grouping: this.grouping,
			userId,
			entityType: this.entityType,
			formType: this.formType
		};

		/** Fixed by yanh on 2025-06-12
    // Use QueryBuilder instead of direct Prisma call
    const queryBuilder = new QueryBuilder('savedSearch', 'ss');

    // Convert to INSERT query
    queryBuilder.whereRaw(`
      INSERT INTO "SavedSearch" ("name", "filters", "columns", "grouping", "userId")
      VALUES ($1, $2, $3, $4, $5)
      RETURNING "id"
    `, [name, JSON.stringify(filters), JSON.stringify(this.selectedColumns.map(col => col.key)), this.grouping, userId]);

    const result = await queryBuilder.execute();
    return result[0].id;
    */

		const result = await prisma.savedSearch.create({
			data: search
		});
		return result.id;
	}

	static async load(
		searchId: string,
		userRoles: string[] = ['user'],
		userPermissions: string[] = []
	): Promise<SearchBuilder> {
		// Use QueryBuilder instead of direct Prisma call
		// const queryBuilder = new QueryBuilder('SavedSearch', 'ss');
		// Fixed by yanh on 2025-06-12
		const queryBuilder = new QueryBuilder('site.saved_searches', 'ss');
		queryBuilder.select('*');
		queryBuilder.where('id', 'equals', searchId);

		const results = await queryBuilder.execute();
		const saved = results[0];

		if (!saved) {
			throw new Error(`Search ${searchId} not found`);
		}

		// Fixed by yanh on 2025-06-12
		// const builder = new SearchBuilder('incidents', 'i');
		const builder =
			saved.entityType === 'participants'
				? new SearchBuilder('gva_data.incident_participants', 'p')
				: new SearchBuilder('gva_data.incidents', 'i');
		builder.setEntityType(saved.entityType);
		builder.setFormType(saved.formType);

		// Ensure the grouping value is valid
		const grouping = saved.grouping.toUpperCase() as 'AND' | 'OR';
		if (grouping !== 'AND' && grouping !== 'OR') {
			builder.setGrouping('AND'); // Default to AND if invalid
		} else {
			builder.setGrouping(grouping);
		}

		builder.setColumns(saved.columns, userRoles, userPermissions);

		const filters = saved.filters as Array<{ id: string; value: any }> | undefined;
		if (filters) {
			filters.forEach(filter => {
				// Only set the filter if it exists in the registry
				if (filter.id && builder.filterRegistry.get(filter.id)) {
					builder.setFilter(filter.id, filter.value);
				}
			});
		}

		Object.assign(builder, {
			name: saved.name,
			locked: saved.locked,
			editlock: saved.editlock,
			limitResults: saved.limitResults,
			path: saved.path,
			columns: saved.columns,
			id: saved.id,
			userId: saved.userId
		});
		return builder;
	}

	/**
	 * Lists all saved searches for a user
	 */
	static async listForUser(userId: string): Promise<SavedSearch[]> {
		// Use QueryBuilder instead of direct Prisma call
		const queryBuilder = new QueryBuilder('SavedSearch', 'ss');
		queryBuilder.select('*');
		queryBuilder.where('userId', 'equals', userId);
		queryBuilder.orderBy('updatedAt', 'DESC');

		const searches = await queryBuilder.execute();

		return searches;
	}

	/**
   * Updates an existing saved search
  async update(searchId: string, name?: string): Promise<void> {
    // Get the current search data
    const updateData: any = {};

    if (name) {
      updateData.name = name;
    }

    // Always update the filters, columns, and grouping to the current state
    const filters = Array.from(this.activeFilters.entries()).map(([id, value]) => ({
      id,
      value
    }));
    const columns = this.selectedColumns.map(col => col.key);
    const grouping = this.grouping;

    // Use QueryBuilder instead of direct Prisma call
    const queryBuilder = new QueryBuilder('SavedSearch', 'ss');

    // Build the update query
    let updateQuery = 'UPDATE "SavedSearch" SET ';
    const updateValues = [];
    let paramIndex = 1;

    if (name) {
      updateQuery += `"name" = $${paramIndex++}, `;
      updateValues.push(name);
    }

    updateQuery += `"filters" = $${paramIndex++}, "columns" = $${paramIndex++}, "grouping" = $${paramIndex++}, "updatedAt" = $${paramIndex++} `;
    updateValues.push(JSON.stringify(filters), JSON.stringify(columns), grouping, new Date().toISOString());

    updateQuery += `WHERE "id" = $${paramIndex}`;
    updateValues.push(searchId);

    queryBuilder.whereRaw(updateQuery, updateValues);

    await queryBuilder.execute();
  }
  Fixed by yanh on 2025-06-12
  */
	async update(
		searchId: string,
		search: Pick<SavedSearch, 'name' | 'locked' | 'editlock' | 'limitResults' | 'path'>
	): Promise<void> {
		// Always update the filters, columns, and grouping to the current state
		const filters = Array.from(this.activeFilters.entries()).map(([id, value]) => ({
			id,
			value
		}));
		const columns = this.selectedColumns.map(col => col.key);
		const grouping = this.grouping;

		await prisma.savedSearch.update({
			where: { id: searchId },
			data: {
				...search,
				filters,
				columns,
				grouping
			}
		});
	}

	/**
	 * Deletes a saved search
	 */
	static async delete(searchId: string): Promise<void> {
		/** Fixed by yanh on 2025-06-12
    // Use QueryBuilder instead of direct Prisma call
    const queryBuilder = new QueryBuilder('SavedSearch', 'ss');

    // Build the delete query
    queryBuilder.whereRaw('DELETE FROM "SavedSearch" WHERE "id" = $1', [searchId]);

    await queryBuilder.execute();
    */
		await prisma.savedSearch.delete({
			where: { id: searchId }
		});
	}

	/**
	 * Registers common filters and columns
	 */
	private registerDefaultFilters(): void {
		// The filters and columns are now registered in their respective registries' constructors
		// This method is kept for backward compatibility but doesn't need to do anything
	}

	/**
	 * Type guard to check if an object is a Filter
	 */
	private isFilter(obj: Filter | FilterGroup): obj is Filter {
		return obj && obj.type === 'filter';
	}

	/**
	 * Creates a tracking proxy around a QueryBuilder to track selected fields
	 * @param builder The QueryBuilder to wrap
	 * @returns A tuple containing the tracking builder and column fields map
	 */
	private createTrackingBuilder(builder: QueryBuilder): [any, Map<string, Set<string>>] {
		// Track which fields are selected by each column
		const columnFieldsMap = new Map<string, Set<string>>();

		// Create a wrapper around the QueryBuilder to track selected fields
		const trackingBuilder = new Proxy(builder, {
			get: (target, prop, receiver) => {
				const originalMethod = Reflect.get(target, prop, receiver);

				// Intercept the select and selectRaw methods to track fields
				if (prop === 'select' || prop === 'selectRaw') {
					return (...args: any[]) => {
						// Get the current column being processed
						const currentColumn = this.selectedColumns[this.selectedColumns.length - 1];
						if (currentColumn) {
							// Initialize the set of fields for this column if it doesn't exist
							if (!columnFieldsMap.has(currentColumn.key)) {
								columnFieldsMap.set(currentColumn.key, new Set<string>());
							}

							// Extract field names from the arguments
							const fields = args[0];
							if (Array.isArray(fields)) {
								// Handle array of fields
								fields.forEach(field => {
									// Extract the field name or alias
									let fieldName = field;
									if (field.includes(' AS ')) {
										fieldName = field.split(/ AS /i)[1].trim();
									} else if (field.includes('.')) {
										fieldName = field.split('.').pop() || field;
									}

									// Add the field to the set for this column
									columnFieldsMap.get(currentColumn.key)!.add(fieldName);
								});
							} else if (typeof fields === 'string') {
								// Handle single field
								let fieldName = fields;
								if (fields.includes(' AS ')) {
									fieldName = fields.split(/ AS /i)[1].trim();
								} else if (fields.includes('.')) {
									fieldName = fields.split('.').pop() || fields;
								}

								// Add the field to the set for this column
								columnFieldsMap.get(currentColumn.key)!.add(fieldName);
							}
						}

						// Call the original method
						return originalMethod.apply(target, args);
					};
				}

				// Return the original method for all other properties
				return typeof originalMethod === 'function'
					? function (...args: any[]) {
							return originalMethod.apply(target, args);
						}
					: originalMethod;
			}
		});

		return [trackingBuilder, columnFieldsMap];
	}

	/**
	 * Applies column query modifications to the tracking builder
	 * @param trackingBuilder The tracking builder to apply modifications to
	 */
	private applyColumnQueryModifications(trackingBuilder: any): void {
		// Apply column query modifications
		for (const column of this.selectedColumns) {
			// Create a column-specific proxy that isolates the query builder for this column
			const columnProxy = new Proxy(trackingBuilder, {
				get: (target, prop, receiver) => {
					const originalMethod = Reflect.get(target, prop, receiver);

					// For all methods, we'll track that they're being called for this specific column
					if (typeof originalMethod === 'function') {
						return function (...args: any[]) {
							// Set the current column context
							(target as any).__currentColumn = column.key;

							// Call the original method
							const result = originalMethod.apply(target, args);

							// Clear the column context
							delete (target as any).__currentColumn;

							return result;
						};
					}

					return originalMethod;
				}
			});

			if (column.beforeQuery) {
				column.beforeQuery(columnProxy);
			}
			column.addToQuery(columnProxy);
			if (column.afterQuery) {
				column.afterQuery(columnProxy);
			}
		}
	}

	/**
	 * Finds the appropriate filter instance for a given field
	 */
	private findFilterForField(field: string): BaseFilter | undefined {
		return this.filterRegistry.findByField(field);
	}

	/**
	 * Normalizes AI-provided values to match filter expectations
	 */
	private normalizeAIValue(value: any, filter: BaseFilter): any {
		if (filter instanceof DateRangeFilter) {
			if (Array.isArray(value)) {
				return {
					start: new Date(value[0]),
					end: new Date(value[1])
				};
			}
			return { start: new Date(value), end: new Date(value) };
		}

		return value;
	}

	/**
	 * Converts state names to standard abbreviations
	 */
	private normalizeStateValue(state: string): string {
		const stateMap: Record<string, string> = {
			kentucky: 'KY',
			california: 'CA'
			// ... add other states as needed
		};

		const normalized = state.toLowerCase();
		return stateMap[normalized] || state.toUpperCase();
	}

	/**
	 * Returns all active filters and their values
	 */
	getActiveFilters(): Map<string, any> {
		return new Map(this.activeFilters);
	}

	/**
	 * Returns active filters as an array of objects
	 */
	getActiveFiltersArray(): Array<{ id: string; field: string; value: any }> {
		return Array.from(this.activeFilters.entries()).map(([id, value]) => {
			const filter = this.filterRegistry.get(id);
			return {
				id,
				field: filter?.getFieldName?.() || '',
				value
			};
		});
	}

	setEntityType(type: 'incidents' | 'participants'): this {
		this.entityType = type;
		return this;
	}

	getEntityType(): 'incidents' | 'participants' {
		return this.entityType;
	}

	setFormType(type: 'basic' | 'advanced'): this {
		this.formType = type;
		return this;
	}

	getFormType(): 'basic' | 'advanced' {
		return this.formType;
	}
}
