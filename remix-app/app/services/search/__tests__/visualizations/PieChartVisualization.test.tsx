import { PieChartVisualization } from '../../visualizations/PieChartVisualization';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

describe('PieChartVisualization', () => {
	let visualization: PieChartVisualization;

	beforeEach(() => {
		visualization = new PieChartVisualization();
	});

	describe('properties', () => {
		it('should have the correct key', () => {
			expect(visualization.key).toBe('pie_chart');
		});

		it('should have the correct label', () => {
			expect(visualization.label).toBe('Pie Chart');
		});

		it('should have the correct type', () => {
			expect(visualization.type).toBe('pie');
		});

		it('should have the correct description', () => {
			expect(visualization.description).toBe('Visualize data as a pie chart');
		});
	});

	describe('getConfigOptions', () => {
		it('should return the default configuration options', () => {
			const config = visualization.getConfigOptions();
			expect(config).toEqual({
				labelField: '',
				valueField: '',
				title: 'Pie Chart',
				colors: ['#4285F4', '#34A853', '#FBBC05', '#EA4335', '#8E24AA', '#D81B60', '#7CB342', '#FB8C00'],
				showLegend: true,
				showPercentage: true,
				donut: false,
				height: 400,
				width: 600
			});
		});
	});

	describe('prepareData', () => {
		it('should return the data unchanged', () => {
			const data = [
				{ id: 1, name: 'Item 1', value: 10 },
				{ id: 2, name: 'Item 2', value: 20 }
			];
			const prepared = visualization.prepareData(data);
			expect(prepared).toEqual(data);
		});

		it('should handle empty data array', () => {
			const prepared = visualization.prepareData([]);
			expect(prepared).toEqual([]);
		});
	});

	describe('render', () => {
		it('should render the chart with the provided data and config', () => {
			const data = [
				{ id: 1, name: 'Item 1', value: 10 },
				{ id: 2, name: 'Item 2', value: 20 }
			];
			const config = {
				labelField: 'name',
				valueField: 'value',
				title: 'Test Pie Chart',
				showLegend: true,
				showPercentage: true
			};

			const { container } = render(visualization.render(data, config));

			// Check that the chart container is rendered
			expect(container.querySelector('.pie-chart-container')).toBeInTheDocument();

			// Check that the title is rendered
			expect(screen.getByText('Test Pie Chart')).toBeInTheDocument();

			// Check that the field information is rendered
			expect(screen.getByText(/Label Field: name/)).toBeInTheDocument();
			expect(screen.getByText(/Value Field: value/)).toBeInTheDocument();

			// Check that the data points count is rendered
			expect(screen.getByText(/Data Points: 2/)).toBeInTheDocument();

			// Check that the legend is rendered when showLegend is true
			expect(container.querySelector('.chart-legend')).toBeInTheDocument();
		});

		it('should not render the legend when showLegend is false', () => {
			const data = [{ id: 1, name: 'Item 1', value: 10 }];
			const config = {
				labelField: 'name',
				valueField: 'value',
				showLegend: false
			};

			const { container } = render(visualization.render(data, config));

			// Check that the legend is not rendered
			expect(container.querySelector('.chart-legend')).not.toBeInTheDocument();
		});

		it('should apply custom width and height', () => {
			const data = [{ id: 1, name: 'Item 1', value: 10 }];
			const config = {
				labelField: 'name',
				valueField: 'value',
				width: 800,
				height: 600
			};

			const { container } = render(visualization.render(data, config));

			// Check that the custom width and height are applied
			const chartContainer = container.querySelector('.pie-chart-container');
			expect(chartContainer).toHaveStyle('width: 800px');
			expect(chartContainer).toHaveStyle('height: 600px');
		});

		it('should display information about donut and showPercentage options', () => {
			const data = [{ id: 1, name: 'Item 1', value: 10 }];
			const config = {
				labelField: 'name',
				valueField: 'value',
				donut: true,
				showPercentage: false
			};

			render(visualization.render(data, config));

			// Check that the donut and showPercentage information is displayed
			expect(screen.getByText(/Donut: true/)).toBeInTheDocument();
			expect(screen.getByText(/Show Percentage: false/)).toBeInTheDocument();
		});

		it('should display total value', () => {
			const data = [
				{ id: 1, name: 'Item 1', value: 10 },
				{ id: 2, name: 'Item 2', value: 20 }
			];
			const config = {
				labelField: 'name',
				valueField: 'value'
			};

			render(visualization.render(data, config));

			// Check that the total value is displayed
			expect(screen.getByText(/Total Value: 30/)).toBeInTheDocument();
		});
	});
});
