import { VisualizationRegistry } from '../../VisualizationRegistry';
import { BaseVisualization } from '../../BaseVisualization';
import type { VisualizationType } from '../../types';
import { SearchPermission } from '../../types';

// Create mock visualization classes for testing
class MockBarVisualization extends BaseVisualization {
	key = 'mock_bar';
	label = 'Mock Bar Chart';
	type: VisualizationType = 'bar';
	description = 'A mock bar chart for testing';

	getConfigOptions(): any {
		return { type: 'bar' };
	}

	prepareData(data: any[]): any {
		return data;
	}

	render(data: any, config: any): JSX.Element {
		return {} as JSX.Element;
	}
}

class MockLineVisualization extends BaseVisualization {
	key = 'mock_line';
	label = 'Mock Line Chart';
	type: VisualizationType = 'line';
	description = 'A mock line chart for testing';

	getConfigOptions(): any {
		return { type: 'line' };
	}

	prepareData(data: any[]): any {
		return data;
	}

	render(data: any, config: any): JSX.Element {
		return {} as JSX.Element;
	}
}

describe('VisualizationRegistry', () => {
	let registry: VisualizationRegistry;

	beforeEach(() => {
		// Clear the registry before each test
		VisualizationRegistry.getInstance().clear();
		registry = VisualizationRegistry.getInstance();
	});

	describe('getInstance', () => {
		it('should return the same instance each time', () => {
			const instance1 = VisualizationRegistry.getInstance();
			const instance2 = VisualizationRegistry.getInstance();
			expect(instance1).toBe(instance2);
		});
	});

	describe('register', () => {
		it('should register a visualization', () => {
			const visualization = new MockBarVisualization();
			registry.register(visualization);
			expect(registry.get('mock_bar')).toBe(visualization);
		});

		it('should overwrite existing visualization with same key', () => {
			const visualization1 = new MockBarVisualization();
			const visualization2 = new MockBarVisualization();
			registry.register(visualization1);
			registry.register(visualization2);
			expect(registry.get('mock_bar')).toBe(visualization2);
		});
	});

	describe('registerClass', () => {
		it('should register a visualization class', () => {
			registry.registerClass(MockBarVisualization);
			const visualization = registry.get('mock_bar');
			expect(visualization).toBeInstanceOf(MockBarVisualization);
		});
	});

	describe('get', () => {
		it('should return the visualization with the given key', () => {
			const visualization = new MockBarVisualization();
			registry.register(visualization);
			expect(registry.get('mock_bar')).toBe(visualization);
		});

		it('should return undefined for non-existent key', () => {
			expect(registry.get('non_existent')).toBeUndefined();
		});
	});

	describe('getAll', () => {
		it('should return all registered visualizations', () => {
			const barVisualization = new MockBarVisualization();
			const lineVisualization = new MockLineVisualization();
			registry.register(barVisualization);
			registry.register(lineVisualization);
			const all = registry.getAll();
			expect(all).toHaveLength(2);
			expect(all).toContain(barVisualization);
			expect(all).toContain(lineVisualization);
		});

		it('should return empty array if no visualizations registered', () => {
			expect(registry.getAll()).toEqual([]);
		});
	});

	describe('getAllAsMap', () => {
		it('should return all registered visualizations as a map', () => {
			const barVisualization = new MockBarVisualization();
			const lineVisualization = new MockLineVisualization();
			registry.register(barVisualization);
			registry.register(lineVisualization);
			const map = registry.getAllAsMap();
			expect(map.size).toBe(2);
			expect(map.get('mock_bar')).toBe(barVisualization);
			expect(map.get('mock_line')).toBe(lineVisualization);
		});

		it('should return empty map if no visualizations registered', () => {
			expect(registry.getAllAsMap().size).toBe(0);
		});
	});

	describe('getByType', () => {
		it('should return visualizations of the specified type', () => {
			const barVisualization1 = new MockBarVisualization();
			const barVisualization2 = new MockBarVisualization();
			barVisualization2.key = 'mock_bar_2';
			const lineVisualization = new MockLineVisualization();
			registry.register(barVisualization1);
			registry.register(barVisualization2);
			registry.register(lineVisualization);

			const barVisualizations = registry.getByType('bar');
			expect(barVisualizations).toHaveLength(2);
			expect(barVisualizations).toContain(barVisualization1);
			expect(barVisualizations).toContain(barVisualization2);

			const lineVisualizations = registry.getByType('line');
			expect(lineVisualizations).toHaveLength(1);
			expect(lineVisualizations).toContain(lineVisualization);
		});

		it('should return empty array if no visualizations of the specified type', () => {
			const barVisualization = new MockBarVisualization();
			registry.register(barVisualization);
			expect(registry.getByType('pie')).toEqual([]);
		});
	});

	describe('clear', () => {
		it('should remove all registered visualizations', () => {
			const barVisualization = new MockBarVisualization();
			const lineVisualization = new MockLineVisualization();
			registry.register(barVisualization);
			registry.register(lineVisualization);
			expect(registry.getAll()).toHaveLength(2);

			registry.clear();
			expect(registry.getAll()).toHaveLength(0);
		});
	});
});
