import { SearchBuilder } from '../../SearchBuilder.server';
import { BaseVisualization } from '../../BaseVisualization';
import { VisualizationRegistry } from '../../VisualizationRegistry';
import { BarChartVisualization } from '../../visualizations/BarChartVisualization';
import { LineChartVisualization } from '../../visualizations/LineChartVisualization';
import { PieChartVisualization } from '../../visualizations/PieChartVisualization';

// Mock the QueryBuilder
jest.mock('../../../query/QueryBuilder.server', () => {
	return {
		QueryBuilder: jest.fn().mockImplementation(() => {
			return {
				select: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				orWhere: jest.fn().mockReturnThis(),
				orderBy: jest.fn().mockReturnThis(),
				limit: jest.fn().mockReturnThis(),
				offset: jest.fn().mockReturnThis(),
				count: jest.fn().mockResolvedValue(10),
				execute: jest.fn().mockResolvedValue([
					{ id: 1, name: 'Item 1', value: 10 },
					{ id: 2, name: 'Item 2', value: 20 }
				])
			};
		})
	};
});

// Mock the db
jest.mock('~/utils/db.server', () => {
	return {
		db: {
			savedSearch: {
				findUnique: jest.fn().mockResolvedValue(null),
				create: jest.fn().mockResolvedValue({ id: 1 }),
				update: jest.fn().mockResolvedValue({ id: 1 }),
				delete: jest.fn().mockResolvedValue({ id: 1 }),
				findMany: jest.fn().mockResolvedValue([])
			}
		}
	};
});

describe('SearchBuilder Visualization Integration', () => {
	let searchBuilder: SearchBuilder;
	let barChartVisualization: BarChartVisualization;
	let lineChartVisualization: LineChartVisualization;
	let pieChartVisualization: PieChartVisualization;

	beforeEach(() => {
		// Clear the registry before each test
		VisualizationRegistry.getInstance().clear();

		// Create a new SearchBuilder instance
		searchBuilder = new SearchBuilder('incidents', 'i');

		// Create visualization instances
		barChartVisualization = new BarChartVisualization();
		lineChartVisualization = new LineChartVisualization();
		pieChartVisualization = new PieChartVisualization();
	});

	describe('registerVisualization', () => {
		it('should register a visualization with the SearchBuilder', () => {
			searchBuilder.registerVisualization(barChartVisualization);

			// Check that the visualization is registered with the registry
			const registry = VisualizationRegistry.getInstance();
			expect(registry.get('bar_chart')).toBe(barChartVisualization);
		});

		it('should register multiple visualizations', () => {
			searchBuilder.registerVisualization(barChartVisualization);
			searchBuilder.registerVisualization(lineChartVisualization);
			searchBuilder.registerVisualization(pieChartVisualization);

			const registry = VisualizationRegistry.getInstance();
			expect(registry.get('bar_chart')).toBe(barChartVisualization);
			expect(registry.get('line_chart')).toBe(lineChartVisualization);
			expect(registry.get('pie_chart')).toBe(pieChartVisualization);
		});
	});

	describe('setVisualization', () => {
		beforeEach(() => {
			// Register visualizations
			searchBuilder.registerVisualization(barChartVisualization);
			searchBuilder.registerVisualization(lineChartVisualization);
			searchBuilder.registerVisualization(pieChartVisualization);
		});

		it('should set the active visualization by key', () => {
			searchBuilder.setVisualization('bar_chart', { title: 'Test Chart' });

			expect(searchBuilder.getVisualization()).toBe('bar_chart');
			expect(searchBuilder.getVisualizationConfig()).toEqual({ title: 'Test Chart' });
		});

		it('should throw an error if the visualization key is not registered', () => {
			expect(() => {
				searchBuilder.setVisualization('non_existent', {});
			}).toThrow('Visualization with key non_existent not found');
		});

		it('should merge the provided config with default config', () => {
			searchBuilder.setVisualization('bar_chart', { title: 'Custom Title' });

			const config = searchBuilder.getVisualizationConfig();
			expect(config.title).toBe('Custom Title');
			expect(config.xAxisField).toBe(''); // Default value
			expect(config.yAxisField).toBe(''); // Default value
		});
	});

	describe('clearVisualization', () => {
		beforeEach(() => {
			searchBuilder.registerVisualization(barChartVisualization);
			searchBuilder.setVisualization('bar_chart', { title: 'Test Chart' });
		});

		it('should clear the active visualization', () => {
			searchBuilder.clearVisualization();

			expect(searchBuilder.getVisualization()).toBeNull();
			expect(searchBuilder.getVisualizationConfig()).toBeNull();
		});
	});

	describe('generateVisualization', () => {
		beforeEach(() => {
			searchBuilder.registerVisualization(barChartVisualization);
		});

		it('should return null if no visualization is set', async () => {
			const result = await searchBuilder.generateVisualization(['user']);
			expect(result).toBeNull();
		});

		it('should generate a visualization based on search results', async () => {
			searchBuilder.setVisualization('bar_chart', {
				xAxisField: 'name',
				yAxisField: 'value',
				title: 'Test Chart'
			});

			const result = await searchBuilder.generateVisualization(['user']);

			expect(result).not.toBeNull();
			expect(result?.type).toBe('div');
			expect(result?.props.className).toBe('bar-chart-container');

			// Check that the title is set correctly
			const titleElement = result?.props.children[0];
			expect(titleElement.type).toBe('h3');
			expect(titleElement.props.children).toBe('Test Chart');
		});

		it('should return null if user does not have permission', async () => {
			// Create a visualization with restricted permissions
			const restrictedVisualization = new BarChartVisualization([{ roles: ['admin'] }]);
			searchBuilder.registerVisualization(restrictedVisualization);
			searchBuilder.setVisualization('bar_chart', {});

			const result = await searchBuilder.generateVisualization(['user']);
			expect(result).toBeNull();
		});
	});

	describe('save and load with visualizations', () => {
		beforeEach(() => {
			searchBuilder.registerVisualization(barChartVisualization);
			searchBuilder.setVisualization('bar_chart', {
				xAxisField: 'name',
				yAxisField: 'value',
				title: 'Test Chart'
			});
		});

		it('should include visualization in saved search', async () => {
			// Mock the db.savedSearch.create method
			const mockCreate = jest.fn().mockResolvedValue({ id: 1 });
			(require('~/utils/db.server').db.savedSearch.create as jest.Mock).mockImplementation(mockCreate);

			await searchBuilder.save('Test Search', 'user1');

			// Check that the visualization was included in the saved search
			expect(mockCreate).toHaveBeenCalled();
			const createArgs = mockCreate.mock.calls[0][0];
			expect(createArgs.data.visualization).toBe('bar_chart');
			expect(createArgs.data.visualizationConfig).toContain('Test Chart');
		});

		it('should load visualization from saved search', async () => {
			// Mock the db.savedSearch.findUnique method
			const mockFindUnique = jest.fn().mockResolvedValue({
				id: 1,
				name: 'Test Search',
				userId: 'user1',
				filters: '{}',
				columns: '[]',
				visualization: 'bar_chart',
				visualizationConfig: JSON.stringify({
					xAxisField: 'name',
					yAxisField: 'value',
					title: 'Loaded Chart'
				})
			});
			(require('~/utils/db.server').db.savedSearch.findUnique as jest.Mock).mockImplementation(mockFindUnique);

			const loadedBuilder = await SearchBuilder.load('1', ['user']);
			// Replace the current searchBuilder with the loaded one
			Object.assign(searchBuilder, loadedBuilder);

			// Check that the visualization was loaded
			expect(searchBuilder.getVisualization()).toBe('bar_chart');
			expect(searchBuilder.getVisualizationConfig().title).toBe('Loaded Chart');
		});
	});
});
