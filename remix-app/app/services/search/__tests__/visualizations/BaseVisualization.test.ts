import { BaseVisualization } from '../../BaseVisualization';
import type { SearchPermission, VisualizationType } from '../../types';
import React from 'react';

// Create a mock implementation of BaseVisualization for testing
class MockVisualization extends BaseVisualization {
	key = 'mock_visualization';
	label = 'Mock Visualization';
	type: VisualizationType = 'bar';
	description = 'A mock visualization for testing';

	getConfigOptions(): any {
		return {
			option1: 'value1',
			option2: 'value2'
		};
	}

	prepareData(data: any[]): any {
		return data.map(item => ({ ...item, processed: true }));
	}

	render(data: any, config: any): JSX.Element {
		return React.createElement('div', { className: 'mock-visualization' }, 'Mock Visualization');
	}
}

describe('BaseVisualization', () => {
	let visualization: MockVisualization;

	beforeEach(() => {
		visualization = new MockVisualization();
	});

	describe('constructor', () => {
		it('should set default permissions if none provided', () => {
			expect(visualization.permissions).toEqual([{ roles: ['user', 'admin'] }]);
		});

		it('should set custom permissions if provided', () => {
			const customPermissions: SearchPermission[] = [{ roles: ['admin'] }];
			const customViz = new MockVisualization(customPermissions);
			expect(customViz.permissions).toEqual(customPermissions);
		});
	});

	describe('hasPermission', () => {
		it('should return true if user has required role', () => {
			expect(visualization.hasPermission(['user'])).toBe(true);
			expect(visualization.hasPermission(['admin'])).toBe(true);
			expect(visualization.hasPermission(['user', 'editor'])).toBe(true);
		});

		it('should return false if user does not have required role', () => {
			expect(visualization.hasPermission(['guest'])).toBe(false);
			expect(visualization.hasPermission([])).toBe(false);
		});

		it('should handle custom permissions correctly', () => {
			const customViz = new MockVisualization([{ roles: ['editor'] }]);
			expect(customViz.hasPermission(['editor'])).toBe(true);
			expect(customViz.hasPermission(['user'])).toBe(false);
			expect(customViz.hasPermission(['admin'])).toBe(false);
		});

		it('should handle multiple permission entries correctly', () => {
			const customViz = new MockVisualization([{ roles: ['editor'] }, { roles: ['viewer'] }]);
			expect(customViz.hasPermission(['editor'])).toBe(true);
			expect(customViz.hasPermission(['viewer'])).toBe(true);
			expect(customViz.hasPermission(['user'])).toBe(false);
		});
	});

	describe('abstract methods', () => {
		it('should implement getConfigOptions', () => {
			const config = visualization.getConfigOptions();
			expect(config).toEqual({
				option1: 'value1',
				option2: 'value2'
			});
		});

		it('should implement prepareData', () => {
			const data = [{ id: 1 }, { id: 2 }];
			const prepared = visualization.prepareData(data);
			expect(prepared).toEqual([
				{ id: 1, processed: true },
				{ id: 2, processed: true }
			]);
		});

		it('should implement render', () => {
			const data = [{ id: 1 }];
			const config = { option: 'value' };
			const rendered = visualization.render(data, config);
			expect(rendered.type).toBe('div');
			expect(rendered.props.className).toBe('mock-visualization');
		});
	});
});
