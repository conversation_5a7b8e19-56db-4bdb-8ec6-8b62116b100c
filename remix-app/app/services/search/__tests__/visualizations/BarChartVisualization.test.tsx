import type { BarChartConfig } from '../../visualizations/BarChartVisualization';
import { BarChartVisualization } from '../../visualizations/BarChartVisualization';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

describe('BarChartVisualization', () => {
	let visualization: BarChartVisualization;

	beforeEach(() => {
		visualization = new BarChartVisualization();
	});

	describe('properties', () => {
		it('should have the correct key', () => {
			expect(visualization.key).toBe('bar_chart');
		});

		it('should have the correct label', () => {
			expect(visualization.label).toBe('Bar Chart');
		});

		it('should have the correct type', () => {
			expect(visualization.type).toBe('bar');
		});

		it('should have the correct description', () => {
			expect(visualization.description).toBe('Visualize data as a bar chart');
		});
	});

	describe('getConfigOptions', () => {
		it('should return the default configuration options', () => {
			const config = visualization.getConfigOptions();
			expect(config).toEqual({
				xAxisField: '',
				yAxisField: '',
				title: 'Bar Chart',
				xAxisLabel: 'X Axis',
				yAxisLabel: 'Y Axis',
				colors: ['#4285F4', '#34A853', '#FBBC05', '#EA4335'],
				stacked: false,
				horizontal: false,
				showLegend: true,
				height: 400,
				width: 600
			});
		});
	});

	describe('prepareData', () => {
		it('should return the data unchanged', () => {
			const data = [
				{ id: 1, name: 'Item 1' },
				{ id: 2, name: 'Item 2' }
			];
			const prepared = visualization.prepareData(data);
			expect(prepared).toEqual(data);
		});

		it('should handle empty data array', () => {
			const prepared = visualization.prepareData([]);
			expect(prepared).toEqual([]);
		});
	});

	describe('render', () => {
		it('should render the chart with the provided data and config', () => {
			const data = [
				{ id: 1, name: 'Item 1' },
				{ id: 2, name: 'Item 2' }
			];
			const config: BarChartConfig = {
				xAxisField: 'name',
				yAxisField: 'id',
				title: 'Test Chart',
				xAxisLabel: 'Name',
				yAxisLabel: 'ID',
				showLegend: true
			};

			const { container } = render(visualization.render(data, config));

			// Check that the chart container is rendered
			expect(container.querySelector('.bar-chart-container')).toBeInTheDocument();

			// Check that the title is rendered
			expect(screen.getByText('Test Chart')).toBeInTheDocument();

			// Check that the axis information is rendered
			expect(screen.getByText(/X Axis: name/)).toBeInTheDocument();
			expect(screen.getByText(/Y Axis: id/)).toBeInTheDocument();

			// Check that the data points count is rendered
			expect(screen.getByText(/Data Points: 2/)).toBeInTheDocument();

			// Check that the legend is rendered when showLegend is true
			expect(container.querySelector('.chart-legend')).toBeInTheDocument();
		});

		it('should not render the legend when showLegend is false', () => {
			const data = [{ id: 1 }];
			const config: BarChartConfig = {
				xAxisField: 'id',
				yAxisField: 'id',
				showLegend: false
			};

			const { container } = render(visualization.render(data, config));

			// Check that the legend is not rendered
			expect(container.querySelector('.chart-legend')).not.toBeInTheDocument();
		});

		it('should apply custom width and height', () => {
			const data = [{ id: 1 }];
			const config: BarChartConfig = {
				xAxisField: 'id',
				yAxisField: 'id',
				width: 800,
				height: 600
			};

			const { container } = render(visualization.render(data, config));

			// Check that the custom width and height are applied
			const chartContainer = container.querySelector('.bar-chart-container');
			expect(chartContainer).toHaveStyle('width: 800px');
			expect(chartContainer).toHaveStyle('height: 600px');
		});
	});
});
