import { IncidentDateColumn } from '../../columns/IncidentDateColumn';
import { QueryBuilder } from '../../../query/QueryBuilder.server';
import { format } from 'date-fns';

// Mock the QueryBuilder
jest.mock('../../../query/QueryBuilder.server', () => {
	return {
		QueryBuilder: jest.fn().mockImplementation(() => {
			return {
				select: jest.fn().mockReturnThis()
			};
		})
	};
});

describe('IncidentDateColumn', () => {
	let column: IncidentDateColumn;
	let queryBuilder: jest.Mocked<QueryBuilder>;

	beforeEach(() => {
		column = new IncidentDateColumn();
		queryBuilder = new QueryBuilder('incidents', 'i') as jest.Mocked<QueryBuilder>;
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	describe('properties', () => {
		it('should have the correct key', () => {
			expect(column.key).toBe('incident_date');
		});

		it('should have the correct label', () => {
			expect(column.label).toBe('Date');
		});

		it('should be sortable', () => {
			expect(column.sortable).toBe(true);
		});
	});

	describe('addToQuery', () => {
		it('should add the correct fields to the query', () => {
			column.addToQuery(queryBuilder);

			expect(queryBuilder.select).toHaveBeenCalledTimes(1);
			expect(queryBuilder.select).toHaveBeenCalledWith(['i.incident_date']);
		});
	});

	describe('beforeRender', () => {
		it('should convert string date to Date object', () => {
			const data = { incident_date: '2023-01-01T00:00:00.000Z' };
			const result = column.beforeRender(data);

			expect(result.incident_date).toBeInstanceOf(Date);
			expect(result.incident_date.toISOString()).toBe('2023-01-01T00:00:00.000Z');
		});

		it('should return data unchanged if incident_date is missing', () => {
			const data = { other_field: 'value' };
			const result = column.beforeRender(data);

			expect(result).toEqual(data);
		});
	});

	describe('render', () => {
		it('should format date correctly', () => {
			const date = new Date('2023-01-01');
			const result = column.render(date, {});

			expect(result).toBe(format(date, 'MMM d, yyyy'));
		});

		it('should return empty string for null or undefined date', () => {
			expect(column.render(null as any, {})).toBe('');
			expect(column.render(undefined as any, {})).toBe('');
		});
	});

	describe('getCssClasses', () => {
		it('should return the correct CSS classes', () => {
			const classes = column.getCssClasses();

			expect(classes).toContain('date-column');
			expect(classes).toContain('text-center');
		});
	});
});
