import { DateRangeFilter } from '../../filters/DateRangeFilter';
import { QueryBuilder } from '../../../query/QueryBuilder.server';

// Mock the QueryBuilder
jest.mock('../../../query/QueryBuilder.server', () => {
	return {
		QueryBuilder: jest.fn().mockImplementation(() => {
			return {
				where: jest.fn().mockReturnThis(),
				whereRaw: jest.fn().mockReturnThis()
			};
		})
	};
});

describe('DateRangeFilter', () => {
	let filter: DateRangeFilter;
	let queryBuilder: jest.Mocked<QueryBuilder>;

	beforeEach(() => {
		filter = new DateRangeFilter();
		queryBuilder = new QueryBuilder('incidents', 'i') as jest.Mocked<QueryBuilder>;
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	describe('getOperators', () => {
		it('should return the correct operators', () => {
			const operators = filter.getOperators();
			expect(operators).toHaveLength(4);
			expect(operators[0].value).toBe('between');
			expect(operators[1].value).toBe('this_year');
			expect(operators[2].value).toBe('last_year');
			expect(operators[3].value).toBe('next_year');
		});
	});

	describe('getDefaultValue', () => {
		it('should return undefined for this_year operator', () => {
			expect(filter.getDefaultValue('this_year')).toBeUndefined();
		});

		it('should return undefined for last_year operator', () => {
			expect(filter.getDefaultValue('last_year')).toBeUndefined();
		});

		it('should return undefined for next_year operator', () => {
			expect(filter.getDefaultValue('next_year')).toBeUndefined();
		});

		it('should return an object with start and end undefined for between operator', () => {
			const defaultValue = filter.getDefaultValue('between');
			expect(defaultValue).toEqual({ start: undefined, end: undefined });
		});

		it('should return undefined for unknown operator', () => {
			expect(filter.getDefaultValue('unknown')).toBeUndefined();
		});
	});

	describe('formatValue', () => {
		it('should format between values correctly', () => {
			const start = new Date('2023-01-01');
			const end = new Date('2023-12-31');
			const formatted = filter.formatValue({ start, end }, 'between');
			expect(formatted).toBe('Jan 1, 2023 - Dec 31, 2023');
		});

		it('should return empty string if start or end is missing', () => {
			expect(filter.formatValue({ start: undefined, end: new Date() }, 'between')).toBe('');
			expect(filter.formatValue({ start: new Date(), end: undefined }, 'between')).toBe('');
		});

		it('should return "This Year" for this_year operator', () => {
			expect(filter.formatValue(undefined, 'this_year')).toBe('This Year');
		});

		it('should return "Last Year" for last_year operator', () => {
			expect(filter.formatValue(undefined, 'last_year')).toBe('Last Year');
		});

		it('should return "Next Year" for next_year operator', () => {
			expect(filter.formatValue(undefined, 'next_year')).toBe('Next Year');
		});

		it('should return empty string for unknown operator', () => {
			expect(filter.formatValue(undefined, 'unknown')).toBe('');
		});
	});

	describe('apply', () => {
		it('should apply between operator correctly with start and end dates', () => {
			const start = new Date('2023-01-01');
			const end = new Date('2023-12-31');
			filter.apply(queryBuilder, { operator: 'between', start, end });

			expect(queryBuilder.where).toHaveBeenCalledTimes(2);
			expect(queryBuilder.where).toHaveBeenNthCalledWith(1, 'incident_date', 'gte', start);
			expect(queryBuilder.where).toHaveBeenNthCalledWith(2, 'incident_date', 'lte', end);
		});

		it('should apply between operator correctly with only start date', () => {
			const start = new Date('2023-01-01');
			filter.apply(queryBuilder, { operator: 'between', start });

			expect(queryBuilder.where).toHaveBeenCalledTimes(1);
			expect(queryBuilder.where).toHaveBeenCalledWith('incident_date', 'gte', start);
		});

		it('should apply between operator correctly with only end date', () => {
			const end = new Date('2023-12-31');
			filter.apply(queryBuilder, { operator: 'between', end });

			expect(queryBuilder.where).toHaveBeenCalledTimes(1);
			expect(queryBuilder.where).toHaveBeenCalledWith('incident_date', 'lte', end);
		});

		it('should apply this_year operator correctly', () => {
			filter.apply(queryBuilder, { operator: 'this_year' });

			expect(queryBuilder.whereRaw).toHaveBeenCalledTimes(1);
			expect(queryBuilder.whereRaw).toHaveBeenCalledWith(
				'EXTRACT(YEAR FROM incident_date) = EXTRACT(YEAR FROM CURRENT_DATE)'
			);
		});

		it('should apply last_year operator correctly', () => {
			filter.apply(queryBuilder, { operator: 'last_year' });

			expect(queryBuilder.whereRaw).toHaveBeenCalledTimes(1);
			expect(queryBuilder.whereRaw).toHaveBeenCalledWith(
				'EXTRACT(YEAR FROM incident_date) = EXTRACT(YEAR FROM CURRENT_DATE) - 1'
			);
		});

		it('should apply next_year operator correctly', () => {
			filter.apply(queryBuilder, { operator: 'next_year' });

			expect(queryBuilder.whereRaw).toHaveBeenCalledTimes(1);
			expect(queryBuilder.whereRaw).toHaveBeenCalledWith(
				'EXTRACT(YEAR FROM incident_date) = EXTRACT(YEAR FROM CURRENT_DATE) + 1'
			);
		});
	});

	describe('validate', () => {
		it('should validate between operator with valid dates', () => {
			const start = new Date();
			const end = new Date();
			expect(filter.validate({ operator: 'between', start, end })).toBe(true);
		});

		it('should invalidate between operator with invalid dates', () => {
			expect(filter.validate({ operator: 'between', start: 'invalid', end: new Date() })).toBe(false);
			expect(filter.validate({ operator: 'between', start: new Date(), end: 'invalid' })).toBe(false);
		});

		it('should validate this_year operator', () => {
			expect(filter.validate({ operator: 'this_year' })).toBe(true);
		});

		it('should validate last_year operator', () => {
			expect(filter.validate({ operator: 'last_year' })).toBe(true);
		});

		it('should validate next_year operator', () => {
			expect(filter.validate({ operator: 'next_year' })).toBe(true);
		});

		it('should invalidate unknown operator', () => {
			expect(filter.validate({ operator: 'unknown' })).toBe(false);
		});
	});
});
