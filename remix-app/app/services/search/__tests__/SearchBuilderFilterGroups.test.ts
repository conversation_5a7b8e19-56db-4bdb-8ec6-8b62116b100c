import { SearchBuilder } from '../SearchBuilder.server';
import { FilterRegistry } from '../FilterRegistry';
import { DateRangeFilter } from '../filters/DateRangeFilter';
import { StatusFilter } from '../filters/StatusFilter';
import { QueryBuilder } from '../../query/QueryBuilder.server';

// Mock the QueryBuilder
jest.mock('../../query/QueryBuilder.server', () => {
	return {
		QueryBuilder: jest.fn().mockImplementation(() => {
			return {
				select: jest.fn().mockReturnThis(),
				where: jest.fn().mockImplementation(callback => {
					if (callback) callback(mockSubQueryBuilder);
					return mockQueryBuilder;
				}),
				andWhere: jest.fn().mockReturnThis(),
				orWhere: jest.fn().mockImplementation(callback => {
					if (callback) callback(mockSubQueryBuilder);
					return mockQueryBuilder;
				}),
				orderBy: jest.fn().mockReturnThis(),
				limit: jest.fn().mockReturnThis(),
				offset: jest.fn().mockReturnThis(),
				count: jest.fn().mockResolvedValue(10),
				execute: jest.fn().mockResolvedValue([
					{ id: 1, name: 'Item 1', value: 10 },
					{ id: 2, name: 'Item 2', value: 20 }
				])
			};
		})
	};
});

// Mock the db
jest.mock('~/utils/db.server', () => {
	return {
		db: {
			savedSearch: {
				findUnique: jest.fn().mockResolvedValue(null),
				create: jest.fn().mockResolvedValue({ id: 1 }),
				update: jest.fn().mockResolvedValue({ id: 1 }),
				delete: jest.fn().mockResolvedValue({ id: 1 }),
				findMany: jest.fn().mockResolvedValue([])
			}
		}
	};
});

// Create a mock sub-query builder for nested queries
const mockSubQueryBuilder = {
	where: jest.fn().mockReturnThis(),
	andWhere: jest.fn().mockReturnThis(),
	orWhere: jest.fn().mockReturnThis()
};

// Create a mock query builder for the main query
const mockQueryBuilder = {
	select: jest.fn().mockReturnThis(),
	where: jest.fn().mockImplementation(callback => {
		if (callback) callback(mockSubQueryBuilder);
		return mockQueryBuilder;
	}),
	andWhere: jest.fn().mockReturnThis(),
	orWhere: jest.fn().mockImplementation(callback => {
		if (callback) callback(mockSubQueryBuilder);
		return mockQueryBuilder;
	}),
	orderBy: jest.fn().mockReturnThis(),
	limit: jest.fn().mockReturnThis(),
	offset: jest.fn().mockReturnThis(),
	count: jest.fn().mockResolvedValue(10),
	execute: jest.fn().mockResolvedValue([
		{ id: 1, name: 'Item 1', value: 10 },
		{ id: 2, name: 'Item 2', value: 20 }
	])
};

describe('SearchBuilder Filter Groups', () => {
	let searchBuilder: SearchBuilder;
	let filterRegistry: FilterRegistry;

	beforeEach(() => {
		// Clear mocks
		jest.clearAllMocks();

		// Clear the registry before each test
		FilterRegistry.getInstance().clear();
		filterRegistry = FilterRegistry.getInstance();

		// Register test filters
		// Create test-specific filter instances
		const dateFilter = new DateRangeFilter();
		// Override the properties for testing
		dateFilter.id = 'date';
		dateFilter.name = 'Date';
		(dateFilter as any).field = 'date_field';
		dateFilter.permissions = [{ roles: ['user', 'admin'] }];
		filterRegistry.register(dateFilter);

		const statusFilter = new StatusFilter();
		// Override the properties for testing
		statusFilter.id = 'status';
		statusFilter.name = 'Status';
		(statusFilter as any).field = 'status_field';
		statusFilter.permissions = [{ roles: ['user', 'admin'] }];
		filterRegistry.register(statusFilter);

		// Create a new SearchBuilder instance
		searchBuilder = new SearchBuilder('incidents', 'i');
	});

	describe('addFilterGroup', () => {
		it('should add a filter group to the SearchBuilder', () => {
			const filterGroup = {
				operator: 'AND' as const,
				conditions: [{ id: 'date', value: { operator: 'between', value: ['2023-01-01', '2023-12-31'] } }]
			};

			searchBuilder.addFilterGroup(filterGroup);

			expect(searchBuilder.getFilterGroups()).toHaveLength(1);
			expect(searchBuilder.getFilterGroups()[0]).toEqual(filterGroup);
		});

		it('should add multiple filter groups', () => {
			const filterGroup1 = {
				operator: 'AND' as const,
				conditions: [{ id: 'date', value: { operator: 'between', value: ['2023-01-01', '2023-12-31'] } }]
			};

			const filterGroup2 = {
				operator: 'OR' as const,
				conditions: [{ id: 'status', value: { operator: 'equals', value: 'active' } }]
			};

			searchBuilder.addFilterGroup(filterGroup1);
			searchBuilder.addFilterGroup(filterGroup2);

			expect(searchBuilder.getFilterGroups()).toHaveLength(2);
			expect(searchBuilder.getFilterGroups()[0]).toEqual(filterGroup1);
			expect(searchBuilder.getFilterGroups()[1]).toEqual(filterGroup2);
		});
	});

	describe('removeFilterGroup', () => {
		it('should remove a filter group at the specified index', () => {
			const filterGroup1 = {
				operator: 'AND' as const,
				conditions: [{ id: 'date', value: { operator: 'between', value: ['2023-01-01', '2023-12-31'] } }]
			};

			const filterGroup2 = {
				operator: 'OR' as const,
				conditions: [{ id: 'status', value: { operator: 'equals', value: 'active' } }]
			};

			searchBuilder.addFilterGroup(filterGroup1);
			searchBuilder.addFilterGroup(filterGroup2);

			searchBuilder.removeFilterGroup(0);

			expect(searchBuilder.getFilterGroups()).toHaveLength(1);
			expect(searchBuilder.getFilterGroups()[0]).toEqual(filterGroup2);
		});

		it('should do nothing if the index is out of bounds', () => {
			const filterGroup = {
				operator: 'AND' as const,
				conditions: [{ id: 'date', value: { operator: 'between', value: ['2023-01-01', '2023-12-31'] } }]
			};

			searchBuilder.addFilterGroup(filterGroup);

			searchBuilder.removeFilterGroup(1); // Out of bounds

			expect(searchBuilder.getFilterGroups()).toHaveLength(1);
			expect(searchBuilder.getFilterGroups()[0]).toEqual(filterGroup);
		});
	});

	describe('clearFilterGroups', () => {
		it('should remove all filter groups', () => {
			const filterGroup1 = {
				operator: 'AND' as const,
				conditions: [{ id: 'date', value: { operator: 'between', value: ['2023-01-01', '2023-12-31'] } }]
			};

			const filterGroup2 = {
				operator: 'OR' as const,
				conditions: [{ id: 'status', value: { operator: 'equals', value: 'active' } }]
			};

			searchBuilder.addFilterGroup(filterGroup1);
			searchBuilder.addFilterGroup(filterGroup2);

			searchBuilder.clearFilterGroups();

			expect(searchBuilder.getFilterGroups()).toHaveLength(0);
		});
	});

	describe('execute with filter groups', () => {
		it('should apply filter groups when executing the search', async () => {
			const filterGroup = {
				operator: 'AND' as const,
				conditions: [{ id: 'date', value: { operator: 'between', value: ['2023-01-01', '2023-12-31'] } }]
			};

			searchBuilder.addFilterGroup(filterGroup);

			await searchBuilder.execute(['user', 'admin']);

			// Verify that the where method was called on the query builder
			expect(mockQueryBuilder.where).toHaveBeenCalled();
		});

		it('should apply nested filter groups correctly', async () => {
			const nestedFilterGroup = {
				operator: 'OR' as const,
				conditions: [
					{ id: 'status', value: { operator: 'equals', value: 'active' } },
					{ id: 'status', value: { operator: 'equals', value: 'pending' } }
				]
			};

			const filterGroup = {
				operator: 'AND' as const,
				conditions: [
					{ id: 'date', value: { operator: 'between', value: ['2023-01-01', '2023-12-31'] } },
					nestedFilterGroup
				]
			};

			searchBuilder.addFilterGroup(filterGroup);

			await searchBuilder.execute(['user', 'admin']);

			// Verify that the where method was called on the query builder
			expect(mockQueryBuilder.where).toHaveBeenCalled();

			// Since we're using mocks, we can't easily verify the exact structure of the nested queries,
			// but we can at least verify that the orWhere method was called for the OR group
			expect(mockSubQueryBuilder.orWhere).toHaveBeenCalled();
		});
	});
});
