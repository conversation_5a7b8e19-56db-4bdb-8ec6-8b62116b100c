import { BaseColumn } from '../BaseColumn';
import type { QueryBuilder } from '../../query/QueryBuilder.server';

export class AddressColumn extends BaseColumn {
	key = 'address';
	label = 'Address';
	sortable = true;

	addToQuery(builder: QueryBuilder): void {
		builder.select(['i.address']);
	}

	beforeRender(data: any): any {
		// Ensure we have string values
		return {
			address: data.address || ''
		};
	}

	render(value: any, row: any): string {
		return value.address;
	}

	getCssClasses(): string[] {
		return ['address-column'];
	}
}
