import type { QueryBuilder } from '../../query/QueryBuilder.server';

export interface ISearchColumn {
	key: string;
	label: string;
	sortable?: boolean;

	// Query hooks
	beforeQuery?(builder: QueryBuilder): void;
	addToQuery(builder: QueryBuilder): void;
	afterQuery?(builder: QueryBuilder): void;

	// Render hooks and output
	beforeRender?(data: any): any;
	render(value: any, row: any): any;
	afterRender?(output: any): any;

	// Optional CSS classes for the column
	getCssClasses?(): string[];
}

export type ColumnConstructor = new () => ISearchColumn;
