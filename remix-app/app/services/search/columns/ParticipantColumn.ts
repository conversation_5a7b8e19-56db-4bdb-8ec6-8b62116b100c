import { BaseColumn } from '../BaseColumn';
import { type QueryBuilder } from '../../query/QueryBuilder.server';

export class ParticipantColumn extends BaseColumn {
	key = 'participants';
	label = 'Participants';
	sortable = false; // Sorting complex aggregated data can be problematic

	beforeQuery(builder: QueryBuilder): void {
		// Add join to incident_participants table
		builder.join('gva_data.incident_participants', 'ip.incident_id = i.incident_id', 'left', 'ip');
	}

	addToQuery(builder: QueryBuilder): void {
		// Count participants by type
		builder.selectRaw(
			"COUNT(DISTINCT CASE WHEN ip.participant_type = 'victim' THEN ip.participant_id END) AS participants_victim_count",
			'participants_victim_count'
		);
		builder.selectRaw(
			"COUNT(DISTINCT CASE WHEN ip.participant_type = 'perpetrator' THEN ip.participant_id END) AS participants_suspect_count",
			'participants_suspect_count'
		);
		builder.selectRaw("string_agg(DISTINCT ip.name, ', ') AS participants_names", 'participants_names');
		builder.groupBy('i.incident_id');
	}

	beforeRender(data: any): any {
		// Ensure we have numeric values
		// console.log('ParticipantColumn beforeRender:', data);
		data.participants_victim_count = parseInt(data.participants_victim_count || '0', 10);
		data.participants_suspect_count = parseInt(data.participants_suspect_count || '0', 10);
		data.participants_names = data.participants_names || '';
		return data;
	}

	render(value: any, row: any): string {
		const counts = [];
		if (value.participants_victim_count > 0) {
			counts.push(`${value.participants_victim_count} victim${value.participants_victim_count !== 1 ? 's' : ''}`);
		}
		if (value.participants_suspect_count > 0) {
			counts.push(
				`${value.participants_suspect_count} suspect${value.participants_suspect_count !== 1 ? 's' : ''}`
			);
		}

		let result = counts.join(', ');

		// Add names if available (truncated for display)
		if (value.participants_names) {
			const names =
				value.participants_names.length > 50
					? value.participants_names.substring(0, 50) + '...'
					: value.participants_names;
			result += result ? ` (${names})` : names;
		}

		return result || 'No participants';
	}

	getCssClasses(): string[] {
		return ['participants-column'];
	}
}
