import { BaseColumn } from '../BaseColumn';
import type { QueryBuilder } from '../../query/QueryBuilder.server';

export class ZipCodeColumn extends BaseColumn {
	key = 'zipcode';
	label = 'ZIP Code';
	sortable = true;

	addToQuery(builder: QueryBuilder): void {
		builder.select(['i.zipcode']);
	}

	beforeRender(data: any): any {
		// Ensure we have string values
		data.zipcode = data.zipcode || '';
		return data;
	}

	render(value: string, row: any): string {
		return value.zipcode;
	}

	getCssClasses(): string[] {
		return ['zipcode-column', 'text-center'];
	}
}
