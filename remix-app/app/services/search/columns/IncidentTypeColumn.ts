import { BaseColumn } from '../BaseColumn';
import { type QueryBuilder } from '../../query/QueryBuilder.server';

export class IncidentTypeColumn extends BaseColumn {
	key = 'incident_type';
	label = 'Incident Type';
	sortable = false; // Sorting complex aggregated data can be problematic

	beforeQuery(builder: QueryBuilder): void {
		// Add join to incident_types table and taxonomy tables
		builder.join('gva_data.incident_types', 'inc_t.incident_id = i.incident_id', 'left', 'inc_t');
		builder.join('gva_data.taxonomy', 't_type.tid = inc_t.type_tid', 'left', 't_type');
	}

	addToQuery(builder: QueryBuilder): void {
		builder.selectRaw("string_agg(DISTINCT t_type.value, ', ') AS incident_type", 'incident_type');
		builder.groupBy('i.incident_id');
	}

	getCssClasses(): string[] {
		return ['incident-type-column'];
	}

	// Add a method to get a CSS class based on the incident type
	getTypeClass(value: string): string {
		const classMap: Record<string, string> = {
			'Mass Shooting': 'bg-red-100 text-red-800',
			'Domestic Violence': 'bg-purple-100 text-purple-800',
			'Officer Involved': 'bg-blue-100 text-blue-800',
			'Defensive Use': 'bg-green-100 text-green-800',
			Unintentional: 'bg-yellow-100 text-yellow-800',
			Suicide: 'bg-gray-100 text-gray-800',
			Other: 'bg-gray-100 text-gray-800'
		};

		return classMap[value] || '';
	}

	beforeRender(data: any): any {
		// Ensure we have string values
		data.incident_type = data.incident_type || '';
		return data;
	}

	// Render method to include a badge with the incident type
	render(value: string, row: any): any {
		let html = '';
		value.incident_type.split(', ').forEach(type => {
			const label = this.getLabel(type);
			const typeClass = this.getTypeClass(type);
			html += `<span class="px-2 py-1 inline-block rounded-full text-xs font-medium ${typeClass}">${label}</span>`;
		});

		return {
			__html: html
		};
	}

	// Helper method to get the label for an incident type
	private getLabel(value: string): string {
		const typeMap: Record<string, string> = {
			mass_shooting: 'Mass Shooting',
			domestic_violence: 'Domestic Violence',
			officer_involved: 'Officer Involved',
			defensive_use: 'Defensive Use',
			unintentional: 'Unintentional',
			suicide: 'Suicide',
			other: 'Other'
		};

		return typeMap[value] || value;
	}
}
