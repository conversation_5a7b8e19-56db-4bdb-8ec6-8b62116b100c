import { BaseColumn } from '../BaseColumn';
import { type QueryBuilder } from '../../query/QueryBuilder.server';

export class VictimsColumn extends BaseColumn {
	key = 'victims_total';
	label = 'Total Victims';
	sortable = false; // Sorting complex aggregated data can be problematic

	beforeQuery(builder: QueryBuilder): void {
		// Add join to incident_participants table
		builder.join('gva_data.incident_participants', 'ip.incident_id = i.incident_id', 'left', 'ip');
	}

	addToQuery(builder: QueryBuilder): void {
		// builder.select(['i.victims_killed', 'i.victims_injured']);
		// builder.selectRaw('(i.victims_killed + i.victims_injured) as victims_total', 'victims_total');
		// todo: Use the actual taxonomy items from the database
		builder.selectRaw(
			"COUNT(DISTINCT CASE WHEN (ip.participant_type = 'victim' AND ',' || ip.participant_status_tid || ',' LIKE '%,98,%') THEN ip.participant_id END) AS victims_killed",
			'victims_killed'
		); // 98: Killed
		builder.selectRaw(
			"COUNT(DISTINCT CASE WHEN (ip.participant_type = 'victim' AND ',' || ip.participant_status_tid || ',' LIKE '%,99,%') THEN ip.participant_id END) AS victims_injured",
			'victims_injured'
		); // 99: Killed
		builder.selectRaw(
			"COUNT(DISTINCT CASE WHEN ip.participant_type = 'victim' AND (',' || ip.participant_status_tid || ',' LIKE '%,98,%' OR ',' || ip.participant_status_tid || ',' LIKE '%,99,%') THEN ip.participant_id END) AS victims_total",
			'victims_total'
		); // Killed + Injured
		builder.groupBy('i.incident_id');
	}

	beforeRender(data: any): any {
		// Create a new object with the required properties from the data
		return {
			victims_killed: parseInt(data.victims_killed || '0', 10),
			victims_injured: parseInt(data.victims_injured || '0', 10),
			victims_total: parseInt(data.victims_total || '0', 10)
		};
	}

	render(value: number, row: any): string {
		return `${value.victims_total} (${value.victims_killed} killed, ${value.victims_injured} injured)`;
	}

	getCssClasses(): string[] {
		return ['victims-column', 'text-center', 'font-medium'];
	}
}
