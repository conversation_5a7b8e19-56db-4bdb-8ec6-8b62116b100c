import { BaseColumn } from '../BaseColumn';
import { type QueryBuilder } from '../../query/QueryBuilder.server';

export class GunTypeColumn extends BaseColumn {
	key = 'gun_type';
	label = 'Gun Type';
	sortable = true;

	beforeQuery(builder: QueryBuilder): void {
		// Add join to incident_guns and taxonomy tables
		builder.join('gva_data.incident_guns', 'ig.incident_id = i.incident_id', 'left', 'ig');
		builder.join('gva_data.taxonomy', 't_gun.tid = ig.gun_type_tid', 'left', 't_gun');
	}

	addToQuery(builder: QueryBuilder): void {
		builder.selectRaw("string_agg(DISTINCT t_gun.value, ', ') AS gun_type", 'gun_type');
		builder.groupBy('i.incident_id');
	}

	beforeRender(data: any): any {
		// Ensure we have string values
		data.gun_type = data.gun_type || '';
		return data;
	}

	render(value: string, row: any): string {
		return value.gun_type;
	}

	getCssClasses(): string[] {
		return ['gun-type-column'];
	}
}
