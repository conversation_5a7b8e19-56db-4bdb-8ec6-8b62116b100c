import { BaseColumn } from '../BaseColumn';
import type { QueryBuilder } from '../../query/QueryBuilder.server';

export class NotesColumn extends BaseColumn {
	key = 'notes';
	label = 'Notes';
	sortable = true;

	addToQuery(builder: QueryBuilder): void {
		builder.select(['i.notes']);
	}

	beforeRender(data: any): any {
		// Ensure we have string values
		data.notes = data.notes || '';
		return data;
	}

	render(value: string, row: any): string {
		// Truncate long notes for display
		if (value.notes.length > 100) {
			return value.notes.substring(0, 100) + '...';
		}
		return value.notes;
	}

	getCssClasses(): string[] {
		return ['notes-column'];
	}
}
