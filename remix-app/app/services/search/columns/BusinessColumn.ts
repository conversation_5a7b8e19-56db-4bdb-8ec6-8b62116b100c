import { BaseColumn } from '../BaseColumn';
import type { QueryBuilder } from '../../query/QueryBuilder.server';

export class BusinessColumn extends BaseColumn {
	key = 'business';
	label = 'Business';
	sortable = true;

	addToQuery(builder: QueryBuilder): void {
		builder.select(['i.business']);
	}

	beforeRender(data: any): any {
		// Create a new object with the required properties from the data
		return {
			business: data.business || ''
		};
	}

	render(value: string, row: any): string {
		return value.business;
	}

	getCssClasses(): string[] {
		return ['business-column'];
	}
}
