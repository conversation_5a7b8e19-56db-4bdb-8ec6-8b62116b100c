import type { BaseVisualization } from './BaseVisualization';
import type { VisualizationConstructor } from './types';
import { ISearchVisualization } from './types';

/**
 * Registry for search visualizations
 * Provides a central place to register and retrieve visualizations
 */
export class VisualizationRegistry {
	private static instance: VisualizationRegistry;
	private visualizations: Map<string, BaseVisualization> = new Map();

	private constructor() {
		// Private constructor to enforce singleton pattern
	}

	/**
	 * Get the singleton instance of the registry
	 */
	public static getInstance(): VisualizationRegistry {
		if (!VisualizationRegistry.instance) {
			VisualizationRegistry.instance = new VisualizationRegistry();
		}
		return VisualizationRegistry.instance;
	}

	/**
	 * Register a visualization with the registry
	 * @param visualization The visualization to register
	 */
	public register(visualization: BaseVisualization): void {
		this.visualizations.set(visualization.key, visualization);
	}

	/**
	 * Register a visualization class with the registry
	 * @param visualizationClass The visualization class to register
	 */
	public registerClass(visualizationClass: VisualizationConstructor): void {
		const visualization = new visualizationClass();
		this.register(visualization);
	}

	/**
	 * Get a visualization by key
	 * @param key The key of the visualization to retrieve
	 * @returns The visualization, or undefined if not found
	 */
	public get(key: string): BaseVisualization | undefined {
		return this.visualizations.get(key);
	}

	/**
	 * Get all registered visualizations
	 * @returns An array of all registered visualizations
	 */
	public getAll(): BaseVisualization[] {
		return Array.from(this.visualizations.values());
	}

	/**
	 * Get all registered visualizations as a map
	 * @returns A map of all registered visualizations, keyed by key
	 */
	public getAllAsMap(): Map<string, BaseVisualization> {
		return new Map(this.visualizations);
	}

	/**
	 * Get all visualizations of a specific type
	 * @param type The type of visualizations to retrieve
	 * @returns An array of visualizations of the specified type
	 */
	public getByType(type: string): BaseVisualization[] {
		return Array.from(this.visualizations.values()).filter(visualization => visualization.type === type);
	}

	/**
	 * Clear all registered visualizations
	 */
	public clear(): void {
		this.visualizations.clear();
	}
}
