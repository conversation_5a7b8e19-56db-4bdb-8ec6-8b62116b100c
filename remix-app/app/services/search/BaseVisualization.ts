import type { ISearchVisualization, SearchPermission, VisualizationType } from './types';

export abstract class BaseVisualization implements ISearchVisualization {
	abstract key: string;
	abstract label: string;
	abstract type: VisualizationType;
	description?: string;
	permissions: SearchPermission[];

	constructor(permissions: SearchPermission[] = [{ roles: ['user', 'admin'] }]) {
		this.permissions = permissions;
	}

	/**
	 * Get configuration options for the visualization
	 * Override this method in specific visualization implementations
	 */
	abstract getConfigOptions(): any;

	/**
	 * Prepare data for visualization
	 * Override this method in specific visualization implementations
	 */
	abstract prepareData(data: any[]): any;

	/**
	 * Render the visualization
	 * Override this method in specific visualization implementations
	 */
	abstract render(data: any, config: any): JSX.Element;

	/**
	 * Check if the user has permission to use this visualization
	 */
	hasPermission(userRoles: string[], userPermissions: string[] = []): boolean {
		return this.permissions.some(permission => {
			// Check if user has any of the required roles
			const hasRole = permission.roles.some(role => userRoles.includes(role));

			// If no specific permissions are required, role check is sufficient
			if (!permission.permissions || permission.permissions.length === 0) {
				return hasRole;
			}

			// If specific permissions are required, check if user has any of them
			const hasPermission = permission.permissions.some(perm => userPermissions.includes(perm));

			// User must have both a required role AND a required permission (if specified)
			return hasRole && hasPermission;
		});
	}
}
