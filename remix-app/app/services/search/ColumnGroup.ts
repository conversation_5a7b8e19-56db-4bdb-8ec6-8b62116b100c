import type { ISearchColumn, SearchPermission } from './types';
import type { QueryBuilder } from '../query/QueryBuilder.server';
import { BaseColumn } from './BaseColumn';

/**
 * A group of columns that are processed together during rendering.
 * This allows multiple columns to be treated as a single unit for rendering purposes.
 */
export class ColumnGroup implements ISearchColumn {
	key: string;
	label: string;
	sortable: boolean = false;
	permissions: SearchPermission[] = [{ roles: ['user', 'admin'] }];

	private columns: ISearchColumn[] = [];

	/**
	 * Create a new column group
	 * @param key The unique key for this column group
	 * @param label The display label for this column group
	 * @param columns The columns to include in this group
	 */
	constructor(key: string, label: string, columns: ISearchColumn[] = []) {
		this.key = key;
		this.label = label;
		this.columns = columns;
	}

	/**
	 * Add a column to this group
	 * @param column The column to add
	 */
	addColumn(column: ISearchColumn): void {
		this.columns.push(column);
	}

	/**
	 * Get the columns in this group
	 */
	getColumns(): ISearchColumn[] {
		return [...this.columns];
	}

	/**
	 * Called before the query is executed
	 * Calls beforeQuery on all columns in the group
	 */
	beforeQuery(builder: QueryBuilder): void {
		for (const column of this.columns) {
			column.beforeQuery?.(builder);
		}
	}

	/**
	 * Add all columns in the group to the query
	 */
	addToQuery(builder: QueryBuilder): void {
		for (const column of this.columns) {
			column.addToQuery(builder);
		}
	}

	/**
	 * Called after the query is executed
	 * Calls afterQuery on all columns in the group
	 */
	afterQuery(builder: QueryBuilder): void {
		for (const column of this.columns) {
			column.afterQuery?.(builder);
		}
	}

	/**
	 * Process the data before rendering
	 * Calls beforeRender on all columns in the group and collects the results
	 */
	beforeRender(data: any): any {
		const result: any = {};

		// Process each column's data
		for (const column of this.columns) {
			// Extract data relevant to this column
			const columnData: any = {};

			// Copy all fields that might be relevant to this column
			Object.keys(data).forEach(key => {
				if (key === column.key || key.startsWith(column.key + '_')) {
					columnData[key] = data[key];
				}
			});

			// Process the data with the column's beforeRender method
			const processedData = column.beforeRender ? column.beforeRender(columnData) : columnData[column.key];

			// Store the processed data in the result
			result[column.key] = processedData;
		}

		return result;
	}

	/**
	 * Render the data
	 * By default, this calls render on all columns in the group and collects the results
	 * Override this method to customize how the group is rendered
	 */
	render(value: any, row: any): any {
		const result: any = {};

		// Render each column
		for (const column of this.columns) {
			const columnValue = value[column.key];
			const columnRow = { [column.key]: columnValue };
			result[column.key] = column.render(columnValue, columnRow);
		}

		return result;
	}

	/**
	 * Process the rendered data
	 * By default, this calls afterRender on all columns in the group and collects the results
	 * Override this method to customize how the group's rendered data is processed
	 */
	afterRender(output: any): any {
		const result: any = {};

		// Process each column's rendered output
		for (const column of this.columns) {
			const columnOutput = output[column.key];
			result[column.key] = column.afterRender ? column.afterRender(columnOutput) : columnOutput;
		}

		return result;
	}

	/**
	 * Get CSS classes for this column group
	 */
	getCssClasses(): string[] {
		// Collect CSS classes from all columns in the group
		const classes: string[] = [];
		for (const column of this.columns) {
			const columnClasses = column.getCssClasses?.() || [];
			classes.push(...columnClasses);
		}
		return classes;
	}

	/**
	 * Check if the user has permission to use this column group
	 * A user has permission if they have permission to use all columns in the group
	 */
	hasPermission(userRoles: string[], userPermissions: string[] = []): boolean {
		// A user has permission to use the group if they have permission to use all columns in the group
		return this.columns.every(column => column.hasPermission?.(userRoles, userPermissions) ?? true);
	}
}
