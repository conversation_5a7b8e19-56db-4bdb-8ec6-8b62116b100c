import type { ISearchColumn, SearchPermission } from './types';
import type { QueryBuilder } from '../query/QueryBuilder.server';

export abstract class BaseColumn implements ISearchColumn {
	abstract key: string;
	abstract label: string;
	sortable: boolean = false;
	permissions: SearchPermission[] = [{ roles: ['user', 'admin'] }]; // Default permissions

	beforeQuery?(builder: QueryBuilder): void {
		// Default implementation does nothing
	}

	abstract addToQuery(builder: QueryBuilder): void;

	afterQuery?(builder: QueryBuilder): void {
		// Default implementation does nothing
	}

	beforeRender?(data: any): any {
		// Default implementation returns the data as is
		// Columns with multiple fields should override this to extract and format the data
		return data;
	}

	render(value: any, row: any): any {
		return value;
	}

	afterRender?(output: any): any {
		return output;
	}

	getCssClasses(): string[] {
		return [];
	}

	hasPermission(userRoles: string[], userPermissions: string[] = []): boolean {
		return this.permissions.some(permission => {
			// Check if user has any of the required roles
			const hasRole = permission.roles.some(role => userRoles.includes(role));

			// If no specific permissions are required, role check is sufficient
			if (!permission.permissions || permission.permissions.length === 0) {
				return hasRole;
			}

			// If specific permissions are required, check if user has any of them
			const hasPermission = permission.permissions.some(perm => userPermissions.includes(perm));

			// User must have both a required role AND a required permission (if specified)
			return hasRole && hasPermission;
		});
	}
}
