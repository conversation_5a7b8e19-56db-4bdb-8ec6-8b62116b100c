import { db } from '~/utils/db.server';
import type * as qTypes from '~/services/query/types';
import { getTaxonomyDataByVocabulary } from '~/models/taxonomy.server';
import type TollItem from '~/services/toll/TollItem.server';
import type RedisCache from '~/utils/RedisCache.server';
import { redisManager } from '~/utils/redis-connection.server';
import { AndGroup } from './FilterGroups/AndGroup.server';
import { OrGroup } from './FilterGroups/OrGroup.server';
import type { GroupFilterBase } from './FilterGroups/GroupFilterBase.server';

/*
TODO Add method that handles duplicate aliases by comparing each parameter of the matching alias object to the new object.
 Append _1 (+1 for each occurrence of the same alias) if there is a duplicate
 */

/**
 * The QueryBuilder class provides methods to programmatically build and manage SQL queries.
 * It supports adding fields, conditions, joins, and other components while ensuring alias uniqueness and preventing duplicates.
 */
export class QueryBuilder {
	private fields: Map<string, string> = new Map(); // field -> alias
	private joins: Map<string, qTypes.QueryJoin> = new Map(); // joinKey -> join
	conditions: Map<string, qTypes.QueryCondition> = new Map(); // conditionKey -> condition
	private groupByFields: Set<string> = new Set();
	private havingConditions: Map<string, string> = new Map(); // condition -> params key
	private orderByFields: Map<string, qTypes.QueryOrderBy> = new Map(); // column -> orderBy
	private filteredAggregations: Map<string, qTypes.FilteredAggregation> = new Map(); // alias -> aggregation
	private limitValue?: number;
	private offsetValue?: number;
	private parameters: any[] = [];
	private paramCount: number = 1;
	private rawConditions: Map<string, { sql: string; params?: any[] }> = new Map();
	private distinctOn: Set<string> = new Set();
	table: string;
	tableAlias: string;
	private aliasCounter: Map<string, number> = new Map();
	private taxonomyData: any = {};
	private prisma: typeof db;
	private cache: RedisCache;
	private exportFormat?: string;

	constructor(table: string, alias: string, exportFormat?: string) {
		this.table = table;
		this.tableAlias = alias;
		this.prisma = db;
		this.cache = redisManager.getClient();
		this.exportFormat = exportFormat;
	}

	async addTollItem(toll: TollItem): Promise<any> {
		await toll.getQuery(this);
		return;
	}

	/**
	 * Generate a unique alias for a given base name
	 */
	private generateAlias(base: string): string {
		const count = (this.aliasCounter.get(base) || 0) + 1;
		this.aliasCounter.set(base, count);
		return count === 1 ? base : `${base}${count}`;
	}

	/**
	 * Create a unique key for a field
	 * @param field The field name
	 * @param preserveSpaces Whether to preserve spaces in the field key (default: false)
	 */
	private createFieldKey(field: string, preserveSpaces: boolean = false): string {
		return preserveSpaces ? field.toLowerCase() : field.toLowerCase().replace(/\s+/g, '');
	}

	/**
	 * Create a unique key for a join
	 */
	private createJoinKey(table: string, condition: string): string {
		return `${table}:${condition}`.toLowerCase();
	}

	/**
	 * Create a unique key for a condition or condition group
	 */
	private createConditionKey(
		input: qTypes.QueryCondition | qTypes.QueryConditionGroup,
		options: qTypes.ConditionKeyOptions = {}
	): string {
		const { includeBoolean = true, includePath = true, salt = '' } = options;

		if ('conditions' in input) {
			// Handle condition group
			const groupKey = input.conditions
				.map(cond => this.createConditionKey(cond, { ...options, includeBoolean: true }))
				.join('_');
			const booleanPart = includeBoolean ? `_${input.boolean}` : '';
			const notPart = input.not ? '_NOT' : '';
			return `(${groupKey})${booleanPart}${notPart}${salt}`.toLowerCase();
		} else {
			// Handle single condition
			const parts = [
				includePath ? input.column : input.column.split('.').pop(),
				input.operator,
				typeof input.value === 'object' ? JSON.stringify(input.value) : input.value
			];
			if (includeBoolean && input.boolean) {
				parts.push(input.boolean);
			}
			if (salt) {
				parts.push(salt);
			}
			return parts.join(':').toLowerCase();
		}
	}

	/**
	 * Add a condition group
	 */
	addConditionGroup(group: qTypes.QueryConditionGroup): boolean {
		const key = this.createConditionKey(group);

		if (this.conditions.has(key)) {
			return false;
		}

		const buildGroup = (group: qTypes.QueryConditionGroup, parentBoolean?: 'AND' | 'OR'): string[] => {
			const conditions: string[] = [];

			group.conditions.forEach((condition, index) => {
				if ('conditions' in condition) {
					// Nested group
					const nestedConditions = buildGroup(condition, group.boolean);
					const needsParens = nestedConditions.length > 1;
					const groupStr = needsParens
						? `(${nestedConditions.join(` ${condition.boolean} `)})`
						: nestedConditions[0];

					if (condition.not) {
						conditions.push(`NOT ${groupStr}`);
					} else {
						conditions.push(groupStr);
					}
				} else {
					// Single condition
					const op = this.buildOperator(condition.operator, condition.value);
					const placeholder = this.getNextParamPlaceholder();

					let conditionStr: string;
					if (condition.value === null && (condition.operator === 'equals' || condition.operator === 'not')) {
						conditionStr = `${condition.column} ${op}`;
					} else if (
						Array.isArray(condition.value) &&
						(condition.operator === 'in' || condition.operator === 'notIn')
					) {
						const placeholders = condition.value.map(() => this.getNextParamPlaceholder()).join(', ');
						conditionStr = `${condition.column} ${op} (${placeholders})`;
						this.parameters.push(...condition.value);
					} else {
						conditionStr = `${condition.column} ${op} ${placeholder}`;
						this.parameters.push(condition.value);
					}
					conditions.push(conditionStr);
				}

				// Add the boolean operator if it's not the last condition
				if (index < group.conditions.length - 1) {
					conditions.push(group.boolean);
				}
			});

			return conditions;
		};

		const conditionStrings = buildGroup(group);
		const finalCondition = conditionStrings.join(' ');

		// Store the condition group
		this.conditions.set(key, {
			column: `(${finalCondition})`,
			operator: 'equals',
			value: true,
			boolean: group.boolean
		});

		return true;
	}

	/**
	 * Add fields to select with duplicate checking
	 * Returns the alias if the field was already selected
	 */
	select(
		fields: string | string[],
		forceAlias: boolean = false
	): { [key: string]: string | null } & { parsedFields?: { [key: string]: { columnName: string; alias: string } } } {
		const fieldArray = Array.isArray(fields) ? fields : [fields];
		const results: { [key: string]: string | null } & {
			parsedFields?: { [key: string]: { columnName: string; alias: string } };
		} = {};

		fieldArray.forEach(field => {
			// Check if field contains AS and split into columnName and alias
			let columnName = field;
			let explicitAlias: string | null = null;

			if (field.includes(' AS ')) {
				const parts = field.split(/ AS /i);
				if (parts.length >= 2) {
					columnName = parts[0].trim();
					explicitAlias = parts[1].trim();
				}
			}

			const fieldKey = this.createFieldKey(columnName);
			const existingAlias = this.fields.get(fieldKey);

			if (existingAlias) {
				// Field already exists, return existing alias
				results[fieldKey] = existingAlias;
			} else {
				// New field
				let alias: string | null = null;
				if (forceAlias || field.includes('(') || field.includes(' AS ')) {
					// Use the explicit alias if available, otherwise generate one
					alias = explicitAlias || this.generateAlias(field.split(/[\s.]+/).pop()!);
					this.fields.set(columnName, alias);
				} else {
					this.fields.set(columnName, fieldKey);
				}
				results[fieldKey] = alias;
			}
		});

		return results;
	}

	/**
	 * Add raw expressions to select with duplicate checking and support for expression names
	 * Returns the alias if the field was already selected
	 */
	selectRaw(
		field: string,
		expName: string,
		forceAlias: boolean = false
	): { [key: string]: string | null } & { parsedFields?: { [key: string]: { columnName: string; alias: string } } } {
		const results: { [key: string]: string | null } & {
			parsedFields?: { [key: string]: { columnName: string; alias: string } };
		} = {};
		const expression = `${expName}(${field})`;

		const fieldKey = this.createFieldKey(field);
		const existingAlias = this.fields.get(fieldKey);

		if (existingAlias) {
			// Field already exists, return existing alias
			results[field] = existingAlias;
		} else {
			// New field
			let alias: string | null = null;

			// Check if field contains AS and split into columnName and alias
			let columnName = field;
			let explicitAlias: string | null = null;

			if (field.includes(' AS ')) {
				const parts = field.split(/ AS /i);
				if (parts.length >= 2) {
					columnName = parts[0].trim();
					explicitAlias = parts[1].trim();
				}
			}

			if (forceAlias || field.includes('(') || field.includes(' AS ')) {
				// Use the explicit alias if available, otherwise generate one
				alias = explicitAlias || this.generateAlias(field.split(/[\s.]+/).pop()!);
				// Fixed by yanh on 2025-06-13
				// this.fields.set(fieldKey, alias);
				this.fields.set(columnName, alias);
			} else {
				// Fixed by yanh on 2025-06-13
				// this.fields.set(fieldKey, field);
				this.fields.set(columnName, fieldKey);
			}
			results[field] = alias;

			// Store the column name and alias for later use if AS was present
			if (explicitAlias) {
				if (!results.parsedFields) {
					results.parsedFields = {};
				}
				results.parsedFields[field] = {
					columnName: columnName,
					alias: explicitAlias
				};
			}
		}

		return results;
	}

	/**
	 * Add a COUNT expression to the select fields
	 * @param field
	 * @param alias
	 * @param distinct Whether to use COUNT(DISTINCT field)
	 * @returns The alias used for the count field
	 */
	selectCount(field: string = '*', alias?: string, distinct: boolean = false): string {
		if (distinct && field === '*') {
			throw new Error('Cannot use COUNT(DISTINCT *)');
		}
		if (distinct) {
			field = `(${field})`;
		}

		const countExpression = `COUNT(${distinct ? 'DISTINCT ' : ''}${field})`;
		const finalAlias = alias || this.generateAlias('count');

		const fieldKey = this.createFieldKey(countExpression, true);

		const existingAlias = this.fields.get(fieldKey);
		if (existingAlias) {
			return existingAlias;
		} else {
			this.fields.set(fieldKey, finalAlias);
		}

		return finalAlias;
	}

	/**
	 * Add DISTINCT ON fields with duplicate checking
	 */
	addDistinctOn(fields: string | string[]): this {
		const fieldArray = Array.isArray(fields) ? fields : [fields];
		fieldArray.forEach(field => this.distinctOn.add(field));
		return this;
	}

	/**
	 * Add a JOIN clause with duplicate checking
	 * Returns the alias of the joined table
	 */
	join(
		table: string,
		condition: string,
		type: qTypes.QueryJoinTypes = 'inner',
		alias?: string,
		params?: any[]
	): string {
		const joinKey = this.createJoinKey(table, condition);
		const existingJoin = this.joins.get(joinKey);
		const baseCheck = this.tableAlias === alias; //Check the alias against the base query table alias to prevent conflicts

		if (existingJoin) {
			return existingJoin.alias || table;
		} else if (baseCheck) {
			return this.tableAlias;
		}

		const finalAlias = alias || this.generateAlias(table);
		const join: qTypes.QueryJoin = {
			type,
			table,
			condition,
			alias: finalAlias,
			params
		};

		this.joins.set(joinKey, join);
		if (params) {
			this.parameters.push(...params);
		}

		return finalAlias;
	}

	/**
	 * Given the name of a taxonomy term and the vocabulary, gets the TID associated
	 * with it.
	 *
	 *
	 * @return string
	 * @param vocabulary
	 * @param name
	 * @param ret
	 */
	async getTaxonomyItem(vocabulary: string, name: string, ret: string = 'string'): Promise<string> {
		if (!this.taxonomyData[vocabulary]) {
			this.taxonomyData[vocabulary] = await getTaxonomyDataByVocabulary(vocabulary);
		}

		const rt = this.taxonomyData[vocabulary].find(function (o: { value: string }) {
			return o.value.toLowerCase() === name.toLowerCase();
		})?.tid;

		if (ret === 'string') {
			return rt.toString();
		} else {
			return rt;
		}
	}

	/**
	 * Process and sanitize a value for SQL query
	 */
	private processValue(
		value: any,
		operator: qTypes.QueryOperators
	): {
		value: any;
		needsParameter: boolean;
		isArray: boolean;
	} {
		// Handle null/undefined
		if (value === null || value === undefined) {
			return {
				value: null,
				needsParameter: false,
				isArray: false
			};
		}

		// Handle arrays for IN/NOT IN operators
		if (Array.isArray(value)) {
			// Filter out null/undefined and process each value
			const validValues = value.filter(v => v !== null && v !== undefined).map(v => this.sanitizeValue(v));

			return {
				value: validValues,
				needsParameter: true,
				isArray: true
			};
		}

		// Handle Date objects
		if (value instanceof Date) {
			return {
				value: value.toISOString(),
				needsParameter: true,
				isArray: false
			};
		}

		// Handle boolean values
		if (typeof value === 'boolean') {
			return {
				value: value,
				needsParameter: true,
				isArray: false
			};
		}

		// Handle numbers
		if (typeof value === 'number') {
			if (!Number.isFinite(value)) {
				throw new Error(`Invalid number value: ${value}`);
			}
			return {
				value: value,
				needsParameter: true,
				isArray: false
			};
		}

		// Handle strings
		if (typeof value === 'string') {
			// For LIKE/ILIKE operators, handle wildcards
			if (operator === 'contains' || operator === 'startsWith' || operator === 'endsWith') {
				let processedValue = value;
				if (operator === 'contains') {
					processedValue = `%${value}%`;
				} else if (operator === 'startsWith') {
					processedValue = `${value}%`;
				} else if (operator === 'endsWith') {
					processedValue = `%${value}`;
				}
				return {
					value: processedValue,
					needsParameter: true,
					isArray: false
				};
			}

			return {
				value: value,
				needsParameter: true,
				isArray: false
			};
		}

		// Handle geography type
		if (value && typeof value === 'object' && value.type === 'geography') {
			return {
				value: value.value,
				needsParameter: true,
				isArray: false
			};
		}

		// Handle objects (convert to string)
		if (typeof value === 'object') {
			return {
				value: JSON.stringify(value),
				needsParameter: true,
				isArray: false
			};
		}

		throw new Error(`Unsupported value type: ${typeof value}`);
	}

	/**
	 * Sanitize a single value
	 */
	private sanitizeValue(value: any): any {
		if (value === null || value === undefined) {
			return null;
		}

		if (value instanceof Date) {
			return value.toISOString();
		}

		if (typeof value === 'boolean' || typeof value === 'number') {
			return value;
		}

		if (typeof value === 'string') {
			// Remove any potentially harmful characters
			return value.replace(/[\0\x08\x09\x1a\n\r"'\\\%]/g, function (char) {
				switch (char) {
					case '\0':
						return '\\0';
					case '\x08':
						return '\\b';
					case '\x09':
						return '\\t';
					case '\x1a':
						return '\\z';
					case '\n':
						return '\\n';
					case '\r':
						return '\\r';
					case '"':
					case "'":
					case '\\':
					case '%':
						return '\\' + char; // prepends a backslash to backslash, percent, and double/single quotes
				}
				return char;
			});
		}

		// Handle geography type
		if (value && typeof value === 'object' && value.type === 'geography') {
			return value.value;
		}

		if (typeof value === 'object') {
			return JSON.stringify(value);
		}

		return String(value);
	}

	/**
	 * Add a WHERE condition with proper value handling
	 * Overloaded to accept either a condition or a callback function
	 */
	where(
		columnOrCallback: string | ((qb: QueryBuilder) => void),
		operator?: qTypes.QueryOperators,
		value?: any,
		boolean: 'AND' | 'OR' = 'AND'
	): boolean | this {
		// Handle callback version
		if (typeof columnOrCallback === 'function') {
			// Create a new query builder for the subquery
			const subQueryBuilder = new QueryBuilder(this.table, this.tableAlias);

			// Execute the callback with the subquery builder
			columnOrCallback(subQueryBuilder);

			// Get the SQL from the subquery
			const { text, params } = subQueryBuilder.toSQL();

			// Extract the WHERE clause from the SQL
			const whereClauseMatch = text.match(/WHERE\s+(.+?)(?:\s+(?:GROUP BY|ORDER BY|LIMIT|$))/i);
			if (whereClauseMatch && whereClauseMatch[1]) {
				// Add the WHERE clause as a raw condition
				this.whereRaw(`(${whereClauseMatch[1]})`, params);
			}

			return this;
		}

		// Handle regular version
		const processedValue = this.processValue(value!, operator!);
		const conditionKey = this.createConditionKey(
			{ column: columnOrCallback, operator: operator!, value: processedValue.value, boolean },
			{ salt: String(this.paramCount) }
		);

		if (this.conditions.has(conditionKey)) {
			return false;
		}

		this.conditions.set(conditionKey, {
			column: columnOrCallback,
			operator: operator!,
			value: processedValue.value,
			boolean,
			// @ts-ignore
			needsParameter: processedValue.needsParameter,
			isArray: processedValue.isArray
		});

		return true;
	}

	/**
	 * Add an OR WHERE condition with proper value handling
	 * Overloaded to accept either a condition or a callback function
	 */
	orWhere(
		columnOrCallback: string | ((qb: QueryBuilder) => void),
		operator?: qTypes.QueryOperators,
		value?: any
	): boolean | this {
		if (typeof columnOrCallback === 'function') {
			// Create a new query builder for the subquery
			const subQueryBuilder = new QueryBuilder(this.table, this.tableAlias);

			// Execute the callback with the subquery builder
			columnOrCallback(subQueryBuilder);

			// Get the SQL from the subquery
			const { text, params } = subQueryBuilder.toSQL();

			// Extract the WHERE clause from the SQL
			const whereClauseMatch = text.match(/WHERE\s+(.+?)(?:\s+(?:GROUP BY|ORDER BY|LIMIT|$))/i);
			if (whereClauseMatch && whereClauseMatch[1]) {
				// Add the WHERE clause as a raw condition with OR
				this.whereRaw(`(${whereClauseMatch[1]})`, params);

				// Mark the last condition as OR
				const lastKey = Array.from(this.conditions.keys()).pop();
				if (lastKey) {
					const condition = this.conditions.get(lastKey);
					if (condition) {
						condition.boolean = 'OR';
						this.conditions.set(lastKey, condition);
					}
				}
			}

			return this;
		}

		// Call the regular where method with OR boolean
		return this.where(columnOrCallback, operator!, value!, 'OR');
	}

	/**
	 * Build the operator part of a condition with proper value handling
	 */
	private buildOperator(operator: qTypes.QueryOperators, value: any): string {
		switch (operator) {
			case 'equals':
				return value === null ? 'IS NULL' : '=';
			case 'not':
				return value === null ? 'IS NOT NULL' : '!=';
			case 'in':
				return 'IN';
			case 'notIn':
				return 'NOT IN';
			case 'lt':
				return '<';
			case 'lte':
				return '<=';
			case 'gt':
				return '>';
			case 'gte':
				return '>=';
			case 'contains':
			case 'startsWith':
			case 'endsWith':
				return 'LIKE';
			case 'search':
				return '@@';
			case 'dwithin':
				return 'ST_DWithin';
			default:
				throw new Error(`Unsupported operator: ${operator}`);
		}
	}

	/**
	 * Build a WHERE condition with proper value handling
	 */
	private buildWhereCondition(
		condition: qTypes.QueryCondition & {
			needsParameter?: boolean;
			isArray?: boolean;
		}
	): string {
		const operator = this.buildOperator(condition.operator, condition.value);

		if (!condition.needsParameter) {
			return `${condition.column} ${operator}`;
		}

		if (condition.isArray) {
			const placeholders = (condition.value as any[]).map(() => this.getNextParamPlaceholder()).join(', ');
			this.parameters.push(...condition.value);
			return `${condition.column} ${operator} (${placeholders})`;
		}

		// Special handling for geography operators
		if (condition.operator === 'dwithin') {
			// For ST_DWithin, we need to handle the distance parameter separately
			// The value should be an object with point and distance properties
			if (
				typeof condition.value === 'object' &&
				condition.value !== null &&
				'point' in condition.value &&
				'distance' in condition.value
			) {
				const pointPlaceholder = this.getNextParamPlaceholder();
				const distancePlaceholder = this.getNextParamPlaceholder();
				this.parameters.push(condition.value.point, condition.value.distance);
				return `${operator}(${condition.column}, ${pointPlaceholder}, ${distancePlaceholder})`;
			} else {
				throw new Error(`Invalid value for dwithin operator: ${JSON.stringify(condition.value)}`);
			}
		}

		const placeholder = this.getNextParamPlaceholder();
		this.parameters.push(condition.value);
		return `${condition.column} ${operator} ${placeholder}`;
	}

	/**
	 * Add a raw WHERE condition with duplicate checking
	 */
	whereRaw(sql: string, params?: any[]): boolean {
		const key = `${sql}:${JSON.stringify(params)}`;

		if (this.rawConditions.has(key)) {
			return false;
		}

		this.rawConditions.set(key, { sql, params });
		if (params) {
			this.parameters.push(...params);
		}
		return true;
	}

	/**
	 * Add GROUP BY fields with duplicate checking
	 */
	groupBy(fields: string | string[]): this {
		const fieldArray = Array.isArray(fields) ? fields : [fields];
		fieldArray.forEach(field => this.groupByFields.add(field));
		return this;
	}

	/**
	 * Add a HAVING condition with duplicate checking
	 */
	having(condition: string, params?: any[]): boolean {
		const key = `${condition}:${JSON.stringify(params)}`;

		if (this.havingConditions.has(key)) {
			return false;
		}

		this.havingConditions.set(condition, key);
		if (params) {
			this.parameters.push(...params);
		}
		return true;
	}

	/**
	 * Add ORDER BY fields with duplicate checking
	 */
	orderBy(column: string, direction: 'ASC' | 'DESC' = 'ASC'): boolean {
		if (this.orderByFields.has(column)) {
			return false;
		}

		this.orderByFields.set(column, { column, direction });
		return true;
	}

	/**
	 * Set LIMIT
	 */
	limit(value: number): this {
		this.limitValue = value;
		return this;
	}

	/**
	 * Set OFFSET
	 */
	offset(value: number): this {
		this.offsetValue = value;
		return this;
	}

	/**
	 * Get the next parameter placeholder
	 */
	private getNextParamPlaceholder(): string {
		return `$${this.paramCount++}`;
	}

	/**
	 * Add a filtered aggregation to the SELECT clause
	 */
	addFilteredAggregation(aggregation: qTypes.FilteredAggregation): string {
		const existingAgg = this.filteredAggregations.get(aggregation.alias);
		if (existingAgg) {
			return existingAgg.alias;
		}

		this.filteredAggregations.set(aggregation.alias, aggregation);
		return aggregation.alias;
	}

	/**
	 * Helper method to build a filtered aggregation expression
	 */
	private buildFilteredAggregation(agg: qTypes.FilteredAggregation): string {
		const buildFilterConditions = (
			conditions: (qTypes.QueryCondition | qTypes.QueryConditionGroup)[],
			boolean: 'AND' | 'OR' = 'AND'
		): string[] => {
			return conditions.map((condition, index) => {
				if ('conditions' in condition) {
					// Handle condition group
					const nestedConditions = buildFilterConditions(condition.conditions, condition.boolean);
					const needsParens = nestedConditions.length > 1;
					const groupStr = needsParens
						? `(${nestedConditions.join(` ${condition.boolean} `)})`
						: nestedConditions[0];
					return condition.not ? `NOT ${groupStr}` : groupStr;
				} else {
					// Handle single condition
					const op = this.buildOperator(condition.operator, condition.value);

					if (condition.value === null && (condition.operator === 'equals' || condition.operator === 'not')) {
						return `${condition.column} ${op}`;
					} else if (
						Array.isArray(condition.value) &&
						(condition.operator === 'in' || condition.operator === 'notIn')
					) {
						const placeholders = condition.value.map(() => this.getNextParamPlaceholder()).join(', ');
						this.parameters.push(...condition.value);
						return `${condition.column} ${op} (${placeholders})`;
					} else {
						const placeholder = this.getNextParamPlaceholder();
						this.parameters.push(condition.value);
						return `${condition.column} ${op} ${placeholder}`;
					}
				}
			});
		};

		const filterConditions = buildFilterConditions(agg.filter, agg.filterBoolean);
		const filterClause =
			filterConditions.length > 0
				? `FILTER (WHERE ${filterConditions.join(` ${agg.filterBoolean || 'AND'} `)})`
				: '';

		let aggregateExpr = `${agg.function}(${agg.distinct ? 'DISTINCT ' : ''}${agg.field}`;

		// Handle special aggregate functions that need additional arguments
		if (agg.args && agg.args.length > 0) {
			if (agg.function === 'STRING_AGG') {
				const delimiter = this.getNextParamPlaceholder();
				this.parameters.push(agg.args[0]);
				aggregateExpr += `, ${delimiter}`;
			}
		}

		aggregateExpr += ')';

		// Add ORDER BY for array_agg if specified
		if (agg.function === 'ARRAY_AGG' && agg.orderBy) {
			aggregateExpr += ` WITHIN GROUP (ORDER BY ${agg.orderBy.column} ${agg.orderBy.direction})`;
		}

		return `${aggregateExpr} ${filterClause} AS ${agg.alias}`;
	}

	/**
	 * Build the complete SQL query with proper value handling
	 */
	toSQL(): { text: string; params: any[] } {
		const parts: string[] = ['SELECT'];

		// Add DISTINCT ON if specified
		if (this.distinctOn.size > 0) {
			parts.push(`DISTINCT ON (${Array.from(this.distinctOn).join(', ')})`);
		}

		// Combine regular fields and filtered aggregations
		const selectedFields = Array.from(this.fields.entries()).map(([field, alias]) =>
			alias === field ? field : `${field} AS ${alias}`
		);

		const aggregations = Array.from(this.filteredAggregations.values()).map(agg =>
			this.buildFilteredAggregation(agg)
		);

		const allFields = [...selectedFields, ...aggregations];
		parts.push(allFields.length > 0 ? allFields.join(', ') : '*');

		// Add FROM clause
		parts.push(`FROM ${this.table}${this.tableAlias ? ` AS ${this.tableAlias}` : ''}`);

		// Add JOINs
		if (this.joins.size > 0) {
			const joinClauses = Array.from(this.joins.values()).map(join => {
				const joinType = join.type.toUpperCase() + ' JOIN';
				const tableClause = join.alias ? `${join.table} AS ${join.alias}` : join.table;
				return `${joinType} ${tableClause} ON ${join.condition}`;
			});
			parts.push(joinClauses.join(' '));
		}

		// Add WHERE conditions
		const whereConditions: string[] = [];
		Array.from(this.conditions.values()).forEach((condition, index) => {
			const conditionStr = this.buildWhereCondition(condition);

			if (index === 0) {
				whereConditions.push(conditionStr);
			} else {
				whereConditions.push(`${condition.boolean} ${conditionStr}`);
			}
		});

		// Add raw conditions
		if (this.rawConditions.size > 0) {
			whereConditions.push(...Array.from(this.rawConditions.values()).map(c => c.sql));
		}

		if (whereConditions.length > 0) {
			parts.push('WHERE ' + whereConditions.join(' '));
		}

		// Add GROUP BY
		if (this.groupByFields.size > 0) {
			parts.push('GROUP BY ' + Array.from(this.groupByFields).join(', '));
		}

		// Add HAVING
		if (this.havingConditions.size > 0) {
			parts.push('HAVING ' + Array.from(this.havingConditions.keys()).join(' AND '));
		}

		// Add ORDER BY
		if (this.orderByFields.size > 0) {
			const orderClauses = Array.from(this.orderByFields.values())
				.map(({ column, direction }) => `${column} ${direction}`)
				.join(', ');
			parts.push('ORDER BY ' + orderClauses);
		}

		// Add LIMIT
		if (this.limitValue !== undefined) {
			parts.push(`LIMIT ${this.limitValue}`);
		}

		// Add OFFSET
		if (this.offsetValue !== undefined) {
			parts.push(`OFFSET ${this.offsetValue}`);
		}

		return {
			text: parts.join(' '),
			params: this.parameters
		};
	}

	/**
	 * Get all current aliases
	 */
	getAliases(): { [key: string]: string } {
		const aliases: { [key: string]: string } = {};

		// Table alias
		if (this.tableAlias) {
			aliases[this.table] = this.tableAlias;
		}

		// Join aliases
		this.joins.forEach(join => {
			if (join.alias) {
				aliases[join.table] = join.alias;
			}
		});

		// Field aliases
		this.fields.forEach((alias, field) => {
			if (alias !== field) {
				aliases[field] = alias;
			}
		});

		return aliases;
	}

	/**
	 * Set the export format for the query
	 * @param format The export format (json, csv, html, table, array, pdf)
	 */
	setExportFormat(format: string): this {
		this.exportFormat = format;
		return this;
	}

	/**
	 * Get the current export format
	 */
	getExportFormat(): string | undefined {
		return this.exportFormat;
	}

	/**
	 * Check if a field is already selected
	 * Checks both with and without spaces to handle fields added with different space preservation settings
	 */
	hasField(field: string): boolean {
		// Check with spaces preserved
		if (this.fields.has(this.createFieldKey(field, true))) {
			return true;
		}
		// Check with spaces removed (default behavior)
		return this.fields.has(this.createFieldKey(field, false));
	}

	/**
	 * Check if a join already exists
	 */
	hasJoin(table: string, condition: string): boolean {
		return this.joins.has(this.createJoinKey(table, condition));
	}

	/**
	 * Check if a condition already exists
	 */
	hasCondition(column: string, operator: qTypes.QueryOperators, value: any, boolean: 'AND' | 'OR' = 'AND'): boolean {
		const processedValue = this.processValue(value, operator);
		const conditionKey = this.createConditionKey(
			{ column, operator, value: processedValue.value, boolean },
			{ salt: String(this.paramCount) }
		);
		return this.conditions.has(conditionKey);
	}

	async fillSqlParameters(sql: string, params: any[]): Promise<string> {
		let index = 0;
		return sql.replace(/\$(\d+)/g, (match, number) => {
			const paramIndex = parseInt(number, 10) - 1;
			if (paramIndex < 0 || paramIndex >= params.length) {
				throw new Error(`Parameter index out of range: ${number}`);
			}
			const param = params[paramIndex];
			return typeof param === 'string' ? `'${param.replace(/'/g, "''")}'` : param;
		});
	}

	/**
	 * Execute the query using Prisma's $queryRawUnsafe method and return the result(s)
	 */
	async execute<T = any>(cache: boolean = false): Promise<T[]> {
		const { text, params } = this.toSQL();
		if (cache) {
			const cacheKeyCheck = [text, params];
			const cacheKey = this.cache.generateKey(cacheKeyCheck);

			return await this.cache.getOrCreate(
				cacheKey,
				14400,
				async () => {
					return this.prisma.$queryRawUnsafe(text, ...params);
				},
				{
					staleTime: 900, // 5 minutes stale time
					backgroundRefresh: true
				}
			);
		}

		return this.prisma.$queryRawUnsafe(text, ...params);
	}

	/**
	 * Execute the query and return a single row
	 */
	async executeSingle<T = any>(): Promise<T | null> {
		const results = await this.limit(1).execute<T>();
		return results.length > 0 ? results[0] : null;
	}

	/**
	 * Create an AND filter group
	 * @returns A new AND filter group
	 */
	createAndGroup(): AndGroup {
		return new AndGroup();
	}

	/**
	 * Create an OR filter group
	 * @returns A new OR filter group
	 */
	createOrGroup(): OrGroup {
		return new OrGroup();
	}

	/**
	 * Apply a filter group to the query
	 * @param group The filter group to apply
	 * @returns This instance for method chaining
	 */
	applyFilterGroup(group: GroupFilterBase): this {
		group.apply(this);
		return this;
	}

	/**
	 * Clone the current query builder
	 */
	clone(): QueryBuilder {
		const clone = new QueryBuilder(this.table, this.tableAlias, this.exportFormat);
		clone.fields = new Map(this.fields);
		clone.joins = new Map(this.joins);
		clone.conditions = new Map(this.conditions);
		clone.groupByFields = new Set(this.groupByFields);
		clone.havingConditions = new Map(this.havingConditions);
		clone.orderByFields = new Map(this.orderByFields);
		clone.limitValue = this.limitValue;
		clone.offsetValue = this.offsetValue;
		clone.parameters = [...this.parameters];
		clone.paramCount = this.paramCount;
		clone.rawConditions = new Map(this.rawConditions);
		clone.distinctOn = new Set(this.distinctOn);
		clone.aliasCounter = new Map(this.aliasCounter);
		return clone;
	}

	/**
	 * Cleanup method to release Redis connection
	 */
	async cleanup() {
		await redisManager.releaseClient();
	}
}
