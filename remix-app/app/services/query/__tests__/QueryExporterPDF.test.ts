import type { OutputOptions } from '../QueryExporter.server';
import { QueryExporter } from '../QueryExporter.server';
import { QueryBuilder } from '../QueryBuilder.server';

// Mock the QueryBuilder
jest.mock('../QueryBuilder.server', () => {
	return {
		QueryBuilder: jest.fn().mockImplementation(() => {
			return {
				select: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				execute: jest.fn().mockResolvedValue([
					{ id: 1, name: 'Item 1', value: 10 },
					{ id: 2, name: 'Item 2', value: 20 }
				]),
				clone: jest.fn().mockReturnThis(),
				offset: jest.fn().mockReturnThis(),
				limit: jest.fn().mockReturnThis()
			};
		})
	};
});

// Mock jsPDF
jest.mock('jspdf', () => {
	return {
		jsPDF: jest.fn().mockImplementation(() => {
			return {
				text: jest.fn(),
				setFontSize: jest.fn(),
				setPage: jest.fn(),
				internal: {
					getNumberOfPages: jest.fn().mockReturnValue(1),
					pageSize: {
						width: 210,
						height: 297
					}
				},
				output: jest.fn().mockReturnValue(new Uint8Array([1, 2, 3])),
				autoTable: jest.fn()
			};
		})
	};
});

// Mock jspdf-autotable
jest.mock('jspdf-autotable', () => {});

describe('QueryExporter PDF Export', () => {
	let queryBuilder: jest.Mocked<QueryBuilder>;
	let mockData: any[];

	beforeEach(() => {
		jest.clearAllMocks();
		queryBuilder = new QueryBuilder('incidents', 'i') as jest.Mocked<QueryBuilder>;
		mockData = [
			{ id: 1, name: 'Item 1', value: 10 },
			{ id: 2, name: 'Item 2', value: 20 }
		];
	});

	describe('execute with PDF format', () => {
		it('should return PDF data when format is pdf', async () => {
			const options: OutputOptions = {
				format: 'pdf'
			};

			const exporter = new QueryExporter(queryBuilder, options);
			const result = await exporter.execute();

			// Verify that the result is a Uint8Array (PDF data)
			expect(result).toBeInstanceOf(Uint8Array);
		});

		it('should use default PDF options when none are provided', async () => {
			const options: OutputOptions = {
				format: 'pdf'
			};

			const exporter = new QueryExporter(queryBuilder, options);
			await exporter.execute();

			// Verify that jsPDF was called with default options
			expect(require('jspdf').jsPDF).toHaveBeenCalledWith({
				orientation: 'portrait',
				format: 'a4'
			});
		});

		it('should use custom PDF options when provided', async () => {
			const options: OutputOptions = {
				format: 'pdf',
				pdfOptions: {
					orientation: 'landscape',
					pageSize: 'letter',
					title: 'Custom Title',
					subtitle: 'Custom Subtitle',
					includeTimestamp: false,
					includePageNumbers: false,
					tableStyles: {
						fontSize: 12,
						cellPadding: 5,
						headerFillColor: [100, 100, 100],
						headerTextColor: [255, 255, 255],
						alternateRowFillColor: [240, 240, 240],
						lineColor: [150, 150, 150],
						lineWidth: 0.2
					}
				}
			};

			const exporter = new QueryExporter(queryBuilder, options);
			await exporter.execute();

			// Verify that jsPDF was called with custom options
			expect(require('jspdf').jsPDF).toHaveBeenCalledWith({
				orientation: 'landscape',
				format: 'letter'
			});
		});

		it('should handle empty data', async () => {
			const options: OutputOptions = {
				format: 'pdf'
			};

			// Mock empty data
			queryBuilder.execute.mockResolvedValue([]);

			const exporter = new QueryExporter(queryBuilder, options);
			const result = await exporter.execute();

			// Verify that the result is a Uint8Array (PDF data)
			expect(result).toBeInstanceOf(Uint8Array);

			// Verify that 'No data available' text was added
			const jsPDFInstance = (require('jspdf').jsPDF as jest.Mock).mock.results[0].value;
			expect(jsPDFInstance.text).toHaveBeenCalledWith('No data available', 14, 20);
		});
	});

	describe('stream with PDF format', () => {
		it('should yield PDF data when format is pdf', async () => {
			const options: OutputOptions = {
				format: 'pdf'
			};

			const exporter = new QueryExporter(queryBuilder, options);
			const generator = exporter.stream(10);
			const result = await generator.next();

			// Verify that the yielded value is a Uint8Array (PDF data)
			expect(result.value).toBeInstanceOf(Uint8Array);
		});
	});
});
