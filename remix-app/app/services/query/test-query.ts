import { QueryBuilder } from './QueryBuilder.server';

/**
 * This is a test function to demonstrate the usage of the new selectRaw and selectFunction methods
 */
export async function testSelectFunction() {
	// Create a new query builder
	const builder = new QueryBuilder('incidents', 'i');

	// Test the selectRaw method
	const rawAlias = builder.selectRaw("CONCAT(i.city_or_county, ', ', i.state)", 'CONCAT', true);
	console.log('Raw alias:', rawAlias);

	// Test the selectRaw method with an explicit alias
	const rawAliasExplicit = builder.selectRaw("CONCAT(i.city_or_county, ', ', i.state) as location", 'CONCAT', true);
	console.log('Raw alias with explicit alias:', rawAliasExplicit);

	// Test using selectRaw instead of selectFunction (which doesn't exist)
	const funcAlias = builder.selectRaw("CONCAT(i.city_or_county, ', ', i.state)", 'function_result');
	console.log('Function alias:', funcAlias);

	// Test using selectRaw with an explicit alias
	const funcAliasExplicit = builder.selectRaw("CONCAT(i.city_or_county, ', ', i.state)", 'location');
	console.log('Function alias with explicit alias:', funcAliasExplicit);

	// Add some basic fields and conditions
	builder.select(['i.id', 'i.date']);
	builder.where('i.status', 'equals', 'active');
	builder.limit(10);

	// Generate the SQL
	const { text, params } = builder.toSQL();
	console.log('SQL:', text);
	console.log('Params:', params);

	// Execute the query
	const results = await builder.execute();
	console.log('Results:', results);
	return results;
}

/**
 * This is a test function to demonstrate the usage of the new callback-based where and orWhere methods
 */
export async function testQueryBuilder() {
	// Create a new query builder
	const builder = new QueryBuilder('incidents', 'i');

	// Test the callback-based where method
	builder.where(qb => {
		qb.where('i.status', 'equals', 'active');
		qb.where('i.created_at', 'gte', new Date('2023-01-01'));
	});

	// Test the callback-based orWhere method
	builder.orWhere(qb => {
		qb.where('i.type', 'equals', 'shooting');
		qb.where('i.type', 'equals', 'stabbing');
	});

	// Generate the SQL
	const { text, params } = builder.toSQL();
	console.log('SQL:', text);
	console.log('Params:', params);

	// Execute the query
	const results = await builder.execute();
	return results;
}

/**
 * This is a test function to demonstrate the usage of the new selectCount method
 */
export async function testSelectCount() {
	// Create a new query builder
	const builder = new QueryBuilder('incidents', 'i');

	// Test the selectCount method with default parameters (COUNT(*))
	const countAlias = builder.selectCount();
	console.log('Count alias:', countAlias);

	// Test the selectCount method with a specific field
	const victimCountAlias = builder.selectCount('i.victim_count', 'total_victims');
	console.log('Victim count alias:', victimCountAlias);

	// Test the selectCount method with DISTINCT
	const distinctTypeCountAlias = builder.selectCount('i.type', 'distinct_types', true);
	console.log('Distinct type count alias:', distinctTypeCountAlias);

	// Add some conditions
	builder.where('i.status', 'equals', 'active');

	// Generate the SQL
	const { text, params } = builder.toSQL();
	console.log('SQL:', text);
	console.log('Params:', params);

	// Execute the query
	const results = await builder.execute();
	console.log('Results:', results);
	return results;
}
