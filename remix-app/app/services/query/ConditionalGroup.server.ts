import { Prisma, PrismaClient } from '@prisma/client';
import { hasPermission } from '~/utils/client-utils';
import _ from 'lodash';
import type { QueryOperators, QueryGrouping } from '~/services/query/types';

export class ConditionalGroup {
	public grouping: QueryGrouping = 'AND';
	private filters: any = [];

	constructor(grouping: QueryGrouping = 'AND') {
		this.grouping = grouping;
	}

	/**
	 * Adds a filter condition.
	 * @param column The field name.
	 * @param table
	 * @param value The value to filter by.
	 * @param operator
	 */
	addFilter(column: string, table: string, value: any, operator: QueryOperators): this {
		this.filters.push({
			[table]: {
				[column]: {
					[operator]: value
				}
			}
		});

		return this;
	}

	/**
	 * Gets the filters for this filter group instance for inserting into query.
	 *
	 * @return any
	 */
	getFilters() {
		if (this.filters.length === 0) {
			return {};
		}

		return {
			[this.grouping]: [...this.filters]
		};
	}
}
