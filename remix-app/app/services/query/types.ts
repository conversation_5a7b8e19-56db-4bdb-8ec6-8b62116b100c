import { Prisma, PrismaClient } from '@prisma/client';

export type QueryType = 'incidents' | 'participants';
export type QueryGrouping = 'AND' | 'OR' | 'NOT';
export type QueryConditionalsGrouping = 'AND' | 'OR';
export type QueryOperators =
	| 'equals'
	| 'not'
	| 'in'
	| 'notIn'
	| 'lt'
	| 'lte'
	| 'gt'
	| 'gte'
	| 'contains'
	| 'startsWith'
	| 'endsWith'
	| 'search'
	| 'dwithin'
	| 'between'; // Added for geography distance queries

export type QueryFilterModes = 'insensitive';
export type QueryExecuteTypes = 'findMany' | 'findFirst' | 'findUnique' | 'count';
export type QueryJoinTypes = 'inner' | 'left' | 'right' | 'full';
export type QueryRelationTypes = 'is' | 'some' | 'every' | 'none' | 'isNot';

export type AggregateFunction =
	| 'COUNT'
	| 'SUM'
	| 'AVG'
	| 'MIN'
	| 'MAX'
	| 'ARRAY_AGG'
	| 'STRING_AGG'
	| 'BOOL_AND'
	| 'BOOL_OR';

export type QueryConditionGroup = {
	conditions: (QueryCondition | QueryConditionGroup)[];
	boolean: 'AND' | 'OR';
	not?: boolean;
};

export type ConditionKeyOptions = {
	includeBoolean?: boolean;
	includePath?: boolean;
	salt?: string;
};

export type FilteredAggregation = {
	function: AggregateFunction;
	field: string;
	filter: (QueryCondition | QueryConditionGroup)[];
	alias: string;
	distinct?: boolean;
	orderBy?: QueryOrderBy;
	filterBoolean?: 'AND' | 'OR';
	args?: any[];
};

export type QueryCondition = {
	column: string;
	operator: QueryOperators;
	value: any;
	boolean?: 'AND' | 'OR';
};

export type QueryRawCondition = {
	sql: string;
	params?: any[];
	boolean?: 'AND' | 'OR';
};

export type QueryJoin = {
	type: QueryJoinTypes;
	table: string;
	alias?: string;
	condition: string;
	params?: any[];
};

export type QueryOrderBy = {
	column: string;
	direction: 'ASC' | 'DESC';
};

export type SqlQuery = {
	text: string;
	params: any[];
};

export interface IQueryBuilder {
	select(fields: string | string[]): IQueryBuilder;
	selectRaw(expression: string): string;
	selectFunction(func: string, args: string, alias?: string): string;
	distinctOn(fields: string | string[]): IQueryBuilder;
	join(table: string, condition: string, type?: QueryJoinTypes, alias?: string, params?: any[]): IQueryBuilder;
	where(column: string, operator: QueryOperators, value: any, boolean?: 'AND' | 'OR'): IQueryBuilder;
	where(callback: (qb: IQueryBuilder) => void): IQueryBuilder;
	orWhere(column: string, operator: QueryOperators, value: any): IQueryBuilder;
	orWhere(callback: (qb: IQueryBuilder) => void): IQueryBuilder;
	whereRaw(sql: string, params?: any[]): IQueryBuilder;
	groupBy(fields: string | string[]): IQueryBuilder;
	having(condition: string, params?: any[]): IQueryBuilder;
	orderBy(column: string, direction?: 'ASC' | 'DESC'): IQueryBuilder;
	limit(value: number): IQueryBuilder;
	offset(value: number): IQueryBuilder;
	createAndGroup(): any; // AndGroup
	createOrGroup(): any; // OrGroup
	applyFilterGroup(group: any): IQueryBuilder; // GroupFilterBase
	toSQL(): SqlQuery;
	execute<T = any>(cache?: boolean): Promise<T[]>;
	executeSingle<T = any>(): Promise<T | null>;
	clone(): IQueryBuilder;
}
