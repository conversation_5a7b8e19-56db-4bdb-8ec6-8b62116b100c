import { QueryBuilder } from './QueryBuilder.server';
import { Parser } from '@json2csv/plainjs';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';

export type OutputFormat = 'json' | 'csv' | 'html' | 'table' | 'array' | 'pdf';

export type OutputOptions = {
	format: OutputFormat;
	filename?: string;
	headers?: boolean;
	customHeaders?: Record<string, string>;
	dateFormat?: string;
	numberFormat?: string;
	nullValue?: string;
	cssClasses?: {
		table?: string;
		header?: string;
		row?: string;
		cell?: string;
	};
	columnClasses?: Record<string, string[]>;
	transformers?: {
		[key: string]: (value: any) => any;
	};
	queryType?: 'toll' | 'incidents' | 'participants' | 'count' | 'stats';
	pdfOptions?: {
		title?: string;
		subtitle?: string;
		orientation?: 'portrait' | 'landscape';
		pageSize?: 'a4' | 'letter' | 'legal';
		includeTimestamp?: boolean;
		includePageNumbers?: boolean;
		tableStyles?: {
			fontSize?: number;
			cellPadding?: number;
			headerFillColor?: [number, number, number];
			headerTextColor?: number | [number, number, number];
			alternateRowFillColor?: [number, number, number];
			lineColor?: [number, number, number];
			lineWidth?: number;
		};
	};
};

export class QueryExporter {
	public query: QueryBuilder;
	private options: OutputOptions;

	constructor(query: QueryBuilder | null, options: OutputOptions) {
		options.queryType = options.queryType ?? 'incidents';
		this.query = query === null ? new QueryBuilder('incident_participants', 'ip', options.format) : query;

		// If query is provided, set its export format
		if (query !== null && options.format) {
			query.setExportFormat(options.format);
		}

		this.options = {
			headers: true,
			dateFormat: 'YYYY-MM-DD HH:mm:ss',
			numberFormat: '0,0.00',
			nullValue: '',
			...options
		};
	}

	/**
	 * Format a single value based on its type and options
	 */
	private formatValue(value: any, key: string): any {
		if (value === null || value === undefined) {
			return this.options.nullValue;
		}

		// Apply custom transformer if exists
		if (this.options.transformers?.[key]) {
			return this.options.transformers[key](value);
		}

		if (value instanceof Date) {
			return value.toLocaleDateString();
		}

		if (['incident_id', 'participant_id'].includes(key)) {
			return value;
		}

		// Fixed by yanh on 2025-06-16
		if (typeof value === 'number' || typeof value === 'bigint') {
			return new Intl.NumberFormat('en-US', {
				minimumFractionDigits: 2,
				maximumFractionDigits: 2
			}).format(value);
		}

		return value;
	}

	/**
	 * Convert query results to CSV
	 */
	private async toCSV(data: any[]): Promise<string> {
		if (data.length === 0) return '';

		const fields = Object.keys(data[0]);
		const opts = {
			fields,
			header: this.options.headers,
			quote: '"',
			delimiter: ','
		};

		const parser = new Parser(opts);
		return parser.parse(
			data.map(row =>
				Object.fromEntries(Object.entries(row).map(([key, value]) => [key, this.formatValue(value, key)]))
			)
		);
	}

	/**
	 * Convert query results to HTML table
	 */
	private async toHTML(data: any[]): Promise<string> {
		if (data.length === 0) return '<table></table>';

		const headers = Object.keys(data[0]);
		const { cssClasses, columnClasses } = this.options;

		const headerRow = this.options.headers
			? `<tr class="${cssClasses?.header || ''}">${headers
					.map(header => `<th>${this.options.customHeaders?.[header] || header}</th>`)
					.join('')}</tr>`
			: '';

		const rows = data
			.map(
				row =>
					`<tr class="${cssClasses?.row || ''}">${headers
						.map(header => {
							const columnClassList = columnClasses?.[header] || [];
							const cellClasses = [cssClasses?.cell || '', ...columnClassList].filter(Boolean).join(' ');
							return `<td class="${cellClasses}">${this.formatValue(row[header], header)}</td>`;
						})
						.join('')}</tr>`
			)
			.join('');

		return `<table class="${cssClasses?.table || ''}">${headerRow}${rows}</table>`;
	}

	/**
	 * Convert query results to PDF
	 */
	private async toPDF(data: any[]): Promise<Uint8Array> {
		if (data.length === 0) {
			// Return an empty PDF
			const pdf = new jsPDF({
				orientation: this.options.pdfOptions?.orientation || 'portrait',
				format: this.options.pdfOptions?.pageSize || 'a4'
			});
			pdf.text('No data available', 14, 20);
			// Convert ArrayBuffer to Uint8Array
			const arrayBuffer = pdf.output('arraybuffer');
			return new Uint8Array(arrayBuffer);
		}

		const headers = Object.keys(data[0]);
		const headerLabels = headers.map(header => this.options.customHeaders?.[header] || header);

		// Format the data for jspdf-autotable
		const tableData = data.map(row => headers.map(header => this.formatValue(row[header], header)));

		// Create PDF document with options
		const pdf = new jsPDF({
			orientation: this.options.pdfOptions?.orientation || 'portrait',
			format: this.options.pdfOptions?.pageSize || 'a4'
		});

		// Add title
		const title =
			this.options.pdfOptions?.title ||
			(this.options.queryType
				? `${this.options.queryType.charAt(0).toUpperCase() + this.options.queryType.slice(1)} Report`
				: 'Search Results');

		pdf.text(title, 14, 20);

		// Add subtitle if provided
		let startY = 35;
		if (this.options.pdfOptions?.subtitle) {
			pdf.setFontSize(12);
			pdf.text(this.options.pdfOptions.subtitle, 14, 28);
			startY = 40;
		}

		// Add timestamp if enabled (default is true)
		const includeTimestamp = this.options.pdfOptions?.includeTimestamp !== false;
		if (includeTimestamp) {
			const timestamp = new Date().toLocaleString();
			pdf.setFontSize(10);
			pdf.text(`Generated: ${timestamp}`, 14, this.options.pdfOptions?.subtitle ? 36 : 30);
		}

		// Get table styles from options or use defaults
		const tableStyles = this.options.pdfOptions?.tableStyles || {};

		// Add table
		(pdf as any).autoTable({
			head: [headerLabels],
			body: tableData,
			startY: startY,
			styles: {
				fontSize: tableStyles.fontSize || 10,
				cellPadding: tableStyles.cellPadding || 3,
				lineColor: tableStyles.lineColor || [200, 200, 200],
				lineWidth: tableStyles.lineWidth || 0.1
			},
			headStyles: {
				fillColor: tableStyles.headerFillColor || [66, 66, 66],
				textColor: tableStyles.headerTextColor || 255,
				fontStyle: 'bold'
			},
			alternateRowStyles: {
				fillColor: tableStyles.alternateRowFillColor || [245, 245, 245]
			},
			margin: { top: startY }
		});

		// Add page numbers if enabled (default is true)
		const includePageNumbers = this.options.pdfOptions?.includePageNumbers !== false;
		if (includePageNumbers) {
			const pageCount = (pdf as any).internal.getNumberOfPages();
			for (let i = 1; i <= pageCount; i++) {
				pdf.setPage(i);
				pdf.setFontSize(10);
				pdf.text(
					`Page ${i} of ${pageCount}`,
					pdf.internal.pageSize.width - 30,
					pdf.internal.pageSize.height - 10
				);
			}
		}

		// Convert ArrayBuffer to Uint8Array
		const arrayBuffer = pdf.output('arraybuffer');
		return new Uint8Array(arrayBuffer);
	}

	/**
	 * Execute query and return formatted output, or format provided data
	 */
	async execute(data?: any[]): Promise<any> {
		// If no data provided, execute the query
		const results = data || (await this.query.execute());

		switch (this.options.format) {
			case 'csv':
				return this.toCSV(results);

			case 'html':
			case 'table':
				return this.toHTML(results);

			case 'pdf':
				return this.toPDF(results);

			case 'array':
				return results.map(row =>
					Object.fromEntries(Object.entries(row).map(([key, value]) => [key, this.formatValue(value, key)]))
				);

			case 'json':
			default:
				return JSON.stringify(
					results.map(row =>
						Object.fromEntries(
							Object.entries(row).map(([key, value]) => [key, this.formatValue(value, key)])
						)
					),
					null,
					2
				);
		}
	}

	/**
	 * Stream results (useful for large datasets)
	 */
	async *stream(batchSize: number = 1000): AsyncGenerator<any, void, unknown> {
		let offset = 0;
		while (true) {
			const batch = await this.query.clone().offset(offset).limit(batchSize).execute();

			if (batch.length === 0) break;

			switch (this.options.format) {
				case 'csv':
					yield await this.toCSV(batch);
					break;

				case 'html':
				case 'table':
					yield await this.toHTML(batch);
					break;

				case 'pdf':
					yield await this.toPDF(batch);
					break;

				case 'array':
					yield batch.map(row =>
						Object.fromEntries(
							Object.entries(row).map(([key, value]) => [key, this.formatValue(value, key)])
						)
					);
					break;

				case 'json':
				default:
					yield JSON.stringify(
						batch.map(row =>
							Object.fromEntries(
								Object.entries(row).map(([key, value]) => [key, this.formatValue(value, key)])
							)
						),
						null,
						2
					);
			}

			offset += batch.length;
			if (batch.length < batchSize) break;
		}
	}
}
