import type { QueryBuilder } from '../QueryBuilder.server';
import { GroupFilterBase } from './GroupFilterBase.server';

/**
 * OR filter group
 * At least one condition in this group must be satisfied (OR logic)
 */
export class OrGroup extends GroupFilterBase {
	/**
	 * Apply the OR filter group to a query builder
	 * @param queryBuilder The query builder to apply the filter to
	 */
	apply(queryBuilder: QueryBuilder): void {
		if (this.conditions.length === 0) {
			return;
		}

		queryBuilder.where(qb => {
			this.conditions.forEach((condition, index) => {
				if (index === 0) {
					// First condition uses where to start the group
					if (condition instanceof GroupFilterBase) {
						// Handle nested group
						condition.apply(qb);
					} else {
						// Check if this is a raw SQL condition
						if (
							condition.value &&
							typeof condition.value === 'object' &&
							condition.value.__raw__ === true
						) {
							// Handle raw SQL condition
							qb.whereRaw(condition.column, condition.value.params);
						} else {
							// Handle standard condition
							qb.where(condition.column, condition.operator, condition.value);
						}
					}
				} else {
					// Subsequent conditions use orWhere to combine with OR logic
					if (condition instanceof GroupFilterBase) {
						// Handle nested group with OR logic
						qb.orWhere(subQb => {
							condition.apply(subQb);
						});
					} else {
						// Check if this is a raw SQL condition
						if (
							condition.value &&
							typeof condition.value === 'object' &&
							condition.value.__raw__ === true
						) {
							// Handle raw SQL condition with OR logic
							// Since there's no direct orWhereRaw method, we need to use a subquery
							qb.orWhere(subQb => {
								subQb.whereRaw(condition.column, condition.value.params);
							});
						} else {
							// Handle standard condition with OR logic
							qb.orWhere(condition.column, condition.operator, condition.value);
						}
					}
				}
			});
		});
	}

	/**
	 * Get the boolean operator for this group (OR)
	 */
	getOperator(): 'AND' | 'OR' {
		return 'OR';
	}
}
