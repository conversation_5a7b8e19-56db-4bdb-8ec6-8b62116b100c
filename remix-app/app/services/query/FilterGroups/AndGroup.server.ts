import type { QueryBuilder } from '../QueryBuilder.server';
import { GroupFilterBase } from './GroupFilterBase.server';

/**
 * AND filter group
 * All conditions in this group must be satisfied (AND logic)
 */
export class AndGroup extends GroupFilterBase {
	/**
	 * Apply the AND filter group to a query builder
	 * @param queryBuilder The query builder to apply the filter to
	 */
	apply(queryBuilder: QueryBuilder): void {
		if (this.conditions.length === 0) {
			return;
		}

		queryBuilder.where(qb => {
			this.conditions.forEach((condition, index) => {
				if (condition instanceof GroupFilterBase) {
					// Handle nested group
					condition.apply(qb);
				} else {
					// Check if this is a raw SQL condition
					if (condition.value && typeof condition.value === 'object' && condition.value.__raw__ === true) {
						// Handle raw SQL condition
						qb.whereRaw(condition.column, condition.value.params);
					} else {
						// Handle standard condition
						qb.where(condition.column, condition.operator, condition.value);
					}
				}
			});
		});
	}

	/**
	 * Get the boolean operator for this group (AND)
	 */
	getOperator(): 'AND' | 'OR' {
		return 'AND';
	}
}
