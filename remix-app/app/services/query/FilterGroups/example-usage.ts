import { QueryBuilder } from '../QueryBuilder.server';

/**
 * Example of how to use filter groups with QueryBuilder
 */
export function filterGroupsExample() {
	// Create a query builder
	const queryBuilder = new QueryBuilder('incidents', 'i');

	// Example 1: Simple AND group
	const andGroup = queryBuilder.createAndGroup();
	andGroup.addCondition('i.status', 'equals', 'active');
	andGroup.addCondition('i.created_at', 'gte', new Date('2023-01-01'));

	// Apply the AND group to the query
	queryBuilder.applyFilterGroup(andGroup);

	// Example 2: Simple OR group
	const orGroup = queryBuilder.createOrGroup();
	orGroup.addCondition('i.type', 'equals', 'shooting');
	orGroup.addCondition('i.type', 'equals', 'mass_shooting');

	// Apply the OR group to the query
	queryBuilder.applyFilterGroup(orGroup);

	// Example 3: Nested groups
	const outerAndGroup = queryBuilder.createAndGroup();

	// Add a condition directly to the outer group
	outerAndGroup.addCondition('i.city', 'equals', 'New York');

	// Create a nested OR group
	const nestedOrGroup = queryBuilder.createOrGroup();
	nestedOrGroup.addCondition('i.victims_count', 'gte', 1);
	nestedOrGroup.addCondition('i.injuries_count', 'gte', 1);

	// Add the nested OR group to the outer AND group
	outerAndGroup.addGroup(nestedOrGroup);

	// Apply the nested group structure to the query
	queryBuilder.applyFilterGroup(outerAndGroup);

	// Get the SQL for the query
	const { text, params } = queryBuilder.toSQL();
	console.log('SQL:', text);
	console.log('Parameters:', params);

	return { text, params };
}

/**
 * This example demonstrates how to use filter groups with the SearchBuilder
 * by converting SearchFilterGroup objects to QueryBuilder filter groups.
 */
export function convertSearchFilterGroupToQueryBuilderGroup(
	queryBuilder: QueryBuilder,
	searchFilterGroup: any // SearchFilterGroup
): void {
	// Create the appropriate filter group based on the operator
	const filterGroup =
		searchFilterGroup.operator === 'OR' ? queryBuilder.createOrGroup() : queryBuilder.createAndGroup();

	// Process each condition in the search filter group
	for (const condition of searchFilterGroup.conditions) {
		if ('conditions' in condition) {
			// This is a nested filter group, process it recursively
			const nestedGroup =
				condition.operator === 'OR' ? queryBuilder.createOrGroup() : queryBuilder.createAndGroup();

			// Process the nested group recursively
			for (const nestedCondition of condition.conditions) {
				if ('conditions' in nestedCondition) {
					// Handle deeper nesting if needed
					convertSearchFilterGroupToQueryBuilderGroup(queryBuilder, nestedCondition);
				} else {
					// Add the condition to the nested group
					nestedGroup.addCondition(
						`table.${nestedCondition.id}`, // Adjust the column name as needed
						'equals', // Adjust the operator as needed
						nestedCondition.value
					);
				}
			}

			// Add the nested group to the parent group
			filterGroup.addGroup(nestedGroup);
		} else {
			// This is a simple condition
			filterGroup.addCondition(
				`table.${condition.id}`, // Adjust the column name as needed
				'equals', // Adjust the operator as needed
				condition.value
			);
		}
	}

	// Apply the filter group to the query builder
	queryBuilder.applyFilterGroup(filterGroup);
}
