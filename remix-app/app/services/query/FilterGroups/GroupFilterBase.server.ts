import type { QueryBuilder } from '../QueryBuilder.server';
import type { QueryCondition, QueryOperators } from '../types';
import { QueryRawCondition } from '../types';

/**
 * Base class for filter groups
 * Provides common functionality for AND and OR filter groups
 */
export abstract class GroupFilterBase {
	protected conditions: Array<QueryCondition | GroupFilterBase> = [];

	/**
	 * Add a condition to the group
	 * @param column The column to filter on
	 * @param operator The operator to use
	 * @param value The value to filter by
	 * @returns This instance for method chaining
	 */
	addCondition(column: string, operator: QueryOperators, value: any): this {
		this.conditions.push({
			column,
			operator,
			value
		});
		return this;
	}

	/**
	 * Add a nested filter group
	 * @param group The filter group to add
	 * @returns This instance for method chaining
	 */
	addGroup(group: GroupFilterBase): this {
		this.conditions.push(group);
		return this;
	}

	/**
	 * Add a raw SQL condition to the group
	 * @param sql The raw SQL condition
	 * @param params Optional parameters for the SQL condition
	 * @returns This instance for method chaining
	 */
	addRawCondition(sql: string, params?: any[]): this {
		// Create a condition that will be processed as a raw SQL condition
		this.conditions.push({
			column: sql, // Store the SQL in the column field
			operator: 'equals', // Use a placeholder operator
			value: { __raw__: true, params }, // Mark this as a raw condition with params
			boolean: 'AND' // Default to AND
		});
		return this;
	}

	/**
	 * Apply the filter group to a query builder
	 * @param queryBuilder The query builder to apply the filter to
	 */
	abstract apply(queryBuilder: QueryBuilder): void;

	/**
	 * Get the boolean operator for this group (AND or OR)
	 */
	abstract getOperator(): 'AND' | 'OR';
}
