import { ColumnFactory } from './query/columns/ColumnFactory.server';
import { QueryFilterFactory, QueryFilterFactoryException } from './query/filters/QueryFilterFactory.server';

/**
 * Test function to verify that the ColumnFactory works correctly
 */
export function testColumnFactory() {
  try {
    const factory = ColumnFactory.getInstance();
    
    // Get all columns
    const columns = factory.allColumns();
    console.log('Available columns:', columns);
    
    // Try to create a column instance
    if (columns.length > 0) {
      const column = factory.factory(columns[0]);
      console.log(`Created column instance for ${columns[0]}`);
      
      // Test the column methods
      const display = column.display({ [columns[0].toLowerCase().replace('.', '_')]: 'Test Value' });
      console.log(`Display result: ${display}`);
    }
    
    return 'ColumnFactory test passed';
  } catch (error) {
    console.error('ColumnFactory test failed:', error);
    return `ColumnFactory test failed: ${error.message}`;
  }
}

/**
 * Test function to verify that the QueryFilterFactory works correctly
 */
export function testQueryFilterFactory() {
  try {
    const factory = QueryFilterFactory.getInstance();
    
    // Try to create a filter instance
    const filter = factory.factory('IncidentDate');
    console.log('Created filter instance for IncidentDate');
    
    // Test the filter methods
    const shortName = filter.getShortName();
    console.log(`Short name: ${shortName}`);
    
    const comparators = filter.comparators('test-id', {});
    console.log('Available comparators:', comparators);
    
    return 'QueryFilterFactory test passed';
  } catch (error) {
    console.error('QueryFilterFactory test failed:', error);
    return `QueryFilterFactory test failed: ${error.message}`;
  }
}

/**
 * Run all tests
 */
export function runTests() {
  const columnFactoryResult = testColumnFactory();
  const queryFilterFactoryResult = testQueryFilterFactory();
  
  return {
    columnFactoryResult,
    queryFilterFactoryResult
  };
}