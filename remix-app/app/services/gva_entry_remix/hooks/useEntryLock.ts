import { useEffect, useState, useCallback, RefObject } from 'react';
import { useFetcher } from '@remix-run/react';

/**
 * Interface for entry lock state
 */
interface EntryLockState {
  isLocked: boolean;
  isDisconnected: boolean;
  errorMessage: string | null;
}

/**
 * Interface for lock response data
 */
interface LockResponse {
  allow: boolean;
}

/**
 * Hook to manage entry locking functionality in Remix
 * 
 * @param incidentId - The ID of the incident being edited
 * @param onLockStateChange - Optional callback for when lock state changes
 * @returns Object containing the lock state and UI state management functions
 */
export function useEntryLock(
  incidentId: string | null,
  onLockStateChange?: (state: EntryLockState) => void
) {
  const [lockState, setLockState] = useState<EntryLockState>({
    isLocked: false,
    isDisconnected: false,
    errorMessage: null
  });

  const fetcher = useFetcher<LockResponse>();

  // Function to poll the lock status
  const pollLockStatus = useCallback(() => {
    if (!incidentId) return;

    const url = lockState.isDisconnected 
      ? `/admin/entry-lock/${incidentId}/reconnect`
      : `/admin/entry-lock/${incidentId}`;

    fetcher.load(url);
  }, [incidentId, lockState.isDisconnected, fetcher]);

  // Handle fetcher data changes
  useEffect(() => {
    if (fetcher.data) {
      if (fetcher.data.allow) {
        // User is allowed to edit
        setLockState(prevState => {
          const newState = {
            isLocked: false,
            isDisconnected: false,
            errorMessage: null
          };

          // Call the callback if provided and state changed
          if (onLockStateChange && 
              (prevState.isLocked !== newState.isLocked || 
               prevState.isDisconnected !== newState.isDisconnected || 
               prevState.errorMessage !== newState.errorMessage)) {
            onLockStateChange(newState);
          }

          return newState;
        });
      } else if (lockState.isDisconnected) {
        // User was disconnected and someone else started editing
        const message = 'Someone began editing this incident while you were disconnected. You will not be able to save your changes.';

        setLockState(prevState => {
          const newState = {
            isLocked: true,
            isDisconnected: true,
            errorMessage: message
          };

          // Call the callback if provided and state changed
          if (onLockStateChange && 
              (prevState.isLocked !== newState.isLocked || 
               prevState.isDisconnected !== newState.isDisconnected || 
               prevState.errorMessage !== newState.errorMessage)) {
            onLockStateChange(newState);
          }

          return newState;
        });
      }
    }
  }, [fetcher.data, lockState.isDisconnected, onLockStateChange]);

  // Handle fetcher errors
  useEffect(() => {
    if (fetcher.state === 'idle' && fetcher.data === undefined && fetcher.submission) {
      // This indicates an error occurred
      const errorCount = lockState.errorMessage ? 2 : 1;

      if (errorCount >= 2) {
        const message = 'We are having issues connecting to the server. This may be caused by a server issue, or you may have lost internet. This message will disappear once the connection is restored.';

        setLockState(prevState => {
          const newState = {
            isLocked: true,
            isDisconnected: true,
            errorMessage: message
          };

          // Call the callback if provided and state changed
          if (onLockStateChange && 
              (prevState.isLocked !== newState.isLocked || 
               prevState.isDisconnected !== newState.isDisconnected || 
               prevState.errorMessage !== newState.errorMessage)) {
            onLockStateChange(newState);
          }

          return newState;
        });
      } else {
        setLockState(prevState => {
          const newState = {
            ...prevState,
            errorMessage: 'Connection error'
          };

          // Call the callback if provided and state changed
          if (onLockStateChange && prevState.errorMessage !== newState.errorMessage) {
            onLockStateChange(newState);
          }

          return newState;
        });
      }
    }
  }, [fetcher.state, fetcher.data, fetcher.submission, lockState.errorMessage, onLockStateChange]);

  useEffect(() => {
    if (!incidentId) return;

    // Initial poll
    pollLockStatus();

    // Set up polling interval
    const pollInterval = setInterval(pollLockStatus, 15000);

    // Set up beforeunload handler to release lock
    const handleBeforeUnload = () => {
      if (incidentId) {
        // Use navigator.sendBeacon for more reliable sending during page unload
        navigator.sendBeacon(`/admin/entry-lock/${incidentId}/disconnect`);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // Cleanup on unmount
    return () => {
      clearInterval(pollInterval);
      window.removeEventListener('beforeunload', handleBeforeUnload);

      // Release lock when component unmounts
      if (incidentId) {
        fetcher.load(`/admin/entry-lock/${incidentId}/disconnect`);
      }
    };
  }, [incidentId, pollLockStatus, fetcher]);

  // Function to manually disconnect
  const disconnect = useCallback(() => {
    if (incidentId) {
      fetcher.load(`/admin/entry-lock/${incidentId}/disconnect`);
    }
  }, [incidentId, fetcher]);

  // Function to manually reconnect
  const reconnect = useCallback(() => {
    if (incidentId) {
      fetcher.load(`/admin/entry-lock/${incidentId}/reconnect`);
    }
  }, [incidentId, fetcher]);

  return { 
    lockState,
    disconnect,
    reconnect
  };
}
