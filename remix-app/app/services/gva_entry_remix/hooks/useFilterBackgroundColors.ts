import { useEffect, RefObject } from 'react';
import { getNestedFilterBackgroundColor } from '../utils/colorUtils';

/**
 * Hook to apply background colors to nested filter elements based on their depth
 * 
 * @param containerRef - Reference to the container element with filter elements
 * @param baseColor - The base color to adjust (default: #f2f2f2)
 * @param luminanceFactor - The luminance adjustment factor per depth level (default: -0.04)
 */
export function useFilterBackgroundColors(
  containerRef: RefObject<HTMLElement>,
  baseColor: string = '#f2f2f2',
  luminanceFactor: number = -0.04
): void {
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Function to set background colors
    const setBackgroundColors = () => {
      // Select all filter elements
      const filterElements = container.querySelectorAll('.and, .or, .filter');
      
      filterElements.forEach((element) => {
        // Calculate the depth of nesting
        const depth = getElementDepth(element as HTMLElement);
        
        // Only apply background color to elements with depth > 1
        if (depth > 1) {
          const backgroundColor = getNestedFilterBackgroundColor(depth, baseColor, luminanceFactor);
          (element as HTMLElement).style.backgroundColor = backgroundColor;
        }
      });
    };

    // Helper function to get the depth of an element
    const getElementDepth = (element: HTMLElement): number => {
      let depth = 0;
      let current: HTMLElement | null = element;
      
      while (current && current !== container) {
        if (
          current.classList.contains('and') || 
          current.classList.contains('or') || 
          current.classList.contains('filter')
        ) {
          depth++;
        }
        current = current.parentElement;
      }
      
      return depth;
    };

    // Set background colors initially
    setBackgroundColors();

    // Set up a mutation observer to handle dynamically added elements
    const observer = new MutationObserver(setBackgroundColors);
    
    observer.observe(container, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class']
    });

    // Clean up the observer when the component unmounts
    return () => {
      observer.disconnect();
    };
  }, [containerRef, baseColor, luminanceFactor]);
}