import { useEffect, RefObject } from 'react';

/**
 * Hook to automatically submit a form when a select element's value changes to 'custom'
 * 
 * @param formRef - Reference to the form element
 * @param selectRef - Reference to the select element
 */
export function useSubmitOnCustom(
  formRef: RefObject<HTMLFormElement>,
  selectRef: RefObject<HTMLSelectElement>
): void {
  useEffect(() => {
    const selectElement = selectRef.current;
    
    if (!selectElement) return;
    
    const handleChange = (event: Event) => {
      const select = event.target as HTMLSelectElement;
      const selectedOption = select.options[select.selectedIndex];
      
      if (selectedOption.value === 'custom' && formRef.current) {
        formRef.current.submit();
      }
    };
    
    selectElement.addEventListener('change', handleChange);
    
    return () => {
      selectElement.removeEventListener('change', handleChange);
    };
  }, [formRef, selectRef]);
}

/**
 * Alternative implementation as a component prop
 * 
 * @param event - The change event
 * @param formRef - Reference to the form element
 */
export function handleCustomSelectChange(
  event: React.ChangeEvent<HTMLSelectElement>,
  formRef: RefObject<HTMLFormElement>
): void {
  const select = event.target;
  const selectedOption = select.options[select.selectedIndex];
  
  if (selectedOption.value === 'custom' && formRef.current) {
    formRef.current.submit();
  }
}