import { useEffect, RefObject } from 'react';

/**
 * Interface for age group option data attributes
 */
export interface AgeGroupOption {
  value: string;
  label: string;
  minimumAge?: number;
  maximumAge?: number;
}

/**
 * Hook to automatically select an age group based on the entered age
 * 
 * @param ageInputRef - Reference to the age input element
 * @param ageGroupSelectRef - Reference to the age group select element
 * @param ageGroupOptions - Array of age group options with their age ranges
 * @param onAgeGroupChange - Optional callback when age group changes
 */
export function useAgeGroupSelection(
  ageInputRef: RefObject<HTMLInputElement>,
  ageGroupSelectRef: RefObject<HTMLSelectElement>,
  ageGroupOptions: AgeGroupOption[],
  onAgeGroupChange?: (selectedValue: string) => void
): void {
  useEffect(() => {
    const ageInput = ageInputRef.current;
    const ageGroupSelect = ageGroupSelectRef.current;
    
    if (!ageInput || !ageGroupSelect) return;
    
    const handleAgeBlur = () => {
      const ageValue = ageInput.value;
      
      if (!ageValue) return;
      
      const age = parseInt(ageValue, 10);
      if (isNaN(age)) return;
      
      // Find the appropriate age group option
      const matchingOption = ageGroupOptions.find(option => {
        if (option.minimumAge !== undefined && option.maximumAge !== undefined) {
          return age >= option.minimumAge && age <= option.maximumAge;
        } else if (option.minimumAge !== undefined) {
          return age >= option.minimumAge;
        } else if (option.maximumAge !== undefined) {
          return age <= option.maximumAge;
        }
        return false;
      });
      
      if (matchingOption) {
        ageGroupSelect.value = matchingOption.value;
        
        // Dispatch a change event to trigger any listeners
        const event = new Event('change', { bubbles: true });
        ageGroupSelect.dispatchEvent(event);
        
        // Call the callback if provided
        if (onAgeGroupChange) {
          onAgeGroupChange(matchingOption.value);
        }
      }
    };
    
    ageInput.addEventListener('blur', handleAgeBlur);
    
    return () => {
      ageInput.removeEventListener('blur', handleAgeBlur);
    };
  }, [ageInputRef, ageGroupSelectRef, ageGroupOptions, onAgeGroupChange]);
}