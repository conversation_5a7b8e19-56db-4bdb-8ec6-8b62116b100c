import { useEffect, RefObject } from 'react';

/**
 * Hook to manage filter dropdown functionality
 * 
 * @param containerRef - Reference to the container element with filter dropdowns
 */
export function useFilterDropdowns(containerRef: RefObject<HTMLElement>) {
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    
    /**
     * Handle click on filter dropdown trigger
     */
    const handleTriggerClick = (event: Event) => {
      event.stopPropagation();
      const trigger = event.currentTarget as HTMLElement;
      const dropdown = trigger.nextElementSibling as HTMLElement;
      
      if (dropdown) {
        dropdown.style.display = 'block';
      }
    };
    
    /**
     * Handle click on filter dropdown item
     */
    const handleDropdownItemClick = (event: Event) => {
      const item = event.currentTarget as HTMLElement;
      const dropdown = item.closest('.filter-dropdown') as HTMLElement;
      const select = dropdown?.nextElementSibling?.querySelector('select') as HTMLSelectElement;
      
      if (select && item.dataset.value) {
        // Find and select the option with the matching value
        const option = Array.from(select.options).find(opt => opt.value === item.dataset.value);
        if (option) {
          option.selected = true;
          
          // Trigger change event
          const changeEvent = new Event('change', { bubbles: true });
          select.dispatchEvent(changeEvent);
        }
      }
    };
    
    /**
     * Handle document click to hide dropdowns
     */
    const handleDocumentClick = () => {
      const dropdowns = container.querySelectorAll('.filter-dropdown');
      dropdowns.forEach(dropdown => {
        (dropdown as HTMLElement).style.display = 'none';
      });
    };
    
    /**
     * Handle base group dropdown change
     */
    const handleBaseGroupChange = (event: Event) => {
      const select = event.currentTarget as HTMLSelectElement;
      const filtersContainer = document.getElementById('filters-ajax-container-edit-query');
      
      if (filtersContainer) {
        // Remove all classes and add the new one
        filtersContainer.className = '';
        filtersContainer.classList.add(select.value.toLowerCase());
      }
    };
    
    // Add event listeners to filter dropdown triggers
    const triggers = container.querySelectorAll('.filter-dropdown-trigger');
    triggers.forEach(trigger => {
      trigger.addEventListener('click', handleTriggerClick);
    });
    
    // Add event listeners to filter dropdown items
    const dropdownItems = container.querySelectorAll('.filter-dropdown a');
    dropdownItems.forEach(item => {
      item.addEventListener('click', handleDropdownItemClick);
    });
    
    // Add event listener to document for hiding dropdowns
    document.addEventListener('click', handleDocumentClick);
    
    // Add event listener to base group dropdown
    const baseGroupDropdown = document.getElementById('base-group-dropdown') as HTMLSelectElement;
    if (baseGroupDropdown) {
      baseGroupDropdown.addEventListener('change', handleBaseGroupChange);
      
      // Set initial class on filters container
      const filtersContainer = document.getElementById('filters-ajax-container-edit-query');
      if (filtersContainer) {
        if (baseGroupDropdown.value) {
          filtersContainer.className = baseGroupDropdown.value.toLowerCase();
        } else {
          filtersContainer.className = 'and';
        }
      }
    }
    
    // Cleanup event listeners on unmount
    return () => {
      triggers.forEach(trigger => {
        trigger.removeEventListener('click', handleTriggerClick);
      });
      
      dropdownItems.forEach(item => {
        item.removeEventListener('click', handleDropdownItemClick);
      });
      
      document.removeEventListener('click', handleDocumentClick);
      
      if (baseGroupDropdown) {
        baseGroupDropdown.removeEventListener('change', handleBaseGroupChange);
      }
    };
  }, [containerRef]);
}