import { useEffect, RefObject } from 'react';
import { useTransition } from '@remix-run/react';

/**
 * Hook to disable form elements during form submissions in Remix
 * 
 * @param formRef - Reference to the form element
 * @returns Object containing the current submission state
 */
export function useFormDisableOnAjax(formRef: RefObject<HTMLFormElement>) {
  const transition = useTransition();
  const isSubmitting = transition.state === 'submitting';

  useEffect(() => {
    const form = formRef.current;
    if (!form) return;

    // Find all form elements and submit buttons
    const elements = form.querySelectorAll('input, select, textarea, button[type="submit"], .incident-submit.form-submit');

    if (isSubmitting) {
      // Disable elements that aren't already disabled
      elements.forEach((element) => {
        const htmlElement = element as HTMLElement;
        if (!htmlElement.hasAttribute('disabled')) {
          htmlElement.setAttribute('disabled', 'disabled');
          htmlElement.classList.add('disabled-manually');
        }
      });
    } else {
      // Re-enable elements that were manually disabled
      const disabledElements = form.querySelectorAll('.disabled-manually');
      disabledElements.forEach((element) => {
        const htmlElement = element as HTMLElement;
        htmlElement.removeAttribute('disabled');
        htmlElement.classList.remove('disabled-manually');
      });
    }
  }, [formRef, isSubmitting]);
}
