import { useEffect, RefObject } from 'react';

/**
 * Hook to manage gun functionality
 * 
 * @param gunsContainerRef - Reference to the guns container element
 * @param gunCountRef - Reference to the gun count input element
 * @param showHideButtonRef - Reference to the show/hide guns button
 * @returns Object containing functions to update gun count, clear gun forms, and toggle gun details visibility
 */
export function useGunManagement(
  gunsContainerRef: RefObject<HTMLElement>,
  gunCountRef: RefObject<HTMLInputElement>,
  showHideButtonRef: RefObject<HTMLButtonElement>
) {
  /**
   * Updates the number of guns
   */
  const updateNumGuns = (): number => {
    let numGuns = 0;
    
    const container = gunsContainerRef.current;
    if (!container) return numGuns;
    
    // Get all gun forms
    const gunForms = container.querySelectorAll('.gun-form');
    
    gunForms.forEach((element) => {
      // Skip deleted guns
      const deleteInput = element.querySelector("input[name$='[delete]']") as HTMLInputElement;
      if (deleteInput && deleteInput.value === '1') return;
      
      numGuns++;
    });
    
    // Update the count input if it exists and the count is within limits
    if (gunCountRef.current && numGuns <= 50) {
      gunCountRef.current.value = numGuns.toString();
    }
    
    return numGuns;
  };
  
  /**
   * Clears a gun form
   * 
   * @param formElement - The form element to clear
   */
  const clearGunForm = (formElement: HTMLElement) => {
    // Clear text inputs
    const textInputs = formElement.querySelectorAll('input[type=text]');
    textInputs.forEach((input) => {
      (input as HTMLInputElement).value = '';
    });
    
    // Uncheck checkboxes (note: in React, you'd typically use state for this)
    const checkboxes = formElement.querySelectorAll('input[type=checkbox]');
    checkboxes.forEach((checkbox) => {
      (checkbox as HTMLInputElement).checked = false;
    });
    
    // Reset selects
    const firstSelect = formElement.querySelector('.medium-9 select:first-child') as HTMLSelectElement;
    if (firstSelect) {
      firstSelect.selectedIndex = 0;
      // In React, you'd dispatch a change event or update state
    }
    
    const secondSelect = formElement.querySelector('.medium-3 select') as HTMLSelectElement;
    if (secondSelect) {
      secondSelect.selectedIndex = 0;
      // In React, you'd dispatch a change event or update state
    }
  };
  
  /**
   * Deletes (hides) a gun form
   * 
   * @param formElement - The form element to delete
   */
  const deleteGunForm = (formElement: HTMLElement) => {
    const deleteInput = formElement.querySelector("input[name$='[delete]']") as HTMLInputElement;
    if (deleteInput) {
      deleteInput.value = '1';
      formElement.style.display = 'none';
      updateNumGuns();
    }
  };
  
  /**
   * Toggles the visibility of gun details
   */
  const toggleGunDetails = () => {
    const container = gunsContainerRef.current;
    const button = showHideButtonRef.current;
    
    if (!container || !button) return;
    
    if (container.style.display === 'none') {
      container.style.display = 'block';
      button.textContent = 'Hide Details';
    } else {
      container.style.display = 'none';
      button.textContent = 'Show Details';
    }
  };
  
  /**
   * Refreshes the gun display based on button state
   */
  const refreshGunDisplay = () => {
    const container = gunsContainerRef.current;
    const button = showHideButtonRef.current;
    
    if (!container || !button) return;
    
    if (button.textContent === 'Hide Details') {
      container.style.display = 'block';
    } else {
      container.style.display = 'none';
    }
  };
  
  useEffect(() => {
    const container = gunsContainerRef.current;
    const button = showHideButtonRef.current;
    
    if (!container || !button) return;
    
    // Initially hide the gun details
    container.style.display = 'none';
    button.textContent = 'Show Details';
    
    // Wire up show/hide button
    const handleShowHideClick = (e: Event) => {
      e.preventDefault();
      toggleGunDetails();
    };
    
    button.addEventListener('click', handleShowHideClick);
    
    // Wire up clear buttons
    const clearButtons = container.querySelectorAll('.button.icon-clear');
    clearButtons.forEach((clearButton) => {
      clearButton.addEventListener('click', (e) => {
        e.preventDefault();
        const form = (clearButton as HTMLElement).closest('.gun-form');
        if (form) {
          clearGunForm(form as HTMLElement);
        }
      });
    });
    
    // Wire up delete buttons
    const deleteButtons = container.querySelectorAll('.button.icon-delete-hide-only');
    deleteButtons.forEach((deleteButton) => {
      deleteButton.addEventListener('click', (e) => {
        e.preventDefault();
        const form = (deleteButton as HTMLElement).closest('.gun-form');
        if (form) {
          deleteGunForm(form as HTMLElement);
        }
      });
    });
    
    // Initial count update
    updateNumGuns();
    
    // Cleanup event listeners on unmount
    return () => {
      button.removeEventListener('click', handleShowHideClick);
      
      clearButtons.forEach((clearButton) => {
        clearButton.removeEventListener('click', (e) => {
          e.preventDefault();
          const form = (clearButton as HTMLElement).closest('.gun-form');
          if (form) {
            clearGunForm(form as HTMLElement);
          }
        });
      });
      
      deleteButtons.forEach((deleteButton) => {
        deleteButton.removeEventListener('click', (e) => {
          e.preventDefault();
          const form = (deleteButton as HTMLElement).closest('.gun-form');
          if (form) {
            deleteGunForm(form as HTMLElement);
          }
        });
      });
    };
  }, [gunsContainerRef, gunCountRef, showHideButtonRef]);
  
  return {
    updateNumGuns,
    clearGunForm,
    deleteGunForm,
    toggleGunDetails,
    refreshGunDisplay
  };
}