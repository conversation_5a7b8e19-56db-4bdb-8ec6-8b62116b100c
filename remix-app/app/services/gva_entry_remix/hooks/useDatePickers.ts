import { useEffect, RefObject } from 'react';

/**
 * Hook to initialize date and time pickers
 * 
 * @param containerRef - Reference to the container element with date/time inputs
 */
export function useDatePickers(containerRef: RefObject<HTMLElement>) {
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    
    // Dynamically import daterangepicker and timepicker
    Promise.all([
      import('daterangepicker'),
      import('jquery-timepicker')
    ]).then(([daterangepicker, timepicker]) => {
      // Import jQuery
      import('jquery').then((jQuery) => {
        const $ = jQuery.default;
        
        // Initialize single date pickers
        $(container).find('.date-picker-single').daterangepicker({
          format: 'M/D/YYYY',
          singleDatePicker: true,
          showDropdowns: true,
          minDate: '01/01/1900',
          maxDate: new Date()
        }, function(start: any, end: any, label: any) {
          // Callback function
        });
        
        // Initialize time pickers
        $(container).find('.time-picker-single').timepicker({
          timeFormat: 'h:mm p',
          interval: 15,
          minTime: '00',
          maxTime: '11:59pm',
          defaultTime: 'now',
          startTime: '00:00',
          dynamic: true,
          dropdown: true,
          scrollbar: true,
          zindex: 10
        });
        
        // Initialize month/day pickers
        $(container).find('.md-picker-single').daterangepicker({
          format: 'M/D',
          singleDatePicker: true,
          showDropdowns: true
        }, function(start: any, end: any, label: any) {
          // Callback function
        }).on('show.daterangepicker', function(ev: any, picker: any) {
          picker.container.addClass('mdpicker');
        });
        
        // Initialize date range pickers
        $(container).find('.date-picker-range').daterangepicker({
          format: 'M/D/YYYY',
          startDate: new Date(new Date().setDate(new Date().getDate() - 29)),
          endDate: new Date(),
          minDate: '01/01/1900',
          showDropdowns: true,
          showWeekNumbers: true,
          timePicker: false,
          ranges: {
            'Today': [new Date(), new Date()],
            'Yesterday': [new Date(new Date().setDate(new Date().getDate() - 1)), new Date(new Date().setDate(new Date().getDate() - 1))],
            'Last 7 Days': [new Date(new Date().setDate(new Date().getDate() - 6)), new Date()],
            'Last 30 Days': [new Date(new Date().setDate(new Date().getDate() - 29)), new Date()],
            'This Month': [new Date(new Date().setDate(1)), new Date(new Date(new Date().setMonth(new Date().getMonth() + 1)).setDate(0))],
            'Last Month': [
              new Date(new Date(new Date().setMonth(new Date().getMonth() - 1)).setDate(1)),
              new Date(new Date(new Date().setDate(0)))
            ]
          },
          opens: 'right',
          drops: 'down',
          buttonClasses: ['btn', 'btn-sm'],
          applyClass: 'btn-primary',
          cancelClass: 'btn-default',
          separator: ' to ',
          locale: {
            applyLabel: 'Submit',
            cancelLabel: 'Cancel',
            fromLabel: 'From',
            toLabel: 'To',
            customRangeLabel: 'Custom',
            daysOfWeek: ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'],
            monthNames: [
              'January', 'February', 'March', 'April', 'May', 'June',
              'July', 'August', 'September', 'October', 'November', 'December'
            ],
            firstDay: 1
          }
        }, function(start: any, end: any, label: any) {
          $(this).text(start.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));
        });
      });
    }).catch(error => {
      console.error('Error loading date picker libraries:', error);
    });
    
    // No specific cleanup needed as the date picker libraries handle their own cleanup
  }, [containerRef]);
}