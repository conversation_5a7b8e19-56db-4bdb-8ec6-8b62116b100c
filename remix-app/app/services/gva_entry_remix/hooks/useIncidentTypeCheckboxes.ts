import { useState, useEffect } from 'react';
import { IncidentType, getParentIds, getChildIds, collectReminders, ReminderMessage } from '../utils/incidentTypeUtils';

/**
 * Hook to manage hierarchical incident type checkboxes
 * 
 * @param incidentTypes - Array of incident types
 * @param initialSelectedIds - Array of initially selected incident type IDs
 * @returns Object with selected IDs, reminders, and functions to handle selection changes
 */
export function useIncidentTypeCheckboxes(
  incidentTypes: IncidentType[],
  initialSelectedIds: string[] = []
) {
  const [selectedIds, setSelectedIds] = useState<string[]>(initialSelectedIds);
  const [reminders, setReminders] = useState<ReminderMessage[]>([]);
  
  // Update reminders when selected IDs change
  useEffect(() => {
    const newReminders = collectReminders(incidentTypes, selectedIds);
    setReminders(newReminders);
  }, [incidentTypes, selectedIds]);
  
  /**
   * Handle checkbox selection
   * 
   * @param typeId - The ID of the incident type being selected
   * @param checked - Whether the checkbox is checked or unchecked
   */
  const handleSelectionChange = (typeId: string, checked: boolean) => {
    if (checked) {
      // When checking a box, also check all parent boxes
      const parentIds = getParentIds(incidentTypes, typeId);
      const newSelectedIds = [...new Set([...selectedIds, typeId, ...parentIds])];
      setSelectedIds(newSelectedIds);
    } else {
      // When unchecking a box, also uncheck all child boxes
      const childIds = getChildIds(incidentTypes, typeId);
      const idsToRemove = [typeId, ...childIds];
      
      // Check if this is a parent with only one child selected
      const parentType = incidentTypes.find(type => 
        type.children?.some(child => child.id === typeId)
      );
      
      if (parentType) {
        const selectedChildren = parentType.children?.filter(child => 
          selectedIds.includes(child.id)
        ) || [];
        
        if (selectedChildren.length === 1 && selectedChildren[0].id === typeId) {
          // If this is the only selected child, also uncheck the parent
          idsToRemove.push(parentType.id);
        }
      }
      
      const newSelectedIds = selectedIds.filter(id => !idsToRemove.includes(id));
      setSelectedIds(newSelectedIds);
    }
  };
  
  /**
   * Check if a specific incident type is selected
   * 
   * @param typeId - The ID of the incident type to check
   * @returns Whether the incident type is selected
   */
  const isSelected = (typeId: string): boolean => {
    return selectedIds.includes(typeId);
  };
  
  return {
    selectedIds,
    reminders,
    handleSelectionChange,
    isSelected,
    setSelectedIds
  };
}