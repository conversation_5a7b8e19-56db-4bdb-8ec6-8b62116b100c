import { useEffect, useState, useRef, RefObject } from 'react';

/**
 * Interface for help tip data
 */
interface HelpTips {
  [key: string]: string;
}

/**
 * Hook to manage filter help tips
 * 
 * @param containerRef - Reference to the container element with filters
 * @param helpTipsData - Object containing help tip messages for different filter types
 * @returns Object containing functions to reindex filters
 */
export function useFilterHelpTips(
  containerRef: RefObject<HTMLElement>,
  helpTipsData: HelpTips
) {
  const [filters, setFilters] = useState<HTMLElement[]>([]);
  const [inactiveTime, setInactiveTime] = useState(0);
  const [waiting, setWaiting] = useState(false);
  const timerRef = useRef<number | null>(null);
  
  // List of filter types that should only show help once
  const showOnceTypes = [
    'IncidentType',
    'NumberOfParticipants',
    'NumberOfPerpetrators',
    'NumberOfVictims',
    'ParticipantsAge',
    'ParticipantsAgeGroup',
    'ParticipantsGender',
    'ParticipantsStatus',
    'ParticipantsType',
    'ParticipantsCharacteristic',
    'ParticipantsRelationship',
    'NumberOfGuns',
    'Pending',
    'Twitter'
  ];
  
  /**
   * Reindexes filters and updates the filters array
   * 
   * @param forceReindex - Whether to force reindexing
   */
  const reindexFilters = (forceReindex: boolean = false) => {
    const container = containerRef.current;
    if (!container) return;
    
    // Remove existing help tips
    const helpTips = container.querySelectorAll('.help-tip');
    helpTips.forEach(tip => tip.remove());
    
    // Get all filter rows that are not options rows
    if (forceReindex || filters.length === 0) {
      const filterRows = Array.from(
        container.querySelectorAll('.filter-row:not(.options-row)')
      ) as HTMLElement[];
      
      setFilters(filterRows);
    }
    
    // Reset waiting and inactive time
    setWaiting(false);
    setInactiveTime(0);
  };
  
  useEffect(() => {
    // Clear existing timer
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    
    // Set up timer for help tips
    timerRef.current = window.setInterval(() => {
      if (waiting || filters.length === 0) return;
      
      // Increment inactive time
      setInactiveTime(prevTime => prevTime + 1);
      
      // Check if we should show a help tip
      if (inactiveTime >= 5) {
        let showTip = true;
        const lastFilter = filters[filters.length - 1];
        
        if (!lastFilter) {
          showTip = false;
        } else {
          // Get filter type
          const typeInput = lastFilter.querySelector('input:hidden') as HTMLInputElement;
          const type = typeInput ? typeInput.value : '';
          
          // Check if we should show the tip based on filter type and state
          if (lastFilter.classList.contains('or') || lastFilter.classList.contains('and')) {
            // Don't show tip if the group has filters
            if (lastFilter.querySelectorAll('.filter-row:not(.options-row)').length !== 0) {
              showTip = false;
            }
          } else if (showOnceTypes.includes(type)) {
            // Don't show tip if it's already been shown for this type
            if (lastFilter.dataset.shown === 'true') {
              showTip = false;
            }
          } else {
            // Don't show tip if any inputs have values
            const inputs = lastFilter.querySelectorAll(
              'input:not(:hidden), select:not(:disabled, [id*="comparator"]), textarea'
            );
            
            for (let i = 0; i < inputs.length; i++) {
              const input = inputs[i] as HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement;
              if (input.value.trim().length !== 0) {
                showTip = false;
                break;
              }
            }
          }
          
          // Don't show tip if there's no help text for this type
          if (!helpTipsData[type]) {
            showTip = false;
          }
          
          // Show the tip if all conditions are met
          if (showTip) {
            setWaiting(true);
            setInactiveTime(0);
            
            // Create and append help tip
            const helpTip = document.createElement('div');
            helpTip.className = 'help-tip';
            helpTip.innerHTML = helpTipsData[type];
            lastFilter.appendChild(helpTip);
            
            // Mark as shown
            lastFilter.dataset.shown = 'true';
            
            // Add click handler to prevent propagation
            helpTip.addEventListener('click', (event) => {
              event.stopPropagation();
            });
          } else {
            // Remove this filter from the array
            setFilters(prevFilters => prevFilters.slice(0, -1));
          }
        }
      }
    }, 1000);
    
    // Set up document click handler to reindex filters
    const handleDocumentClick = () => {
      reindexFilters();
    };
    
    // Set up input handler to reindex filters
    const handleInputKeypress = () => {
      reindexFilters();
    };
    
    document.addEventListener('click', handleDocumentClick);
    document.addEventListener('keypress', handleInputKeypress, { capture: true });
    
    // Cleanup on unmount
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      
      document.removeEventListener('click', handleDocumentClick);
      document.removeEventListener('keypress', handleInputKeypress, { capture: true });
    };
  }, [containerRef, filters, helpTipsData, inactiveTime, waiting, showOnceTypes]);
  
  return { reindexFilters };
}