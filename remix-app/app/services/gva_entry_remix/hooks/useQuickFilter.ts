import { useState, useEffect } from 'react';
import { getUrlParameter, navigateWithUpdatedParameter } from '../utils/urlUtils';

/**
 * Hook to manage a quick filter that updates the URL with a parameter
 * 
 * @param paramName - The name of the URL parameter to use (default: 'characteristic')
 * @returns An object with the current filter value and a function to update it
 */
export function useQuickFilter(paramName: string = 'characteristic') {
  // Initialize state with the current URL parameter value
  const [filterValue, setFilterValue] = useState<string | null>(null);
  
  // Load the initial value from the URL when the component mounts
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const initialValue = getUrlParameter(paramName);
      setFilterValue(initialValue);
    }
  }, [paramName]);
  
  /**
   * Update the filter value and navigate to the new URL
   * 
   * @param newValue - The new filter value
   */
  const updateFilter = (newValue: string | null) => {
    setFilterValue(newValue);
    navigateWithUpdatedParameter(paramName, newValue);
  };
  
  return {
    filterValue,
    updateFilter
  };
}