import { useState } from 'react';

/**
 * Hook to manage gender checkboxes where only one can be selected at a time
 * 
 * @returns An object containing the selected gender and a function to handle checkbox changes
 */
export function useGenderCheckboxes() {
  const [selectedGender, setSelectedGender] = useState<string | null>(null);
  
  /**
   * Handle checkbox change events
   * 
   * @param gender - The gender value of the checkbox that was changed
   * @param checked - Whether the checkbox is now checked or unchecked
   */
  const handleGenderChange = (gender: string, checked: boolean) => {
    if (checked) {
      setSelectedGender(gender);
    } else if (selectedGender === gender) {
      setSelectedGender(null);
    }
  };
  
  return {
    selectedGender,
    handleGenderChange
  };
}