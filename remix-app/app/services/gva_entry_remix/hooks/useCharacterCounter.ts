import { useEffect, RefObject } from 'react';

/**
 * Hook to add character counters to textareas with maxlength
 * 
 * @param containerRef - Reference to the container element with textareas
 */
export function useCharacterCounter(containerRef: RefObject<HTMLElement>) {
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    
    // Find all textareas with maxlength and add-counter class
    const textareas = container.querySelectorAll('textarea[maxlength].add-counter');
    
    // Create and attach character counters
    textareas.forEach((textarea) => {
      const textareaElement = textarea as HTMLTextAreaElement;
      const maxLength = textareaElement.getAttribute('maxlength');
      
      if (!maxLength) return;
      
      // Create counter element
      const counter = document.createElement('div');
      counter.className = 'counter';
      counter.innerHTML = `Used <span class='character-count'>${textareaElement.value.length}</span> of ${maxLength} characters.`;
      
      // Append counter after textarea
      textareaElement.parentElement?.appendChild(counter);
      
      // Update counter on input
      const updateCounter = () => {
        const currentLength = textareaElement.value.length;
        const maxLengthNum = parseInt(maxLength, 10);
        
        // Truncate if exceeds maxlength
        if (currentLength > maxLengthNum) {
          textareaElement.value = textareaElement.value.substring(0, maxLengthNum);
        }
        
        // Update counter text
        const characterCount = counter.querySelector('.character-count');
        if (characterCount) {
          characterCount.textContent = textareaElement.value.length.toString();
        }
      };
      
      // Add event listeners
      textareaElement.addEventListener('keyup', updateCounter);
      textareaElement.addEventListener('keydown', updateCounter);
      textareaElement.addEventListener('keypress', updateCounter);
      textareaElement.addEventListener('change', updateCounter);
    });
    
    // Cleanup event listeners on unmount
    return () => {
      textareas.forEach((textarea) => {
        const textareaElement = textarea as HTMLTextAreaElement;
        
        textareaElement.removeEventListener('keyup', () => {});
        textareaElement.removeEventListener('keydown', () => {});
        textareaElement.removeEventListener('keypress', () => {});
        textareaElement.removeEventListener('change', () => {});
        
        // Remove counter elements
        const counter = textareaElement.parentElement?.querySelector('.counter');
        if (counter) {
          counter.remove();
        }
      });
    };
  }, [containerRef]);
}