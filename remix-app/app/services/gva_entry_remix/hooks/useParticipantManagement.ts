import { useEffect, RefObject } from 'react';

/**
 * Interface for participant counts
 */
interface ParticipantCounts {
  victims: number;
  perpetrators: number;
}

/**
 * Hook to manage participant functionality
 * 
 * @param participantsContainerRef - Reference to the participants container element
 * @param victimCountRef - Reference to the victim count input element
 * @param perpetratorCountRef - Reference to the perpetrator count input element
 * @returns Object containing functions to update participant counts and clear participant forms
 */
export function useParticipantManagement(
  participantsContainerRef: RefObject<HTMLElement>,
  victimCountRef: RefObject<HTMLInputElement>,
  perpetratorCountRef: RefObject<HTMLInputElement>
) {
  /**
   * Updates the number of participants (victims and perpetrators)
   */
  const updateNumParticipants = (): ParticipantCounts => {
    let numVictims = 0;
    let numPerps = 0;
    
    const container = participantsContainerRef.current;
    if (!container) return { victims: numVictims, perpetrators: numPerps };
    
    // Get all participant forms
    const participantForms = container.querySelectorAll('.participant-form');
    
    participantForms.forEach((element) => {
      // Skip deleted participants
      const deleteInput = element.querySelector("input[name$='[delete]']") as HTMLInputElement;
      if (deleteInput && deleteInput.value === '1') return;
      
      // Check participant type
      const typeSelect = element.querySelector('select.participant-type') as HTMLSelectElement;
      if (typeSelect) {
        const selectedOption = typeSelect.options[typeSelect.selectedIndex];
        const type = selectedOption ? selectedOption.value : '';
        
        if (type === 'victim') {
          numVictims++;
        } else if (type === 'perpetrator') {
          numPerps++;
        }
      }
    });
    
    // Update the count inputs if they exist
    if (victimCountRef.current) {
      victimCountRef.current.value = numVictims.toString();
    }
    
    if (perpetratorCountRef.current) {
      perpetratorCountRef.current.value = numPerps.toString();
    }
    
    return { victims: numVictims, perpetrators: numPerps };
  };
  
  /**
   * Clears a participant form
   * 
   * @param formElement - The form element to clear
   */
  const clearParticipantForm = (formElement: HTMLElement) => {
    // Clear text inputs
    const textInputs = formElement.querySelectorAll('input[type=text]');
    textInputs.forEach((input) => {
      (input as HTMLInputElement).value = '';
    });
    
    // Uncheck checkboxes (note: in React, you'd typically use state for this)
    const checkboxes = formElement.querySelectorAll('input[type=checkbox]');
    checkboxes.forEach((checkbox) => {
      (checkbox as HTMLInputElement).checked = false;
    });
    
    // Reset selects
    const ageGroupSelect = formElement.querySelector('select.age-group') as HTMLSelectElement;
    if (ageGroupSelect) {
      ageGroupSelect.selectedIndex = 0;
      // In React, you'd dispatch a change event or update state
    }
    
    const relationshipSelect = formElement.querySelector('select.participant-relationship') as HTMLSelectElement;
    if (relationshipSelect) {
      relationshipSelect.selectedIndex = 0;
      // In React, you'd dispatch a change event or update state
    }
  };
  
  /**
   * Deletes (hides) a participant form
   * 
   * @param formElement - The form element to delete
   */
  const deleteParticipantForm = (formElement: HTMLElement) => {
    const deleteInput = formElement.querySelector("input[name$='[delete]']") as HTMLInputElement;
    if (deleteInput) {
      deleteInput.value = '1';
      formElement.style.display = 'none';
      updateNumParticipants();
    }
  };
  
  useEffect(() => {
    const container = participantsContainerRef.current;
    if (!container) return;
    
    // Wire up clear buttons
    const clearButtons = container.querySelectorAll('.button.icon-clear');
    clearButtons.forEach((button) => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        const form = (button as HTMLElement).closest('.participant-form');
        if (form) {
          clearParticipantForm(form as HTMLElement);
        }
      });
    });
    
    // Wire up delete buttons
    const deleteButtons = container.querySelectorAll('.button.icon-delete-hide-only');
    deleteButtons.forEach((button) => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        const form = (button as HTMLElement).closest('.participant-form');
        if (form) {
          deleteParticipantForm(form as HTMLElement);
        }
      });
    });
    
    // Initial count update
    updateNumParticipants();
    
    // Cleanup event listeners on unmount
    return () => {
      clearButtons.forEach((button) => {
        button.removeEventListener('click', (e) => {
          e.preventDefault();
          const form = (button as HTMLElement).closest('.participant-form');
          if (form) {
            clearParticipantForm(form as HTMLElement);
          }
        });
      });
      
      deleteButtons.forEach((button) => {
        button.removeEventListener('click', (e) => {
          e.preventDefault();
          const form = (button as HTMLElement).closest('.participant-form');
          if (form) {
            deleteParticipantForm(form as HTMLElement);
          }
        });
      });
    };
  }, [participantsContainerRef, victimCountRef, perpetratorCountRef]);
  
  return {
    updateNumParticipants,
    clearParticipantForm,
    deleteParticipantForm
  };
}