import { useEffect, RefObject } from 'react';

/**
 * Types for chart data structure
 */
interface ChartDay {
  rawDate: number;
  count: number;
}

interface ChartMonth {
  month: string;
  count: number;
  mass: number;
  c_killed: number;
  c_injured: number;
  days: ChartDay[];
}

interface ChartState {
  state: string;
  count: number;
}

interface ChartYear {
  year: string;
  count: number;
  mass: number;
  c_killed: number;
  c_injured: number;
  months: ChartMonth[];
  states: ChartState[];
}

interface ChartData {
  years: ChartYear[];
}

/**
 * Helper function to map days data for charts
 */
function mapDays(arr: ChartMonth[]) {
  const outa: Array<{ x: Date; y: number }[]> = [];
  let outb: Array<{ x: Date; y: number }> = [];

  for (let i = 0; i < arr.length; i++) {
    outa.push(
      arr[i].days.map(function (key) {
        return {
          x: new Date(key.rawDate * 1000),
          y: key.count,
        };
      })
    );
  }

  for (let i = 0; i < outa.length; i++) {
    outb = outb.concat(outa[i]);
  }

  return outb;
}

/**
 * Hook to initialize and render charts
 * 
 * @param data - The chart data
 * @param chartRefs - Object containing refs to chart canvas elements
 */
export function useCharts(
  data: ChartData,
  chartRefs: {
    incidents?: RefObject<HTMLCanvasElement>;
    imonths?: RefObject<HTMLCanvasElement>;
    imonthsD?: RefObject<HTMLCanvasElement>;
    mincidents?: RefObject<HTMLCanvasElement>;
    mmonths?: RefObject<HTMLCanvasElement>;
    ckincidents?: RefObject<HTMLCanvasElement>;
    ckmonths?: RefObject<HTMLCanvasElement>;
    ciincidents?: RefObject<HTMLCanvasElement>;
    cimonths?: RefObject<HTMLCanvasElement>;
    allstates?: RefObject<HTMLCanvasElement>;
  }
): void {
  useEffect(() => {
    // Dynamically import Chart.js
    import('chart.js').then((ChartModule) => {
      const Chart = ChartModule.default;
      
      // Total incidents by year
      if (chartRefs.incidents?.current) {
        new Chart(chartRefs.incidents.current, {
          type: 'bar',
          data: {
            labels: data.years.map(row => row.year),
            datasets: [
              {
                label: 'Total Incidents by year',
                data: data.years.map(row => row.count)
              }
            ]
          }
        });
      }

      // Incidents by month
      if (chartRefs.imonths?.current) {
        const months = data.years.map(function(row) { 
          return {
            'label': row.year, 
            'data': row.months.map(function(key){
              return {
                x: key.month,
                y: key.count
              }
            }) 
          }; 
        });

        new Chart(chartRefs.imonths.current, {
          type: 'bar',
          data: {
            datasets: months
          }
        });
      }

      // Incidents by day
      if (chartRefs.imonthsD?.current) {
        const days = data.years.map(function(row) { 
          return {
            'label': row.year, 
            'data': mapDays(row.months) 
          }; 
        });

        new Chart(chartRefs.imonthsD.current, {
          type: 'line',
          data: {
            datasets: days
          },
          options: {
            scales: {
              x: {
                type: 'timeseries',
                time: {
                  unit: 'day',
                  tooltipFormat: 'MM-dd-yyyy'
                },
                ticks: {
                  maxTicksLimit: 10
                }
              }
            }
          }
        });
      }

      // Mass shootings by year
      if (chartRefs.mincidents?.current) {
        new Chart(chartRefs.mincidents.current, {
          type: 'bar',
          data: {
            labels: data.years.map(row => row.year),
            datasets: [
              {
                label: 'Mass Shootings by year',
                data: data.years.map(row => row.mass)
              }
            ]
          }
        });
      }

      // Mass shootings by month
      if (chartRefs.mmonths?.current) {
        const mmonths = data.years.map(function(row) { 
          return {
            'label': row.year, 
            'data': row.months.map(function(key){
              return {
                x: key.month,
                y: key.mass
              }
            }) 
          }; 
        });

        new Chart(chartRefs.mmonths.current, {
          type: 'bar',
          data: {
            datasets: mmonths
          }
        });
      }

      // Children killed by year
      if (chartRefs.ckincidents?.current) {
        new Chart(chartRefs.ckincidents.current, {
          type: 'bar',
          data: {
            labels: data.years.map(row => row.year),
            datasets: [
              {
                label: 'Children Killed by year',
                data: data.years.map(row => row.c_killed)
              }
            ]
          }
        });
      }

      // Children killed by month
      if (chartRefs.ckmonths?.current) {
        const ckmonths = data.years.map(function(row) { 
          return {
            'label': row.year, 
            'data': row.months.map(function(key){
              return {
                x: key.month,
                y: key.c_killed
              }
            }) 
          }; 
        });

        new Chart(chartRefs.ckmonths.current, {
          type: 'bar',
          data: {
            datasets: ckmonths
          }
        });
      }

      // Children injured by year
      if (chartRefs.ciincidents?.current) {
        new Chart(chartRefs.ciincidents.current, {
          type: 'bar',
          data: {
            labels: data.years.map(row => row.year),
            datasets: [
              {
                label: 'Children Injured by year',
                data: data.years.map(row => row.c_injured)
              }
            ]
          }
        });
      }

      // Children injured by month
      if (chartRefs.cimonths?.current) {
        const cimonths = data.years.map(function(row) { 
          return {
            'label': row.year, 
            'data': row.months.map(function(key){
              return {
                x: key.month,
                y: key.c_injured
              }
            }) 
          }; 
        });

        new Chart(chartRefs.cimonths.current, {
          type: 'bar',
          data: {
            datasets: cimonths
          }
        });
      }

      // Incidents by state
      if (chartRefs.allstates?.current) {
        const states = data.years.map(function(row) { 
          return {
            'label': row.year, 
            'data': row.states.map(function(key){
              return {
                x: key.state,
                y: key.count
              }
            }) 
          }; 
        });

        new Chart(chartRefs.allstates.current, {
          type: 'bar',
          data: {
            datasets: states
          }
        });
      }
    });

    // Cleanup function to destroy charts when component unmounts
    return () => {
      // Chart.js automatically handles cleanup when the canvas is destroyed
    };
  }, [data, chartRefs]);
}