# GVA Entry Module Conversion

This folder contains the conversion of the Drupal module `gva_entry` to a Remix.js compatible service.

## Original Module Structure
The original Drupal module (`gva_entry`) has the following structure:
- JavaScript files:
  - age-age-group.js
  - charts.js
  - entry.js
  - filters.js
  - incident-type.js
  - participants.js
  - quick-filter.js
  - refresh.js
  - submit-on-custom.js
- Drupal specific files:
  - gva_entry.info
  - gva_entry.install
  - gva_entry.module
- Directories:
  - callbacks
  - doc
  - helpers
  - hooks
  - lib
  - node_modules
  - themes
  - vendor
- Package management:
  - composer.json
  - composer.lock

## Conversion Progress

### Completed
- [x] Initial setup of the conversion folder
- [x] Analysis of the original module structure
- [x] Planning the conversion approach
- [x] Set up basic directory structure for the conversion
- [x] Created index.ts file to export all components, hooks, and utilities
- [x] Converted JavaScript files to TypeScript:
  - [x] refresh.js → utils/refresh.ts
  - [x] submit-on-custom.js → hooks/useSubmitOnCustom.ts
  - [x] participants.js → hooks/useGenderCheckboxes.ts + components/GenderCheckboxes.tsx
  - [x] age-age-group.js → hooks/useAgeGroupSelection.ts + components/AgeGroupSelector.tsx
  - [x] quick-filter.js → utils/urlUtils.ts + hooks/useQuickFilter.ts + components/QuickFilter.tsx
  - [x] incident-type.js → utils/incidentTypeUtils.ts + hooks/useIncidentTypeCheckboxes.ts + components/IncidentTypeReminders.tsx + components/IncidentTypeCheckboxes.tsx
  - [x] charts.js → hooks/useCharts.ts + components/Charts.tsx
  - [x] entry.js → hooks/useParticipantManagement.ts + hooks/useGunManagement.ts + hooks/useCharacterCounter.ts + hooks/useFormDisableOnAjax.ts + hooks/useEntryLock.ts
  - [x] filters.js → utils/colorUtils.ts + hooks/useFilterBackgroundColors.ts + hooks/useDatePickers.ts + hooks/useFilterHelpTips.ts + hooks/useFilterDropdowns.ts

### In Progress
- [x] Adapt Drupal-specific functionality to Remix.js
  - [x] Create Prisma model for gva_entry_locks table
- [x] Implement callbacks as API endpoints
  - [x] Convert entry.php to Remix API endpoints
    - [x] Create API endpoints for lock_poll, reconnect, and disconnect functions
  - [x] Convert autocomplete.php to Remix API endpoints
    - [x] All five functions implemented:
      - [x] action_cities -> admin.autocomplete.city.$state.tsx
      - [x] action_counties -> admin.autocomplete.county.$state.$city.tsx
      - [x] action_address -> admin.autocomplete.address.tsx
      - [x] action_name -> admin.autocomplete.name.tsx
      - [x] action_airport -> admin.autocomplete.airport.$state.tsx
  - [x] Convert export.php to Remix API endpoints
    - [x] Export functionality already implemented in search.$id.export.tsx
    - [x] Note: The approach in Remix.js is different from the original Drupal module. Instead of having a separate page for export completion, the export is handled directly as a download in the browser.
  - [x] Convert import.php to Remix API endpoints
    - [x] Created Prisma model for gva_entry_imports_queue table
    - [x] Created import-queue.server.ts model file
    - [x] Created admin.import.status.$filename.tsx API endpoint
  - [x] Convert incident.php to Remix API endpoints
    - [x] Created admin.incident.delete.$id.tsx API endpoint
  - [x] Convert query.php to Remix API endpoints
    - [x] Created admin.query.delete.$id.tsx API endpoint
  - [x] Convert twitter.php to Remix API endpoints
    - [x] Created admin.twitter.tweet.$id.tsx API endpoint

### To Do
- [x] Convert helpers to utility functions
  - [x] Added header rendering functionality to IncidentTypeCheckboxes.tsx
  - [x] Created csvUtils.ts for CSV conversion functionality
  - [x] Updated incidentTypeUtils.ts to include header property
- [x] Adapt hooks to Remix.js lifecycle methods
  - [x] Converted useFormDisableOnAjax to use Remix.js useTransition hook
  - [x] Converted useEntryLock to use Remix.js useFetcher hook
  - [x] Removed direct DOM manipulation in favor of React patterns
  - [x] Updated API endpoint paths to follow Remix.js conventions
- [x] Migrate themes to Remix.js styling approach
  - [x] Created Layout.tsx with Section, Row, and Column components to replace form.inc
  - [x] Created FilterResults.tsx component to replace results.inc
  - [x] Hierarchical checkboxes already implemented in IncidentTypeCheckboxes.tsx
  - [x] Updated index.ts to export the new components
- [x] Test converted functionality
  - [x] Created test-hooks.tsx with example components for testing
  - [x] Added documentation on how to use the converted hooks in Remix routes
- [x] Implement query builder and related components
  - [x] Created FilterGroup.server.ts to handle filter groups
  - [x] Created QueryBuilder.server.ts for building and managing queries
  - [x] Created Column.server.ts and column interfaces for handling columns
  - [x] Created ColumnFactory.server.ts for creating column instances
  - [x] Created QueryFilterable.server.ts and QueryFilterFactory.server.ts for handling filters

## Notes
- The implementation into the Remix app will happen later
- This conversion focuses on making the Drupal module compatible with Remix.js without changing other parts of the application

## Recent Updates
- Created directory structure for column implementations (Base, Counts, Districts, Location, Participant, Stats, Templates)
- Created directory structure for filter implementations (EntryFields, ExtraFilters)
- Updated ColumnFactory.server.ts to implement the factory method with basic column implementations
- Updated QueryFilterFactory.server.ts to implement the factory method with a basic filter implementation
- Note: These are placeholder implementations that would need to be expanded with actual column and filter implementations for each specific type
