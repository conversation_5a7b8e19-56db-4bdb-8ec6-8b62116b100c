import React, { useRef } from 'react';
import { useFormDisableOnAjax } from './hooks/useFormDisableOnAjax';
import { useEntryLock } from './hooks/useEntryLock';

/**
 * Test component for useFormDisableOnAjax hook
 */
export function FormDisableTest() {
  const formRef = useRef<HTMLFormElement>(null);
  
  // Use the hook
  useFormDisableOnAjax(formRef);
  
  return (
    <form ref={formRef}>
      <input type="text" name="test" />
      <button type="submit">Submit</button>
    </form>
  );
}

/**
 * Test component for useEntryLock hook
 */
export function EntryLockTest() {
  // Use the hook
  const { lockState, disconnect, reconnect } = useEntryLock('123', (state) => {
    console.log('Lock state changed:', state);
  });
  
  return (
    <div>
      <div>Lock Status: {lockState.isLocked ? 'Locked' : 'Unlocked'}</div>
      <div>Connection Status: {lockState.isDisconnected ? 'Disconnected' : 'Connected'}</div>
      {lockState.errorMessage && <div>Error: {lockState.errorMessage}</div>}
      
      <button onClick={disconnect}>Disconnect</button>
      <button onClick={reconnect}>Reconnect</button>
    </div>
  );
}

/**
 * This file is for testing purposes only.
 * It demonstrates how to use the converted hooks in a Remix.js environment.
 * 
 * To test these components:
 * 1. Import them into a Remix route
 * 2. Render them in the route component
 * 3. Verify that they work as expected
 * 
 * Example:
 * ```tsx
 * import { FormDisableTest, EntryLockTest } from '~/services/gva_entry_remix/test-hooks';
 * 
 * export default function TestRoute() {
 *   return (
 *     <div>
 *       <h1>Form Disable Test</h1>
 *       <FormDisableTest />
 *       
 *       <h1>Entry Lock Test</h1>
 *       <EntryLockTest />
 *     </div>
 *   );
 * }
 * ```
 */