import { FilterGroup } from '../FilterGroup.server';

/**
 * Interface for filterable objects
 */
export interface QueryFilterable {
  /**
   * Gets the filter callback for the current filter class.
   *
   * @returns The filter callback
   */
  getFilterCallback(): (...args: any[]) => any;

  /**
   * Alters the current query to add any filtering logic.
   *
   * @param query - The query to alter.
   * @param condition - The current condition class to attach conditions to.
   * @param havingCondition - The current condition class to attach having conditions to.
   * @param comparator - The comparator from the filter configuration.
   * @param arguments - Any additional arguments passed to the filter.
   */
  filter(
    query: any,
    condition: any,
    havingCondition: any,
    comparator: string,
    args: Record<string, any>
  ): void;

  /**
   * Gets the callback used when rendering the form.
   *
   * @returns The form callback
   */
  getFormCallback(): (...args: any[]) => any;

  /**
   * Gets the render array representing the form to be displayed on the
   * filter form.
   *
   * @param id - The UUID of the field to render.
   * @param arguments - An array of arguments to use on the filter.
   * @param comparator - The comparator used to render the filter form.
   * @returns A render array representing the fields to render.
   */
  filterForm(
    id: string,
    args: Record<string, any>,
    comparator: string
  ): Record<string, any>;

  /**
   * Gets the method to be called when filling in the defaults on
   * the filter form.
   *
   * @returns The form defaults callback
   */
  getFormDefaultsCallback(): (...args: any[]) => any;

  /**
   * Fills in the default values (given a list of arguments) on the
   * filter form.
   *
   * @param form - The render array returned from the main builder function.
   * @param id - The UUID of the query filter.
   * @param arguments - The running array of arguments passed to the filter.
   * @param comparator - The comparator.
   */
  filterFormDefaults(
    form: Record<string, any>,
    id: string,
    args: Record<string, any>,
    comparator: string
  ): void;

  /**
   * Gets the default comparator for new fields.
   *
   * @param id - The ID of the filter.
   * @param arguments - The arguments that are passed to the filter.
   * @returns The default comparator
   */
  getDefaultComparator(
    id: string,
    args: Record<string, any>
  ): string;

  /**
   * Gets the callback function for getting the list of comparators.
   *
   * @returns The comparators callback
   */
  getComparatorsCallback(): (...args: any[]) => any;

  /**
   * Gets an array of supported comparators for this filter.
   *
   * @param id - The ID of the current filter.
   * @param arguments - An array of arguments supplied to the filter.
   * @returns An array of supported comparators.
   */
  comparators(
    id: string,
    args: Record<string, any>
  ): string[];

  /**
   * Gets the callback function for validating the filter results.
   *
   * @returns The validate callback
   */
  getValidateCallback(): (...args: any[]) => any;

  /**
   * Validates the filter.
   *
   * @param comparator - The comparator the user selected.
   * @param arguments - An array of arguments representing the value of the filter.
   * @returns Whether the filter is valid
   */
  validateFilter(
    comparator: string,
    args: Record<string, any>
  ): boolean;

  /**
   * Gets the short name of the filter (the name that would be passed to the factory).
   *
   * @returns The short name
   */
  getShortName(): string;

  /**
   * Translates form state values to a filter group filter.
   *
   * @param group - The query builder, passed by reference.
   * @param id - The ID of the filter.
   * @param values - The values from the form state.
   */
  translate(
    group: FilterGroup,
    id: string,
    values: Record<string, any>
  ): void;

  /**
   * Given an array representing the filter, process it and return the new
   * value.
   *
   * @param filter - An array representing the current filter.
   * @returns The current filter's configuration array.
   */
  translateOldValue(
    filter: Record<string, any>
  ): Record<string, any>;

  /**
   * Determines whether or not the current user has access to use this filter.
   *
   * @returns Whether the user has access
   */
  access(): boolean;
}

/**
 * Abstract class implementing the QueryFilterable interface
 */
export abstract class QueryFilterableBase implements QueryFilterable {
  /**
   * Gets the filter callback for the current filter class.
   *
   * @returns The filter callback
   */
  public getFilterCallback(): (...args: any[]) => any {
    return this.filter.bind(this);
  }

  /**
   * Alters the current query to add any filtering logic.
   *
   * @param query - The query to alter.
   * @param condition - The current condition class to attach conditions to.
   * @param havingCondition - The current condition class to attach having conditions to.
   * @param comparator - The comparator from the filter configuration.
   * @param arguments - Any additional arguments passed to the filter.
   */
  public filter(
    query: any,
    condition: any,
    havingCondition: any,
    comparator: string,
    args: Record<string, any>
  ): void {
    // Intentionally left blank for now
  }

  /**
   * Gets the callback used when rendering the form.
   *
   * @returns The form callback
   */
  public getFormCallback(): (...args: any[]) => any {
    return this.filterForm.bind(this);
  }

  /**
   * Gets the render array representing the form to be displayed on the
   * filter form.
   *
   * @param id - The UUID of the field to render.
   * @param arguments - An array of arguments to use on the filter.
   * @param comparator - The comparator used to render the filter form.
   * @returns A render array representing the fields to render.
   */
  public filterForm(
    id: string,
    args: Record<string, any>,
    comparator: string
  ): Record<string, any> {
    return {};
  }

  /**
   * Gets the method to be called when filling in the defaults on
   * the filter form.
   *
   * @returns The form defaults callback
   */
  public getFormDefaultsCallback(): (...args: any[]) => any {
    return this.filterFormDefaults.bind(this);
  }

  /**
   * Fills in the default values (given a list of arguments) on the
   * filter form.
   *
   * @param form - The render array returned from the main builder function.
   * @param id - The UUID of the query filter.
   * @param arguments - The running array of arguments passed to the filter.
   * @param comparator - The comparator.
   */
  public filterFormDefaults(
    form: Record<string, any>,
    id: string,
    args: Record<string, any>,
    comparator: string
  ): void {
    if (form['#type'] && args['value']) {
      form['#default_value'] = args['value'];
    } else {
      this.processFormKey(form, {}, args);
    }
  }

  /**
   * Sets the default value on a form key given a value and an
   * array of parents.
   *
   * @param form - The form object.
   * @param parents - The current running list of parents.
   * @param value - The current value.
   */
  protected processFormKey(
    form: Record<string, any>,
    parents: Record<string, any>,
    value: any
  ): void {
    if (typeof value === 'object' && value !== null) {
      Object.entries(value).forEach(([key, item]) => {
        this.processFormKey(form, { ...parents, [key]: item }, item);
      });
    } else {
      // In a real implementation, we would set the nested value
      // For now, we'll just do nothing
    }
  }

  /**
   * Converts a date to a timestamp
   *
   * @param row - The date information
   * @param timeOfDay - Whether to use the start or end of the day
   * @returns The timestamp
   */
  protected convertDateToTimestamp(
    row: Record<string, any> = {},
    timeOfDay: 'start' | 'end' = 'start'
  ): number | false {
    // Check if supplied date was a string
    if (row['string']) {
      const dateSegments = row['string'].split('/');
      if (dateSegments.length === 1) {
        row['year'] = dateSegments[0];
      } else if (dateSegments.length === 2) {
        row['month'] = dateSegments[0];
        row['year'] = dateSegments[1];
      } else {
        row['month'] = dateSegments[0];
        row['day'] = dateSegments[1];
        row['year'] = dateSegments[2];
      }
    }

    const month = row['month'] ? parseInt(row['month']) : 1;
    const day = row['day'] ? parseInt(row['day']) : 1;
    const year = row['year'] ? parseInt(row['year']) : 0;

    // They must at least supply a year.
    if (year === 0) {
      return false;
    }

    // Use whatever they gave to provide a timestamp.
    const date = new Date(year, month - 1, day);

    if (timeOfDay === 'start') {
      date.setHours(0, 0, 0, 0); // Make sure we're at the beginning of the day.
    } else {
      date.setHours(23, 59, 59, 999); // Make sure we're at the end of the day.
    }

    return date.getTime() / 1000; // Convert to seconds for compatibility with PHP
  }

  /**
   * Gets the default comparator for new fields.
   *
   * @param id - The ID of the filter.
   * @param arguments - The arguments that are passed to the filter.
   * @returns The default comparator
   */
  public getDefaultComparator(
    id: string,
    args: Record<string, any>
  ): string {
    const comparators = this.comparators(id, args);
    return comparators.length > 0 ? comparators[0] : 'is exactly';
  }

  /**
   * Gets the callback function for getting the list of comparators.
   *
   * @returns The comparators callback
   */
  public getComparatorsCallback(): (...args: any[]) => any {
    return this.comparators.bind(this);
  }

  /**
   * Gets an array of supported comparators for this filter.
   *
   * @param id - The ID of the current filter.
   * @param arguments - An array of arguments supplied to the filter.
   * @returns An array of supported comparators.
   */
  public comparators(
    id: string,
    args: Record<string, any>
  ): string[] {
    return [
      FilterGroup.COMPARATOR_IS,
      FilterGroup.COMPARATOR_IS_NOT,
    ];
  }

  /**
   * Gets the callback function for validating the filter results.
   *
   * @returns The validate callback
   */
  public getValidateCallback(): (...args: any[]) => any {
    return this.validateFilter.bind(this);
  }

  /**
   * Validates the filter.
   *
   * @param comparator - The comparator the user selected.
   * @param arguments - An array of arguments representing the value of the filter.
   * @returns Whether the filter is valid
   */
  public validateFilter(
    comparator: string,
    args: Record<string, any>
  ): boolean {
    return true;
  }

  /**
   * Gets the short name of the filter (the name that would be passed to the factory).
   *
   * @returns The short name
   */
  public getShortName(): string {
    // In a real implementation, we would get the class name
    // For now, we'll just return a placeholder
    return 'Filter';
  }

  /**
   * Translates form state values to a filter group filter.
   *
   * @param group - The query builder, passed by reference.
   * @param id - The ID of the filter.
   * @param values - The values from the form state.
   */
  public translate(
    group: FilterGroup,
    id: string,
    values: Record<string, any>
  ): void {
    const weight = values.outer_filter?.weight || 0;
    const comparator = values.outer_filter?.comparator || null;
    const args = values.outer_filter ? { ...values.outer_filter } : {};

    // Remove the comparator and weight from the arguments if they're there.
    delete args.comparator;
    delete args.weight;

    // Get the default comparator and attempt to set it if we don't already have one.
    const finalComparator = comparator || this.getDefaultComparator(id, args);

    // Add the filter.
    group.addFilter(this.getShortName(), weight, finalComparator, args, id);
  }

  /**
   * Given an array representing the filter, process it and return the new
   * value.
   *
   * @param filter - An array representing the current filter.
   * @returns The current filter's configuration array.
   */
  public translateOldValue(
    filter: Record<string, any> = {}
  ): Record<string, any> {
    return filter;
  }

  /**
   * Validation function to ensure that single-value filters actually have a value.
   *
   * @param arguments - The arguments passed to the filter.
   * @returns Whether the filter is valid
   */
  protected static validateSingleValue(
    args: Record<string, any> = {}
  ): boolean {
    if (!('value' in args)) return false;
    if (!args.value) return false;
    return true;
  }

  /**
   * Given a comparator, translates it into something that SQL would understand.
   *
   * @param comparator - The comparator.
   * @returns The SQL comparison operator, or false if not supported
   */
  protected static translateComparator(
    comparator: string
  ): string | false {
    if (comparator === 'is greater than') return '>';
    if (comparator === 'is less than') return '<';
    if (comparator === 'is equal to') return '=';
    if (comparator === 'is not equal to') return '!=';
    return false;
  }

  /**
   * Adds a join to the specified query object, checking for duplicates at the same time.
   *
   * @param query - The query to add the join to.
   * @param joinType - The type of join to add (LEFT, INNER, etc).
   * @param table - The name of the table to join on.
   * @param alias - The alias to give the table.
   * @param condition - The conditions to join on.
   * @param arguments - Any arguments to pass to the conditions.
   * @returns The alias of the joined table, or false if the join already exists
   */
  protected static addJoinToQuery(
    query: any,
    joinType: string,
    table: string,
    alias: string,
    condition: string,
    args: Record<string, any> = {}
  ): string | false {
    // In a real implementation, we would check for duplicate joins
    // For now, we'll just return the alias
    return alias;
  }

  /**
   * Determines whether or not the current user has access to use this filter.
   *
   * @returns Whether the user has access
   */
  public access(): boolean {
    return true;
  }
}