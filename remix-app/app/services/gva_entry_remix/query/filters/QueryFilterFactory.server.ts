import { QueryFilterable, QueryFilterableBase } from './QueryFilterable.server';

/**
 * Exception thrown when a query filter type does not exist
 */
export class QueryFilterFactoryException extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'QueryFilterFactoryException';
  }
}

/**
 * Factory class for creating filter instances
 */
export class QueryFilterFactory {
  private static instance: QueryFilterFactory;

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {}

  /**
   * Gets the singleton instance of the factory
   * 
   * @returns The singleton instance
   */
  public static getInstance(): QueryFilterFactory {
    if (!QueryFilterFactory.instance) {
      QueryFilterFactory.instance = new QueryFilterFactory();
    }
    return QueryFilterFactory.instance;
  }

  /**
   * Factory function for filters.
   *
   * @param filterType - The type of filter to load.
   * @returns The filterable entry field class.
   * @throws QueryFilterFactoryException
   */
  public factory(filterType: string): QueryFilterable {
    try {
      // Determine if this is an EntryField or ExtraFilter
      let category = 'EntryFields';
      let name = filterType;

      // If the filter type contains a dot, it's in the format Category.Name
      if (filterType.includes('.')) {
        [category, name] = filterType.split('.');
      }

      // For now, we'll return a basic implementation
      // This would need to be expanded with actual filter implementations
      return new BasicQueryFilter(filterType);
    } catch (error) {
      console.error(`Error creating filter ${filterType}:`, error);
      throw new QueryFilterFactoryException(`The query filter type ${filterType} does not exist.`);
    }
  }
}

/**
 * Basic implementation of QueryFilterable for testing purposes
 */
class BasicQueryFilter extends QueryFilterableBase {
  private filterType: string;

  constructor(filterType: string) {
    super();
    this.filterType = filterType;
  }

  public getShortName(): string {
    return this.filterType;
  }

  public filter(
    query: any,
    condition: any,
    havingCondition: any,
    comparator: string,
    args: Record<string, any>
  ): void {
    // Basic implementation
    console.log(`Filtering with ${this.filterType}, comparator: ${comparator}`);
  }

  public filterForm(
    id: string,
    args: Record<string, any>,
    comparator: string
  ): Record<string, any> {
    // Basic implementation
    return {
      '#type': 'textfield',
      '#title': `Filter by ${this.filterType}`,
      '#default_value': args.value || ''
    };
  }

  public comparators(
    id: string,
    args: Record<string, any>
  ): string[] {
    // Basic implementation
    return [
      'is exactly',
      'is not',
      'contains',
      'does not contain',
      'starts with',
      'ends with'
    ];
  }
}
