import { v4 as uuidv4 } from 'uuid';
import { prisma } from '~/db.server';
import { FilterGroup, FilterConfig } from './FilterGroup.server';

/**
 * Interface for column template
 */
export interface ColumnTemplate {
  id: string;
  name: string;
  columns: string[];
  type: string;
  supportedExportTypes: string[];
  access: () => boolean;
}

/**
 * Interface for query configuration
 */
export interface QueryConfig {
  uuid?: string;
  type?: string;
  filters?: Record<string, FilterConfig>;
  grouping?: string;
  column_templates?: Record<string, string>;
  [key: string]: any;
}

/**
 * QueryBuilder class for building and managing queries
 */
export class QueryBuilder extends FilterGroup {
  static readonly QUERY_TYPE_INCIDENTS = 'incidents';
  static readonly QUERY_TYPE_PARTICIPANTS = 'participants';
  static readonly QUERY_TYPE_STATS = 'stats';

  /**
   * The current query definition.
   */
  protected query: QueryConfig = {};

  /**
   * The UUID representing the current query.
   */
  protected query_id: string = '';

  /**
   * The number of views the query has gotten.
   */
  public query_views: number = 0;

  /**
   * The last time the query was viewed.
   */
  public query_viewed: number | boolean = false;

  /**
   * Whether or not the query is locked from deletion.
   */
  public locked: boolean = false;

  /**
   * Whether or not the query is locked from edits.
   */
  public editlock: boolean = false;

  /**
   * The label for the query.
   */
  public label: string = '';

  /**
   * The path alias for the query.
   */
  public path: string = '';

  /**
   * Whether or not to limit results for anonymous users.
   */
  public limit_results: boolean = true;

  /**
   * The type of query. Defaults to QUERY_TYPE_INCIDENTS.
   */
  public type: string = QueryBuilder.QUERY_TYPE_INCIDENTS;

  /**
   * The base query conjunctive type. "And" or "Or"
   */
  public grouping: string = 'And';

  /**
   * The quick filter settings
   */
  public quick_filters: any = null;

  /**
   * The custom column template configuration for the current query.
   */
  public column_templates: Record<string, string> = {};

  /**
   * The override column template configuration for the current query.
   */
  public default_column_template_override: Record<string, string> = {};

  /**
   * Constructor
   */
  constructor(
    query: QueryConfig = {},
    views: number = 0,
    viewed: number | boolean = false,
    queryType: string | boolean = false,
    columnTemplates: Record<string, string> = {},
    grouping: string | boolean = false,
    defaultColumnTemplateOverride: Record<string, string> = {}
  ) {
    super();
    
    this.query = query;
    this.query_views = views;
    
    if (!viewed) {
      this.query_viewed = Date.now();
    } else {
      this.query_viewed = viewed;
    }

    // Process the type
    if (queryType === false) {
      if (this.query.type) {
        queryType = this.query.type;
      } else {
        queryType = QueryBuilder.QUERY_TYPE_INCIDENTS;
      }
    }
    this.type = queryType as string;

    // Process the grouping
    if (grouping === false) {
      if (this.query.grouping) {
        grouping = this.query.grouping;
      } else {
        grouping = 'And';
      }
    }
    this.grouping = grouping as string;

    // Process the column template configuration
    if (this.query.column_templates) {
      this.column_templates = { ...this.query.column_templates, ...columnTemplates };
    } else {
      this.column_templates = columnTemplates;
    }

    this.default_column_template_override = defaultColumnTemplateOverride;

    if (this.query.uuid) {
      this.query_id = this.query.uuid;
    } else {
      this.query_id = uuidv4();
    }

    if (this.query.filters) {
      this.filters = this.query.filters;
      this.translateOldFilters();
    }
  }

  /**
   * Translates old filter format to new format
   */
  protected translateOldFilters(): void {
    const newFilters: Record<string, FilterConfig> = {};
    
    Object.entries(this.filters).forEach(([index, filter]) => {
      if (typeof filter === 'object' && filter !== null) {
        // In a real implementation, we would use a factory to create the filter
        // For now, we'll just pass through the filter
        newFilters[index] = filter;
      }
    });
    
    this.filters = newFilters;
  }

  /**
   * Import an existing query definition into this class to manage.
   *
   * @param query - The query definition to manage.
   * @returns A new QueryBuilder instance
   */
  public static import(query: QueryConfig = {}): QueryBuilder {
    return new QueryBuilder(query);
  }

  /**
   * Loads an existing query from the database.
   *
   * @param quuid - The UUID of the query to load.
   * @returns The query if found, null otherwise
   */
  public static async load(quuid: string): Promise<QueryBuilder | null> {
    try {
      const queryRecord = await prisma.gva_entry_queries.findUnique({
        where: { quuid }
      });

      if (!queryRecord) {
        return null;
      }

      const query = JSON.parse(queryRecord.query as string);
      query.uuid = queryRecord.quuid;
      
      const columnTemplates = queryRecord.column_templates 
        ? JSON.parse(queryRecord.column_templates as string) 
        : {};
        
      const defaultColumnTemplateOverride = queryRecord.default_column_template_override 
        ? JSON.parse(queryRecord.default_column_template_override as string) 
        : {};

      const result = new QueryBuilder(
        query,
        queryRecord.views as number,
        queryRecord.viewed as number,
        queryRecord.type as string,
        columnTemplates,
        false,
        defaultColumnTemplateOverride
      );

      result.locked = !!queryRecord.locked;
      result.editlock = !!queryRecord.editlock;
      result.label = queryRecord.label as string;
      result.limit_results = !!queryRecord.limit_results;
      result.quick_filters = queryRecord.quick_filters;
      
      // In Remix, we'll handle path aliases differently
      result.path = '';

      return result;
    } catch (error) {
      console.error('Error loading query:', error);
      return null;
    }
  }

  /**
   * Deletes a query.
   *
   * @param quuid - The UUID of the query to delete.
   */
  public static async delete(quuid: string): Promise<void> {
    try {
      await prisma.gva_entry_queries.delete({
        where: { quuid }
      });
      
      // In Remix, we'll handle path aliases differently
    } catch (error) {
      console.error('Error deleting query:', error);
    }
  }

  /**
   * Exports the current query to an object, performing some filtering first.
   *
   * @returns The exported query.
   */
  public export(): QueryConfig {
    const query = { ...this.query };
    query.uuid = this.query_id;
    query.filters = this.getFilters();
    query.type = this.type;
    query.column_templates = this.column_templates;
    return query;
  }

  /**
   * Return the query id
   *
   * @returns The query id.
   */
  public getQueryId(): string {
    return this.query_id;
  }

  /**
   * Saves the query to the database.
   */
  public async save(): Promise<void> {
    try {
      // In a real implementation, we would get the current user
      // For now, we'll just use a placeholder
      const userId = 1;
      const ipAddress = '127.0.0.1';

      await prisma.gva_entry_queries.upsert({
        where: { quuid: this.query_id },
        update: {
          query: JSON.stringify(this.export()),
          views: this.query_views,
          viewed: this.query_viewed as number,
          locked: this.locked ? 1 : 0,
          editlock: this.editlock ? 1 : 0,
          limit_results: this.limit_results ? 1 : 0,
          label: this.label,
          type: this.type,
          column_templates: JSON.stringify(this.column_templates),
          last_edited_by: userId,
          last_edited_ip: ipAddress,
          quick_filters: this.quick_filters,
        },
        create: {
          quuid: this.query_id,
          query: JSON.stringify(this.export()),
          views: this.query_views,
          viewed: this.query_viewed as number,
          locked: this.locked ? 1 : 0,
          editlock: this.editlock ? 1 : 0,
          limit_results: this.limit_results ? 1 : 0,
          label: this.label,
          type: this.type,
          column_templates: JSON.stringify(this.column_templates),
          last_edited_by: userId,
          last_edited_ip: ipAddress,
          quick_filters: this.quick_filters,
        }
      });

      // In Remix, we'll handle path aliases and cache clearing differently
    } catch (error) {
      console.error('Error saving query:', error);
    }
  }

  /**
   * Adds a view to the currently loaded query.
   */
  public async addView(): Promise<void> {
    this.query_views++;
    this.query_viewed = Date.now();

    try {
      await prisma.gva_entry_queries.update({
        where: { quuid: this.query_id },
        data: {
          views: this.query_views,
          viewed: this.query_viewed as number,
        }
      });
    } catch (error) {
      console.error('Error adding view to query:', error);
    }
  }

  /**
   * Gets the ID of the query.
   */
  public uuid(): string {
    return this.query_id;
  }

  /**
   * Gets the number of times the query was viewed.
   */
  public views(): number {
    return this.query_views;
  }

  /**
   * Gets the query property
   */
  public query_(): QueryConfig {
    return this.query;
  }

  /**
   * Gets the last time the query was viewed.
   */
  public lastViewed(): number | boolean {
    return this.query_viewed;
  }

  /**
   * Gets the machine-readable query array representing the current query.
   *
   * @returns The machine-readable query array.
   */
  public machine(): any[] {
    const machineQuery: any[] = [];

    // Add the individual filters themselves.
    Object.entries(this.getFilters()).forEach(([id, filter]) => {
      machineQuery.push(this.processFilter(id, filter));
    });

    // Sort the filters.
    this.sortFilters(machineQuery);

    // Return the result.
    return machineQuery;
  }

  /**
   * Given an exporter, returns the column template for it and the current query.
   *
   * @param exporter - The name of the exporter.
   * @returns The column template
   */
  public async getColumnTemplate(exporter: string): Promise<any> {
    // In a real implementation, we would load the column template from the database
    // For now, we'll just return a placeholder
    return {
      name: 'Default Template',
      columns: [],
      type: this.type,
      supportedExportTypes: [exporter],
      access: () => true
    };
  }

  /**
   * Gets the default column template configuration.
   *
   * @param type - The type of query to get the column templates for.
   * @returns The default column template configuration
   */
  public static getDefaultColumnTemplates(type: string): Record<string, any> {
    if (type === QueryBuilder.QUERY_TYPE_INCIDENTS || type === QueryBuilder.QUERY_TYPE_STATS) {
      return {
        default: {
          template: 'Default Incident Columns',
        },
        authenticated: {
          roles: ['authenticated user'],
          template: 'Logged In Incident Columns',
        },
        admin: {
          roles: ['administrator', 'entry user'],
          template: 'Administrative Incident Columns',
        },
      };
    } else if (type === QueryBuilder.QUERY_TYPE_PARTICIPANTS) {
      return {
        default: {
          template: 'Default Participant Columns'
        },
        authenticated: {
          roles: ['authenticated user'],
          template: 'Logged In Participant Columns',
        },
        admin: {
          roles: ['administrator', 'entry user'],
          template: 'Administrative Participant Columns',
        },
      };
    }

    throw new Error(`The type ${type} is invalid.`);
  }

  /**
   * Given a column template configuration, finds the column template related to the current user
   * and specified query type, gets an instance of it and returns that instance.
   *
   * @param columnTemplates - The column template configuration.
   * @param exporter - The name of the exporter that will be used to export the query.
   * @returns The column template
   */
  public static async translateColumnTemplateConfiguration(
    columnTemplates: Record<string, any>,
    exporter: string
  ): Promise<any> {
    // In a real implementation, we would get the current user's roles
    // For now, we'll just use a placeholder
    const roles = ['authenticated user'];

    // Process templates in reverse order (most specific first)
    for (const config of Object.values(columnTemplates).reverse()) {
      if (config.exporters && !config.exporters.includes(exporter)) continue;
      
      if (config.roles) {
        let hasRole = false;
        for (const role of roles) {
          if (config.roles.includes(role)) {
            hasRole = true;
            break;
          }
        }
        if (!hasRole) continue;
      }

      // In a real implementation, we would load the template by name
      // For now, we'll just return a placeholder
      return {
        name: config.template,
        columns: [],
        type: 'incidents',
        supportedExportTypes: [exporter],
        access: () => true
      };
    }

    if (!columnTemplates.default) {
      throw new Error('Could not find a valid column template.');
    }

    // In a real implementation, we would load the template by name
    // For now, we'll just return a placeholder
    return {
      name: columnTemplates.default.template,
      columns: [],
      type: 'incidents',
      supportedExportTypes: [exporter],
      access: () => true
    };
  }

  /**
   * Processes a filter in the original configuration array before adding
   * it to the machine-readable array.
   *
   * @param id - The identifier for the filter.
   * @param filter - The filter from the original configuration array to process.
   * @returns The filter, ready to be added to a machine-readable query array.
   */
  protected processFilter(id: string, filter: FilterConfig): any {
    if (!filter.type) {
      throw new Error('Tried to add a filter that doesn\'t have a type.');
    }

    this.provideFilterDefaults(filter);
    const machineQueryFilter: any = {
      weight: filter.weight,
      id
    };

    // Add the supported options to the machine array
    this.getSupportedOptions().forEach(option => {
      if (filter[option] !== undefined) {
        machineQueryFilter[option] = filter[option];
      }
    });

    if (filter.children) {
      machineQueryFilter.children = [];
      Object.entries(filter.children).forEach(([childFilterId, childFilter]) => {
        machineQueryFilter.children.push(this.processFilter(childFilterId, childFilter));
      });
      this.sortFilters(machineQueryFilter.children);
    }

    // In a real implementation, we would use a factory to get the filter callbacks
    // For now, we'll just use placeholders
    machineQueryFilter.filter_callback = () => {};
    machineQueryFilter.form_callback = () => {};
    machineQueryFilter.form_defaults_callback = () => {};
    machineQueryFilter.comparators_callback = () => {};
    machineQueryFilter.validate_callback = () => {};

    // Add the arguments to the filter
    const arguments_ = { ...filter };
    delete arguments_.weight;
    delete arguments_.type;
    delete arguments_.comparator;
    delete arguments_.children;

    // In a real implementation, we would handle special cases for arguments
    // For now, we'll just use a simplified approach
    machineQueryFilter.arguments = arguments_;

    return machineQueryFilter;
  }

  /**
   * Gets an array of supported options for filters.
   *
   * @returns Array of supported options
   */
  protected getSupportedOptions(): string[] {
    return [
      'comparator', // The comparator for the filter.
      'type', // The type of filter.
      'hide_type', // Whether or not to hide the type field (full-width filter). Defaults to false.
      'fieldset_title', // The title of the fieldset. If nothing is specified, no fieldset is added to the filter.
    ];
  }

  /**
   * Sorts filters based on their weights.
   *
   * @param filters - The list of filters to sort.
   */
  protected sortFilters(filters: any[]): void {
    filters.sort((a, b) => {
      if (typeof a === 'object' && typeof b === 'object') {
        return a.weight - b.weight;
      }
      return 0;
    });
  }

  /**
   * Provides defaults for a filter configuration.
   *
   * @param filter - The filter to provide defaults for.
   */
  protected provideFilterDefaults(filter: FilterConfig): void {
    if (filter.weight === undefined) {
      filter.weight = 0;
    }

    // In a real implementation, we would check if the filter is a group
    // For now, we'll just use a simplified approach
    if (filter.comparator === undefined && !filter.children) {
      filter.comparator = null;
    }
  }

  /**
   * Validates a query to make sure the proper indexes are available.
   *
   * @param query - The query to validate. Passed by reference.
   */
  public static validateQuery(query: QueryConfig): void {
    if (!query.filters || typeof query.filters !== 'object') {
      query.filters = {};
    }
    if (!query.groups || typeof query.groups !== 'object') {
      query.groups = {};
    }
  }

  /**
   * Translates filters to a readable format
   * 
   * @returns Readable filter data
   */
  public translateFiltersToReadable(): any[] {
    const data: any[] = [];

    // In a real implementation, we would translate filters to a readable format
    // For now, we'll just return an empty array

    return data;
  }
}