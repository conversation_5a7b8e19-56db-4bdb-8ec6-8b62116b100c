import { v4 as uuidv4 } from 'uuid';

/**
 * Interface for filter configuration
 */
export interface FilterConfig {
  type: string;
  weight: number;
  comparator?: string;
  children?: Record<string, FilterConfig>;
  [key: string]: any;
}

/**
 * FilterGroup class for managing collections of filters
 */
export class FilterGroup {
  static readonly COMPARATOR_IS = 'is exactly';
  static readonly COMPARATOR_IS_NOT = 'is not';
  static readonly COMPARATOR_CONTAINS = 'contains';

  /**
   * The current list of filters.
   */
  protected filters: Record<string, FilterConfig> = {};

  /**
   * Adds a filter to the current query.
   *
   * @param type - The type of filter to add (without the 'Field' suffix).
   * @param weight - The weight of the filter.
   * @param comparator - The string to use when filtering the values.
   * @param arguments - Any additional arguments to override or pass to the filter callback.
   * @param id - The existing ID for the filter (if one exists).
   *
   * @returns The UUID for the filter.
   */
  public addFilter(
    type: string,
    weight = 0,
    comparator = FilterGroup.COMPARATOR_IS,
    args: Record<string, any> = {},
    id?: string
  ): string {
    if (!id) {
      id = uuidv4();
    }

    const config: FilterConfig = {
      type,
      weight,
      comparator,
      ...args
    };
    
    this.filters[id] = config;

    return id;
  }

  /**
   * Adds a filter group to the existing list of filters.
   *
   * @param group - The group to add.
   * @param weight - The weight of the filter group.
   * @param arguments - Any additional arguments to override or pass to the filter callback.
   * @param id - The existing ID for the filter (if one exists).
   *
   * @returns The UUID of the new filter group.
   */
  public addFilterGroup(
    group: FilterGroup,
    weight = 0,
    args: Record<string, any> = {},
    id?: string
  ): string {
    if (!id) {
      id = uuidv4();
    }

    const filter = {
      ...group.getGroupDefaults(),
      children: group.getFilters(),
      weight,
      ...args
    };
    
    this.filters[id] = filter;

    return id;
  }

  /**
   * Removes a filter from the current query.
   *
   * @param filterUuid - The UUID of the filter instance to remove.
   */
  public removeFilter(filterUuid: string): void {
    if (this.filters[filterUuid]) {
      delete this.filters[filterUuid];
    }
  }

  /**
   * Removes all filters of a certain type.
   *
   * @param type - The type of filters to remove.
   */
  public removeFiltersByType(type: string): void {
    Object.keys(this.filters).forEach(uuid => {
      if (this.filters[uuid].type === type) {
        delete this.filters[uuid];
      }
    });
  }

  /**
   * Gets the current list of filters.
   *
   * @returns The current list of filters.
   */
  public getFilters(): Record<string, FilterConfig> {
    return this.filters;
  }

  /**
   * Get all filters of a certain type.
   *
   * @param type - The type of filters to get.
   * @returns All filters of the given type. Empty object if there are none of that type.
   */
  public getFiltersByType(type: string): Record<string, FilterConfig> | null {
    const filters: Record<string, FilterConfig> = {};
    
    Object.keys(this.filters).forEach(uuid => {
      if (this.filters[uuid].type === type) {
        filters[uuid] = this.filters[uuid];
      }
    });

    return Object.keys(filters).length > 0 ? filters : null;
  }

  /**
   * Sets the current list of filters.
   *
   * @param filters - The current list of filters.
   */
  public setFilters(filters: Record<string, FilterConfig> = {}): void {
    this.filters = filters;
  }

  /**
   * Returns the default override values for the filter group (for example, the
   * OR group overrides the type value).
   *
   * @returns The override values, which are merged later.
   */
  protected getGroupDefaults(): Record<string, any> {
    return {
      weight: 0,
    };
  }
}