import { Column } from './Column.server';

/**
 * Factory class for creating column instances
 */
export class ColumnFactory {
  private static instance: ColumnFactory;
  private allColumnsCache: string[] | null = null;

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {}

  /**
   * Gets the singleton instance of the factory
   * 
   * @returns The singleton instance
   */
  public static getInstance(): ColumnFactory {
    if (!ColumnFactory.instance) {
      ColumnFactory.instance = new ColumnFactory();
    }
    return ColumnFactory.instance;
  }

  /**
   * Gets the full name of the specified column's representative class.
   *
   * @param column - The name of the column to get.
   * @returns The class name
   * @throws Error if the column does not exist
   */
  public className(column: string): string {
    const columnClassname = column.replace('.', '/') + 'Column';

    // In a real implementation, we would dynamically load the class
    // For now, we'll just return a placeholder
    const className = `Column${columnClassname}`;

    // Check if the class exists
    // In a real implementation, we would check if the class exists
    // For now, we'll just assume it does

    return className;
  }

  /**
   * Given a column name and a method, calls that method with the arguments
   * passed to this method forwarded. Returns the results from the function.
   *
   * @param column - The name of the column.
   * @param method - The name of the function to call.
   * @param args - Arguments to pass to the method
   * @returns The result of the method call
   */
  protected staticCall(column: string, method: string, ...args: any[]): any {
    try {
      const className = this.className(column);

      // In a real implementation, we would dynamically call the method
      // For now, we'll just return a placeholder

      if (method === 'access') {
        return true;
      } else if (method === 'label') {
        return column.split('.').pop();
      } else if (method === 'permissionName') {
        return `view column ${column.split('.').pop()}`;
      }

      return null;
    } catch (error) {
      console.error(`Error calling ${method} on ${column}:`, error);
      return null;
    }
  }

  /**
   * Checks whether or not the current user has access to the specified column.
   *
   * @param column - The column to check.
   * @returns Whether the user has access
   */
  public access(column: string): boolean {
    return this.staticCall(column, 'access') || false;
  }

  /**
   * Gets the human-readable label for the column.
   *
   * @param column - The column name.
   * @returns The human-readable label
   */
  public label(column: string): string {
    return this.staticCall(column, 'label') || column;
  }

  /**
   * Gets the name of the permission for the column.
   *
   * @param column - The column name.
   * @returns The permission name
   */
  public permissionName(column: string): string {
    return this.staticCall(column, 'permissionName') || `view column ${column}`;
  }

  /**
   * Gets an instance of the specified column's representative class.
   *
   * @param column - The name of the column to get the class for.
   * @returns The column instance
   * @throws Error if the column does not exist
   */
  public factory(column: string): Column {
    // Split the column name into category and name
    const [category, name] = column.split('.');

    try {
      // Dynamically import the column class based on category and name
      // This is a simplified implementation that would need to be expanded
      // with actual column implementations

      // For now, we'll return a basic implementation for the columns in allColumns()
      if (this.allColumnsCache?.includes(column)) {
        return {
          display: (row: Record<string, any> = {}, tags: string[] = []) => {
            return row[column.toLowerCase().replace('.', '_')] || 'N/A';
          },
          alter: (query: any) => {
            // Basic implementation
            return [];
          },
          getSortField: (query: any) => {
            return column.toLowerCase().replace('.', '_');
          },
          preDisplay: (row: Record<string, any> = {}, exporter: any = null) => {
            return row[column.toLowerCase().replace('.', '_')] || 'N/A';
          }
        } as Column;
      }

      throw new Error(`Column ${column} not implemented yet`);
    } catch (error) {
      console.error(`Error creating column ${column}:`, error);
      throw new Error(`Column ${column} not implemented yet`);
    }
  }

  /**
   * Gets an array of all columns.
   *
   * @returns An array of column names to pass into the factory method.
   */
  public allColumns(): string[] {
    if (!this.allColumnsCache) {
      // In a real implementation, we would scan the directory for column classes
      // For now, we'll just return a placeholder list
      this.allColumnsCache = [
        'Base.IncidentID',
        'Base.IncidentDate',
        'Base.IncidentTime',
        'Location.State',
        'Location.CityOrCounty',
        'Location.Address',
        'Base.NumberOfGuns',
        'Base.IncidentCharacteristics',
        'Base.IncidentTimeAt'
      ];
    }

    return this.reorderColumns();
  }

  /**
   * Custom Reorder an array of all columns.
   *
   * @returns An array of column names to pass into the factory method.
   */
  public reorderColumns(): string[] {
    if (this.allColumnsCache) {
      // Move 'Base.IncidentTimeAt' to the end of the array
      const index = this.allColumnsCache.indexOf('Base.IncidentTimeAt');
      if (index !== -1) {
        this.allColumnsCache.splice(index, 1);
        this.allColumnsCache.push('Base.IncidentTimeAt');
      }
    }

    return this.allColumnsCache || [];
  }
}
