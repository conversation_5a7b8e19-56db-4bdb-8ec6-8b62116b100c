/**
 * Base abstract class for columns in the query system
 */
export abstract class Column {
  /**
   * The current query exporter calling this column.
   */
  protected exporter: any = null;

  /**
   * Display callback for the column.
   *
   * @param row - The raw result from the database.
   * @param tags - An array of tags to add to the exporter.
   * @returns The formatted display value
   */
  public abstract display(row: Record<string, any> = {}, tags: string[] = []): any;

  /**
   * Alters the query and optionally returns an array of tags to add to the exporter.
   *
   * @param query - The query to alter.
   * @returns Any tags to add to the exporter
   */
  public abstract alter(query: any): any;

  /**
   * Modifies the passed query (to add any necessary joins) and then
   * returns the field to sort on. If false is returned, the column
   * is considered to not support sorting.
   *
   * @param query - The current sorting query.
   * @returns False to not support sorting, the column name to sort on otherwise.
   */
  public getSortField(query: any): string | boolean {
    return false;
  }

  /**
   * This function does any preparations before calling the main display
   * function of the column.
   *
   * @param row - The row to pass to the display method.
   * @param exporter - The current exporter.
   * @returns The formatted display value
   */
  public preDisplay(row: Record<string, any> = {}, exporter: any = null): any {
    this.exporter = exporter;
    const tags: string[] = [];
    const result = this.display(row, tags);

    if (exporter && typeof exporter.addTag === 'function') {
      tags.forEach(tag => {
        exporter.addTag(tag);
      });
    }

    return result;
  }

  /**
   * Given a row and a key, determines whether or not the value for
   * that key is empty. If it is, this returns a proper string representing
   * the not available value. If it is not empty, this returns true.
   *
   * @param row - The row from the database.
   * @param key - The key to check for.
   * @param valueCallback - The function to call with the row, the key and the value.
   * @param emptyValue - The value to return if the field is empty.
   * @returns The processed value or empty value
   */
  protected checkEmptyValue(
    row: Record<string, any> = {}, 
    key: string, 
    valueCallback: ((row: Record<string, any>, key: string, value: any) => any) | null = null, 
    emptyValue: string = 'N/A'
  ): any {
    if (valueCallback === null) {
      valueCallback = (row, key, value) => value;
    }
    
    if (!(key in row) || !row[key]) {
      return emptyValue;
    } else {
      const result = valueCallback(row, key, row[key]);
      
      if (this.exporter && typeof this.exporter.requiresPlainText === 'function' && this.exporter.requiresPlainText()) {
        // In a browser environment, we would use a DOM parser to strip tags
        // For now, we'll use a simple regex approach
        return typeof result === 'string' ? result.replace(/<[^>]*>/g, '') : result;
      }
      
      return result;
    }
  }

  /**
   * Given a row and a key, determines whether or not the value for the
   * key represents a true or false value. The respective value is then
   * returned.
   *
   * @param row - The row from the database.
   * @param key - The key to check.
   * @param yes - The value to output if the value evaluates to true.
   * @param no - The value to output if the value evaluates to false or does not exist.
   * @returns The boolean value as a string
   */
  protected booleanValue(
    row: Record<string, any> = {}, 
    key: string, 
    yes: string = 'Yes', 
    no: string = 'No'
  ): string {
    return this.checkEmptyValue(
      row, 
      key, 
      (row, key, value) => yes, 
      no
    );
  }

  /**
   * Given a row and a key, returns a formatted list of items.
   *
   * @param row - The row from the database.
   * @param key - The key passed to the function.
   * @param argumentsCallback - The function to call when rendering the arguments.
   * @param emptyValue - The value to use when there is no value for the field.
   * @returns The formatted list
   */
  protected listValue(
    row: Record<string, any> = {}, 
    key: string, 
    argumentsCallback: ((row: Record<string, any>, key: string, values: string[]) => any) | null = null,
    emptyValue: string = 'N/A'
  ): any {
    return this.checkEmptyValue(
      row, 
      key, 
      (row, key, value) => {
        const values = value.split('|');
        
        if (this.exporter && typeof this.exporter.requiresPlainText === 'function' && this.exporter.requiresPlainText()) {
          if (argumentsCallback === null) {
            argumentsCallback = (row, key, values) => ({
              items: values
            });
          }
          
          const items = argumentsCallback(row, key, values);
          return items.items.join(', ');
        } else {
          if (argumentsCallback === null) {
            argumentsCallback = (row, key, values) => ({
              items: values
            });
          }
          
          // In Remix, we would return a React component
          // For now, we'll just return the items
          return argumentsCallback(row, key, values);
        }
      }, 
      emptyValue
    );
  }

  /**
   * Given a row and a key, returns a formatted set of images.
   *
   * @param row - The row from the database.
   * @param key - The key passed to the function.
   * @param title - The title of the incident.
   * @param emptyValue - The value to use when there is no value for the field.
   * @returns The formatted images
   */
  protected images(
    row: Record<string, any> = {}, 
    key: string, 
    title: string,
    emptyValue: string = 'N/A'
  ): any {
    return this.checkEmptyValue(
      row, 
      key, 
      (row, key, value) => {
        const fids = value.split('|');
        const images: any[] = [];
        
        // In a real implementation, we would load the images from the database
        // For now, we'll just return placeholders
        fids.forEach(fid => {
          images.push({
            title,
            path: `/images/${fid}`
          });
        });
        
        return images;
      }, 
      emptyValue
    );
  }

  /**
   * Gets the human-readable label for the column.
   *
   * @returns The human-readable label
   */
  public static label(): string {
    const className = this.name().replace(/Column$/, '');
    // Split the class name on capital letters and join with spaces
    const words = className.split(/(?=[A-Z])/).join(' ');
    return words.trim();
  }

  /**
   * Gets the permission name that identifies the column.
   *
   * @returns The permission name
   */
  public static permissionName(): string {
    return `view column ${this.label()}`;
  }

  /**
   * Determines whether or not the current user has access to the column.
   *
   * @returns Whether the user has access
   */
  public static access(): boolean {
    // In a real implementation, we would check user permissions
    // For now, we'll just return true
    return true;
  }

  /**
   * Gets the name of the column class.
   *
   * @returns The column class name
   */
  public static name(): string {
    return this.toString().split('(' || /s+/)[0].split(' ' || /s+/)[1];
  }

  /**
   * Adds a join to the specified query object, checking for duplicates at the same time.
   *
   * @param query - The query to add the join to.
   * @param joinType - The type of join to add (LEFT, INNER, etc).
   * @param table - The name of the table to join on.
   * @param alias - The alias to give the table.
   * @param condition - The conditions to join on.
   * @param arguments - Any arguments to pass to the conditions.
   * @returns The alias of the joined table
   */
  protected static addJoinToQuery(
    query: any, 
    joinType: string, 
    table: string, 
    alias: string | null = null, 
    condition: string | null = null, 
    args: Record<string, any> = {}
  ): string {
    // In a real implementation, we would check for duplicate joins
    // For now, we'll just return the alias
    return alias || table;
  }
}