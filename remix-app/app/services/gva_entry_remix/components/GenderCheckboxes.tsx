import React from 'react';
import { useGenderCheckboxes } from '../hooks/useGenderCheckboxes';

interface GenderOption {
  value: string;
  label: string;
}

interface GenderCheckboxesProps {
  options: GenderOption[];
  name: string;
  onChange?: (selectedGender: string | null) => void;
  className?: string;
}

/**
 * A component that renders a group of gender checkboxes where only one can be selected at a time
 */
export function GenderCheckboxes({ 
  options, 
  name, 
  onChange, 
  className = 'gender_checkboxes' 
}: GenderCheckboxesProps) {
  const { selectedGender, handleGenderChange } = useGenderCheckboxes();
  
  // Call the onChange prop when selectedGender changes
  React.useEffect(() => {
    if (onChange) {
      onChange(selectedGender);
    }
  }, [selectedGender, onChange]);
  
  return (
    <div className={`form-checkboxes ${className}`}>
      {options.map((option) => (
        <div key={option.value} className="form-check">
          <input
            type="checkbox"
            id={`${name}-${option.value}`}
            name={name}
            value={option.value}
            checked={selectedGender === option.value}
            onChange={(e) => handleGenderChange(option.value, e.target.checked)}
            className="form-check-input"
          />
          <label htmlFor={`${name}-${option.value}`} className="form-check-label">
            {option.label}
          </label>
        </div>
      ))}
    </div>
  );
}