import React, { useRef } from 'react';
import { useAgeGroupSelection, AgeGroupOption } from '../hooks/useAgeGroupSelection';

interface AgeGroupSelectorProps {
  options: AgeGroupOption[];
  name: string;
  ageInputName?: string;
  ageGroupName?: string;
  initialAge?: string;
  initialAgeGroup?: string;
  onChange?: (age: string, ageGroup: string) => void;
  className?: string;
}

/**
 * A component that renders an age input and age group select that are linked
 * The age group is automatically selected based on the entered age
 */
export function AgeGroupSelector({
  options,
  name,
  ageInputName = `${name}-age`,
  ageGroupName = `${name}-age-group`,
  initialAge = '',
  initialAgeGroup = '',
  onChange,
  className = 'age-age-group'
}: AgeGroupSelectorProps) {
  const ageInputRef = useRef<HTMLInputElement>(null);
  const ageGroupSelectRef = useRef<HTMLSelectElement>(null);
  
  // Handle age group changes
  const handleAgeGroupChange = (selectedValue: string) => {
    if (onChange && ageInputRef.current) {
      onChange(ageInputRef.current.value, selectedValue);
    }
  };
  
  // Handle age input changes
  const handleAgeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange && ageGroupSelectRef.current) {
      onChange(e.target.value, ageGroupSelectRef.current.value);
    }
  };
  
  // Use the hook to link the age input and age group select
  useAgeGroupSelection(ageInputRef, ageGroupSelectRef, options, handleAgeGroupChange);
  
  return (
    <div className={className}>
      <div className="age-input-container">
        <label htmlFor={ageInputName}>Age:</label>
        <input
          ref={ageInputRef}
          type="text"
          id={ageInputName}
          name={ageInputName}
          className="age"
          defaultValue={initialAge}
          onChange={handleAgeChange}
        />
      </div>
      
      <div className="age-group-container">
        <label htmlFor={ageGroupName}>Age Group:</label>
        <select
          ref={ageGroupSelectRef}
          id={ageGroupName}
          name={ageGroupName}
          className="age-group"
          defaultValue={initialAgeGroup}
          onChange={(e) => handleAgeGroupChange(e.target.value)}
        >
          <option value="">- Select -</option>
          {options.map((option) => (
            <option
              key={option.value}
              value={option.value}
              data-minimum-age={option.minimumAge}
              data-maximum-age={option.maximumAge}
            >
              {option.label}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
}