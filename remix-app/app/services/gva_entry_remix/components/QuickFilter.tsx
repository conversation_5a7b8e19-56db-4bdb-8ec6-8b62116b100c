import React from 'react';
import { useQuickFilter } from '../hooks/useQuickFilter';

interface FilterOption {
  value: string;
  label: string;
}

interface QuickFilterProps {
  options: FilterOption[];
  paramName?: string;
  defaultLabel?: string;
  className?: string;
  onChange?: (value: string | null) => void;
}

/**
 * A component that renders a quick filter select element that updates the URL
 */
export function QuickFilter({
  options,
  paramName = 'characteristic',
  defaultLabel = 'Select a filter',
  className = 'quick-filter',
  onChange
}: QuickFilterProps) {
  const { filterValue, updateFilter } = useQuickFilter(paramName);
  
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newValue = e.target.value || null;
    updateFilter(newValue);
    
    if (onChange) {
      onChange(newValue);
    }
  };
  
  return (
    <div className={className}>
      <select 
        name="quick-filter"
        value={filterValue || ''}
        onChange={handleChange}
      >
        <option value="">{defaultLabel}</option>
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );
}