import React from 'react';

/**
 * Props for the Section component
 */
interface SectionProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Section component - equivalent to theme_gva_entry_section in Drupal
 */
export const Section: React.FC<SectionProps> = ({ children, className }) => {
  return (
    <section className={className}>
      {children}
    </section>
  );
};

/**
 * Props for the Row component
 */
interface RowProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Row component - equivalent to theme_gva_entry_row in Drupal
 */
export const Row: React.FC<RowProps> = ({ children, className = 'row' }) => {
  return (
    <div className={className}>
      {children}
    </div>
  );
};

/**
 * Props for the Column component
 */
interface ColumnProps {
  children: React.ReactNode;
  width?: number;
  isRow?: boolean;
  className?: string;
}

/**
 * Column component - equivalent to theme_gva_entry_column in Drupal
 */
export const Column: React.FC<ColumnProps> = ({ 
  children, 
  width = 12, 
  isRow = false,
  className
}) => {
  if (!width) {
    return <>{children}</>;
  }

  const classes = [`medium-${width}`, 'columns'];
  if (isRow) {
    classes.push('row');
  }
  if (className) {
    classes.push(className);
  }

  return (
    <div className={classes.join(' ')}>
      {children}
    </div>
  );
};