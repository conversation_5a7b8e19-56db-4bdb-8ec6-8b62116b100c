import React from 'react';
import { Charts } from './Charts';

/**
 * Props for the FilterResults component
 */
interface FilterResultsProps {
  query: {
    uuid: string;
    type: string;
    limit_results?: boolean;
  };
  enableLimiting?: boolean;
  yearRange?: string;
  state?: string;
  district?: string;
  characteristic?: string;
  disclaimer?: string;
  statsData?: any;
  tableData?: React.ReactNode;
  hasSourceLinks?: boolean;
  limitingForm?: React.ReactNode;
}

/**
 * FilterResults component - equivalent to theme_gva_entry_filter_results in Drupal
 */
export const FilterResults: React.FC<FilterResultsProps> = ({
  query,
  enableLimiting = true,
  yearRange,
  state,
  district,
  characteristic,
  disclaimer,
  statsData,
  tableData,
  hasSourceLinks = false,
  limitingForm,
}) => {
  // Disable limiting if the query says so
  if (enableLimiting && !query.limit_results) {
    enableLimiting = false;
  }

  return (
    <div className="filter-results">
      {/* Limiting form */}
      {enableLimiting && limitingForm && (
        <div className="limiting">
          {limitingForm}
        </div>
      )}

      {/* Disclaimer */}
      {disclaimer && hasSourceLinks && (
        <p className="results-top">
          {disclaimer}
        </p>
      )}

      {/* Results content */}
      {query.type === 'stats' && statsData ? (
        <div className="stats-results">
          <Charts data={statsData} />
        </div>
      ) : (
        <div className="table-results">
          {tableData}
        </div>
      )}
    </div>
  );
};