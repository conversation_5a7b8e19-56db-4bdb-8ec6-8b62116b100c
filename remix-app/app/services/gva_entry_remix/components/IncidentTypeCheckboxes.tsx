import React from 'react';
import { IncidentType } from '../utils/incidentTypeUtils';
import { useIncidentTypeCheckboxes } from '../hooks/useIncidentTypeCheckboxes';
import { IncidentTypeReminders } from './IncidentTypeReminders';

interface IncidentTypeCheckboxesProps {
  incidentTypes: IncidentType[];
  initialSelectedIds?: string[];
  onChange?: (selectedIds: string[]) => void;
  className?: string;
  name?: string;
}

/**
 * Component to render hierarchical incident type checkboxes with reminders
 */
export function IncidentTypeCheckboxes({
  incidentTypes,
  initialSelectedIds = [],
  onChange,
  className = 'form-heirarchical-checkboxes',
  name = 'incident-types'
}: IncidentTypeCheckboxesProps) {
  const { 
    selectedIds, 
    reminders, 
    handleSelectionChange, 
    isSelected 
  } = useIncidentTypeCheckboxes(incidentTypes, initialSelectedIds);

  // Call onChange when selectedIds changes
  React.useEffect(() => {
    if (onChange) {
      onChange(selectedIds);
    }
  }, [selectedIds, onChange]);

  // Recursive function to render incident types and their children
  const renderIncidentType = (type: IncidentType, depth: number = 0) => {
    const hasChildren = type.children && type.children.length > 0;
    const checked = isSelected(type.id);
    const hasHeader = type.header && type.header.trim() !== '';

    // If the type has a header, wrap the checkbox in a container with a header
    if (hasHeader) {
      return (
        <li 
          key={type.id} 
          className={`form-type-checkbox with-header ${hasChildren ? 'has-children' : ''}`}
          style={{ marginLeft: `${depth * 20}px` }}
        >
          <h3 className="incident-type-header">{type.header}</h3>
          <div className="checkbox-container">
            <input
              type="checkbox"
              id={`${name}-${type.id}`}
              name={`${name}[${type.id}]`}
              value={type.id}
              checked={checked}
              onChange={(e) => handleSelectionChange(type.id, e.target.checked)}
              data-reminder={type.reminder}
              data-header={type.header}
            />
            <label htmlFor={`${name}-${type.id}`}>{type.label}</label>
          </div>

          {hasChildren && (
            <ul className="children">
              {type.children!.map(child => renderIncidentType(child, depth + 1))}
            </ul>
          )}
        </li>
      );
    }

    // Regular rendering without header
    return (
      <li 
        key={type.id} 
        className={`form-type-checkbox ${hasChildren ? 'has-children' : ''}`}
        style={{ marginLeft: `${depth * 20}px` }}
      >
        <div className="checkbox-container">
          <input
            type="checkbox"
            id={`${name}-${type.id}`}
            name={`${name}[${type.id}]`}
            value={type.id}
            checked={checked}
            onChange={(e) => handleSelectionChange(type.id, e.target.checked)}
            data-reminder={type.reminder}
          />
          <label htmlFor={`${name}-${type.id}`}>{type.label}</label>
        </div>

        {hasChildren && (
          <ul className="children">
            {type.children!.map(child => renderIncidentType(child, depth + 1))}
          </ul>
        )}
      </li>
    );
  };

  return (
    <div className="incident-type-container">
      <div className={className}>
        <ul className="incident-types">
          {incidentTypes.map(type => renderIncidentType(type))}
        </ul>
      </div>

      <IncidentTypeReminders reminders={reminders} />
    </div>
  );
}
