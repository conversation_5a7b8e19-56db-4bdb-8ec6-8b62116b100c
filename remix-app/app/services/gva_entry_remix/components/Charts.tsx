import React, { useRef } from 'react';
import { useCharts } from '../hooks/useCharts';

/**
 * Props for the Charts component
 */
interface ChartsProps {
  data: {
    years: Array<{
      year: string;
      count: number;
      mass: number;
      c_killed: number;
      c_injured: number;
      months: Array<{
        month: string;
        count: number;
        mass: number;
        c_killed: number;
        c_injured: number;
        days: Array<{
          rawDate: number;
          count: number;
        }>;
      }>;
      states: Array<{
        state: string;
        count: number;
      }>;
    }>;
  };
}

/**
 * Component to render various charts related to incidents
 */
export const Charts: React.FC<ChartsProps> = ({ data }) => {
  // Create refs for all chart canvases
  const incidentsRef = useRef<HTMLCanvasElement>(null);
  const imonthsRef = useRef<HTMLCanvasElement>(null);
  const imonthsDRef = useRef<HTMLCanvasElement>(null);
  const mincidentsRef = useRef<HTMLCanvasElement>(null);
  const mmonthsRef = useRef<HTMLCanvasElement>(null);
  const ckincidentsRef = useRef<HTMLCanvasElement>(null);
  const ckmonthsRef = useRef<HTMLCanvasElement>(null);
  const ciincidentsRef = useRef<HTMLCanvasElement>(null);
  const cimonthsRef = useRef<HTMLCanvasElement>(null);
  const allstatesRef = useRef<HTMLCanvasElement>(null);

  // Use the charts hook
  useCharts(data, {
    incidents: incidentsRef,
    imonths: imonthsRef,
    imonthsD: imonthsDRef,
    mincidents: mincidentsRef,
    mmonths: mmonthsRef,
    ckincidents: ckincidentsRef,
    ckmonths: ckmonthsRef,
    ciincidents: ciincidentsRef,
    cimonths: cimonthsRef,
    allstates: allstatesRef,
  });

  return (
    <div className="charts-container">
      <div className="chart-section">
        <h2>Total Incidents</h2>
        <div className="chart-row">
          <div className="chart-column">
            <h3>By Year</h3>
            <canvas ref={incidentsRef} id="incidents"></canvas>
          </div>
          <div className="chart-column">
            <h3>By Month</h3>
            <canvas ref={imonthsRef} id="imonths"></canvas>
          </div>
        </div>
        <div className="chart-row">
          <div className="chart-column full-width">
            <h3>By Day</h3>
            <canvas ref={imonthsDRef} id="imonths-d"></canvas>
          </div>
        </div>
      </div>

      <div className="chart-section">
        <h2>Mass Shootings</h2>
        <div className="chart-row">
          <div className="chart-column">
            <h3>By Year</h3>
            <canvas ref={mincidentsRef} id="mincidents"></canvas>
          </div>
          <div className="chart-column">
            <h3>By Month</h3>
            <canvas ref={mmonthsRef} id="mmonths"></canvas>
          </div>
        </div>
      </div>

      <div className="chart-section">
        <h2>Children Killed</h2>
        <div className="chart-row">
          <div className="chart-column">
            <h3>By Year</h3>
            <canvas ref={ckincidentsRef} id="ckincidents"></canvas>
          </div>
          <div className="chart-column">
            <h3>By Month</h3>
            <canvas ref={ckmonthsRef} id="ckmonths"></canvas>
          </div>
        </div>
      </div>

      <div className="chart-section">
        <h2>Children Injured</h2>
        <div className="chart-row">
          <div className="chart-column">
            <h3>By Year</h3>
            <canvas ref={ciincidentsRef} id="ciincidents"></canvas>
          </div>
          <div className="chart-column">
            <h3>By Month</h3>
            <canvas ref={cimonthsRef} id="cimonths"></canvas>
          </div>
        </div>
      </div>

      <div className="chart-section">
        <h2>Incidents by State</h2>
        <div className="chart-row">
          <div className="chart-column full-width">
            <canvas ref={allstatesRef} id="allstates"></canvas>
          </div>
        </div>
      </div>
    </div>
  );
};