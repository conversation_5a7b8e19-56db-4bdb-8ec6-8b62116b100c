import React from 'react';
import { ReminderMessage } from '../utils/incidentTypeUtils';

interface IncidentTypeRemindersProps {
  reminders: ReminderMessage[];
  className?: string;
}

/**
 * Component to display reminders for selected incident types
 */
export function IncidentTypeReminders({
  reminders,
  className = 'incident-type-reminders'
}: IncidentTypeRemindersProps) {
  if (reminders.length === 0) {
    return <div id="incident-type-reminders" className={className}></div>;
  }
  
  return (
    <div id="incident-type-reminders" className={className}>
      <div className="messages warning">
        <h2 className="element-invisible">Warning message</h2>
        <ul>
          {reminders.map((reminder, index) => (
            <li key={index}>
              <strong>{reminder.label.trim()}:</strong> {reminder.reminder}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}