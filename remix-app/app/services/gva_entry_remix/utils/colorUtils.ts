/**
 * Adjusts the luminance of a hex color
 * 
 * @param hex - The hex color to adjust (with or without # prefix)
 * @param lum - The luminance factor (negative darkens, positive lightens)
 * @returns The adjusted hex color with # prefix
 */
export function adjustColorLuminance(hex: string, lum: number = 0): string {
  // Validate and normalize hex string
  hex = String(hex).replace(/[^0-9a-f]/gi, '');
  if (hex.length < 6) {
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
  }

  // Convert to decimal and change luminosity
  let rgb = "#";
  for (let i = 0; i < 3; i++) {
    const c = parseInt(hex.substr(i * 2, 2), 16);
    const adjustedC = Math.round(Math.min(Math.max(0, c + (c * lum)), 255));
    const hexC = adjustedC.toString(16).padStart(2, '0');
    rgb += hexC;
  }

  return rgb;
}

/**
 * Calculates background colors for nested filter elements
 * 
 * @param depth - The nesting depth of the element
 * @param baseColor - The base color to adjust (default: #f2f2f2)
 * @param luminanceFactor - The luminance adjustment factor per depth level (default: -0.04)
 * @returns The calculated background color
 */
export function getNestedFilterBackgroundColor(
  depth: number, 
  baseColor: string = '#f2f2f2', 
  luminanceFactor: number = -0.04
): string {
  if (depth <= 1) {
    return baseColor;
  }
  
  return adjustColorLuminance(baseColor, luminanceFactor * (depth - 2));
}