/**
 * Interface for an incident type with optional reminder and header
 */
export interface IncidentType {
  id: string;
  label: string;
  reminder?: string;
  header?: string;
  children?: IncidentType[];
  parentId?: string;
}

/**
 * Interface for a reminder message
 */
export interface ReminderMessage {
  label: string;
  reminder: string;
}

/**
 * Collects all reminders from selected incident types
 * 
 * @param incidentTypes - Array of incident types
 * @param selectedIds - Array of selected incident type IDs
 * @returns Array of reminder messages
 */
export function collectReminders(
  incidentTypes: IncidentType[],
  selectedIds: string[]
): ReminderMessage[] {
  const reminders: ReminderMessage[] = [];

  // Helper function to recursively process incident types
  const processIncidentTypes = (types: IncidentType[]) => {
    types.forEach(type => {
      if (selectedIds.includes(type.id) && type.reminder) {
        reminders.push({
          label: type.label,
          reminder: type.reminder
        });
      }

      if (type.children && type.children.length > 0) {
        processIncidentTypes(type.children);
      }
    });
  };

  processIncidentTypes(incidentTypes);
  return reminders;
}

/**
 * Gets all parent IDs for a given incident type
 * 
 * @param incidentTypes - Array of incident types
 * @param typeId - The ID of the incident type to get parents for
 * @returns Array of parent IDs
 */
export function getParentIds(
  incidentTypes: IncidentType[],
  typeId: string
): string[] {
  const parentIds: string[] = [];

  // Helper function to find a type by ID
  const findTypeById = (types: IncidentType[], id: string): IncidentType | null => {
    for (const type of types) {
      if (type.id === id) {
        return type;
      }

      if (type.children && type.children.length > 0) {
        const found = findTypeById(type.children, id);
        if (found) {
          return found;
        }
      }
    }

    return null;
  };

  // Helper function to collect parent IDs
  const collectParentIds = (types: IncidentType[], id: string) => {
    const type = findTypeById(types, id);
    if (type && type.parentId) {
      parentIds.push(type.parentId);
      collectParentIds(types, type.parentId);
    }
  };

  collectParentIds(incidentTypes, typeId);
  return parentIds;
}

/**
 * Gets all child IDs for a given incident type
 * 
 * @param incidentTypes - Array of incident types
 * @param typeId - The ID of the incident type to get children for
 * @returns Array of child IDs
 */
export function getChildIds(
  incidentTypes: IncidentType[],
  typeId: string
): string[] {
  const childIds: string[] = [];

  // Helper function to find a type by ID
  const findTypeById = (types: IncidentType[], id: string): IncidentType | null => {
    for (const type of types) {
      if (type.id === id) {
        return type;
      }

      if (type.children && type.children.length > 0) {
        const found = findTypeById(type.children, id);
        if (found) {
          return found;
        }
      }
    }

    return null;
  };

  // Helper function to collect child IDs
  const collectChildIds = (type: IncidentType) => {
    if (type.children && type.children.length > 0) {
      type.children.forEach(child => {
        childIds.push(child.id);
        collectChildIds(child);
      });
    }
  };

  const type = findTypeById(incidentTypes, typeId);
  if (type) {
    collectChildIds(type);
  }

  return childIds;
}
