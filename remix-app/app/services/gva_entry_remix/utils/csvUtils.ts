/**
 * Utility functions for CSV operations
 */

/**
 * Converts an array to a CSV string
 * 
 * @param input - The array to convert to CSV
 * @param delimiter - The delimiter to use (default: ',')
 * @param enclosure - The enclosure to use (default: '"')
 * @returns The CSV string
 */
export function arrayToCsv(
  input: any[],
  delimiter: string = ',',
  enclosure: string = '"'
): string {
  if (!input || !input.length) {
    return '';
  }

  // Process each item in the array
  const processedItems = input.map(item => {
    // If the item contains the delimiter, enclosure, or newline, enclose it
    if (
      typeof item === 'string' && 
      (item.includes(delimiter) || item.includes(enclosure) || item.includes('\n'))
    ) {
      // Escape any enclosure characters by doubling them
      const escapedItem = item.replace(new RegExp(enclosure, 'g'), enclosure + enclosure);
      return enclosure + escapedItem + enclosure;
    }
    
    // Convert null or undefined to empty string
    if (item === null || item === undefined) {
      return '';
    }
    
    return String(item);
  });

  // Join the processed items with the delimiter
  return processedItems.join(delimiter);
}

/**
 * Converts a 2D array to a CSV string with multiple rows
 * 
 * @param input - The 2D array to convert to CSV
 * @param delimiter - The delimiter to use (default: ',')
 * @param enclosure - The enclosure to use (default: '"')
 * @returns The CSV string with multiple rows
 */
export function arraysToCsv(
  input: any[][],
  delimiter: string = ',',
  enclosure: string = '"'
): string {
  if (!input || !input.length) {
    return '';
  }

  // Convert each row to a CSV string and join with newlines
  return input.map(row => arrayToCsv(row, delimiter, enclosure)).join('\n');
}