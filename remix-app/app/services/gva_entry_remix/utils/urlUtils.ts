/**
 * Gets a URL parameter value from the current URL
 * 
 * @param param - The parameter name to get
 * @returns The parameter value or null if not found
 */
export function getUrlParameter(param: string): string | null {
  if (typeof window === 'undefined') return null;
  
  const searchParams = new URLSearchParams(window.location.search);
  return searchParams.get(param);
}

/**
 * Updates a URL parameter in the current URL
 * 
 * @param param - The parameter name to update
 * @param value - The new parameter value (if null or empty, the parameter will be removed)
 * @returns The updated URL
 */
export function updateUrlParameter(param: string, value: string | null): string {
  if (typeof window === 'undefined') return '';
  
  const url = new URL(window.location.href);
  const searchParams = url.searchParams;
  
  // Remove the parameter if it exists
  searchParams.delete(param);
  
  // Add the parameter with the new value if it's not null or empty
  if (value) {
    searchParams.set(param, value);
  }
  
  // Update the URL
  url.search = searchParams.toString();
  return url.toString();
}

/**
 * Navigates to a URL with an updated parameter
 * 
 * @param param - The parameter name to update
 * @param value - The new parameter value (if null or empty, the parameter will be removed)
 */
export function navigateWithUpdatedParameter(param: string, value: string | null): void {
  if (typeof window === 'undefined') return;
  
  const newUrl = updateUrlParameter(param, value);
  window.location.href = newUrl;
}