// Export all components, hooks, and utilities

// Components
export { AgeGroupSelector } from './components/AgeGroupSelector';
export { Charts } from './components/Charts';
export { FilterResults } from './components/FilterResults';
export { GenderCheckboxes } from './components/GenderCheckboxes';
export { IncidentTypeCheckboxes } from './components/IncidentTypeCheckboxes';
export { IncidentTypeReminders } from './components/IncidentTypeReminders';
export { Section, Row, Column } from './components/Layout';
export { QuickFilter } from './components/QuickFilter';

// Hooks
export { useAgeGroupSelection } from './hooks/useAgeGroupSelection';
export { useCharacterCounter } from './hooks/useCharacterCounter';
export { useCharts } from './hooks/useCharts';
export { useDatePickers } from './hooks/useDatePickers';
export { useEntryLock } from './hooks/useEntryLock';
export { useFilterBackgroundColors } from './hooks/useFilterBackgroundColors';
export { useFilterDropdowns } from './hooks/useFilterDropdowns';
export { useFilterHelpTips } from './hooks/useFilterHelpTips';
export { useFormDisableOnAjax } from './hooks/useFormDisableOnAjax';
export { useGenderCheckboxes } from './hooks/useGenderCheckboxes';
export { useGunManagement } from './hooks/useGunManagement';
export { useIncidentTypeCheckboxes } from './hooks/useIncidentTypeCheckboxes';
export { useParticipantManagement } from './hooks/useParticipantManagement';
export { useQuickFilter } from './hooks/useQuickFilter';
export { useSubmitOnCustom, handleCustomSelectChange } from './hooks/useSubmitOnCustom';

// Utils
export { adjustColorLuminance, getNestedFilterBackgroundColor } from './utils/colorUtils';
export { arrayToCsv, arraysToCsv } from './utils/csvUtils';
export { 
  type IncidentType, 
  type ReminderMessage,
  collectReminders,
  getParentIds,
  getChildIds
} from './utils/incidentTypeUtils';
export { refreshPage } from './utils/refresh';
export { 
  getUrlParameter, 
  updateUrlParameter, 
  navigateWithUpdatedParameter 
} from './utils/urlUtils';

// Query
export { FilterGroup, type FilterConfig } from './query/FilterGroup.server';
export { 
  QueryBuilder, 
  type ColumnTemplate, 
  type QueryConfig 
} from './query/QueryBuilder.server';
export { Column } from './query/columns/Column.server';
export { 
  IncidentsColumnInterface, 
  ParticipantsColumnInterface, 
  StatsColumnInterface 
} from './query/columns/ColumnInterfaces.server';
export { ColumnFactory } from './query/columns/ColumnFactory.server';
export { 
  type QueryFilterable, 
  QueryFilterableBase 
} from './query/filters/QueryFilterable.server';
export { 
  QueryFilterFactory, 
  QueryFilterFactoryException 
} from './query/filters/QueryFilterFactory.server';

// Tests
export {
  testColumnFactory,
  testQueryFilterFactory,
  runTests
} from './test-factories';
