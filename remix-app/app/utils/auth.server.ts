import { redirect } from '@remix-run/node';
import { Authenticator, AuthorizationError } from 'remix-auth';
import bcrypt from 'bcryptjs';
import moment from 'moment';
import { getSession, sessionStorage } from './session.server';
import type { UserSession } from '~/models/users.server';
import { createUserSession, removeUserSession, getUserBySession } from '~/models/users.server';
import { CustomStrategy } from './custom-strategy.server';
import { db as prisma } from './db.server';
import { verifyJwtToken } from './jwt.server';

export const authenticator = new Authenticator<UserSession>(sessionStorage, {
	sessionKey: 'token'
});

authenticator.use(
	new CustomStrategy(async ({ form, request }) => {
		let user = null;

		const email = form?.get('email');
		const password = form?.get('password');

		if (typeof email !== 'string') throw new AuthorizationError('Email must be a string.');
		if (!email || email?.length === 0) throw new AuthorizationError('Email is required.');

		if (typeof password !== 'string') throw new AuthorizationError('Password must be a string.');
		if (!password || password?.length === 0) throw new AuthorizationError('Password is required.');

		// SELECT * FROM users WHERE email = ? AND active = 1
		let userLookup = await prisma.users.findFirst({ where: { email: email, status: true, locked: false } });

		if (!userLookup) {
			throw new AuthorizationError('Invalid email/password.');
		}

		const isCorrectPassword = await bcrypt.compare(password, userLookup.password as string);

		if (!isCorrectPassword) {
			throw new AuthorizationError('Invalid email/password.');
		}

		const sesId = createUserSession(request, userLookup);

		user = {
			token: await sesId,
			id: userLookup.id
		};

		return user as UserSession;
	}),
	'user-pass'
);

/**
 * Retrieves the user ID from the session data.
 * @param request - The request object.
 * @returns A promise that resolves to the user ID or undefined if not found.
 */
export async function getUserId(request: Request) {
	const user = await getUser(request);
	const userId = user ? user.id : undefined;
	return userId;
}

/**
 * Retrieves the user associated with the provided request.
 * @param request - The request object.
 * @returns A promise that resolves to the user object if found, or null if not found.
 * @throws An error if the user is not found or if there is an issue with the logout process.
 */
export async function getUser(request: Request) {
	const session = await getSession(request);
	const data = session.get('token');
	if (data === undefined) return null;

	const valid = await validateSession(request, data);
	if (!valid) return null;

	const user = await getUserBySession(data.token);
	if (user) return user;

	throw await logout(request);
}

/**
 * Requires a user ID for the given request.
 * If the user ID is not found, it redirects to the login page.
 * @param request - The request object.
 * @param redirectTo - The path to redirect to if the user ID is not found. Defaults to the current path.
 * @returns The user ID.

export async function requireUserId(request: Request, redirectTo: string = new URL(request.url).pathname) {
	const userId = await getUserId(request);
	if (!userId) {
		console.log('USER NOT FOUND');
		const searchParams = new URLSearchParams([['redirectTo', redirectTo]]);
		throw redirect(`/?${searchParams}`);
	}
	return userId;
}
 */

/**
 * Logs out the user and destroys the session.
 * @param request - The request object.
 * @returns A redirect response after logging out the user.
 */
export async function logout(request: Request) {
	await logoutSession(request, '/');
}

export async function validateSession(request: Request, session: UserSession | null) {
	if (session) {
		const sesId = session?.token;

		if (!sesId) {
			return logoutSession(request, '/');
		}

		const timestamp = moment.utc();
		// SELECT session_id FROM users_sessions WHERE token = ? AND expires_at > ? AND code = '0'
		let sesLookup = await prisma.users_sessions.findFirst({
			where: { token: sesId, expires_at: { gt: timestamp.toDate() }, code: 0 }
		});

		return !!sesLookup;
	}

	return false;
}

export async function redirectLoggedIn(request: Request, redirectTo: string, codeDone: Boolean = false) {
	let sesAuth = await authenticator.isAuthenticated(request);

	if (sesAuth) {
		//check if code has been entered already
		const sessionValid = await validateSession(request, sesAuth);

		if (codeDone && sessionValid) {
			return redirect(redirectTo);
		} else if (!codeDone && !sessionValid) {
			return redirect('/user/verify?redirectTo=' + redirectTo);
		}
	}

	return null;
}

export async function redirectLoggedOut(request: Request, redirectTo: string = '/') {
	let sesAuth = await authenticator.isAuthenticated(request);

	if (!sesAuth) {
		return redirect(redirectTo);
	}

	return null;
}

export async function logoutSession(request: Request, redirectTo: string) {
	let sesObj = await authenticator.isAuthenticated(request);
	let sesId = sesObj?.token;

	if (!sesId) return await authenticator.logout(request, { redirectTo: redirectTo });

	return (await removeUserSession(sesId)) && (await authenticator.logout(request, { redirectTo: redirectTo }));
}

export function requireJwtToken(request: Request) {
	const token = request.headers.get('Authorization')?.split(' ')[1];
	if (token) {
		const decoded = verifyJwtToken(token);
		if (decoded) {
			return decoded;
		}
	}
	throw new Response('Unauthorized.', { status: 401 });
}
