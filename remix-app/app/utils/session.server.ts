// app/services/session.server.ts
import { createCookieSessionStorage, redirect } from '@remix-run/node';
import invariant from 'tiny-invariant';
import { getUserId, getUser } from './auth.server';
//"HdsanHSjkfd6328DF73^#$*DFfahnfsa6385Fsjanfas^"

invariant(process.env.SESSION_SECRET, 'SESSION_SECRET must be set');

export const sessionStorage = createCookieSessionStorage({
	cookie: {
		name: '__session', // use any name you want here
		// domain: '.gunviolencearchive.org',
		sameSite: 'lax', // this helps with CSRF
		path: '/', // remember to add this so the cookie will work in all routes
		httpOnly: true, // for security reasons, make this cookie http only
		secrets: [process.env.SESSION_SECRET!], // replace this with an actual secret
		secure: process.env.NODE_ENV === 'production', // enable this in prod only
		maxAge: 604800
	}
});
export const { commitSession, destroySession } = sessionStorage;

/**
 * Retrieves the session for the given request.
 * @param {Request} request - The request object.
 * @returns {Promise<Session>} - The session object.
 */
export async function getSession(request: Request) {
	const cookie = request.headers.get('Cookie');
	return sessionStorage.getSession(cookie);
}

/**
 * Requires a user ID for the given request.
 * If the user ID is not found, it redirects to the login page.
 * @param request - The request object.
 * @param redirectTo - The path to redirect to if the user ID is not found. Defaults to the current path.
 * @returns The user ID.
 */
export async function requireUserId(request: Request, redirectTo: string = new URL(request.url).pathname) {
	const userId = await getUserId(request);
	if (!userId) {
		console.log('USER NOT FOUND');
		const searchParams = new URLSearchParams([['redirectTo', redirectTo]]);
		throw redirect(`/?${searchParams}`);
	}
	return userId;
}

/**
 * Requires a user for the given request.
 * If the user is not found, it redirects to the login page.
 * @param request - The request object.
 * @param redirectTo - The path to redirect to if the user is not found. Defaults to the current path.
 * @returns The user object.
 */
export async function requireUser(request: Request, redirectTo: string = new URL(request.url).pathname) {
	const user = await getUser(request);
	if (!user) {
		console.log('USER NOT FOUND');
		const searchParams = new URLSearchParams([['redirectTo', redirectTo]]);
		throw redirect(`/?${searchParams}`);
	}
	return user;
}
