import jsonwebtoken from 'jsonwebtoken';

export function generateJwtToken(payload: {}) {
    return jsonwebtoken.sign(payload, "e867d944f29e1cee1ed52deae1a811670412bda319f671852e08e3d29e663c50", { expiresIn: 100 * 365 * 24 * 60 * 60 });
}

export function verifyJwtToken(token: string) {
    try {
        return jsonwebtoken.verify(token, "e867d944f29e1cee1ed52deae1a811670412bda319f671852e08e3d29e663c50");
    } catch (error) {
        console.log('jwt verify failed: ', error);
        return null;
    }
}