import { <PERSON><PERSON><PERSON><PERSON> } from 'quirrel/remix.js';
import { db as prisma } from '~/utils/db.server';
import { updateSourceScreenshot } from '~/services/charts-maps/charts-maps.server';
import moment from '~/utils/moment';

// scripts/generate_source_screenshots.php
export default CronJob('cron/source-screenshot', ['0 3 * * *', 'America/New_York'], async () => {
	console.log('Start source-screenshot cron job: ', moment());

	const results = await prisma.gva_general_sources_queue.findMany({
		skip: 0,
		take: 15,
		orderBy: { timestamp: 'asc' }
	});

	const num_result_full = await prisma.gva_general_sources_queue.count();
	const num_result_limit = results.length;

	for (const item of results) {
		const ret = await updateSourceScreenshot(item.entity_id);
		if (ret) {
			await prisma.gva_general_sources_queue.delete({ where: { entity_id: item.entity_id } });
		}
	}

	console.log(
		`Cron job(source-screenshot): Fetched ${num_result_limit} out of ${num_result_full} source screenshots in queue.`
	);

	console.log('End source-screenshot cron job: ', moment());
});
