import { QueryOperators } from "~/services/query/types";

export type GroupOperator = 'AND' | 'OR';

export interface FilterGroup {
  id: string;
  type: 'group';
  operator: GroupOperator;
  filters: (Filter | FilterGroup)[];
}

export interface Filter {
  id: string;
  type: 'filter';
  field: string;
  operator: QueryOperators;
  value: any;
}

export type SearchFilter = Filter | FilterGroup;

export type SearchState = {
  groups: FilterGroup[];
  page: number;
  perPage: number;
}