{
  "include": ["remix.env.d.ts", "**/*.ts", "**/*.tsx"],
  "exclude": ["tailwind.config.ts", "node_modules"],
  "compilerOptions": {
    "lib": ["DOM", "DOM.Iterable", "ES2022"],
    "isolatedModules": true,
    "esModuleInterop": true,
    "jsx": "react-jsx",
    "moduleResolution": "Bundler",
    "resolveJsonModule": true,
    "target": "ES2022",
    "strict": true,
    "allowJs": true,
    "forceConsistentCasingInFileNames": true,
    "baseUrl": ".",
    "paths": {
      "~/*": ["./app/*"]
    },

    // Skip type checking of declaration files to avoid errors in node_modules
    "skipLibCheck": true,

    // Remix takes care of building everything in `remix build`.
    "noEmit": true
  },
  "references": [
    { "path": "./tsconfig.tailwind.json" }
  ]
}
