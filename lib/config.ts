import { SubnetType } from "aws-cdk-lib/aws-ec2";

export const config = {
  environment: "Production",

  awsAccount: "************",
  awsRegion: "us-east-1",
    
  certificateArn: "arn:aws:acm:us-east-1:************:certificate/a95590cf-a9a0-418c-a8ae-20a4affd8551",
  containerRepository: "************.dkr.ecr.us-east-1.amazonaws.com",
  containerRepositoryArn: "arn:aws:ecr:us-east-1:************:repository/",
  codeRepositoryBranch: "master",
  codestarConnectionArn: "arn:aws:codestar-connections:us-east-1:************:connection/c299ce72-a036-46be-b64e-ef69cd9e0729",
  supersetSecretArn:"arn:aws:secretsmanager:us-east-1:************:secret:Production-SupersetSecret-DVVRbA",
  databaseUrlSecretArn:"arn:aws:secretsmanager:us-east-1:************:secret:Production-DBUrlSecret-69ZWcb",
  s3CredentialsSecretArn:"arn:aws:secretsmanager:us-east-1:************:secret:Production-S3CredentialsSecret-N1EEgj",
  emailSecretArn:"arn:aws:secretsmanager:us-east-1:************:secret:Production-EmailSecret-eeacWI",
  cloudflareSecretArn:"arn:aws:secretsmanager:us-east-1:************:secret:Production-CloudflareSecret-6sheqc",
  geocodeSecretArn:"arn:aws:secretsmanager:us-east-1:************:secret:Production-GeocodeSecret-HaF4mN",
  encryptionSecretArn:"arn:aws:secretsmanager:us-east-1:************:secret:Production-EncryptionSecrets-B5vRFG",
  tinyMceSecretArn:"arn:aws:secretsmanager:us-east-1:************:secret:Production-TinyMCESecret-eFZk5h",
  flyderSecretArn:"arn:aws:secretsmanager:us-east-1:************:secret:Production-FlyderSecret-kZQjsA",
  redisSecretArn:"arn:aws:secretsmanager:us-east-1:************:secret:Production-RedisSecret-XZyNrC",
    
  vpcCidr: "172.31.0.0/16",
  vpcMaxAzs: 2,

  auroraMinCapacity: 0.5,
  auroraMaxCapacity: 4,
  databaseSubnetType: SubnetType.PUBLIC,

  dbSchemaName: 'gva',
  dbName: 'gva',
  dbUsername: 'gva',

  filePublicUrl: 'https://www.gunviolencearchive.org/sites/default/files/',
};