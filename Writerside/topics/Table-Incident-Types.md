# Incident Types Table

## Overview
The `incident_types` table manages the classification and categorization of gun violence incidents, allowing multiple type assignments per incident with weighted relationships.

## Table Structure

### Primary Fields
- `incident_id`: Part of composite primary key, references incidents table
- `type_tid`: Part of composite primary key, references taxonomy table
- `weight`: Integer indicating the significance of this type classification
- `changed_date`: Unix timestamp of last modification

### Related Tables
- Links to `incidents` table via `incident_id`
- Links to `taxonomy` table via `type_tid` for type definitions
- Has corresponding `incident_types_pending` table for draft changes
- Has corresponding `incident_types_temp` table for bulk operations

## Common Queries

### Type Assignment
```typescript
const qb = new QueryBuilder('gva_data.incident_types', 'it');
qb.join('gva_data.taxonomy', 't.tid = it.type_tid', 'inner', 't');
qb.where('t.vocabulary', '=', 'incident_types');
```

### Mass Shooting Analysis
```typescript
const massTid = await qb.getTaxonomyItem('incident_types', 'mass_shooting');
qb.where('it.type_tid', '=', massTid);
qb.where('it.weight', '>=', 1);
```

## Data Processing

### Pending Types
The system maintains three related tables for type management:
- `incident_types`: Primary table for active classifications
- `incident_types_pending`: Holds proposed changes awaiting validation
- `incident_types_temp`: Temporary storage for bulk operations

### Type Migration
```typescript
// Example of promoting pending types to active
const promotePendingTypes = async (incidentId: number) => {
    await prisma.$transaction([
        prisma.incident_types.deleteMany({
            where: { incident_id: incidentId }
        }),
        prisma.$executeRaw`
            INSERT INTO gva_data.incident_types 
            SELECT * FROM gva_data.incident_types_pending 
            WHERE incident_id = ${incidentId}
        `
    ]);
};
```

## Best Practices
1. Always validate type taxonomy IDs before assignment
2. Maintain consistent weighting criteria
3. Use transaction blocks for type updates
4. Regular validation of pending types
5. Implement proper change tracking